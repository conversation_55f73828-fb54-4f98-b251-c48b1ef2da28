import { Middleware } from '@nuxt/types'

const isAuth: Middleware = ({ app, route: { fullPath }, error }) => {
  // Root page
  if (fullPath === '/') return

  // Has a token
  const hasToken = !!app.$apolloHelpers.getToken()
  if (hasToken) return

  // Whitelisted pages
  const list = ['/signin', '/reset-password', '/signup']
  const isWhitelisted = list.find((path) => fullPath.includes(path))
  if (isWhitelisted) return

  // Otherwise, it's an unauthorized access
  const message = 'Unauthorized Access'
  error({ statusCode: 403, message })
}

export default isAuth
