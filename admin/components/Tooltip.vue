<template>
  <span class="tooltip">
    <font-awesome-icon
      :icon="['fas', icon]"
      :style="{ color: `${iconColor}` }"
      class="tooltip-icon"
    />
    <span class="tooltip-text">{{ text }}</span>
  </span>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    text: { type: String, default: 'SET TOOLTIP' },
    icon: { type: String, default: 'question-circle' },
    iconColor: { type: String, default: '#555' },
  },
})
</script>

<style lang="scss" scoped>
.tooltip {
  margin: 0 10px;
  position: relative;
  &:hover {
    cursor: pointer;
  }

  &-text {
    visibility: hidden;
    width: 220px;
    background-color: black;
    color: #fff;
    font-size: 10px;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 150%;
    left: 50%;
    margin-left: -110px;

    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      margin-left: -5px;
      border-width: 5px;
      border-style: solid;
      border-color: black transparent transparent transparent;
    }
  }
  &:hover &-text {
    visibility: visible;
  }
}
</style>
