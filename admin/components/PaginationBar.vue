<template>
  <div class="pagination">
    <p class="pagination__summary">
      {{ summaryTitle }} {{ from }}-{{ to }}件（全{{ itemCount }}件）
    </p>
    <p class="pagination__buttons">
      <NuxtLink
        class="button"
        :class="{ disabled: !hasPreviousPage }"
        :event="!hasPreviousPage ? '' : 'click'"
        :to="{ name: path, query: { ...$route.query, page: prevPage } }"
      >
        前へ
      </NuxtLink>
      <NuxtLink
        class="button"
        :class="{ disabled: !hasNextPage }"
        :event="!hasNextPage ? '' : 'click'"
        :to="{ name: path, query: { ...$route.query, page: nextPage } }"
      >
        次へ
      </NuxtLink>
    </p>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { SortFindManyPropertyInput } from '~/types/types'

export default Vue.extend({
  props: {
    path: { type: String, required: true },
    currentPage: { type: Number, default: () => 1 },
    perPage: { type: Number, default: () => 10 },
    pageCount: { type: Number, default: () => 1 },
    itemCount: { type: Number, default: () => 0 },
    hasNextPage: { type: Boolean, default: () => false },
    hasPreviousPage: { type: Boolean, default: () => false },
    totalItems: { type: Number, default: () => 0 },
    summaryTitle: { type: String, default: () => '反響一覧' },
    sort: {
      type: String,
      default: () => SortFindManyPropertyInput.CreatedatDesc,
    },
  },
  computed: {
    from(): number {
      return (this.currentPage - 1) * this.perPage + 1
    },
    to(): number {
      const lastItem = this.perPage * this.currentPage
      if (lastItem > this.itemCount) return this.itemCount
      return lastItem
    },
    nextPage(): number {
      return this.currentPage + 1
    },
    prevPage(): number {
      return this.currentPage - 1
    },
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/settings/variables';

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:last-of-type {
    margin-top: 15px;
  }

  &:first-of-type {
    margin-bottom: 15px;
  }

  &__summary {
    font-size: 14px;
  }

  &__buttons {
    display: flex;
    .button {
      padding: 10px 20px;
      color: $color_base;
      border: 1px solid $color_emperor;
      border-radius: 4px;
      background-color: $color_white;
      font-size: 12px;

      &:not(:last-child) {
        margin-right: 10px;
      }

      &.disabled {
        border-color: $color_silver_chalice;
        color: $color_silver_chalice;
      }
    }
  }
}
</style>
