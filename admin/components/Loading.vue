<template lang="html">
  <div v-if="loading" class="loading-overlay">
    <spin />
  </div>
</template>

<script>
export default {
  data: () => ({
    loading: false,
    overlay: false,
  }),
  methods: {
    start() {
      this.loading = true
      this.overlay = true
    },
    finish() {
      this.loading = false
      this.overlay = false
    },
    fail(error) {
      console.log(error)
    },
  },
}
</script>
<style lang="scss" scoped>
.loading-overlay {
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  width: calc(100% - 280px); // FIXME: hardcoded sidebar width
  left: 280px; // FIXME: hardcoded, set on variables as width of sidebar?
  height: 100vh;
  z-index: 100;
}
</style>
