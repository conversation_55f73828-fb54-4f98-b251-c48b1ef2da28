<template>
  <popup-modal ref="popup">
    <span class="close" @click="cancel">
      <img src="~/assets/images/cancel2.svg" />
    </span>
    <h2 style="margin-top: 0">{{ title }}</h2>
    <p>{{ message }}</p>
    <div class="btns">
      <button class="cancel-btn" @click="cancel">{{ cancelButton }}</button>
      <button :class="type" @click="confirm">{{ okButton }}</button>
    </div>
  </popup-modal>
</template>

<script lang="ts">
import Vue from 'vue'
import noop from 'lodash/noop'
import {
  ConfirmOptions,
  ConfirmOptionsType,
  eventBus,
} from '~/plugins/confirm-dialog'

const defaultValue: ConfirmOptions = {
  title: '',
  message: '',
  type: ConfirmOptionsType.Danger,
  okButton: '削除する',
  cancelButton: 'キャンセル',
  okCallback: noop,
  cancelCallback: noop,
}

export default Vue.extend({
  name: 'ConfirmDialogue',
  data: () => ({ ...defaultValue }),
  mounted() {
    eventBus.on<ConfirmOptions>('showConfirmDialog', this.handleEvent)
  },
  beforeDestroy() {
    eventBus.off<ConfirmOptions>('showConfirmDialog', this.handleEvent)
  },
  methods: {
    handleEvent(options?: ConfirmOptions) {
      if (!options) throw new Error('Option is required')
      this.setOptions(options)
      ;(this.$refs.popup as any).open()
    },
    cancel() {
      ;(this.$refs.popup as any).close()
      if (this.cancelCallback) this.cancelCallback()
      this.setOptions(defaultValue)
    },
    confirm() {
      ;(this.$refs.popup as any).close()
      if (this.okCallback) this.okCallback()
      this.setOptions(defaultValue)
    },
    setOptions(options: ConfirmOptions) {
      Object.entries(options).forEach(([field, value]) => {
        if (!value) return
        this.$set(this, field, value)
      })
    },
  },
})
</script>

<style scoped>
.close {
  height: 22px;
  width: 22px;
  float: right;
}
.close img {
  filter: invert(63%) sepia(100%) saturate(0%) hue-rotate(288deg)
    brightness(102%) contrast(102%);
}
p {
  color: #333;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
  text-align: center;
  margin-top: 25px;
}
.btns {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  max-width: 60%;
  text-align: center;
  margin: 0 auto;
  position: absolute;
  left: 25%;
  bottom: 24px;
}

.danger {
  box-sizing: border-box;
  height: 40px;
  width: 120px;
  border: none;
  border-radius: 4px;
  background-color: #d1001a;
  color: #fff;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
  padding: -0.5em 1em;
  text-transform: uppercase;
  cursor: pointer;
  margin-left: 10px;
}

.primary {
  box-sizing: border-box;
  height: 40px;
  width: 120px;
  border: none;
  border-radius: 4px;
  background-color: #2470c7;
  color: #fff;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
  padding: -0.5em 1em;
  text-transform: uppercase;
  cursor: pointer;
  margin-left: 10px;
}

.cancel-btn {
  box-sizing: border-box;
  height: 40px;
  width: 120px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
  color: #555;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
  padding: -0.5em 1em;
  text-transform: uppercase;
  cursor: pointer;
  margin-right: 10px;
}
</style>
