<template>
  <div class="sidebar">
    <img class="logo" src="/logo_white.png" />
    <section v-for="(menus, index) in menuSections" :key="index">
      <ul>
        <li
          v-for="menu in menus"
          :key="menu.label"
          :class="{ selected: isActive(menu.path), disabled: menu.isDisabled }"
        >
          <nuxt-link :to="menu.path" :event="menu.isDisabled ? '' : 'click'">
            {{ menu.label }}
          </nuxt-link>
          <span
            v-if="
              menu.path === '/inquiries' && $accessor.getCountReadInquiries > 0
            "
            class="unread-number"
          >
            {{ $accessor.getCountReadInquiries }}
          </span>
        </li>
      </ul>
    </section>
    <div v-if="$accessor.isAdmin" class="switch-agency">
      <switch-agency
        :options="viewedAsOptions"
        :selected="$accessor.viewedAs"
        @select-agency="handleOnViewedAsChange"
      />
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import getUserGql from '~/graphql/queries/getUser.gql'
import getAgenciesGql from '~/graphql/queries/getAgencies.gql'
import { GetAgenciesQuery } from '~/types/types'
import menus from '~/assets/jsons/menus.json'
const defaultAvatar = require('~/assets/images/icon_user.jpg')

interface ViewedAsOption {
  image: string
  name: string
  id: string
  selected?: boolean
}

const administrator: ViewedAsOption = {
  name: 'Administrator',
  id: 'administrator',
  image: defaultAvatar,
}

export default Vue.extend({
  data: () => ({
    userId: '',
    isAdmin: false,
    agencies: {} as GetAgenciesQuery['agencies'],
  }),
  apollo: {
    user: {
      query: getUserGql,
    },
    agencies: {
      query: getAgenciesGql,
    },
  },
  computed: {
    viewedAsOptions(): ViewedAsOption[] {
      const items = this.agencies?.items || []
      return [
        administrator,
        ...items.map((agency) => ({
          image: agency.public?.imageUrl
            ? this.$config.cdnUrl + agency.public?.imageUrl
            : defaultAvatar,
          name: agency.public?.name || '',
          id: agency._id,
        })),
      ]
    },
    menuSections() {
      if (!this.currentAgencyId) return menus.admin

      const modifiedMenu = menus.agencyOwner[1][0]
      modifiedMenu.path = modifiedMenu.path.replace(
        '{agencyId}',
        this.currentAgencyId
      )
      if (!this.$accessor.isAdmin) return menus.agencyOwner

      // If currently admin behave as an agency,
      // Disable some menus, as we are not yet support it
      const disabledMenuIndex = [1, 2]
      return [
        menus.agencyOwner[0],
        menus.agencyOwner[1].map((menu, index) => {
          const shouldModify = disabledMenuIndex.includes(index)
          if (!shouldModify) return menu
          return { ...menu, isDisabled: true }
        }),
      ]
    },
    currentAgencyId() {
      return this.$accessor.currentAgencyId
    },
  },
  watch: {
    async currentAgencyId(newValue, oldValue) {
      if (newValue === oldValue) return
      await this.$accessor.fetchReadInquiry()
    },
  },
  mounted() {
    const userInfo = this.$getUserInfo()
    if (userInfo.roles.includes('admin')) {
      if (!this.$store.state.viewedAs) {
        this.handleOnViewedAsChange('administrator')
      }
      this.$accessor.setAdmin(true)
    } else {
      this.$accessor.setViewedAs(userInfo?.agencyId)
    }
    this.$accessor.fetchReadInquiry()
    this.$accessor.fetchInhouseAgencyIds()
  },
  methods: {
    isActive(menuPath: string) {
      const { path } = this.$nuxt.$route
      if (menuPath === '/' && menuPath !== path) return false
      return path.includes(menuPath)
    },
    handleOnViewedAsChange(agencyId: string) {
      this.$accessor.setViewedAs(agencyId)
    },
  },
})
</script>

<style lang="scss" scoped>
@mixin flex {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
}

.sidebar {
  background: #1f3a4f;
  padding: 20px;
  width: 250px;
  height: 100vh;
  @include flex;

  .logo {
    width: 150px;
    filter: brightness(0) invert(1);
  }

  section {
    padding: 20px 0;
    @include flex;

    &:not(:last-child) {
      border-bottom: 1px solid #5f6d77;
    }

    ul {
      display: flex;
      flex-direction: column;
      padding: 0;

      li {
        display: flex;
        align-items: stretch;
        justify-content: stretch;
        margin: 0;
        list-style: none;
        position: relative;

        a {
          font-size: 14px;
          font-weight: bold;
          flex: 1;
          margin: 0;
          padding: 10px 0;
          color: #fff;

          &.nuxt-link-exact-active.nuxt-link-active,
          &:hover,
          &.selected {
            margin: 0 -20px;
            padding: 10px 20px;
            background-color: #637786;
          }
        }
        &.disabled {
          opacity: 0.4;
        }
      }
    }
  }
}

.unread-number {
  background: #fc7a7a;
  color: #fff;
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 1;
  font-weight: bold;
  position: absolute;
  right: 0;
  top: 50%;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  transform: translateY(-50%);
}

.switch-agency {
  margin-top: auto;
}
</style>
