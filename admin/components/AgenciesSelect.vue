<template>
  <form-select
    v-model="selected"
    :options="options"
    :error="error"
    label="店舗 (Administrator only)"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import getAgenciesGql from '~/graphql/queries/getAgencies.gql'
import { Agency } from '~/types/types'

export default Vue.extend({
  props: {
    value: { type: String, required: true },
    error: { type: String, required: true },
  },
  data: () => ({ options: [] }),
  apollo: {
    agencies: {
      query: getAgenciesGql,
      result({ data }) {
        this.options = data.agencies.items.map((agency: Agency) => ({
          value: agency._id,
          label: agency.basic?.companyName,
        }))
      },
    },
  },
  computed: {
    selected: {
      get(): string {
        return this.value
      },
      set(newValue: string) {
        this.$emit('input', newValue)
      },
    },
  },
})
</script>
