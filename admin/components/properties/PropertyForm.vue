<template>
  <div class="property-form">
    <div class="form building">
      <section class="left">
        <div class="section-title">
          <h2>建物情報</h2>
        </div>
        <div v-if="!(value && value._id)">
          <agencies-select
            v-if="isAgencySelectVisible"
            v-model="selectedAgencyId"
            :error="getError('agencyId')"
          />  
        </div>
        <form-input-text
          v-model="form.buildingName"
          :error="getError('buildingName')"
          label="建物名"
          caption="こちらの物件名は管理用です。サイトには掲載されません。"
          placeholder="建物名"
          is-optional
        />
        <address-input
          ref="addressInput"
          v-model="form.address"
          :errors="getError('address')"
        />
        <div class="horizontal">
          <form-select
            v-model="form.buildingType"
            :error="getError('buildingType')"
            label="物件種別"
            :options="options.buildingType"
          />
          <form-select
            v-model="form.structure"
            :error="getError('structure')"
            :options="options.structure"
            label="建物構造"
          />
        </div>
        <div class="horizontal">
          <form-select
            v-model.number="form.maxFloor"
            :error="getError('maxFloor')"
            :options="options.maxFloor"
            label="物件階層"
          />
          <div class="form-input">
            <label class="form-input__label">施工年月</label>
            <date-select
              v-model="form.builtDate"
              reverse
              :is-day-visible="false"
              :error="getError('builtDate')"
            />
          </div>
        </div>
        <access-input
          ref="accessInput"
          v-model="form.access"
          :error="getError('access')"
        />
        <div class="property-comment">
          <label class="form-input__label">
            物件コメント
            <span class="optional">任意</span>
          </label>
          <p class="form-input__caption">
            英語以外の言語でのコメント入力がない場合、英語コメントの内容を自動表示します。
          </p>
          <ul class="list-languages">
            <li
              v-for="language in languages"
              :key="language.code"
              :class="{ 'language-active': languageValue == language.code }"
              @click="handleLanguage(language)"
            >
              {{ language.name }}
            </li>
          </ul>
          <div v-for="language in languages" :key="language.code">
            <textarea
              v-if="languageValue == language.code"
              v-model="form.languageComment[language.code]"
              class="textarea-language"
              rows="8"
              placeholder="物件の情報、周辺情報、担当者コメントなどをご入力ください。"
            ></textarea>
          </div>
        </div>
      </section>
      <section class="right">
        <drop-upload
          v-model="form.images.collections"
          title="建物の写真(20枚まで)"
          caption="推奨：横1000px × 縦800px、最大アップロードサイズ：1.5MB"
          :path="`agencies/${selectedAgencyId}/images/properties/collections`"
          :limit="20"
          :error="getError('images.collections')"
        />
      </section>
    </div>

    <!-- Tab Rooms -->
    <tabs
      v-if="form.rooms && form.rooms.length > 0"
      :selected-index="selectedTabIndex"
      @on-tab-changed="selectedTabIndex = $event"
      @add-click="addRoom"
      @dupe-click="cloneRoom"
      @delete-click="deleteRoom"
    >
      <tab
        v-for="(room, index) in form.rooms"
        :key="index"
        :title="get(room, 'roomName') || '(No name)'"
        tab-class="form room"
      >
        <room-form
          ref="rooms"
          v-model="form.rooms[index]"
          :nearest-station-id="nearestStationId"
          :agency-id="selectedAgencyId"
          :action="action"
          :errors="getError(`rooms[${index}]`)"
        />
      </tab>
    </tabs>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import minBy from 'lodash/minBy'
import get from 'lodash/get'
import isObject from 'lodash/isObject'
import cloneDeep from 'lodash/cloneDeep'
import set from 'lodash/set'
import isNil from 'lodash/isNil'
import * as Yup from 'yup'
import { createDict, validateForm, watchAndValidate } from './properties.utils'
import { getOptions } from '~/i18n'
import { roomInput } from '~/components/properties/RoomForm.vue'
import { addressInput } from '~/components/properties/AddressInput.vue'
import { accessInput } from '~/components/properties/AccessInput.vue'
import * as YupConstants from '~/constants/yup'

import {
  CreateOnePropertyInput,
  EnumPropertyBuildingType,
  EnumPropertyStructure,
  Maybe,
  PropertyRooms,
} from '~/types/types'

const propertySchema = Yup.object().shape({
  agencyId: YupConstants.required('select'),
  buildingName: Yup.string().optional(),
  buildingType: YupConstants.required('select'),
  structure: YupConstants.required('select'),
  maxFloor: YupConstants.required('select'),
  images: Yup.object({
    collections: Yup.array().of(Yup.string()).required().min(1),
  }),
  builtDate: YupConstants.required('date'),
})

const propertyInput: CreateOnePropertyInput = {
  agencyId: '',
  buildingName: '',
  address: addressInput,
  buildingType: '' as EnumPropertyBuildingType,
  structure: '' as EnumPropertyStructure,
  maxFloor: ('' as unknown) as number,
  images: { collections: [] },
  builtDate: '',
  access: accessInput,
  rooms: [roomInput],
  languageComment: {
    en: '',
    zhTw: '',
    zhCn: '',
    kr: '',
    jp: '',
  },
}

const propertyErrors = createDict(propertyInput)
function setTabIndex(
  roomCode: string,
  rooms: Maybe<Array<PropertyRooms>>
): number {
  const foundIndex = rooms?.findIndex((room) => room?.code === roomCode) || 0
  if (foundIndex === -1) return 0
  return foundIndex
}

export type langI = {
  name: any
  code: any
}

export default Vue.extend({
  props: {
    value: {
      type: Object as PropType<CreateOnePropertyInput>,
      default() {
        return null
      },
    },
    action: {
      type: String as PropType<'create' | 'update'>,
      required: true,
    },
    agencyId: {
      type: String,
      default() {
        return null
      },
    },
    roomCode: {
      type: String,
      default() {
        return null
      },
    },
    fullAddress: {
      type: String,
      required: true,
    },
  },

  data() {
    const input = cloneDeep(propertyInput)
    // To fix the editing property bugs
    if (this.action === 'update') {
      input.rooms.splice(0, 1)
    }

    const initialData = {
      form: this.value || input,
      errors: cloneDeep(propertyErrors),
      selectedTabIndex: 0,
      options: {
        buildingType: getOptions('property.buildingType'),
        structure: getOptions('property.structure'),
        maxFloor: Array(100)
          .fill(0)
          .map((__, index: number) => ({
            value: index + 1,
            label: index + 1,
          })),
      },
      languageValue: 'en',
    }

    if (this.action === 'update') {
      initialData.selectedTabIndex = setTabIndex(
        this.roomCode,
        initialData.form.rooms as any
      )
    }

    if (this.agencyId) initialData.form.agencyId = this.agencyId
    return initialData
  },

  computed: {
    languages(): langI[] {
      return [
        { code: 'en', name: '英語' },
        { code: 'zhCn', name: '簡体字' },
        { code: 'zhTw', name: '繁体字' },
        { code: 'kr', name: '韓国語' },
        { code: 'jp', name: '日本語' },
      ]
    },
    nearestStationId(): any {
      const stations = (this.form.access?.stations || []).filter(
        (station) => station?.stationId !== ''
      )
      const nearest = minBy(stations, 'distance.walk')
      return get(nearest, 'stationId', 'UNKNOWN')
    },
    selectedAgencyId: {
      get(this: any) {
        return this.agencyId || this.form.agencyId
      },
      set(this: any, newValue: string) {
        this.form.agencyId = newValue
      },
    },
    isAgencySelectVisible(): boolean {
      return !this.$accessor.currentAgencyId && this.$accessor.isAdmin
    },
  },

  watch: {
    'form.builtDate': watchAndValidate('builtDate', propertySchema),
    'form.images.featured': watchAndValidate('images.featured', propertySchema),
    'form.images.collections': watchAndValidate(
      'images.collections',
      propertySchema
    ),
    agencyId(newValue) {
      this.form.agencyId = newValue
    },
    roomCode(newValue) {
      if (this.action === 'create') return
      this.selectedTabIndex = setTabIndex(newValue, this.form.rooms as any)
    },
    value(newValue) {
      const assign = (base: any, field: string) => {
        const baseObj = get(base, field)

        if (isObject(baseObj) && !Array.isArray(baseObj)) {
          for (const childField in baseObj) {
            assign(base, `${field}.${childField}`)
          }
          return
        }

        const incomingData = get(newValue, field.slice('form.'.length))
        if (isNil(incomingData)) return
        set(base.form, field.slice('form.'.length), incomingData)
      }

      assign(this, 'form')

      if (this.action === 'update') {
        this.selectedTabIndex = setTabIndex(this.roomCode, newValue.rooms)
      }
    },
  },

  methods: {
    get,
    getError(path: string) {
      return get(this.errors, path)
    },
    addRoom() {
      const newRoom = cloneDeep(roomInput)
      this.form.rooms.push(newRoom)
    },
    cloneRoom(index: number) {
      const clonedRoom = cloneDeep(this.form.rooms[index])
      this.form.rooms.push(clonedRoom)
    },
    deleteRoom(index: number) {
      this.form.rooms.splice(index, 1)
    },
    getValue() {
      return this.form
    },
    async validate() {
      let isValid = true
      try {
        await validateForm(propertySchema, this.form, this)
      } catch (error) {
        console.warn('validate_1: ', error)
        isValid = false
      }

      try {
        const validations = [
          (this.$refs.addressInput as any).validate(),
          (this.$refs.accessInput as any).validate(), // FIXME: validate access
          ...(this.$refs.rooms as any).map((roomRef: any) =>
            roomRef.validate()
          ),
        ]
        const result = await Promise.all(validations)
        isValid = !result.some((isValid) => !isValid)
      } catch (error) {
        console.warn('validate_2: ', error)
        isValid = false
      }

      return isValid
    },
    handleLanguage(language: any) {
      this.languageValue = language.code
    },
  },
})
</script>

<style lang="scss">
.horizontal {
  display: flex;
  & > * {
    flex: 1;

    &:not(:last-child) {
      margin-right: 20px;
    }
  }
}

.form {
  &.building,
  &.room {
    display: flex;
  }

  &.building {
    margin-bottom: 100px;
  }
}

.left {
  flex: 3;
  margin-right: 50px;
}

.right {
  flex: 2;
}

.property-comment {
  .list-languages {
    display: flex;
    color: #555;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;
    border-bottom: 2px solid #ddd;

    li {
      padding: 0 12px 4px;
      cursor: pointer;
    }
  }

  .textarea-language {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 10px;
  }

  .language-active {
    border-bottom: 2px solid #555;
    margin-bottom: -1px;
  }
}
</style>
