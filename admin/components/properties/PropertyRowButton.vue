<template>
  <div class="table-row__status">
    <button
      class="btn__status"
      :class="{ public: visibility }"
      :disabled="visibility"
      @click="handleVisibility"
    >
      公開中
    </button>
    <button
      class="btn__status"
      :class="{ private: !visibility }"
      :disabled="!visibility"
      @click="handleVisibility"
    >
      非公開
    </button>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import updateRoomVisibility from '~/graphql/properties/updateRoomVisibility.mutation.gql'
import { ConfirmOptionsType } from '~/plugins/confirm-dialog'

/**
 * FIXME: delete this component soon
 */
export default Vue.extend({
  props: {
    visibility: { type: Boolean, required: true },
    propertyId: { type: [String, Number], required: true },
    roomCode: { type: [String, Number], required: true },
  },
  methods: {
    handleVisibility() {
      this.$showConfirmDialog({
        type: ConfirmOptionsType.Primary,
        message: `${
          this.visibility
            ? '選択した物件を非公開にしますか'
            : '選択した物件を公開にしますか'
        }`,
        okButton: '変更',
        cancelButton: 'キャンセル',
        okCallback: async () => {
          const visibilityType = this.visibility ? 'private' : 'public' // switch 1 -> 0, 0 -> 1
          const options = {
            mutation: updateRoomVisibility,
            variables: {
              propertyId: this.propertyId,
              code: this.roomCode,
              value: visibilityType,
            },
          }
          const result = await this.$apollo.mutate(options)
          const isSuccess = !!get(result, `data.updateRoomVisibility`)
          if (isSuccess) {
            this.$emit('on-updated', true)
            this.$toast.success('Visibility Room has been updated', {
              duration: 10000,
            })
          } else {
            this.$toast.global.commonError()
          }
        },
      })
    },
  },
})
</script>

<style lang="scss" scoped>
button {
  border: none;
}
.table-row__status {
  border: 1px solid #e8e8e8;
  border-radius: 3px;
  display: flex;
  background-color: #fff;
  font-size: 14px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 7%);
}
.btn__status {
  flex: 1;
  padding: 6px 8px;
  border-radius: 3px;
  background: none;
  margin: 2px;
  color: #aaa;
}
.btn__status.public {
  color: #fff;
  background-color: #0cb21a !important;
}
.btn__status.private {
  color: #fff;
  background-color: #abafb3 !important;
}
</style>
