<template>
  <div class="room-fee-form">
    <div class="horizontal">
      <form-input-text
        v-model.number="form.monthly.rentFee"
        :error="get(errors, 'monthly.rentFee')"
        label="家賃(円)"
      />
      <form-input-text
        v-model.number="form.monthly.maintenanceFee"
        :error="get(errors, 'monthly.maintenanceFee')"
        label="管理費(円)"
      />
    </div>
    <div class="horizontal">
      <div class="horizontal">
        <form-input-text
          v-model.number="form.moveIn.deposit.value"
          :error="get(errors, 'moveIn.deposit.value')"
          label="敷金"
        />
        <form-radio
          v-model="form.moveIn.deposit.feeType"
          :error="get(errors, 'moveIn.deposit.feeType')"
          :options="options.feeType"
          label="⠀"
        />
      </div>
      <div class="horizontal">
        <form-input-text
          v-model.number="form.moveIn.keyMoney.value"
          :error="get(errors, 'moveIn.keyMoney.value')"
          label="礼金"
        />
        <form-radio
          v-model="form.moveIn.keyMoney.feeType"
          :error="get(errors, 'moveIn.keyMoney.feeType')"
          label="⠀"
          :options="options.feeType"
        />
      </div>
    </div>
    <div class="horizontal">
      <div class="horizontal">
        <form-input-text
          v-model.number="form.moveIn.agencyFee.value"
          :error="get(errors, 'moveIn.agencyFee.value')"
          label="仲介手数料"
        />
        <form-radio
          v-model="form.moveIn.agencyFee.feeType"
          :error="get(errors, 'moveIn.agencyFee.feeType')"
          :options="options.feeType"
          label="⠀"
        />
      </div>
      <div class="horizontal">
        <form-input-text
          v-model.number="form.moveIn.guarantorFee.value"
          :error="get(errors, 'moveIn.guarantorFee.value')"
          label="初回保証料"
        >
          <template slot="label">
            <tooltip
              text="ヶ月”を選択した場合、家賃 + 管理費の合計に対しての値となります"
            />
          </template>
        </form-input-text>
        <form-radio
          v-model="form.moveIn.guarantorFee.feeType"
          :error="get(errors, 'moveIn.guarantorFee.feeType')"
          label="⠀"
          :options="options.feeType"
        />
      </div>
    </div>

    <div class="horizontal">
      <div class="horizontal">
        <form-input-text
          v-model.number="form.moveIn.contractRenewalFee.value"
          :error="get(errors, 'moveIn.contractRenewalFee.value')"
          label="更新料"
        />
        <form-radio
          v-model="form.moveIn.contractRenewalFee.feeType"
          :error="get(errors, 'moveIn.contractRenewalFee.feeType')"
          :options="options.feeType"
          label="⠀"
        />
      </div>
      <slot></slot>
    </div>

    <div v-for="index in MAX_OTHERFEE_INPUT" :key="index" class="horizontal">
      <template v-if="!!form.others[index - 1]">
        <form-select
          v-model="form.others[index - 1].feeType"
          :options="options.otherFeeType"
          :error="get(errors, `others[${index - 1}].feeType`)"
          :label="`その他費用${index}`"
          :disable-default-option="false"
          :empty-value="OthersFeeType.Na"
          is-optional
        />
        <div class="horizontal">
          <form-input-text
            v-model.number="form.others[index - 1].value"
            :error="get(errors, `others[${index - 1}].value`)"
            :label="`費用${index}(円)`"
          />
          <form-select
            v-model="form.others[index - 1].chargeTiming"
            :error="get(errors, `others[${index - 1}].chargeTiming`)"
            :options="options.otherFeeChargeTiming"
            :disable-default-option="false"
            :empty-value="OthersChargeTiming.Na"
            label="請求時期"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import * as Yup from 'yup'
import get from 'lodash/get'
import set from 'lodash/set'
import cloneDeep from 'lodash/cloneDeep'
import flatten from 'flat'
import { createDict, validateForm } from '~/plugins/form.utils'
import { getOptions } from '~/i18n'
import * as YupConstants from '~/constants/yup'

import {
  EnumPropertyRoomsFeesMoveInDepositFeeType,
  EnumPropertyRoomsFeesOthersChargeTiming,
  EnumPropertyRoomsFeesOthersFeeType,
  PropertyRoomsFeesInput,
} from '~/types/types'

/**
 * A constant as main reference, how many other fee input
 * is being allowed to shows in the form
 */
const MAX_OTHERFEE_INPUT = 5

const roomOtherFeeSchema = Yup.object().shape({
  feeType: Yup.string().test(
    'required-fee-type',
    '選択してください。',
    (value, context) => {
      if (context.parent?.chargeTiming === undefined) return true
      if (
        context.parent?.chargeTiming !==
        EnumPropertyRoomsFeesOthersChargeTiming.Na
      ) {
        return value !== EnumPropertyRoomsFeesOthersFeeType.Na
      }
      return true
    }
  ),
  chargeTiming: Yup.string()
    .required()
    .when('feeType', {
      is: (value: string) => {
        if (value === undefined) return false
        return value !== EnumPropertyRoomsFeesOthersFeeType.Na
      },
      then: Yup.string().notOneOf(
        [EnumPropertyRoomsFeesOthersChargeTiming.Na],
        '選択してください。'
      ),
    }),
  value: Yup.number().when(['chargeTiming', 'feeType'], {
    is: 'na',
    then: Yup.number().min(0).max(0),
    otherwise: Yup.number()
      .typeError('入力してください。')
      .min(1, '入力してください。'),
  }),
})

const moveInFeeSchema = Yup.object().shape({
  feeType: YupConstants.required('select'),
  value: YupConstants.requiredNumber,
})

export const feeSchema = Yup.object().shape({
  monthly: Yup.object({
    rentFee: YupConstants.requiredNumber,
    maintenanceFee: YupConstants.requiredNumber,
  }),
  moveIn: Yup.object({
    deposit: moveInFeeSchema,
    keyMoney: moveInFeeSchema,
    agencyFee: moveInFeeSchema,
    guarantorFee: moveInFeeSchema,
    contractRenewalFee: moveInFeeSchema,
  }),
  others: Yup.array().of(roomOtherFeeSchema),
})

const commonMoveInFee = {
  feeType: EnumPropertyRoomsFeesMoveInDepositFeeType.MonthlyMultiplier,
  value: ('' as unknown) as number,
}

export const feesInput = {
  monthly: {
    rentFee: ('' as unknown) as number,
    maintenanceFee: ('' as unknown) as number,
  },
  moveIn: {
    deposit: { ...commonMoveInFee },
    keyMoney: { ...commonMoveInFee },
    agencyFee: { ...commonMoveInFee },
    guarantorFee: { ...commonMoveInFee },
    contractRenewalFee: { ...commonMoveInFee },
  },
  others: Array(MAX_OTHERFEE_INPUT)
    .fill(1)
    .map(() => ({
      feeType: 'na' as EnumPropertyRoomsFeesOthersFeeType,
      chargeTiming: 'na' as EnumPropertyRoomsFeesOthersChargeTiming,
      value: (0 as unknown) as number,
    })),
}

const feeErrors = createDict(feesInput)

export default Vue.extend({
  props: {
    value: {
      type: Object as PropType<PropertyRoomsFeesInput>,
      required: true,
    },
  },
  data() {
    // Add other fee fields input as much as max constants allowed
    const form = cloneDeep(this.value)
    for (let i = form.others?.length || 0; i < MAX_OTHERFEE_INPUT; i++) {
      form.others?.push({ ...feesInput.others[0] })
    }

    return {
      form,
      MAX_OTHERFEE_INPUT,
      OthersChargeTiming: EnumPropertyRoomsFeesOthersChargeTiming,
      OthersFeeType: EnumPropertyRoomsFeesOthersFeeType,
      errors: cloneDeep(feeErrors),
      options: {
        feeType: getOptions('room.feeType'),
        otherFeeType: getOptions('room.otherFeeType'),
        otherFeeChargeTiming: getOptions('room.otherFeeChargeTiming'),
      },
    }
  },
  /**
   * Watch any fields change,
   * validate it then notify the parent,
   */
  watch: Object.entries(flatten(feesInput)).reduce(
    (acc, [index]) => ({
      ...acc,
      async [`form.${index}`](this: any, newValue: any) {
        this.$emit('input', this.form)
        const normalizedIndex = index.replace(/(\.(\d+?)\.)/, '[$2].')

        // for otherFee fields, when both timing and type is 'NA'
        // reset the value to 0
        const keys = index.split('.')
        if (keys[0] === 'others') this.normalizeOtherFee(keys[1], keys[2])

        // the rest, just do validation
        try {
          await Yup.reach(feeSchema, index).validate(newValue)
          this.errors[normalizedIndex] = ''
        } catch (error) {
          this.errors[normalizedIndex] = error.message
        }
      },
    }),
    {}
  ),
  methods: {
    get,
    async validate() {
      this.errors = cloneDeep(feeErrors)
      await validateForm(feeSchema, this.value, this)
    },
    normalizeOtherFee(index: number, currentKey: string) {
      if (!this.form.others) return
      const fieldRow = this.form.others[index]
      if (fieldRow?.feeType === 'na' && fieldRow?.chargeTiming === 'na') {
        if (fieldRow?.value === 0) {
          set(this.errors, `others[${index}].feeType`, '')
          set(this.errors, `others[${index}].chargeTiming`, '')
          set(this.errors, `others[${index}].value`, '')

          return
        }

        if (currentKey === 'value') return

        // reset the other value fee to 0
        set(this.form, `others[${index}].value`, 0)
        setTimeout(() => {
          set(this.errors, `others[${index}].value`, '')
        }, 1)
      }
    },
  },
})
</script>
