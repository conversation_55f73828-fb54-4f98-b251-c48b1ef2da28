<template>
  <div class="building-access">
    <div
      v-for="(station, index) in stationValues"
      :key="index"
      class="building-access__field"
    >
      <form-input :label="index > 0 ? '' : '路線'">
        <nested-options
          placeholder="選択する"
          :value="getStation(index)"
          :items="combinedServiceLines"
          :max-depth="1"
          selected-separator=" "
          selected-suffix="駅"
          @input="setStation(index, $event)"
        />
        <div v-if="index === 0 && errors.stations" class="error">
          {{ get(errors, 'stations') }}
        </div>
        <div class="delete">
          <span @click="handleDelete(index)">リセット</span>
        </div>
      </form-input>
      <form-input-text
        :value="stationValues[index].distance.walk"
        :error="get(errors, `stations[${index}].distance.walk`)"
        :label="index > 0 ? '' : '徒歩'"
        suffix="分"
        @input="setDistance(index, 'walk', $event)"
      />
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import get from 'lodash/get'
import cloneDeep from 'lodash/cloneDeep'
import * as Yup from 'yup'
import { createDict, validateForm } from './properties.utils'
import getStationsGql from '~/graphql/properties/getStations.query.gql'
import { ServiceLine, Station } from '~/types/types'
import * as YupConstants from '~/constants/yup'

export const accessSchema = Yup.object({
  stations: Yup.array()
    .of(
      Yup.object({
        lineId: Yup.string(),
        stationId: Yup.string(),
        distance: Yup.object({
          bus: YupConstants.optionalNumber,
          walk: YupConstants.optionalNumber,
        }),
      })
    )
    .test(
      'at-least-one-selected',
      'オプションを一つ選択して下さい。',
      (stations: any) => {
        return stations?.some(
          (station: any) => station?.stationId && station?.stationId !== ''
        )
      }
    ),
})

const stationInput = {
  lineId: '',
  stationId: '',
  distance: {
    bus: 0,
    walk: 0,
  },
}

export const accessInput = {
  stations: [
    cloneDeep(stationInput),
    cloneDeep(stationInput),
    cloneDeep(stationInput),
  ],
}

const initEmptyErrors = () => ({ ...createDict(accessInput), stations: '' })

export default Vue.extend({
  props: {
    value: { type: Object as PropType<any>, required: true },
  },
  data: () => ({
    errors: initEmptyErrors(),
    stations: [] as ServiceLine[],
    searchText: '' as String,
    stationText: '駅' as String,
  }),

  apollo: {
    stations: {
      query: getStationsGql,
      fetchPolicy: 'cache-and-network',
    },
  },
  computed: {
    stationValues() {
      return [...this.value.stations, ...accessInput.stations].slice(0, 3)
    },
    combinedServiceLines(): any {
      return this.stations.map((station: any) => {
        const itemName = station.items
          .map((item: Station) => item?.name)
          .join(', ')
        const itemIds = station.items.map((item: Station) => item?.code)

        return {
          code: `${station.code}`,
          lineIds: itemIds,
          name: `${station.name}${this.stationText} - ${itemName}`,
        }
      })
    },
  },
  watch: {
    async value(newValue: any) {
      this.errors = initEmptyErrors()
      await validateForm(accessSchema, newValue, this)
    },
  },
  methods: {
    handleDelete(index: number) {
      const lineId = ''
      const stationId = ''
      const newValue = {
        ...this.value,
        stations: [...this.value.stations],
      }
      newValue.stations[index] = {
        ...this.stationValues[index],
        lineId,
        stationId,
      }
      this.$emit('input', newValue)
    },
    get,
    getStation(index: number) {
      const { stationId } = this.stationValues[index]
      return [stationId]
    },
    setStation(index: number, selectedPaths: any) {
      const stationId = selectedPaths.code
      const lineIds = selectedPaths.lineIds.join(',')
      const newValue = {
        ...this.value,
        stations: [...this.value.stations],
      }
      newValue.stations[index] = {
        ...this.stationValues[index],
        stationId,
        lineId: lineIds,
      }
      this.$emit('input', newValue)
    },
    setDistance(index: number, path: string, value: string) {
      const newValue = {
        ...this.value,
        stations: [...this.stationValues],
      }

      newValue.stations[index] = {
        ...this.stationValues[index],
        distance: {
          ...this.stationValues[index].distance,
          [path]: Number.parseInt(value),
        },
      }

      this.$emit('input', newValue)
    },

    async validate() {
      this.errors = initEmptyErrors()
      let isValid = true

      try {
        await validateForm(accessSchema, this.value, this)
      } catch (error) {
        isValid = false
      }

      return isValid
    },
  },
})
</script>

<style lang="scss" scoped>
.building-access {
  display: flex;
  flex-direction: column;

  &__field {
    display: flex;
    margin-bottom: 15px;

    & > :first-child {
      flex: 1;
    }

    & > *:not(:last-child) {
      margin-right: 10px;
      margin-bottom: 10px;
    }

    & > *:first-child {
      flex: 1;
    }
  }

  .delete {
    span {
      text-decoration: underline;
      font-size: 12px;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}
</style>

<style lang="scss">
// override the input max width
.building-access input {
  max-width: 50px;
}
</style>
