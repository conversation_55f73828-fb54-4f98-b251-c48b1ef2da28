<template>
  <div class="building__address">
    <div class="building__address__fields">
      <form-input-text
        v-model="postalCode"
        :error="get(errors, 'postalCode')"
        label="郵便番号"
        placeholder="123-4567"
      />
      <form-input label="住所">
        <address-picker
          placeholder="選択する"
          :prefecture="value.prefecture"
          :city="value.city"
          :chome="value.chome"
          @on-picked="handleAddressPick($event)"
        />
        <div v-if="get(errors, 'chome')" class="error">
          {{ get(errors, 'chome') }}
        </div>
      </form-input>
      <form-input-text
        v-model.trim="line"
        :error="get(errors, 'addressLine')"
        label="番地"
      />
    </div>
    <div>
      <GmapMap
        v-if="mapPosition"
        ref="mapRef"
        class="location__maps"
        :center="mapPosition"
        :zoom="14"
        map-type-id="roadmap"
        style="width: 100%; max-width: 700px; height: 300px"
      >
        <GmapMarker
          v-if="line"
          :position="mapPosition"
          :clickable="true"
          :draggable="false"
        />
        <GmapCircle
          v-else
          :center="mapPosition"
          :radius="500"
          :visible="true"
          :options="{ fillColor: 'red', fillOpacity: 0.1, strokeOpacity: 0 }"
        />
      </GmapMap>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import get from 'lodash/get'
import cloneDeep from 'lodash/cloneDeep'
import * as Yup from 'yup'
import { createDict, validateForm } from '~/plugins/form.utils'
import * as YupConstants from '~/constants/yup'
import { PropertyAddress } from '~/types/types'

export const addressSchema = Yup.object().shape({
  postalCode: YupConstants.optionalPostalCode,
  line: Yup.string(),
  prefecture: YupConstants.required('select'),
  city: YupConstants.required('select'),
  chome: YupConstants.required('select'),
})

export const addressInput = {
  postalCode: '',
  line: '',
  prefecture: '13',
  city: '',
  chome: '',
  coord: [0, 0],
}

const computedCreator = (field: string) => ({
  get(this: any): string {
    return this.value[field]
  },
  async set(this: any, newValue: string): Promise<void> {
    const newObj = { ...this.value, [field]: newValue }
    this.$emit('input', newObj)
    try {
      await Yup.reach(addressSchema, field).validate(newValue)
      this.errors[field] = null
    } catch (error) {
      this.errors[field] = error.message
    }
  },
})

const addressErrors = createDict(addressInput)

export default Vue.extend({
  props: {
    value: { type: Object as PropType<PropertyAddress>, required: true },
  },
  data() {
    return {
      mapPosition: {
        lat: parseFloat(get(this.value, 'coord[1]', 0)),
        lng: parseFloat(get(this.value, 'coord[0]', 0)),
      },
      errors: cloneDeep(addressErrors),
      isFetchGeocoding: false,
    }
  },
  computed: {
    postalCode: computedCreator('postalCode'),
    line: computedCreator('line'),
    baseAddress(): string {
      const { prefecture, city, chome } = this.value
      return this.$accessor.getAddress({ prefecture, city, chome })
    },
  },
  watch: {
    line: {
      handler(value: string) {
        if (!value) return
        if (!this.baseAddress) return
        this.geocodingLocation(value)
      },
    },
    baseAddress(value: string) {
      if (!value) return
      this.geocodingLocation(this.line)
    },
    value: {
      handler(newValue: PropertyAddress) {
        if (this.isFetchGeocoding) return
        this.mapPosition = {
          lat: parseFloat(get(newValue, 'coord[1]', 0)),
          lng: parseFloat(get(newValue, 'coord[0]', 0)),
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    get,
    handleAddressPick(newValue: {
      prefecture: string
      city: string
      chome: string
    }) {
      this.errors.chome = ''
      this.$emit('input', {
        ...this.value,
        ...newValue,
      })
    },
    async validate() {
      let isValid = true
      this.errors = cloneDeep(addressErrors)

      try {
        await validateForm(addressSchema, this.value, this)
      } catch (error) {
        isValid = false
      }

      return isValid
    },

    geocodingLocation(addressLine: string) {
      if (this.isFetchGeocoding) return
      this.isFetchGeocoding = true
      const fullAddress = `${this.baseAddress} ${addressLine || ''}`
      const apiUrl = 'https://maps.googleapis.com/maps/api/geocode/json'
      fetch(`${apiUrl}?address=${fullAddress}&key=${this.$config.gMapAPI}`)
        .then((response) => response.json())
        .then((data) => {
          if (data.status !== 'OK') return

          const locationResult = data.results[0]?.geometry?.location
          if (!locationResult) return

          const longitude = parseFloat(locationResult.lng)
          const latitude = parseFloat(locationResult.lat)
          this.mapPosition.lat = latitude
          this.mapPosition.lng = longitude
          this.$emit('input', {
            ...this.value,
            coord: [longitude, latitude],
          })
        })
        .finally(() => {
          this.isFetchGeocoding = false
        })
    },
  },
})
</script>

<style lang="scss" scoped>
.building__address {
  display: flex;

  & > * {
    flex: 1;

    &:not(:last-child) {
      margin-right: 20px;
    }
  }
}
</style>
