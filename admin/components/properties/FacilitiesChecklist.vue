<template>
  <div class="facilities-checklist">
    <div
      v-for="(section, sectionId) in sections"
      :key="sectionId"
      class="facilities-checklist__section"
      :data-content="sectionId"
    >
      <h4>{{ section.name }}</h4>
      <div class="facilities-checklist__section-content">
        <checkbox
          v-for="(label, code) in section.items"
          :key="code"
          :value="value[sectionId]"
          :key-value="code"
          :label="label"
          @input="handleOnChecked(sectionId, $event)"
        />
      </div>
    </div>
  </div>
</template>
_
<script lang="ts">
import Vue, { PropType } from 'vue'
import Checkbox from '../forms/Checkbox.vue'

interface FacilitiesObject {
  basic: string[]
  building: string[]
  other: string[]
}

export default Vue.extend({
  name: 'FacilitiesChecklist',
  components: { Checkbox },
  props: {
    value: {
      type: Object as PropType<FacilitiesObject>,
      required: true,
    },
  },
  data: () => ({
    sections: {
      basic: {
        name: '基本設備',
        items: {
          free_wifi: '無料Wi-Fi',
          air_conditioner: 'エアコン',
          floor_heating_system: '床暖房',
          bath_toilet_separated: 'バス・トイレ別',
          wash_basin: '独立洗面台',
          bath_reheating_system: '追い焚き',
          bath_dehumidifier: '浴室乾燥機',
          jacuzzi: 'ジェットバス',
          mist_sauna: 'ミストサウナ',
          bidet: '温水洗浄便座',
          washing_machine: '洗濯機',
          washing_machine_space: '室内洗濯機置き場',
          electric_stove: 'IHコンロ',
          gas_stove: 'ガスコンロ',
          builtin_kitchen: 'システムキッチン',
          counter_kitchen: 'カウンターキッチン',
          electric_water_heater: '電気温水器',
          gas_water_heater: 'ガス給湯器',
          loft: 'ロフト',
          walkin_closet: 'ウォークインクローゼット',
          auto_lock: 'オートロック',
          tv_intercom: 'TVインターホン',
          ceiling_light_installed: '照明器具付き',
        },
      },
      building: {
        name: '室外・サービス',
        items: {
          parking: '駐車場',
          motorbike_parking: 'バイク置き場',
          biycle_parking: '駐輪場',
          delivery_box: '宅配ボックス',
          elevator: 'エレベーター',
          concierge: 'コンシェルジュ',
          balcony: 'バルコニー',
          communal_garden: '共用庭',
          private_garden: '専用庭',
        },
      },
      other: {
        name: 'その他',
        items: {
          allow_pet: 'ペット可',
          allow_musical_instrument: '楽器可',
          allow_room_sharing: 'ルームシェア可',
          corner_room: '角部屋',
          barrier_free: 'バリアフリー',
        },
      },
    },
  }),
  methods: {
    handleOnChecked(sectionId: keyof FacilitiesObject, value: string[]) {
      const newValue = { ...this.value, [sectionId]: value }
      this.$emit('input', newValue)
    },
  },
})
</script>

<style lang="scss" scoped>
.facilities-checklist {
  margin-bottom: 10px;

  &__section {
    display: flex;
    flex-direction: column;
    background: #fff;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 20px;

    h4 {
      font-size: 12px;
      color: #333;
      margin-bottom: 15px;
    }

    &-content {
      display: flex;
      justify-content: space-between;
      flex-flow: row wrap;

      &:not(:last-child) {
        margin-bottom: 20px;
      }

      .checkbox {
        flex-basis: 32%;
        margin-bottom: 10px;
      }
    }

    /** Quick hack to fill the space with uneven items */
    &[data-content*='other'] > &-content::after,
    &[data-content*='basic'] > &-content::after {
      content: '';
      flex-basis: 32%;
    }
  }
}
</style>
