<template>
  <div class="form room" style="padding-top: 20px">
    <section class="left">
      <div class="section-title">
        <h2>部屋情報</h2>
      </div>
      <div class="horizontal">
        <div class="form-input">
          <label class="form-input__label">公開状態</label>
          <visibility-options v-model="form.visibility" />
        </div>
        <div />
      </div>
      <div class="horizontal">
        <form-input-text
          v-model="form.companyManagement"
          label="元付会社/管理会社"
          :error="get(errors, 'companyManagement')"
          is-optional
        />
        <form-input-text
          v-model="form.telephone"
          label="TEL"
          :error="get(errors, 'telephone')"
          is-optional
        />
      </div>
      <room-name
        :number-of-room="value.numberOfRoom"
        :layout="value.layout"
        :name="value.roomName"
        :station-id="nearestStationId"
      />
      <div class="horizontal">
        <form-input-text
          v-model="form.roomName"
          label="号室/部屋名"
          :error="get(errors, 'roomName')"
          is-optional
        />
        <div class="horizontal">
          <form-select
            v-model.number="form.numberOfRoom"
            :options="options.numberOfRoom"
            :error="get(errors, 'numberOfRoom')"
            label="間取数"
          />
          <form-select
            v-model="form.layout"
            :options="options.layout"
            :error="get(errors, 'layout')"
            label="間取りタイプ"
          />
        </div>
      </div>
      <div class="horizontal">
        <div class="horizontal">
          <form-input-text
            v-model.number="form.size"
            :error="get(errors, 'size')"
            label="平米(㎡)"
          />
          <form-select
            v-model="form.availability.status"
            :options="options.availabilityStatus"
            :error="get(errors, 'availability.status')"
            label="現況"
          />
        </div>
        <div class="horizontal">
          <div class="horizontal">
            <form-select
              v-model="form.floor"
              :options="options.floor"
              :error="get(errors, 'floor')"
              label="所在階数"
            />
            <form-select
              v-model="form.mainWindow"
              :options="options.mainWindow"
              :error="get(errors, 'mainWindow')"
              label="主要採光面"
            />
          </div>
        </div>
      </div>
      <div class="horizontal">
        <div class="horizontal">
          <form-select
            v-model="form.contractType"
            :options="options.contractType"
            :error="get(errors, 'contractType')"
            label="契約種別"
          />
          <form-select
            v-model="form.transactionType"
            :options="options.transactionType"
            :error="get(errors, 'transactionType')"
            label="取引態様"
          />
        </div>
        <form-radio
          v-model="availabilityData"
          :options="availabilityOptions"
          label="入居可能時期"
          container-class="top-choices"
          @input="onAvailabilityInput"
        />
      </div>

      <div class="section-title">
        <h2>賃料等の料金</h2>
      </div>
      <room-fee-form ref="roomFeeForm" v-model="form.fees">
        <form-select
          v-model="form.contractLength"
          :error="get(errors, 'contractLength')"
          :options="options.contractLength"
          label="契約期間(年)"
        />
      </room-fee-form>
      <div class="section-title">
        <h2>設備情報</h2>
      </div>
      <facilities-checklist v-model="form.facilities" />

      <div class="section-title">
        <h2>タグ</h2>
      </div>
      <tags-checklist v-model="form.tags" />
    </section>
    <section class="right">
      <drop-upload
        v-model="featuredImage"
        title="メイン写真(1枚)"
        caption="推奨：横1000px × 縦800px、最大アップロードサイズ：1.5MB"
        :path="`agencies/${agencyId}/images/properties/rooms/featured`"
        :limit="1"
        :error="get(errors, 'images.featured')"
      />
      <drop-upload
        v-model="floorPlanImage"
        title="間取り写真(1枚)"
        caption="推奨：横1000px × 縦800px、最大アップロードサイズ：1.5MB"
        :path="`agencies/${agencyId}/images/properties/rooms/floorplan`"
        :limit="1"
        :error="get(errors, 'images.floorPlan')"
      />
      <drop-upload
        v-model="form.images.collections"
        title="部屋の写真(20枚まで)"
        caption="推奨：横1000px × 縦800px、最大アップロードサイズ：1.5MB"
        :path="`agencies/${agencyId}/images/properties/rooms/collections`"
        :limit="20"
        :error="get(errors, 'images.collections')"
      />
    </section>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import cloneDeep from 'lodash/cloneDeep'
import get from 'lodash/get'
import set from 'lodash/set'
import * as Yup from 'yup'
import flatten from 'flat'
import { feesInput } from './RoomFeeForm.vue'
import {
  createDict,
  validateForm,
  watchAndValidate,
} from '~/plugins/form.utils'
import {
  EnumPropertyRoomsAvailabilityStatus as AvailabilityStatus,
  EnumPropertyRoomsContractType as ContractType,
  EnumPropertyRoomsMainWindow as MainWindow,
  RoomInput,
  EnumPropertyRoomsVisibility as Visibility,
  EnumPropertyRoomsTransactionType as TransactionType,
} from '~/types/types'
import { getOptions } from '~/i18n'
import * as YupConstants from '~/constants/yup'

export const roomSchema = Yup.object().shape({
  roomName: YupConstants.optional,
  companyManagement: YupConstants.optional,
  telephone: YupConstants.optional,
  numberOfRoom: YupConstants.requiredNumber,
  layout: YupConstants.required('select'),
  size: YupConstants.requiredNumber,
  mainWindow: YupConstants.required('select'),
  floor: YupConstants.required('select'),
  availability: Yup.object({
    status: YupConstants.required('select'),
    now: Yup.boolean(),
    inquiry: Yup.boolean(),
  }),
  visibility: YupConstants.required('select'),
  contractType: YupConstants.required('select'),
  transactionType: YupConstants.required('select'),
  contractLength: YupConstants.required('select'),
  images: Yup.object({
    featured: YupConstants.required('image'),
    floorPlan: YupConstants.required('image'),
    collections: Yup.array().of(Yup.string()).required().min(1),
  }),
})

/**
 * Never use the object directly,
 * clone it to avoid mutation. Use cloneDeep() from lodash.
 */
export const roomInput: RoomInput = {
  visibility: Visibility.Public,
  roomName: '',
  companyManagement: '',
  telephone: '',
  numberOfRoom: ('' as unknown) as number,
  layout: '',
  size: ('' as unknown) as number,
  mainWindow: '' as MainWindow,
  floor: '',
  availability: {
    status: '' as AvailabilityStatus,
    now: false,
    enquire: true,
  },
  contractType: '' as ContractType,
  transactionType: '' as TransactionType,
  contractLength: ('' as unknown) as number,
  tags: [],
  facilities: {
    basic: [],
    building: [],
    other: [],
  },
  fees: cloneDeep(feesInput),
  images: {
    featured: '',
    floorPlan: '',
    collections: [],
  },
}

const roomErrors = createDict(roomInput)

export default Vue.extend({
  props: {
    value: {
      type: Object as PropType<RoomInput>,
      required: true,
    },
    agencyId: {
      type: String,
      required: true,
    },
    nearestStationId: {
      type: String,
      required: true,
    },
    action: {
      type: String as PropType<'create' | 'update'>,
      required: true,
    },
  },
  data() {
    return {
      errors: cloneDeep(roomErrors),
      form: cloneDeep(this.value),
      options: {
        layout: ['R', 'K', 'DK', 'SK', 'LDK', 'SDK', 'SLDK'].map((code) => ({
          value: code,
          label: code,
        })),

        numberOfRoom: Array(10)
          .fill(0)
          .map((__, index: number) => ({
            value: index + 1,
            label: index + 1,
          })),
        floor: Array(100)
          .fill(0)
          .map((__, index: number) => ({
            value: `${index + 1}`,
            label: index + 1,
          })),
        contractLength: Array(6)
          .fill(0)
          .map((__, index: number) => ({
            value: index === 0 ? index + 1 : index * 12,
            label: index === 0 ? `${index + 1}ヶ月` : `${index}年`,
          })),
        transactionType: getOptions('room.transactionType'),
        contractType: getOptions('room.contractType'),
        visibility: getOptions('room.visibility'),
        mainWindow: getOptions('room.mainWindow'),
        availabilityStatus: getOptions('room.availabilityStatus'),
      },
      availabilityOptions: [
        {
          value: 'now',
          label: '即入居可',
        },
        {
          value: 'enquire',
          label: '要相談',
        },
      ],
      availabilityData: 'enquire',
    }
  },

  computed: {
    featuredImage: {
      get(): string[] {
        const { featured } = this.form.images
        return featured !== '' ? [featured] : []
      },
      set(newValue: string[]) {
        this.form.images.featured = newValue.length > 0 ? newValue[0] : ''
      },
    },
    floorPlanImage: {
      get(): string[] {
        const { floorPlan } = this.form.images
        return floorPlan !== '' ? [floorPlan] : []
      },
      set(newValue: string[]) {
        this.form.images.floorPlan = newValue.length > 0 ? newValue[0] : ''
      },
    },
  },

  watch: {
    form: {
      handler(value) {
        this.$emit('input', value)
      },
      deep: true,
    },
    'form.size': watchAndValidate('size', roomSchema),
    'form.images.featured': watchAndValidate('images.featured', roomSchema),
    'form.images.floorPlan': watchAndValidate('images.floorPlan', roomSchema),
    'form.images.collections': watchAndValidate(
      'images.collections',
      roomSchema
    ),
    value: {
      handler(newValue) {
        if (this.action === 'update') {
          this.availabilityData = get(newValue, 'availability.now', false)
            ? 'now'
            : get(newValue, 'availability.enquire', false)
            ? 'enquire'
            : ''
        }
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    get,
    async validate() {
      this.errors = flatten(roomInput)
      let isValid = true

      try {
        await (this.$refs.roomFeeForm as any).validate()
      } catch (error) {
        // console.log('roomFeeError', JSON.stringify(error))
        isValid = false
      }

      try {
        await validateForm(roomSchema, this.value, this)
      } catch (error) {
        isValid = false
      }

      return isValid
    },
    onAvailabilityInput(value: 'now' | 'enquire') {
      if (value === 'now') {
        set(this.form, 'availability.now', true)
        set(this.form, 'availability.enquire', false)
      } else {
        set(this.form, 'availability.now', false)
        set(this.form, 'availability.enquire', true)
      }
    },
  },
})
</script>

<style lang="scss" scoped>
.availability {
  justify-content: stretch;
  & > *:nth-child(2) {
    margin-bottom: unset !important;
  }
  &-checkboxes {
    flex: 1;

    .checkbox:first-child {
      margin-bottom: 5px;
    }
  }
}
</style>

<!-- Override default styles -->
<!-- FIXME: delete this soon -->
<style lang="scss">
.top-choices {
  .form-radio {
    justify-content: flex-start !important;
    & > * {
      margin-right: 20px;
    }
  }
}
</style>
