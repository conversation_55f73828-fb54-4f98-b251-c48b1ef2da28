<template>
  <div class="tags-checklist__section-content">
    <checkbox
      v-for="(label, code) in items"
      :key="code"
      :value="value"
      :key-value="code"
      :label="label"
      @input="handleOnChecked($event)"
    />
  </div>
</template>
_
<script lang="ts">
import Vue, { PropType } from 'vue'
import Checkbox from '../forms/Checkbox.vue'

export default Vue.extend({
  name: 'TagsChecklist',
  components: { Checkbox },
  props: {
    value: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
  },
  data: () => ({
    items: {
      furnished: '家具付き',
      free_rent: 'フリーレント ',
      renovation: 'リノベーション',
      office_use: 'オフィス可',
      penthouse: 'ペントハウス',
    },
  }),
  methods: {
    handleOnChecked(newValue: string[]) {
      this.$emit('input', newValue)
    },
  },
})
</script>

<style lang="scss" scoped>
.tags-checklist {
  &__section {
    display: flex;
    flex-direction: column;

    h4 {
      font-size: 12px;
      color: #333;
      margin-bottom: 15px;
    }

    &-content {
      display: flex;
      justify-content: space-between;
      flex-flow: row wrap;
      background: #fff;
      padding: 10px;
      padding-bottom: 0;
      border-radius: 6px;
      margin-bottom: 20px;

      /** Quick hack to fill the space with uneven items */
      &::after {
        content: '';
        flex-basis: 32%;
      }

      &:not(:last-child) {
        margin-bottom: 20px;
      }

      .checkbox {
        flex-basis: 32%;
        margin-bottom: 10px;
      }
    }
  }
}
</style>
