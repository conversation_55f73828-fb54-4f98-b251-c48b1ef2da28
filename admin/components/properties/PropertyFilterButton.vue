<template>
  <div v-on-clickaway="() => (isOpened = false)" class="property-filter">
    <div class="property-filter__button" @click="controlClick">
      <img
        class="property-filter__button-icon"
        src="~/assets/images/filter.svg"
      />
      <span>フィルター</span>
      <div v-if="value.length" class="property-filter__button-count">
        {{ value.length }}
      </div>
    </div>

    <div v-if="isOpened" class="property-filter__content">
      <div class="property-filter__content-header">
        <button @click.stop="clearClick">クリア</button>
        <button @click.stop="doneClick">実行</button>
      </div>
      <label
        v-for="option in options"
        :key="option.value"
        class="property-filter__content-item"
      >
        <checkbox
          v-model="checked"
          :key-value="option.value"
          checked-color="#1f3a4f"
        />
        <div
          class="property-filter__content-item-icon"
          :style="{ backgroundColor: VisibilityColors[option.value] }"
        />
        {{ option.label.replace('(非推奨)', '') }}
      </label>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import { getOptions } from '~/i18n'
import { EnumPropertyRoomsVisibility } from '~/types/types'

const VisibilityColors = {
  [EnumPropertyRoomsVisibility.Public]: '#0CB21A',
  [EnumPropertyRoomsVisibility.Full]: '#C90F44',
  [EnumPropertyRoomsVisibility.Private]: '#828882',
}

export default Vue.extend({
  name: 'PropertyFilterButton',
  props: {
    value: {
      type: Array as PropType<EnumPropertyRoomsVisibility[]>,
      required: true,
    },
  },
  data() {
    return {
      isOpened: false,
      options: getOptions('room.visibility'),
      VisibilityColors,
      checked: this.value,
    }
  },
  watch: {
    value(newData: EnumPropertyRoomsVisibility[]) {
      this.checked = newData
    },
  },
  methods: {
    controlClick(): void {
      this.isOpened = !this.isOpened
    },
    clearClick(): void {
      this.$emit('input', [])
      this.isOpened = false
    },
    doneClick(): void {
      this.$emit('input', this.checked)
      this.isOpened = false
    },
  },
})
</script>

<style lang="scss" scoped>
.property-filter {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  position: relative;
  cursor: pointer;
  margin-left: auto;
  margin-right: 20px;

  &__button {
    border: 1px solid #ccc;
    background-color: #fff;
    display: flex;
    padding: 10px;
    flex-direction: row;
    align-items: center;
    justify-content: stretch;
    border-radius: 3px;
    font-weight: normal;

    &-icon {
      width: 12px;
      height: 12px;
      margin-top: 2px;
      margin-right: 7px;
    }

    span {
      font-size: 14px;
    }

    &-count {
      margin-left: 7px;
      padding-left: 7px;
      border-left: 1px solid #ddd;
      font-size: 14px;
    }
  }

  &__content {
    width: 180px;
    z-index: 9;
    position: absolute;
    top: 50px;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    border: 1px solid #e8e8e8;
    background-color: #fff;
    box-shadow: rgba(0, 0, 0, 0.15) 0 2px 8px;

    &-header {
      display: flex;
      justify-content: space-between;
      background-color: #fff;
      padding: 10px 6px;
      border-bottom: 1px solid #ccc;

      button {
        font-size: 12px;
        padding: 4px 0;
        width: 50px;
        border: 0;
        border-radius: 4px;
        transition: 0.3s;

        &:hover {
          opacity: 0.7;
        }

        &:first-child {
          border: 1px solid #ccc;
          background-color: #fff;
          color: #333;
        }

        &:last-child {
          background-color: #1f3a4f;
          color: #fff;
        }
      }
    }

    &-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: stretch;
      padding: 10px;
      font-size: 12px;
      cursor: pointer;
      border-bottom: 1px solid #e8e8e8;

      // override the checbox style
      .checkbox {
        margin-right: 7px;
        background: transparent;
        padding: 0;
        height: 14px;

        &:hover {
          background-color: transparent;
        }
      }

      &-icon {
        width: 14px;
        height: 14px;
        background-color: red;
        border-radius: 10px;
        margin-right: 7px;
      }
    }
  }
}
</style>
