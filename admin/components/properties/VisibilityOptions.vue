<template>
  <div v-on-clickaway="() => (isOpened = false)" class="visibility-options">
    <div class="visibility-options__selected" @click="controlClick">
      <spin v-if="isLoading" />
      <template v-else>
        <div
          class="visibility-options__content-item-icon"
          :style="{ backgroundColor: VisibilityColors[value] }"
        />
        <span>{{ selectedLabel }}</span>
      </template>
    </div>

    <div v-if="isOpened" class="visibility-options__content">
      <div
        v-for="option in options"
        :key="option.value"
        class="visibility-options__content-item"
        :value="option.value"
        @click.prevent="itemClick(option.value)"
      >
        <div
          class="visibility-options__content-item-icon"
          :style="{ backgroundColor: VisibilityColors[option.value] }"
        />
        {{ option.label }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import get from 'lodash/get'
import { getOptions } from '~/i18n'
import { EnumPropertyRoomsVisibility } from '~/types/types'
import updateRoomVisibility from '~/graphql/properties/updateRoomVisibility.mutation.gql'

const VisibilityColors = {
  [EnumPropertyRoomsVisibility.Public]: '#0CB21A',
  [EnumPropertyRoomsVisibility.Private]: '#828882',
  [EnumPropertyRoomsVisibility.Full]: '#C90F44',
}

export default Vue.extend({
  props: {
    value: {
      type: String as PropType<EnumPropertyRoomsVisibility>,
      required: true,
    },
    propertyId: { type: String as PropType<string>, default: () => null },
    roomCode: { type: String as PropType<string>, default: () => null },
  },
  data: () => ({
    isOpened: false,
    isLoading: false,
    options: getOptions('room.visibility'),
    VisibilityColors,
  }),
  computed: {
    selectedLabel() {
      return this.options.find((opt) => opt.value === this.value)?.label
    },
  },
  methods: {
    controlClick(): void {
      if (this.isLoading) return
      this.isOpened = !this.isOpened
    },
    itemClick(value: EnumPropertyRoomsVisibility): void {
      this.isOpened = false
      if (!this.propertyId || !this.roomCode) {
        this.$emit('input', value)
        return
      }

      this.updateVisibility(value)
    },
    async updateVisibility(value: EnumPropertyRoomsVisibility): Promise<void> {
      this.isLoading = true
      const options = {
        mutation: updateRoomVisibility,
        variables: {
          propertyId: this.propertyId,
          code: this.roomCode,
          value,
        },
      }
      const result = await this.$apollo.mutate(options)
      const isSuccess = !!get(result, `data.updateRoomVisibility`)
      if (isSuccess) {
        this.$emit('on-updated', value)
        this.$toast.success('保存しました。', {
          duration: 6000,
        })
      } else {
        this.$toast.global.commonError()
      }
      this.isLoading = false
    },
  },
})
</script>

<style lang="scss" scoped>
.visibility-options {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  position: relative;
  cursor: pointer;
  min-width: 140px;
  background: #fff;

  &__selected {
    border: 1px solid #e8e8e8;
    background: transparent;
    background-image: url('data:image/svg+xml;base64,PHN2ZyBmaWxsPSdibGFjaycgaGVpZ2h0PScyNCcgdmlld0JveD0nMCAwIDI0IDI0JyB3aWR0aD0nMjQnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZD0nTTcgMTBsNSA1IDUtNXonLz48cGF0aCBkPSdNMCAwaDI0djI0SDB6JyBmaWxsPSdub25lJy8+PC9zdmc+');
    background-repeat: no-repeat;
    background-position-x: 98%;
    background-position-y: 5px;
    height: 34px;
    display: flex;
    padding: 10px;
    flex-direction: row;
    align-items: center;
    justify-content: stretch;
    border-radius: 3px;
    .spinner {
      transform: scale(0.2);
    }
    span {
      font-size: 12px;
    }
  }

  &__content {
    width: 100%;
    z-index: 9;
    position: absolute;
    top: 40px;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    border: 1px solid #e8e8e8;
    background-color: #fff;
    box-shadow: rgba(0, 0, 0, 0.15) 0 2px 8px;

    &-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: stretch;
      padding: 10px;
      font-size: 12px;

      &-icon {
        width: 14px;
        height: 14px;
        background-color: red;
        border-radius: 10px;
        margin-right: 7px;
      }

      &:hover {
        background: #e8e8e8;
      }
    }
  }
}
</style>
