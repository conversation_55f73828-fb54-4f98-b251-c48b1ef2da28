<template>
  <form-input-text
    label="物件名"
    caption="物件名は、最寄り駅および間取りから自動で生成されます。"
    :placeholder="generatedName"
    is-disabled
  />
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import getStationByIdGql from '~/graphql/access/getStationById.query.gql'

export default Vue.extend({
  props: {
    layout: {
      type: String,
      required: true,
    },
    numberOfRoom: {
      type: [Number, String],
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    stationId: {
      type: String,
      required: true,
    },
  },
  apollo: {
    stationById: {
      query: getStationByIdGql,
      variables(): { id: string } {
        return { id: this.stationId }
      },
    },
  },
  computed: {
    generatedName(): string {
      const stationName = get(this, 'stationById.en')
      const station = stationName ? `${stationName} Sta. ` : ''
      return `${station}${this.numberOfRoom}${this.layout} ${this.name}`
    },
  },
})
</script>
