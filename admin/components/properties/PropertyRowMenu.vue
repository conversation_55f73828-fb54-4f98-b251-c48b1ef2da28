<template>
  <div class="property-row-menu">
    <div v-for="button in buttons" :key="button.icon" class="menu-section">
      <div class="menu-button">
        <img class="menu-button__icon" :src="button.icon" />
        <font-awesome-icon
          :icon="['fas', 'caret-down']"
          :style="{ color: '#fff' }"
          class="arrow"
        />
      </div>
      <ul class="menu-button__list">
        <li
          v-for="menu in button.menus"
          :key="menu.label"
          class="menu-button__list__item"
          :class="menu.class"
        >
          <nuxt-link
            v-if="!menu.link.includes('http')"
            :to="menu.link"
            :target="menu.target"
            @click.native="menu.handler && menu.handler()"
          >
            <img v-if="menu.icon" :src="menu.icon" />
            {{ menu.label }}
          </nuxt-link>
          <a v-else :href="menu.link" :target="menu.target">
            <img v-if="menu.icon" :src="menu.icon" />
            {{ menu.label }}
          </a>
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import deleteRoomGql from '~/graphql/properties/deleteRoom.mutation.gql'

export default Vue.extend({
  props: {
    propertyId: { type: String, required: true },
    propertyName: { type: String, required: true },
    buildingName: { type: String, required: true },
    roomCode: { type: String, required: true },
    prefecturePath: { type: String, required: true },
  },
  data() {
    return {
      buttons: [
        {
          icon: require('~/assets/images/settings.svg'),
          menus: [
            {
              icon: require('~/assets/images/edit.svg'),
              link: `/properties/${this.propertyId}?roomCode=${this.roomCode}`,
              label: '編集',
            },
            {
              icon: require('~/assets/images/preview.svg'),
              label: 'プレビュー',
              link: `${this.$config.webUrl}/rent/${this.prefecturePath}/${this.roomCode}`,
              target: '_blank',
            },
            {
              icon: require('~/assets/images/cancel.svg'),
              label: '削除',
              class: 'red',
              link: '#',
              handler: () => {
                this.$showConfirmDialog({
                  message:
                    '一度削除すると復元ができません。この物件を削除しますか？',
                  okCallback: async () => {
                    await this.$apollo.mutate({
                      mutation: deleteRoomGql,
                      variables: {
                        propertyId: this.propertyId,
                        code: this.roomCode,
                      },
                    })
                    this.$toast.success('Property room deleted', {
                      duration: 10000,
                    })

                    this.$emit('on-deleted', {
                      roomCode: this.roomCode,
                      propertyId: this.propertyId,
                    })
                  },
                })
              },
            },
          ],
        },
        {
          icon: require('~/assets/images/print.svg'),
          menus: [
            {
              label: '英語',
              link: `/properties/pdf/${this.roomCode}`,
              target: '_blank',
            },
          ],
        },
      ],
    }
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/settings/variables';

.property-row-menu {
  display: flex;
  justify-content: center;
  position: relative;
}

.menu-section {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 24px;

  .menu-button {
    position: relative;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 24px;
    border-radius: 5px;
    transition: 0.3s;

    &__icon {
      width: 14px;
      height: 14px;
      filter: brightness(0) invert(1);
      margin-right: 6px;
    }

    &__list {
      display: none;
      z-index: 2;
      list-style: none;
      position: absolute;
      left: 0;
      top: 22px;
      width: 120px;
      background: $color_white;
      border: 1px solid #e8e8e8;
      background-color: #fff;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);

      &__item {
        display: flex;
        align-items: center;
        font-size: 12px;

        &:hover {
          background-color: #eee;
          cursor: pointer;
        }

        a {
          flex: 1;
          padding: 7px 10px;
        }

        img {
          margin-right: 10px;
          width: 12px;
          height: 12px;
        }

        &.red {
          color: $color_mexican_red;
          img {
            filter: invert(13%) sepia(31%) saturate(7290%) hue-rotate(340deg)
              brightness(111%) contrast(90%);
          }
        }
      }
    }
  }

  &:first-child .menu-button {
    background-color: #1b7fa7;
  }

  &:last-child .menu-button {
    display: none;
    background-color: $color_emperor;
  }

  &:hover .menu-button {
    background: black;
  }

  &:hover .menu-button__list {
    display: block;
  }
}
</style>
