<template>
  <div class="nested-options">
    <div v-click-outside="() => (isOpened = false)" class="nested-options__value" @click.stop="() => (isOpened = true)">
      <span>
        {{ $accessor.getAddress({ prefecture, city, chome }) || placeholder }}
      </span>
      <font-awesome-icon :icon="['fas', 'caret-down']" :style="{ color: '#555' }" class="arrow" />
    </div>
    <div v-show="isOpened" class="wrapper">
      <div class="options">
        <section>
          <h2>都道府県</h2>
          <ul>
            <nested-option-item v-for="item in prefectures" :key="item._id" :name="item.jp" :title="item.en"
              :is-selected="temporaryValue.prefecture === item._id" is-arrow-visible
              @click="handleOnItemClick('prefecture', item._id)" />
          </ul>
        </section>
        <section>
          <h2>市区町村</h2>
          <div v-if="$apollo.queries.cities.loading" class="items-loading">
            <spin />
          </div>
          <ul v-else-if="temporaryValue.prefecture">
            <nested-option-item v-for="item in cities" :key="item._id" :name="item.jp" :title="item.en"
              :is-selected="temporaryValue.city === item._id" is-arrow-visible
              @click="handleOnItemClick('city', item._id)" />
          </ul>
        </section>
        <section>
          <h2>丁目</h2>
          <div v-if="$apollo.queries.chomes.loading" class="items-loading">
            <spin />
          </div>
          <ul v-else-if="temporaryValue.city">
            <nested-option-item v-for="item in sortedChomes" :key="item._id" :name="item.jp" :title="item.en"
              :is-selected="temporaryValue.chome === item._id" is-arrow-visible @click="handleOnChomeClick(item._id)" />
          </ul>
        </section>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import sortBy from 'lodash/sortBy'
import vClickOutside from 'v-click-outside'
import getPrefecturesGql from '~/graphql/areas/getPrefectures.query.gql'
import getCitiesGql from '~/graphql/areas/getCities.query.gql'
import getChomesGql from '~/graphql/areas/getChomes.query.gql'
import { Chome, City, Prefecture } from '~/types/types'

Vue.use(vClickOutside)

export default Vue.extend({
  props: {
    prefecture: { type: String as PropType<string>, required: true },
    city: { type: String as PropType<string>, required: true },
    chome: { type: String as PropType<string>, required: true },
    placeholder: { type: String, default: () => '' },
  },
  data() {
    return {
      isOpened: false,
      prefectures: [] as Prefecture[],
      cities: [] as City[],
      chomes: [] as Chome[],
      temporaryValue: {
        prefecture: this.prefecture,
        city: this.city,
        chome: this.chome,
      },
    }
  },
  computed: {
    sortedChomes(): Chome[] {
      return sortBy(this.chomes, 'jp', 'asc')
    },
  },
  watch: {
    prefecture(newValue: string): void {
      this.temporaryValue.prefecture = newValue
    },
    city(newValue: string): void {
      this.temporaryValue.city = newValue
    },
    chome(newValue: string): void {
      this.temporaryValue.chome = newValue
    },
  },
  apollo: {
    prefectures: {
      query: getPrefecturesGql,
      variables: {
        prefectures: [{
          "jp": "東京都"
        }, {
          "jp": "埼玉県"
        }, {
          "jp": "神奈川県"
        }, {
          "jp": "千葉県"
        }]
      },
      result({ data }) {
        this.$accessor.setPrefectureMap(data?.prefectures || [])
      },
    },
    cities: {
      query: getCitiesGql,
      skip(): boolean {
        return !this.temporaryValue.prefecture
      },
      variables(): { prefId: string | null } {
        return { prefId: this.temporaryValue.prefecture }
      },
      result({ data }) {
        this.$accessor.setCityMap(data?.cities || [])
      },
    },
    chomes: {
      query: getChomesGql,
      skip(): boolean {
        const { city, prefecture } = this.temporaryValue
        return !prefecture || !city
      },
      variables(): { prefId: string | null; cityId: string | null } {
        const { city, prefecture } = this.temporaryValue
        return { prefId: prefecture, cityId: city }
      },
      result({ data }) {
        this.$accessor.setChomeMap(data?.chomes || [])
      },
    },
  },
  methods: {
    handleOnItemClick(field: 'prefecture' | 'city', id: string) {
      this.temporaryValue[field] = id

      if (field === 'prefecture') {
        this.temporaryValue.city = ''
      }

      if (field === 'prefecture' || field === 'city') {
        this.temporaryValue.chome = ''
      }
    },
    handleOnChomeClick(chomeId: string) {
      this.temporaryValue.chome = chomeId
      this.$emit('on-picked', this.temporaryValue)
      this.isOpened = false
    },
  },
})
</script>

<style scoped lang="scss">
.nested-options {
  &__value {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px 12px;
    position: relative;
    cursor: pointer;
    transition: 0.3s;
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-height: 34px;

    img {
      width: auto;
      height: auto;
      max-width: 20px;
      max-height: 20px;
      margin-right: 14px;
    }

    span {
      flex: 1;
      color: #333;
      font-size: 12px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: calc(100% - 40px);
    }

    &:hover {
      opacity: 0.8;
    }
  }
}

.svg-inline--fa.fa-w-14 {
  height: 0.6em;
}

.wrapper {
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  width: auto !important;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.6);
}

.options {
  background: white;
  margin: auto;
  padding: 40px 20px;
  border-radius: 10px;
  display: flex;

  section {
    transition: 0.3s;
    list-style: none;
    margin: 0;
    width: 350px;
    display: flex;
    flex-direction: column;

    h2 {
      padding: 0 20px;
      font-size: 16px;
      margin-bottom: 25px;
    }

    &:not(:last-child) {
      border-right: 1px solid #ddd;
    }

    ul {
      padding: 0 20px;
      min-width: 250px;
      max-height: 500px;
      overflow-y: auto;
    }
  }
}

.items-loading {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
