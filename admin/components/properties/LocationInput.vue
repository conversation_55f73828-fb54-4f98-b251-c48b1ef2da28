<template>
  <div class="building__address">
    <div class="building__address__fields">
      <form-input-text
        v-model="postalCode"
        :error="get(errors, 'postalCode')"
        label="郵便番号"
        placeholder="123-4567"
      />
      <form-input label="住所">
        <nested-options
          v-model="locationPaths"
          :items="options"
          placeholder="選択する"
          :max-depth="3"
          :header-texts="['市区町村', 'エリア', '丁目']"
          @selectedAddressTemp="selectedAddress"
        />
        <div v-if="get(errors, 'chome')" class="error">
          {{ get(errors, 'chome') }}
        </div>
      </form-input>
      <form-input-text
        v-model.trim="addressLine"
        :error="get(errors, 'addressLine')"
        label="番地"
      />
    </div>
    <div>
      <GmapMap
        v-if="location"
        ref="mapRef"
        class="location__maps"
        :center="location"
        :zoom="14"
        map-type-id="roadmap"
        style="width: 100%; max-width: 700px; height: 300px"
      >
        <GmapMarker
          v-if="addressLine"
          :position="location"
          :clickable="true"
          :draggable="false"
        />
        <GmapCircle
          v-else
          :center="location"
          :radius="500"
          :visible="true"
          :options="{ fillColor: 'red', fillOpacity: 0.1, strokeOpacity: 0 }"
        />
      </GmapMap>
    </div>
  </div>
</template>

<script lang="ts">
/**
 * FIXME: Remove this later,
 * Deprecated in favor of AddressInput
 */
import Vue, { PropType } from 'vue'
import flatMap from 'lodash/flatMap'
import get from 'lodash/get'
import cloneDeep from 'lodash/cloneDeep'
import * as Yup from 'yup'
import { createDict, validateForm } from '~/plugins/form.utils'
import getLocationsGql from '~/graphql/properties/getLocations.query.gql'
import { Area, Maybe } from '~/types/types'
import { Option } from '~/types'
import * as YupConstants from '~/constants/yup'
import { flatIntoDict } from '~/common/utils'

interface AreaExtend extends Area {
  code?: string
}

export const locationSchema = Yup.object().shape({
  postalCode: YupConstants.optionalPostalCode,
  addressLine: Yup.string(),
  region: YupConstants.required('select'),
  prefecture: YupConstants.required('select'),
  cityArea: YupConstants.required('select'),
  city: YupConstants.required('select'),
  area: YupConstants.required('select'),
  chome: YupConstants.required('select'),
})

export const locationInput = {
  postalCode: '',
  addressLine: '',
  region: 'r3', // FIXME: currently hardcoded, kanto
  prefecture: 'r3__p13', // FIXME: currently hardcoded, tokyo
  cityArea: '',
  city: '',
  area: '',
  chome: '',
  position: {
    lat: 0,
    long: 0,
  },
}

const computedCreator = (field: string) => ({
  get(this: any): string {
    return this.value[field]
  },
  async set(this: any, newValue: string): Promise<void> {
    const newObj = { ...this.value, [field]: newValue }
    this.$emit('input', newObj)
    try {
      await Yup.reach(locationSchema, field).validate(newValue)
      this.errors[field] = null
    } catch (error) {
      this.errors[field] = error.message
    }
  },
})

const locationErrors = createDict(locationInput)

export default Vue.extend({
  props: {
    value: { type: Object as PropType<any>, required: true },
  },
  data: () => ({
    locations: [] as (Maybe<Area> | undefined)[],
    options: [] as (Maybe<AreaExtend> | undefined)[],
    errors: cloneDeep(locationErrors),
    location: {
      lat: get(this, 'value.position.lat', 0) as number,
      lng: get(this, 'value.position.long', 0) as number,
    },
    APICalling: false,
  }),
  computed: {
    postalCode: computedCreator('postalCode'),
    addressLine: computedCreator('addressLine'),
    locationPaths: {
      get(this: any) {
        const { city, area, chome } = this.value
        return [city, area, chome]
      },
      set(this: any, paths: string[]) {
        const [city, area, chome] = paths
        const newObj = {
          ...this.value,
          cityArea: this.getCityArea(city),
          city,
          area,
          chome,
        }
        this.errors.chome = ''
        this.$emit('input', newObj)
      },
    },
    streetAddress(): string {
      const { length } = this.locationPaths
      if (length > 0) {
        return this.locationPaths.reduce((acc, index) => {
          return acc.concat(this.dictionary[index] || '')
        }, '')
      }

      return ''
    },
    dictionary(): { [key: string]: any } {
      const items = [...this.options] as Option[]
      return items.reduce(flatIntoDict, {})
    },
  },
  apollo: {
    locations: {
      query: getLocationsGql,
      /// fetchPolicy: 'cache-and-network', BUGGY
      result({ data }) {
        if (data?.locations) {
          const getItems = (areaCity: AreaExtend) => areaCity.items
          this.options = flatMap(
            data.locations as Maybe<AreaExtend[]>,
            getItems
          )
        }
      },
    },
  },
  watch: {
    addressLine: {
      handler(address: string) {
        !this.APICalling &&
          !!address &&
          !!this.streetAddress &&
          this.geocodingLocation(address)
      },
    },
    locationPaths() {
      !this.APICalling && this.geocodingLocation(this.addressLine)
    },
    value: {
      handler(newValue) {
        this.location = {
          lat: get(newValue, 'position.lat', 0) as number,
          lng: get(newValue, 'position.long', 0) as number,
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    get,
    getCityArea(cityCode: string) {
      const cityArea = this.locations.find((item) =>
        // FIXME: use correct typedef
        item?.items?.find((city) => (city as any)?.code === cityCode)
      )

      // FIXME: use correct typedef
      return (cityArea as any)?.code
    },
    async validate() {
      let isValid = true
      this.errors = cloneDeep(locationErrors)

      try {
        await validateForm(locationSchema, this.value, this)
      } catch (error) {
        isValid = false
      }

      return isValid
    },
    selectedAddress(address: any) {
      this.$root.$emit('selectedAddress', address)
    },
    geocodingLocation(addressLine: string) {
      this.APICalling = true
      const fullAddress = this.streetAddress + ' ' + addressLine
      fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=${fullAddress}&key=${this.$config.gMapAPI}`
      )
        .then((response) => response.json())
        .then((data) => {
          if (data.status === 'OK') {
            if (data.results[0]) {
              this.location.lat = parseFloat(
                data.results[0].geometry.location.lat
              )
              this.location.lng = parseFloat(
                data.results[0].geometry.location.lng
              )
              this.$emit('input', {
                ...this.value,
                position: {
                  lat: this.location.lat,
                  long: this.location.lng,
                },
              })
            }
          }
        })
        .finally(() => {
          this.APICalling = false
        })
    },
  },
})
</script>

<style lang="scss" scoped>
.building__address {
  display: flex;

  & > * {
    flex: 1;

    &:not(:last-child) {
      margin-right: 20px;
    }
  }
}
</style>
