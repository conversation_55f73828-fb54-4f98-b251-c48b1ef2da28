<template>
  <div class="search-property">
    <input
      v-model.trim="searchText"
      type="text"
      class="search-input"
      placeholder="物件名、物件ID"
      @input="onInput"
    />
    <button
      v-if="searchText"
      class="search-button"
      @click.prevent="onClear"
    ></button>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import debounce from 'lodash/debounce'

export default Vue.extend({
  name: 'SearchWidget',
  props: {
    value: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      searchText: this.value as string,
    }
  },
  methods: {
    onClear(): void {
      this.searchText = ''
      this.$emit('input', this.searchText)
    },
    onInput: debounce(function (this: any) {
      this.$emit('input', this.searchText)
    }, 500),
  },
})
</script>

<style scoped lang="scss">
.search-property {
  width: 320px;
  display: flex;
  border-radius: 5px;
  padding-left: 40px;
  padding-right: 15px;
  align-items: center;
  border: 1px solid #ddd;
  background: url('~/assets/images/icon-search.svg') 15px center no-repeat #fff;
  background-size: 15px 15px;
  margin-right: 20px;
}

.search-input {
  flex: 1;
  border: none;
  height: 40px;
  font-size: 14px;
  line-height: 23px;
  font-weight: normal;
  background: transparent;
  font-family: 'Hiragino Sans', sans-serif;
}

.search-button {
  background: #aaa;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: block;
  cursor: pointer;
  position: relative;
  border: none;

  &::before,
  &::after {
    background: #fff;
    position: absolute;
    content: '';
    top: 50%;
    left: 5px;
    width: 10px;
    height: 1px;
  }

  &::before {
    transform: translateY(-50%) rotate(45deg);
  }

  &::after {
    transform: translateY(-50%) rotate(-45deg);
  }
}
</style>
