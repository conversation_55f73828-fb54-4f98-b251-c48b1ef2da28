<template lang="html">
  <div class="light">
    <ul class="tabs__header">
      <li
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab-item"
        :class="{ tab__selected: index == selectedIndex, error: tab.isError }"
        @click.stop="selectTab(index)"
      >
        {{ tab.title }}
        <img
          v-click-outside="handleOnMenuClickaway(index)"
          class="setting"
          src="~assets/images/settings.svg"
          @click.stop="openedMenuIndex = index"
        />
        <ul class="menu" :class="{ open: openedMenuIndex === index }">
          <li class="menu-item" @click.stop="$emit('dupe-click', index)">
            <img src="~/assets/images/copy.svg" />複製
          </li>
          <li class="menu-item red" @click.stop="handleOnDeleteClick(index)">
            <img src="~/assets/images/cancel.svg" />削除
          </li>
        </ul>
      </li>
      <li class="add-button" @click="$emit('add-click')">+</li>
    </ul>
    <slot></slot>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import vClickOutside from 'v-click-outside'

Vue.use(vClickOutside)

export default Vue.extend({
  props: {
    selectedIndex: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      openedMenuIndex: -1,
      tabs: [] as Vue[],
    }
  },
  watch: {
    selectedIndex() {
      this.syncActiveTabs()
    },
    tabs() {
      this.syncActiveTabs()
    },
  },
  mounted() {
    this.syncActiveTabs()
  },
  created() {
    this.tabs = this.$children
  },
  methods: {
    syncActiveTabs() {
      // If the selected index out of the bound,
      // make it the previous tab active
      const lastItemIndex = this.tabs.length - 1
      if (this.selectedIndex > lastItemIndex) {
        this.selectTab(lastItemIndex)
        return
      }

      // loop over all the tabs
      this.tabs.forEach((tab: any, index) => {
        tab.isActive = index === this.selectedIndex
      })
    },
    selectTab(tabIndex: number) {
      this.$emit('on-tab-changed', tabIndex)
    },
    handleOnMenuClickaway(index: number) {
      return () => {
        if (this.openedMenuIndex !== index) return
        this.openedMenuIndex = -1
      }
    },
    handleOnDeleteClick(index: number) {
      if (this.tabs.length === 1) {
        this.$toast.error('The property must have at least one room / unit')
        return
      }

      this.openedMenuIndex = -1
      this.$showConfirmDialog({
        message: '一度削除すると復元ができません。この部屋を削除しますか？',
        okCallback: () => {
          this.openedMenuIndex = -1
          this.$emit('delete-click', index)
        },
      })
    },
  },
})
</script>

<style lang="scss">
@import '~/assets/scss/settings/variables';

.tabs__header {
  display: flex;
  align-items: flex-start;
  list-style: none;
  padding: 0;
  border-bottom: 2px solid #ddd;

  & > .tab-item {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 20px;
    margin-bottom: -2px;
    font-size: 14px;
    cursor: pointer;
    background-color: #f3f5f7;
    border-radius: 10px 10px 0 0;
    height: 38px;
    width: 150px;
    border-bottom: 2px solid #ddd;
    border-top: 2px solid #ddd;
    border-right: 2px solid #ddd;
    border-left: 1px solid #ddd;
    padding-right: 5px;

    &.tab__selected {
      background-color: $color_white;
      border: 2px solid #ddd;
      border-bottom: 2px solid $color_white;
      padding-bottom: -2px;
    }

    &.error {
      color: red;
    }

    .setting {
      width: 24px;
      height: 24px;
      padding: 6px;
      filter: invert(32%) sepia(6%) saturate(7%) hue-rotate(322deg)
        brightness(94%) contrast(84%);
    }

    .menu {
      display: none;
      z-index: 2;
      list-style: none;
      position: absolute;
      right: 0;
      top: 0;
      transform: translate(65%, 35%);
      width: 100px;
      background: $color_white;
      border: 1px solid #e8e8e8;
      background-color: #fff;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);

      &.open {
        display: block;
      }

      &-item {
        display: flex;
        align-items: center;
        font-size: 12px;
        padding: 10px;

        &:hover {
          background-color: #eee;
        }

        img {
          margin-right: 10px;
          width: 12px;
          height: 12px;
        }

        &.red {
          color: $color_mexican_red;
          img {
            filter: invert(13%) sepia(31%) saturate(7290%) hue-rotate(340deg)
              brightness(111%) contrast(90%);
          }
        }
      }
    }
  }

  .add-button {
    background-color: #f3f5f7;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    border-radius: 15px;
    align-self: center;
    margin-left: 10px;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.tabs__light li.tab__selected {
  background-color: #fff;
  color: #83ffb3;
}

.tab {
  display: inline-block;
  color: black;
  min-width: 800px;
  border-radius: 10px;
}
</style>
