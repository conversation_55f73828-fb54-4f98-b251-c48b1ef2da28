<template>
  <div v-show="isActive" class="tab" :class="tabClass">
    <slot></slot>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: 'Tab',
    },
    tabClass: {
      type: String,
      default: () => '',
    },
    isError: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      isActive: true,
    }
  },
}
</script>

<style lang="scss" scoped>
.tab {
  display: flex;
}
</style>
