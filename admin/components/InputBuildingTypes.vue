<template>
  <div class="building-types-choices">
    <a
      v-for="item in items"
      :key="item.code"
      class="choice"
      :class="{ selected: item.code === value }"
      @click="$emit('input', item.code)"
    >
      {{ item.name }}
    </a>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: { value: { type: String, required: true } },
  data: () => ({
    items: [
      { code: 'APRT', name: 'Apartment' },
      { code: 'HOUS', name: 'House' },
      { code: 'SHRH', name: 'Share House' },
      { code: 'MNTA', name: 'Monthly Apartment' },
    ],
  }),
})
</script>

<style lang="scss">
.building-types-choices {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  margin-bottom: 10px;

  .choice {
    padding: 10px 15px;
    background-color: #eee;
    cursor: pointer;
    font-size: 14px;

    &:not(:first-child) {
      margin-left: 5px;
    }

    &.selected {
      background-color: #34946f;
      color: #fff;
    }
  }
}
</style>
