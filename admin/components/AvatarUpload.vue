<template>
  <div class="avatar-upload">
    <img v-if="value !== ''" :src="`${$config.cdnUrl}${value}`" />
    <img v-else src="~/assets/images/icon_user.jpg" />
    <div v-if="isUploading">Uploading...</div>
    <div v-else>
      <input
        id="avatar"
        type="file"
        name="file"
        value="0"
        lang="jp-JP"
        @change="onFileChange"
      />
      <p style="color: #333">※ 1MB以内、JPG・PNG形式のいずれかを設定</p>
      <p v-if="visibleError" style="color: red">{{ visibleError }}</p>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import getS3UploadSignedUrlGql from '~/graphql/queries/getS3UploadSignedUrl.gql'
export default Vue.extend({
  props: {
    value: { type: String, required: true },
    path: { type: String, required: true },
    error: { type: String, default: () => '' },
  },

  data: () => ({
    isUploading: false,
    errorMessage: '',
  }),

  computed: {
    visibleError() {
      return this.errorMessage === ''
        ? this.error === ''
          ? null
          : this.error
        : this.errorMessage
    },
  },
  methods: {
    async getSignedUrl(type: string, fileName: string) {
      const result = await this.$apollo.query({
        query: getS3UploadSignedUrlGql,
        variables: {
          type,
          path: `${this.path}/${fileName}`,
        },
      })
      return result.data.s3UploadSignedUrl.url
    },
    generateFilename(file: any) {
      const filePath = file.name.split('.')
      const fileExt = filePath[filePath.length - 1]
      return `${Date.now()}.${fileExt}`
    },
    async onFileChange(event: any) {
      this.isUploading = true
      const [selectedFile] = event.target.files

      const allowedExtensions = ['image/jpeg', 'image/jpg', 'image/png']
      if (!allowedExtensions.includes(selectedFile.type)) {
        this.errorMessage = 'Only JPG or PNG. '
      }

      const maxFileSize = 1000000 // 1 MB
      if (selectedFile.size > maxFileSize) {
        this.errorMessage = this.errorMessage + 'Max file allowed is 1 MB!'
      }

      const fileName = this.generateFilename(selectedFile)
      const signedUrl = await this.getSignedUrl(selectedFile.type, fileName)
      const result = await fetch(signedUrl, {
        method: 'PUT',
        body: selectedFile,
      })

      if (result.ok) {
        this.$emit('input', `/${this.path}/${fileName}`)
      }
      this.isUploading = false
    },
  },
})
</script>

<style lang="scss" scoped>
.avatar-upload {
  display: flex;
  align-items: center;

  & > * {
    margin-right: 10px;
  }

  img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    background-color: #ececec;
  }

  input {
    border: 0;
    padding: 0;
    margin-bottom: 5px;
  }

  p {
    font-size: 12px;
    color: #333;
  }
}
</style>
