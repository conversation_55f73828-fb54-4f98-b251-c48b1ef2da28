<template>
  <div class="nested-options">
    <div
      v-click-outside="() => (isOpened = false)"
      class="nested-options__value"
      @click.stop="
        () => {
          isOpened = true
          handleOption()
        }
      "
    >
      <span>{{ visibleValue }}</span>
      <font-awesome-icon
        :icon="['fas', 'caret-down']"
        :style="{ color: '#555' }"
        class="arrow"
      />
    </div>
    <div v-show="isOpened" class="wrapper">
      <div class="options">
        <h3>路線情報</h3>
        <div class="search-option" @click.stop="() => (isOpened = true)">
          <input
            v-model="searchText"
            class="search-input"
            type="text"
            placeholder="駅名を入力してください。"
          />
          <button class="search-button" @click="onDeleteSearch"></button>
        </div>
        <section v-for="(childItems, depth) in dataSearch" :key="depth">
          <ul v-if="isSearch">
            <nested-option-item
              v-for="option in childItems"
              :key="option.code"
              :name="option.name"
              :count="option.count"
              :is-expanded="false"
              :is-selected="selectedPaths[depth] === option.code"
              :disabled="!!option.disabled"
              :is-tag="true"
              is-arrow-visible
              @click="expand(option, depth)"
            />
          </ul>
        </section>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import vClickOutside from 'v-click-outside'

import { flatIntoDict } from '~/common/utils'
import { Option } from '~/types'

Vue.use(vClickOutside)

export default Vue.extend({
  props: {
    value: { type: Array as PropType<string[]>, required: true },
    placeholder: { type: String, default: () => '' },
    selectedSeparator: { type: String, default: () => '' },
    selectedSuffix: { type: String, default: () => '' },
    headerTexts: { type: Array, default: () => [] },
    maxDepth: { type: Number, default: () => 3 },
    items: { type: Array as PropType<Option[]>, required: true },
  },
  data: () => ({
    availableOptions: [] as Option[][],
    isOpened: false,
    selectedPaths: [] as string[],
    selectedAddress: [] as string[],
    dictionary: {} as { [key: string]: any },
    searchText: '' as String,
    isSearch: false as Boolean,
  }),
  computed: {
    visibleValue() {
      const { length } = this.value
      if (length > 0) {
        const selectedValue = this.value
          .filter((index) => !!this.dictionary[index])
          .map((index) => this.dictionary[index])
          .join(this.selectedSeparator)

        if (selectedValue !== '') return selectedValue
      }

      return this.placeholder
    },
    dataSearch() {
      if (this.searchText === '') {
        return []
      }

      return this.availableOptions.map((options) => {
        return options.filter((option: any) =>
          option.name
            .substring(0, option.name.indexOf(' - '))
            .includes(this.searchText)
        )
      })
    },
  },
  watch: {
    items(newItems) {
      this.availableOptions = [this.items]
      for (let i = 1; i < this.maxDepth; i++) {
        this.availableOptions.push([])
      }
      this.dictionary = newItems.reduce(flatIntoDict, {})
    },
    visibleValue(value: string) {
      if (value !== this.placeholder) {
        this.searchText = value.substring(0, value.indexOf(' - '))
      } else {
        this.searchText = ''
      }
    },
    searchText() {
      this.isSearch = true
    },
  },
  methods: {
    handleOption() {
      this.isSearch = false
    },
    expand(item: any, depth: number) {
      this.selectedPaths[depth] = item.code
      this.selectedAddress[depth] = item.en
      const noChild = !item.items
      if (noChild) {
        this.$emit('input', item)
        this.$emit('selectedAddressTemp', this.selectedAddress)
        this.isOpened = false
        return
      }

      this.setAvailableOptions(item.items, depth)
    },
    setAvailableOptions(items: Option[], depth: number) {
      // set the next available options
      this.$set(this.availableOptions, depth + 1, items)

      // then clear any available options,
      // if the selected item is on the beginning
      for (let i = depth + 2; i < this.availableOptions.length; i++) {
        this.$set(this.availableOptions, depth + 2, [])
      }
    },
    onDeleteSearch() {
      this.searchText = ''
    },
  },
})
</script>

<style scoped lang="scss">
.nested-options {
  &__value {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px 12px;
    position: relative;
    cursor: pointer;
    transition: 0.3s;
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-height: 34px;
    max-width: 794px;

    img {
      width: auto;
      height: auto;
      max-width: 20px;
      max-height: 20px;
      margin-right: 14px;
    }

    span {
      flex: 1;
      color: #333;
      font-size: 12px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: calc(100% - 40px);
    }

    &:hover {
      opacity: 0.8;
    }
  }
}

.svg-inline--fa.fa-w-14 {
  height: 0.6em;
}
.wrapper {
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  width: auto !important;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.6);
}

.options {
  background: white;
  margin: auto;
  padding: 20px 20px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  min-height: 371px;
  min-width: 500px;

  section {
    transition: 0.3s;
    list-style: none;
    margin: 0;
    width: 460px;
    height: 250px;

    h2 {
      padding: 0 20px;
      font-size: 16px;
      margin-bottom: 25px;
    }

    &:not(:last-child) {
      border-right: 1px solid #ddd;
    }
    ul {
      min-width: 250px;
      max-height: 250px;
      overflow-y: auto;
    }
  }

  h3 {
    font-size: 16px;
    margin-bottom: 15px;
  }
}

.search-option {
  display: flex;
  border-radius: 5px;
  padding-right: 15px;
  align-items: center;
  border: 1px solid #ddd;
  margin-right: 20px;
}

.search-input {
  flex: 1;
  border: none;
  height: 40px;
  font-size: 14px;
  line-height: 23px;
  font-weight: normal;
  background: transparent;
  max-width: unset;

  &:active,
  &:focus {
    border: none;
    outline: none;
  }
}

.search-button {
  background: #aaa;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: block;
  cursor: pointer;
  position: relative;
  border: none;

  &::before,
  &::after {
    background: #fff;
    position: absolute;
    content: '';
    top: 50%;
    left: 5px;
    width: 10px;
    height: 1px;
  }

  &::before {
    transform: translateY(-50%) rotate(45deg);
  }

  &::after {
    transform: translateY(-50%) rotate(-45deg);
  }
}
</style>
