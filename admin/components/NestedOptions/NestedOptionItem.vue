<template>
  <li :class="containerClass" @click.stop="$emit('click')">
    <div class="option-item">
      <span class="title">{{ title }}</span>
      <span v-if="isTag" class="tag">東京</span>
      <a class="link">
        {{ name }}<template v-if="count"> ({{ count }})</template>
      </a>
      <font-awesome-icon
        :icon="['fas', 'chevron-right']"
        :style="{ color: '#DDDDDD' }"
        class="arrow"
      />
    </div>
    <slot></slot>
  </li>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    isSelected: { type: Boolean, required: true },
    isArrowVisible: { type: Boolean, default: () => false },
    name: { type: String, required: true },
    count: { type: Number, default: () => 0 },
    title: { type: String, default: () => null },
    disabled: {
      type: <PERSON>ole<PERSON>,
      default: () => false,
    },
    isTag: { type: Boolean, required: false },
  },
  computed: {
    containerClass(): any {
      return { active: this.isSelected, disabled: this.disabled }
    },
  },
})
</script>

<style lang="scss" scoped>
.title {
  display: none;
}

.link {
  display: block;
  max-width: 350px;
}

li {
  color: #333;
  cursor: pointer;
  transition: 0.3s;

  &.active a {
    color: #1b7fa7;
  }

  &.disabled {
    pointer-events: none;
    color: #bbb;
  }

  &.noChild {
    color: #bbb;
  }

  .option-item {
    font-weight: normal;
    padding: 10px 0;
    font-size: 14px;
    border-bottom: 1px solid #eee;
    position: relative;
    letter-spacing: 0;
    line-height: 20px;
    display: flex;
    align-items: center;
  }

  .arrow {
    position: absolute;
    right: 10px;
    top: 14px;
    font-size: 14px;
    transition: 0.3s;

    &.down {
      transform: rotate(90deg);
    }
  }
}

.tag {
  font-size: 12px;
  line-height: 18px;
  padding: 5px 13px;
  color: #fff;
  background: #2b4663;
  border-radius: 2px;
  margin-right: 8px;
}
</style>
