<template>
  <div class="date-range-filter">
    <div v-click-outside="() => (showList = false)" class="select-box">
      <span class="filter-current" @click="showList = !showList">
        {{ selectedFilter }}
      </span>
      <ul class="filter-list" :class="{ show: showList }">
        <li
          v-for="item in filtersSelect"
          :key="item.key"
          class="filter-item"
          :class="{ selected: item.key === currentFilter }"
          @click="onSelectFilter(item)"
        >
          {{ item.label }}
        </li>
      </ul>
    </div>
    <client-only>
      <date-picker
        v-model="filterDate"
        placeholder="日にちを選択する"
        range
        :editable="false"
        :class="{ 'has-value': hasValue }"
        :lang="lang"
        :disabled-date="disabledDate"
        @input="onChangeFilterDate"
      />
    </client-only>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import vClickOutside from 'v-click-outside'
import dayjs from 'dayjs'
import {
  dateRangeValid,
  getDateRangeFormatted,
  datePickerLang as lang,
} from '~/constants/utils'
import { FilterKey } from '~/constants/constant'

Vue.use(vClickOutside)

interface FilterOption {
  key: FilterKey
  label: string
}

const filterList: FilterOption[] = [
  { key: FilterKey.custom, label: 'カスタム' },
  { key: FilterKey.today, label: '今日' },
  { key: FilterKey.yesterday, label: '昨日' },
  { key: FilterKey.last7Days, label: '過去7日間' },
  { key: FilterKey.last4Weeks, label: '過去4週間' },
  { key: FilterKey.last3Months, label: '過去3ヶ月' },
  { key: FilterKey.last12Months, label: '過去12ヶ月' },
]

function getDateRangeFromConstant(key: FilterKey): Date[] {
  const yesterday = dayjs().subtract(1, 'd')
  let dataToEmit = [yesterday, yesterday]
  switch (key) {
    case FilterKey.today:
      dataToEmit = [yesterday.add(1, 'd'), yesterday.add(1, 'd')]
      break
    case FilterKey.yesterday:
      dataToEmit = [yesterday, yesterday]
      break
    case FilterKey.last7Days:
      dataToEmit = [yesterday.subtract(7, 'd'), yesterday]
      break
    case FilterKey.last3Months:
      dataToEmit = [yesterday.subtract(3, 'month'), yesterday]
      break
    case FilterKey.last4Weeks:
      dataToEmit = [yesterday.subtract(4, 'week'), yesterday]
      break
    case FilterKey.last12Months:
      dataToEmit = [yesterday.subtract(12, 'month'), yesterday]
      break
    default:
      dataToEmit = []
  }
  return dataToEmit.map((item) => item.toDate())
}

export default Vue.extend({
  name: 'DateRangeFilter',
  props: {
    value: {
      type: Array as PropType<Date[]>,
      required: false,
      default: () => [],
    },
    filterDefault: {
      type: String as PropType<FilterKey>,
      required: true,
    },
  },
  data() {
    const isDateValid = dateRangeValid(this.value)
    return {
      filterDate: isDateValid
        ? this.value
        : getDateRangeFromConstant(this.filterDefault),
      currentFilter: isDateValid ? FilterKey.custom : this.filterDefault,
      filtersSelect: filterList.filter((item) => item.key !== FilterKey.custom),
      showList: false,
      lang,
    }
  },
  computed: {
    hasValue(): boolean {
      return dateRangeValid(this.filterDate)
    },
    selectedFilter(): string {
      return (
        filterList.find((item) => item.key === this.currentFilter)?.label || ' '
      )
    },
  },
  watch: {
    value(newVal) {
      this.filterDate = newVal
    },
    currentFilter: {
      immediate: true,
      handler(newVal) {
        if (newVal === FilterKey.custom) return
        const dataToEmit = getDateRangeFromConstant(newVal)
        const { start, end } = getDateRangeFormatted(dataToEmit)
        this.emitData([start, end])
      },
    },
  },
  methods: {
    disabledDate(date: Date) {
      return dayjs(date).isAfter(dayjs())
    },
    onChangeFilterDate(dateSelected: Date[]) {
      const isDateValid = dateRangeValid(dateSelected)
      if (!isDateValid) {
        this.currentFilter = this.filterDefault
        return
      }
      const { start, end } = getDateRangeFormatted(dateSelected)
      this.emitData([start, end])
      this.currentFilter = FilterKey.custom
    },
    onSelectFilter(filter: FilterOption) {
      this.currentFilter = filter.key
      this.showList = false
    },
    emitData(data: String[] | null[]) {
      this.$emit('input', data)
    },
  },
})
</script>
<style lang="scss" scoped>
.date-range-filter {
  display: flex;
  border-radius: 5px;
  border: 1px solid #ddd;
}

.select-box {
  height: 40px;
  display: flex;
  font-size: 1rem;
  font-weight: 400;
  background: #fff;
  min-width: 130px;
  position: relative;
  align-items: center;
  padding: 6px 15px 6px 15px;
  border-right: 1px solid #ddd;
  border-radius: 5px 0 0 5px;
}

.filter-list {
  left: 0;
  top: 130%;
  min-width: 100%;
  background: #fff;
  width: fit-content;
  position: absolute;
  border-radius: 5px;
  visibility: hidden;
  z-index: -1;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform: translateY(20px);
  box-shadow: rgb(0 0 0 / 15%) 0 2px 8px;

  &.show {
    visibility: visible;
    z-index: 1;
    opacity: 1;
    transform: translateY(-5px);
  }
}

.filter-item {
  padding: 10px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.12s cubic-bezier(0.55, 0, 0.55, 0.2);

  &.selected {
    color: #1a73e8;
  }

  &:hover {
    background-color: rgba(158, 158, 158, 0.2);
  }
}

.filter-current {
  width: 100%;
  display: flex;
  cursor: pointer;
  line-height: 1rem;
  position: relative;
  align-items: center;
  font-size: 14px;
  justify-content: space-between;
  background: transparent;
  background-repeat: no-repeat;
  background-position-x: 110%;
  background-position-y: 45%;
  background-image: url(data:image/svg+xml;base64,PHN2ZyBmaWxsPSdibGFjaycgaGVpZ2h0PScyNCcgdmlld0JveD0nMCAwIDI0IDI0JyB3aWR0aD0nMjQnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZD0nTTcgMTBsNSA1IDUtNXonLz48cGF0aCBkPSdNMCAwaDI0djI0SDB6JyBmaWxsPSdub25lJy8+PC9zdmc+);
}
</style>

<style lang="scss">
$namespace: 'mx';
$default-color: #555;
$primary-color: #1284e7;
@import '~vue2-datepicker/scss/index.scss';

.mx-table-date td,
.mx-table-date th {
  text-align: center;
}

.mx-btn-current-year,
.mx-btn-current-month {
  padding: 0;
}

.mx-input {
  color: #333;
  height: 40px;
  width: 320px;
  border: none;
  box-shadow: none;
  font-weight: normal;
  border-radius: 0 5px 5px 0;

  &:hover,
  &:focus {
    border-color: #ddd;
  }
}

.mx-icon-calendar {
  font-size: 20px;
}

.has-value {
  .mx-icon-calendar {
    display: none;
  }

  .mx-icon-clear {
    display: block;
  }
}

// There no props to disable button next/prev year, so i using css to hidden them
.mx-calendar-panel-date {
  .mx-btn-icon-double-left,
  .mx-btn-icon-double-right {
    display: none;
  }
}

/** Override  */
.mx-calendar-content .cell {
  &.in-range,
  &.hover-in-range {
    border-radius: 0;
  }
  &.disabled {
    background-color: transparent;
    color: #ccc;
  }
}

.mx-date-row {
  &:hover {
    background: unset;
    cursor: unset;
  }

  .not-current-month {
    opacity: 0;
    pointer-events: none;
  }
}
</style>
