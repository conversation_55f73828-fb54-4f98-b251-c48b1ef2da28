<template>
  <div class="rent-item item-hv">
    <div class="rent-item-inner">
      <figure class="thumb rent-thumb">
        <a
          :href="`${$config.webUrl}/rent/${prefecturePath}/${room.code}`"
          target="_blank"
        >
          <div class="thumb rent-thumb-image">
            <img :src="propertyImage" :alt="room.buildingName" />
          </div>
        </a>
      </figure>
      <div class="rent-content">
        <h3 class="rent-title">
          <a
            :href="`${$config.webUrl}/rent/${prefecturePath}/${room.code}`"
            target="_blank"
          >
            {{ roomName }}
          </a>
        </h3>
        <div class="rent-price">
          <span>
            {{ formatPrice(propertyFee.rentFee) }} +
            {{ formatPrice(propertyFee.maintenanceFee) }}
          </span>
          <small>({{ maintenanceText }})</small>
        </div>
        <p class="rent-location">{{ propertyAddress }}m<sup>2</sup></p>
        <p class="rent-access">{{ propertyAccess }}</p>
      </div>
    </div>
    <nuxt-link
      target="_blank"
      title="Edit the property"
      class="edit-property"
      :to="`/properties/${propertyId}?roomCode=${room.code}`"
    >
      物件管理ページをみる
    </nuxt-link>
  </div>
</template>
<script lang="ts">
import Vue, { PropType } from 'vue'
import get from 'lodash/get'
import { formatPrice } from '~/constants/formatPrice'
import {
  PropertyRooms,
  PropertyAddress,
  PropertyAccess,
  PropertyRoomsFeesMonthly,
} from '~/types/types'
import { getAddressDetail, getStationAccess } from '~/constants/utils'

export default Vue.extend({
  name: 'InquiryRoom',
  props: {
    propertyId: {
      type: String,
      required: true,
    },
    room: {
      type: Object as PropType<PropertyRooms>,
      required: true,
    },
    roomName: {
      type: String,
      required: true,
    },
    prefecturePath: {
      type: String,
      required: true,
    },
    addressDetail: {
      type: Object as PropType<PropertyAddress>,
      required: true,
    },
    access: {
      type: Array as PropType<PropertyAccess>,
      required: true,
    },
  },
  computed: {
    propertyFee(): PropertyRoomsFeesMonthly {
      if (this.room) {
        return {
          rentFee: get(this.room, 'fees.monthly.rentFee', 0),
          maintenanceFee: get(this.room, 'fees.monthly.maintenanceFee', 0),
        }
      }
      return {
        rentFee: 0,
        maintenanceFee: 0,
      }
    },
    propertyImage(): string {
      const url = get(this.room, 'images.featured', '')
      if (url.indexOf('http') === 0) return url
      return `${this.$config.cdnUrl}/${url}`
    },
    propertyAddress(): string {
      const detail = this.addressDetail
      const layout = get(this.room, 'layout', 0)
      const size = get(this.room, 'size', 0)
      const numberOfRoom = get(this.room, 'numberOfRoom', 0)
      if (!detail || !this.room) return ''
      return `${getAddressDetail(detail)} / ${numberOfRoom}${layout} / ${size}`
    },
    propertyAccess(): string {
      const stations = this.access
      return getStationAccess(stations)
    },
    maintenanceText(): string {
      if (this.propertyFee.maintenanceFee) {
        return 'Maintenance Fee'
      }
      return 'No Maintenance Fee'
    },
  },
  methods: {
    formatPrice,
  },
})
</script>

<style lang="scss" scoped>
.edit-property {
  color: #0d54ab;
  font-size: 14px;

  &:hover {
    text-decoration: underline;
  }
}

.rent-thumb-image {
  flex: 1;
  background-color: #ececec;
  width: 140px;
  height: 95px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.rent-item-inner {
  display: flex;
  margin-bottom: 20px;
}

.rent-item-inner image {
  height: 94px;
  width: 140px;
  border: 1px solid #e8e8e8;
  border-radius: 3px;
}

.rent-title a {
  color: #23aa75;
  font-size: 16px;
  font-weight: bold;
  line-height: 24px;
}

.rent-price {
  color: #333;
  font-size: 12px;
  font-weight: bold;
  line-height: 14px;
}

.rent-location {
  padding-top: 10px;
  color: #555;
  font-size: 12px;
  line-height: 17px;
}

.rent-access {
  color: #555;
  font-size: 12px;
  line-height: 17px;
}
.thumb.rent-thumb {
  padding-right: 14px;
}
.rent-item {
  margin: 10px 0;
}
</style>
