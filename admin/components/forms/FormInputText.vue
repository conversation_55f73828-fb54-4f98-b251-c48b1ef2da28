<template>
  <div class="form-input">
    <label v-if="label !== ''" class="form-input__label" :for="inputId">
      {{ label }} <span v-if="isOptional" class="optional">任意</span>
      <slot name="label" />
    </label>
    <p v-if="caption" class="form-input__caption">{{ caption }}</p>
    <div class="input-wrapper">
      <input
        :id="inputId"
        :value="value"
        :type="type"
        :placeholder="placeholder"
        :disabled="isDisabled"
        :class="{ error: !!error }"
        @blur="$emit('input', $event.target.value)"
      />
      <span v-if="suffix">{{ suffix }}</span>
    </div>
    <div v-if="error" class="error">
      {{ error }}
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { v4 as uuidv4 } from 'uuid'

export default Vue.extend({
  props: {
    label: { type: String, default: () => '' },
    caption: { type: String, default: () => null },
    value: { type: [String, Number], default: () => '' },
    type: { type: String, default: () => 'text' },
    placeholder: { type: String, default: () => '' },
    isOptional: { type: Boolean, default: () => false },
    isDisabled: { type: Boolean, default: () => false },
    error: { type: String, default: () => null },
    suffix: { type: String, default: () => null },
  },
  data: () => ({
    inputId: uuidv4(),
  }),
})
</script>

,
<style lang="scss" scoped>
.input-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: stretch;
  align-items: center;

  input {
    flex: 1;
  }

  span {
    font-size: 14px;
    margin-left: 10px;
  }
}
</style>
