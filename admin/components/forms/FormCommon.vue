<template>
  <div class="form-common">
    <div class="form-common__logo">
      <img class="logo" src="/logo_white.png" />
    </div>
    <div class="form-common__content">
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({})
</script>

<style lang="scss" scoped>
.form-common {
  width: 400px;
  border-radius: 5px;
  background-color: #fff;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.2);

  h2 {
    margin-bottom: 12px;
  }

  button {
    outline: 0;
    border: 0;
    background: #000;
    color: #eee;
    padding: 15px;
    cursor: pointer;
    width: 100%;
    margin-bottom: 20px;
    font-size: 14px;

    &:hover {
      opacity: 0.7;
    }
  }
}

.form-common__logo {
  border-radius: 5px 5px 0 0;
  background-color: #18af94;
  padding: 35px;

  img {
    width: 150px;
  }
}

.form-common__content {
  padding: 20px 20px;
}

.form-common__content .error__message {
  color: red;
}

.form-common__content .nuxt__link {
  color: #176dd3;
  font-size: 12px;
}
</style>

<style lang="scss">
.container-common {
  margin: 0 auto;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  background-image: url('~/assets/images/admin-home.jpeg');
  background-size: 100% 100%;
}
.form-common .form-input input {
  border-radius: unset;
  height: 44px;
}
</style>
