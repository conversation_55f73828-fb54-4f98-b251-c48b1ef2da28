<template>
  <div class="drop-upload">
    <div class="drop-upload__header" @click.prevent="isExpanded = !isExpanded">
      <h3>{{ title }}</h3>
      <font-awesome-icon
        :icon="['fas', isExpanded ? 'caret-down' : 'caret-right']"
        :style="{ color: '#555' }"
        class="arrow"
      />
    </div>
    <div class="drop-upload__collapsible" :class="{ isExpanded }">
      <p class="drop-upload__caption">{{ caption }}</p>
      <div
        class="drop-upload__zone"
        :class="{ dragged }"
        @dragover.prevent="dragged = true"
        @dragleave="dragged = false"
        @drop.prevent="handleOnFileDrop"
      >
        <p>ファイルをドロップしてアップロード</p>
        <small>または</small>
        <label :for="inputId" class="upload">ファイルを選択</label>
        <input
          :id="inputId"
          ref="file"
          type="file"
          name="file"
          lang="jp-JP"
          accept=".jpg,.jpeg,.png"
          multiple
          @change="handleOnFileUpload"
        />
      </div>
      <ul class="drop-upload__files">
        <li v-for="temp in uploads" :key="temp.name">
          <img :src="temp.url" />
          <span>{{ temp.name }}</span>
          <spin v-if="temp.isUploading" />
          <font-awesome-icon
            v-else
            :icon="['fas', 'times']"
            class="remove"
            @click.stop="() => {}"
          />
        </li>
        <draggable
          v-model="images"
          v-bind="dragOptions"
          @start="isDragging = true"
          @end="isDragging = false"
          @change="onSortImage"
        >
          <!-- Uploaded -->
          <li v-for="urlPath in images" :key="urlPath">
            <img v :src="getRealImageUrl(urlPath)" />
            <span>{{ last((urlPath || '').split('/')) }}</span>
            <font-awesome-icon
              :icon="['fas', 'times']"
              class="remove"
              @click.stop="doDelete(urlPath)"
            />
          </li>
        </draggable>
      </ul>
      <span
        v-if="images.length > 1"
        class="delete-all"
        title="Delete the all images"
        @click.prevent="doDeleteAll"
      >
        すべての写真を削除する
      </span>
    </div>
    <div v-if="error && !Array.isArray(error)" class="drop-upload__error">
      {{ error }}
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import last from 'lodash/last'
import { v4 as uuidv4 } from 'uuid'
import { customAlphabet } from 'nanoid'
import getS3UploadSignedUrlGql from '~/graphql/queries/getS3UploadSignedUrl.gql'

export const generateRandomName = customAlphabet(
  '1234567890ABCDEFGHIJKLMONPRSTUFWXYZ',
  4
)

interface UploadItem {
  name: string
  isUploading: boolean
  url: string
}

export default Vue.extend({
  props: {
    value: {
      type: Array as PropType<string[]>,
      required: true,
    },
    title: { type: String, required: true },
    caption: { type: String, required: true },
    limit: { type: Number, default: () => 2 },
    path: { type: String, required: true },
    error: { type: [String, Array], default: () => null },
  },
  data: () => ({
    inputId: uuidv4(),
    uploads: [] as UploadItem[],
    isExpanded: true,
    dragged: false,
    images: [] as string[],
  }),
  computed: {
    dragOptions() {
      return {
        animation: 200,
        group: 'description',
        disabled: false,
        ghostClass: 'ghost',
      }
    },
  },
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler(newValue: string[]) {
        this.images = [...newValue]
      },
    },
  },
  methods: {
    last,
    getRealImageUrl(path: string) {
      if (path.indexOf('http') === 0) return path
      return `${this.$config.cdnUrl}/${path}`
    },
    async getSignedUrl(type: string, path: string) {
      const result = await this.$apollo.query({
        query: getS3UploadSignedUrlGql,
        variables: { type, path },
      })
      return result.data.s3UploadSignedUrl.url
    },
    handleOnFileDrop(event: any) {
      const element = this.$refs.file as HTMLInputElement
      element.files = event.dataTransfer.files
      this.handleOnFileUpload()
    },
    handleOnFileUpload() {
      this.dragged = false
      const element = this.$refs.file as HTMLInputElement
      if (!element.files) return

      const imageCount =
        this.value.length + this.uploads.length + element.files.length
      if (imageCount > this.limit) {
        this.$toast.error(`Sorry. you can not upload more than ${this.limit}`)
        return
      }

      element.files.forEach(this.process)
      element.value = (null as unknown) as string
    },

    getLocalPreviewUrl(file: File): Promise<string> {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (event) => {
          const result = event?.target?.result
          if (!result) reject(new Error('can not retrieve preview url'))
          resolve(result as string)
        }
        reader.readAsDataURL(file)
      })
    },

    async process(file: File) {
      const error = this.checkFile(file)
      if (error) {
        return this.$toast.error(error)
      }

      const uploadFile = {
        url: await this.getLocalPreviewUrl(file),
        name: file.name,
        isUploading: true,
      }

      this.uploads.unshift(uploadFile)
      this.upload(uploadFile, file)
    },

    generateS3Path(originalFileName: string) {
      const randomName = generateRandomName()
      const ext = last(originalFileName.split('.'))
      return `${this.path}/${uuidv4()}/${randomName}.${ext}`
    },

    async upload(upload: UploadItem, rawFile: File) {
      const path = this.generateS3Path(rawFile.name)
      const signedUrl = await this.getSignedUrl(rawFile.type, path)
      const result = await fetch(signedUrl, {
        method: 'PUT',
        body: rawFile,
      })
      await new Promise((resolve) => setTimeout(resolve, 4000))
      if (result.ok) {
        this.$emit('input', [path, ...this.images])
        const index = this.uploads.indexOf(upload)
        this.uploads.splice(index, 1) // remove it from upload
      }
    },
    checkFile(file: File) {
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
      if (!allowedTypes.includes(file.type)) {
        return 'Only JPG or PNG. '
      }

      const maxFileSize = 1500000 // 1.5 MB
      if (file.size > maxFileSize) {
        return 'Current max file allowed is 1.5 MB, please try compress the image with https://tinyjpg.com'
      }
      return null
    },
    onSortImage() {
      this.$emit('input', [...this.images])
    },
    doDeleteAll() {
      this.$showConfirmDialog({
        message: '一度削除すると復元ができません。この画像を削除しますか？',
        okCallback: () => {
          this.$emit('input', [])
        },
      })
    },
    doDelete(urlPath: string) {
      this.$showConfirmDialog({
        message: '一度削除すると復元ができません。この画像を削除しますか？',
        okCallback: () => {
          this.$emit(
            'input',
            [...this.value].filter((url) => url !== urlPath)
          )
        },
      })
    },
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/settings/variables';

.drop-upload {
  background-color: $color_white;
  border-radius: 4px;
  padding: 20px;

  &:not(:last-child) {
    margin-bottom: 20px;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: 0.3s;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }

    h3 {
      font-size: 12px;
    }
  }

  &__collapsible {
    overflow: hidden;
    transition: max-height 0.2s ease-out;
    display: flex;
    flex-direction: column;
    max-height: 0;

    &.isExpanded {
      max-height: 100%;
    }

    & > *:not(:last-child) {
      margin-bottom: 10px;
    }
  }

  &__caption {
    margin-top: 10px;
    font-size: 12px;
    color: $color_emperor;
  }

  &__zone {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 3px dotted $color_alto;
    height: 150px;

    input {
      width: 0;
      height: 0;
      display: none;
    }

    &.dragged {
      border-color: $color_matisse;
      background: #e4f0f7;
    }

    & > *:not(:last-child) {
      margin-bottom: 10px;
    }

    p {
      font-size: 12px;
      font-weight: bold;
    }

    small {
      font-size: 10px;
    }

    .upload {
      background-color: rgba(27, 127, 167, 0.05);
      border: 1px solid $color_matisse;
      border-radius: 4px;
      width: 120px;
      height: 30px;
      color: $color_matisse;
      font-size: 10px;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: 0.3s;

      &:hover {
        background-color: $color_matisse;
        color: $color_white;
      }
    }
  }

  &__files {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: stretch;

    li {
      display: flex;
      align-items: center;
      background-color: $color_white;
      padding: 11px 18px;
      border-radius: 4px;
      height: 55px;
      margin-bottom: 10px;
      cursor: pointer;
      border: 1px solid #ddd;
      border-left: 5px solid #555;

      &:hover {
        opacity: 0.8;
      }

      &:last-child {
        margin-bottom: 0;
      }

      & > *:not(:last-child) {
        margin-right: 10px;
      }

      img {
        width: 45px;
        height: 34px;
        border-radius: 3px;
        background-color: #e8e8e8;
      }

      span {
        flex: 1;
        font-size: 12px;
        color: $color_emperor;
      }

      .remove {
        color: $color_mexican_red;
      }

      .spinner {
        transform: scale(0.2);
      }
    }
  }

  &__error {
    font-size: 12px;
    color: red;
  }
}
.delete-all {
  color: #9f1f2e;
  font-size: 14px;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}
</style>

<style lang="scss">
.flip-list-move {
  transition: transform 0.5s;
}

.no-move {
  transition: transform 0s;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
</style>
