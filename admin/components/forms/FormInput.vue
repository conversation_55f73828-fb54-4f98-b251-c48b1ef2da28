<template>
  <div class="form-input">
    <label v-if="label !== ''" class="form-input__label">{{ label }}</label>
    <slot></slot>
    <div v-if="error" class="error">
      {{ error }}
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    label: {
      type: String,
      required: true,
    },
    error: {
      type: String,
      default: () => null,
    },
  },
})
</script>
