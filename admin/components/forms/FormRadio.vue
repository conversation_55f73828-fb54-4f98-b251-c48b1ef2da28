<template>
  <div class="form-input" :class="containerClass">
    <label v-if="label" class="form-input__label">{{ label }}</label>
    <div class="form-radio">
      <label
        v-for="option in options"
        :key="option.value"
        class="form-radio__check"
      >
        <input v-model="selected" type="radio" :value="option.value" />
        <span class="checkmark"></span>
        <span>{{ option.label }}</span>
      </label>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'

interface SelectOption {
  label: string
  value: string
}

export default Vue.extend({
  props: {
    label: {
      type: String,
      default: () => '',
    },
    value: {
      type: String,
      required: true,
    },
    options: {
      type: Array as PropType<SelectOption[]>,
      required: true,
    },
    containerClass: {
      type: String,
      default: () => '',
    },
  },
  computed: {
    selected: {
      get(this: any): string {
        return this.value
      },
      set(this: any, value) {
        this.$emit('input', value)
      },
    },
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/settings/variables';

.form-radio {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  font-size: 14px;
  position: relative;

  &__check {
    display: flex;
    position: relative;
    margin-bottom: 12px;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    input {
      position: absolute;
      opacity: 0;
      cursor: pointer;
    }

    span {
      font-size: 14px;
      margin-left: 5px;
    }

    .checkmark {
      background: #fff;
      position: relative;
      height: 20px;
      width: 20px;
      border: 1px solid $color_emperor;
      border-radius: 50%;
      transition: 0.3s;
    }

    &:hover input ~ .checkmark {
      opacity: 0.8;
    }

    /* When the radio button is checked, add a blue background */
    input:checked ~ .checkmark {
      background-color: $color_white;
    }

    /* Create the indicator (the dot/circle - hidden when not checked) */
    .checkmark::after {
      content: '';
      position: absolute;
      display: none;
      top: 3px;
      left: 3px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: $color_matisse;
    }

    /* Show the indicator (dot/circle) when checked */
    input:checked ~ .checkmark::after {
      display: block;
    }
  }
}
</style>
