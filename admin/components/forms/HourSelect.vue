<template>
  <div class="date-select">
    <select v-model="selected">
      <option disabled>選択する</option>
      <option v-for="hour in hours" :key="hour" :value="hour">
        {{ hour }}
      </option>
    </select>
    {{ suffix }}
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import range from 'lodash/range'

const hours = range(0, 24).map((number: Number) => {
  const options = { minimumIntegerDigits: 2, useGrouping: false }
  return number.toLocaleString('en-US', options) + ':00'
})

export default Vue.extend({
  props: {
    value: { type: Object, required: true },
    suffix: { type: String, default: () => '' },
  },
  data: () => ({ hours }),
  computed: {
    selected: {
      get(): string {
        return (this as any).value
      },
      set(value) {
        ;(this as any).$emit('input', value)
      },
    },
  },
})
</script>

<style lang="scss" scoped>
.date-select {
  font-size: 14px;
}
</style>
