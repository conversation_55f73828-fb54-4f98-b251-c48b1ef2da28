<template>
  <label class="checkbox">
    <input
      type="checkbox"
      :checked="isChecked"
      :value="keyValue"
      @change="handleOnChange"
    />
    <span class="checkmark" :style="checkedStyle"></span>
    {{ label }}
  </label>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'

export default Vue.extend({
  props: {
    keyValue: { type: [String, Number, Boolean], required: true },
    value: {
      type: [<PERSON>rra<PERSON>, Boolean] as PropType<string[] | boolean>,
      required: true,
    },
    label: { type: String, required: true },
    isMultiple: { type: <PERSON>olean, default: () => true },
    checkedColor: { type: String, default: () => '#1B7FA7' },
  },
  computed: {
    // TODO: some TS intersection type check?
    isChecked(): boolean {
      if (this.isMultiple && Array.isArray(this.value)) {
        return this.value.includes(this.keyValue.toString())
      }
      return this.value as boolean
    },
    checkedStyle(): object {
      if (!this.isChecked) return {}
      return {
        borderColor: this.checkedColor,
        backgroundColor: this.checkedColor,
      }
    },
  },

  methods: {
    handleOnChange(event: any) {
      const { value: code, checked } = event.target

      if (!this.isMultiple || !Array.isArray(this.value)) {
        this.$emit('input', checked)
        return
      }

      const index = this.value.indexOf(code)
      const isExist = index !== -1

      if (!isExist && checked) {
        this.$emit('input', [...this.value, code])
      }

      if (isExist && !checked) {
        const newValue = this.value.slice()
        newValue.splice(index, 1)
        this.$emit('input', newValue)
      }
    },
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/settings/variables';

.checkbox {
  display: flex;
  align-items: center;
  background-color: $color_athens_gray;
  padding: 7px 14px;
  border-radius: 10px;
  font-size: 12px;
  color: #333;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  transition: 0.3s;
  height: 34px;

  &:hover {
    background-color: #eee;
  }

  input[type='checkbox'] {
    width: 0;
    height: 0;
    display: none;
  }

  /* Create a custom checkbox */
  .checkmark {
    position: relative;
    height: 20px;
    width: 20px;
    border: 1px solid $color_alto;
    background-color: $color_white;
    margin-right: 7px;

    /* Create the checkmark/indicator (hidden when not checked) */
    &::after {
      content: '';
      position: absolute;
      display: block;
      left: 7px;
      top: 3px;
      width: 5px;
      height: 10px;
      border: solid white;
      border-width: 0 3px 3px 0;
      -webkit-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      transform: rotate(45deg);
    }
  }
}
</style>
