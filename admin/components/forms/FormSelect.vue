<template>
  <div class="form-input">
    <label v-if="label" class="form-input__label">
      {{ label }} <span v-if="isOptional" class="optional">任意</span>
    </label>
    <select
      v-model="selected"
      :disabled="isDisabled"
      :class="{ error: isErrorVisible }"
    >
      <option :disabled="disableDefaultOption" :value="emptyValue">
        選択する
      </option>
      <option
        v-for="option in options"
        :key="option.value"
        :value="option.value"
      >
        {{ option.label }}
      </option>
    </select>
    <div v-if="isErrorVisible" class="error">{{ error }}</div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'

interface SelectOption {
  label: string
  value: string
}

export default Vue.extend({
  props: {
    label: {
      type: String,
      default: () => null,
    },
    value: {
      type: [String, Number],
      required: true,
    },
    disableDefaultOption: {
      type: Boolean,
      default: () => true,
    },
    options: {
      type: Array as PropType<SelectOption[]>,
      required: true,
    },
    error: {
      type: String,
      default: () => null,
    },
    isOptional: { type: Boolean, default: () => false },
    emptyValue: { type: String, default: () => '' },
    isDisabled: { type: Boolean, default: () => false },
  },
  computed: {
    isErrorVisible() {
      if (this.value && this.value !== this.emptyValue) return false
      return this.error
    },
    selected: {
      get(): string {
        return (this as any).value
      },
      set(value: string) {
        ;(this as any).$emit('input', value)
      },
    } as object,
  },
})
</script>

<style lang="scss" scoped>
.form-input {
  select {
    -webkit-appearance: none;
    -moz-appearance: none;
    background-color: #fff;
    background-image: url('data:image/svg+xml;base64,PHN2ZyBmaWxsPSdibGFjaycgaGVpZ2h0PScyNCcgdmlld0JveD0nMCAwIDI0IDI0JyB3aWR0aD0nMjQnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZD0nTTcgMTBsNSA1IDUtNXonLz48cGF0aCBkPSdNMCAwaDI0djI0SDB6JyBmaWxsPSdub25lJy8+PC9zdmc+');
    background-repeat: no-repeat;
    background-position-x: 98%;
    background-position-y: 5px;
    height: 34px;

    &.error {
      border-color: red !important;
    }
  }
}
</style>
