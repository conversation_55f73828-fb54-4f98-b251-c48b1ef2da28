<template>
  <div class="date-select">
    <div class="date-select__component">
      <select v-model="selected.year" :disabled="isDisabled">
        <option disabled value="">—</option>
        <option v-for="year in options.years" :key="year" :value="year">
          {{ year }}
        </option>
      </select>
      年
      <select v-model="selected.month" :disabled="isDisabled">
        <option disabled value="">—</option>
        <option v-for="month in options.months" :key="month" :value="month">
          {{ month }}
        </option>
      </select>
      月
      <template v-if="isDayVisible">
        <select v-model="selected.day" :disabled="isDisabled">
          <option disabled value="">—</option>
          <option v-for="day in options.days" :key="day" :value="day">
            {{ day }}
          </option>
        </select>
        日
      </template>
      <span v-if="suffix !== ''" class="suffix">{{ suffix }}</span>
    </div>
    <div v-if="error" class="error">
      {{ error }}
    </div>
  </div>
</template>

<script lang="ts">
import dayjs from 'dayjs'
import Vue from 'vue'
import range from 'lodash/range'

function addExtraZero(number: Number | string) {
  const options = { minimumIntegerDigits: 2, useGrouping: false }
  return number.toLocaleString('en-US', options)
}

const defaultInput = {
  year: '',
  month: '',
  day: '',
}

function propToSelected(
  value: string,
  isDayVisible: boolean,
  defaultYear: number
) {
  if (value === '') {
    return {
      ...defaultInput,
      year: defaultYear > 0 ? defaultYear.toString() : '',
      day: isDayVisible ? '' : '01',
    }
  }
  const date = dayjs(value)
  return {
    year: date.get('year'),
    month: addExtraZero(date.get('month') + 1),
    day: !isDayVisible ? '01' : addExtraZero(date.get('date')),
  }
}

export default Vue.extend({
  props: {
    reverse: { type: Boolean, default: () => false },
    value: { type: String, default: () => dayjs().format('YYYY-MM-DD') },
    suffix: { type: String, default: () => '' },
    isDayVisible: { type: Boolean, default: () => true },
    error: { type: String, default: () => null },
    defaultYear: { type: Number, default: () => 0 },
    minYear: { type: Number, default: () => 1950 },
    maxYear: { type: Number, default: () => new Date().getFullYear() },
    isDisabled: { type: Boolean, default: false },
  },

  data() {
    const years = range(this.minYear, this.maxYear + 1).map((num) =>
      num.toString()
    )
    return {
      selected: propToSelected(this.value, this.isDayVisible, this.defaultYear),
      options: {
        years: this.reverse ? years.reverse() : years,
        months: range(1, 13).map(addExtraZero),
        days: range(1, 32).map(addExtraZero),
      },
    }
  },

  watch: {
    selected: {
      deep: true,
      handler() {
        // if any of the value still empty, then skip emit the event
        const fields = Object.entries(this.selected)
        for (const [, value] of fields) {
          if (value === '') return
        }
        const { year, month, day } = this.selected
        const date = `${year}-${addExtraZero(month)}-${addExtraZero(day)}`
        this.$emit('input', date)
      },
    },
    value(newValue: string) {
      this.$withoutWatchers(() => {
        this.selected = propToSelected(
          newValue,
          this.isDayVisible,
          this.defaultYear
        )
      })
    },
  },

  methods: {
    // Without this, there is possibility a circular loop
    // Ref: https://github.com/vuejs/vue/issues/1829
    $withoutWatchers(this: any, cb: any) {
      const watchers = this._watchers.map((watcher: any) => ({
        cb: watcher.cb,
        sync: watcher.sync,
      }))

      for (const index in this._watchers) {
        this._watchers[index] = Object.assign(this._watchers[index], {
          cb: () => null,
          sync: true,
        })
      }

      cb()

      for (const index in this._watchers) {
        this._watchers[index] = Object.assign(
          this._watchers[index],
          watchers[index]
        )
      }
    },
  },
})
</script>

<style lang="scss" scoped>
.date-select {
  font-size: 14px;
}
</style>
