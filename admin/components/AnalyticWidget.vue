<template>
  <div class="widget">
    <h3 class="widget-title">
      <slot name="title">{{ title }}</slot>
    </h3>
    <div class="widget-content">
      <slot name="content">
        <template v-if="content">{{ content }}</template>
      </slot>
      <div v-if="extra && extra.length > 0" class="widget-content__extra">
        <div
          v-for="subItem in extra"
          :key="subItem.label"
          class="widget-content__extra-content"
        >
          {{ subItem.label }}: {{ subItem.value }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
export default Vue.extend({
  name: 'AnalyticWidget',
  props: {
    title: {
      type: String,
      required: false,
      default: '',
    },
    content: {
      type: String,
      required: false,
      default: '',
    },
    extra: {
      type: Array as PropType<{ label: string; value: string }[]>,
      required: false,
      default: () => [],
    },
  },
})
</script>

<style lang="scss" scoped>
.widget {
  font-weight: bold;
  border-radius: 5px;
  padding: 20px 15px;
  background-color: #fff;
  border: 1px solid #cfd7df;
  box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.06);
}

.widget-title {
  font-size: 14px;
  line-height: 21px;
  padding-bottom: 5px;
  margin-bottom: 15px;
  border-bottom: 1px solid #cfd7df;
}

.widget-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  &__extra {
    font-size: 10px;
    color: #aaa;
    text-align: right;
    text-transform: uppercase;
    font-weight: normal;
  }
}
</style>
