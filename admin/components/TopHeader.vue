<template>
  <div class="top-header">
    <div class="agency-avatar">
      <div
        v-on-clickaway="() => (panelOpened = false)"
        class="agency-button"
        @click.prevent="panelOpened = true"
      >
        <img class="img-avatar" :src="avatar" />
        <button class="open-panel">
          <img
            src="~/assets/images/icon-arrow.svg"
            class="icon-arrow"
            alt="icon-arrow"
          />
        </button>
      </div>
      <div class="sub-panel" :class="{ opened: panelOpened }">
        <a
          href="https://livingjapan.com/terms-partners"
          target="_blank"
          class="user-link"
          rel="noopener"
        >
          事業者会員規約
          <img
            src="~/assets/images/icon-external-link.svg"
            class="link-icon"
            alt=""
          />
        </a>
        <nuxt-link
          to="/"
          class="user-link"
          title="sign out"
          @click.native="logout"
          >ログアウト
        </nuxt-link>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import getAgency from '~/graphql/queries/getAgency.gql'

export default Vue.extend({
  name: 'TopHeader',
  data() {
    return {
      panelOpened: false,
      agencyId: '' as string | undefined,
      avatar: require('~/assets/images/icon_user.jpg'),
    }
  },
  fetch(): Promise<void> | void {
    const userInfo = this.$getUserInfo()
    if (userInfo.roles.includes('agency-owner')) {
      this.agencyId = userInfo.agencyId
    }
  },
  methods: {
    logout() {
      this.$apolloHelpers.onLogout()
      this.$accessor.reset()
      location.reload()
    },
  },
  apollo: {
    agencyById: {
      query: getAgency,
      variables(): { id: string | undefined } {
        return {
          id: this.agencyId,
        }
      },
      result({ data }) {
        const avatar = get(data, 'agencyById.public.imageUrl', '')
        if (avatar) {
          this.avatar = this.$config.cdnUrl + avatar
        }
      },
    },
  },
})
</script>
<style lang="scss" scoped>
.top-header {
  position: absolute;
  background-color: #fff;
  box-shadow: 0 1px 0 0 #ddd;
  height: 56px;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 13px;
}

.img-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin: 13px 5px 13px 30px;
}

.agency-avatar {
  position: relative;
}

.agency-button {
  display: flex;
  cursor: pointer;
  align-items: center;
}

.sub-panel {
  right: 0;
  top: 90%;
  opacity: 0;
  z-index: -1;
  padding: 14px;
  min-width: 200px;
  border-radius: 5px;
  position: absolute;
  pointer-events: none;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  transition: all ease 0.15s;
  transform: translateY(-10px);
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);

  &.opened {
    opacity: 1;
    z-index: 99;
    visibility: visible;
    pointer-events: auto;
    transform: translateY(0);
  }
}

.user-link {
  color: #333;
  display: flex;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
  align-items: center;

  &:not(:last-child) {
    margin-bottom: 8px;
  }
}
.link-icon {
  width: 12px;
  height: 12px;
  color: #aaa;
  filter: invert(82%) sepia(6%) saturate(9%) hue-rotate(348deg) brightness(84%)
    contrast(83%);
  margin-left: 7px;
}

.icon-arrow {
  width: 14px;
  height: 14px;
  filter: invert(82%) sepia(6%) saturate(9%) hue-rotate(348deg) brightness(84%)
    contrast(83%);
}
.open-panel {
  padding: 0;
  border: none;
  background: none;
}
</style>
