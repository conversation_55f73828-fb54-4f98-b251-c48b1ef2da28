<template>
  <div v-on-clickaway="() => (isShowPanel = false)" class="switch-wrap">
    <div
      v-if="agencySelected"
      class="agency-selected agency-row"
      @click="isShowPanel = true"
    >
      <img
        :src="agencySelected.image"
        class="agency-avatar"
        :alt="`${agencySelected.name} avatar`"
      />
      <p class="agency-name">{{ agencySelected.name }}</p>
      <img src="~/assets/images/icon-arrow.svg" class="icon-arrow" alt="" />
    </div>
    <div
      v-if="otherAgencies.length"
      class="other-agency"
      :class="{ show: isShowPanel }"
    >
      <div
        v-for="agency in otherAgencies"
        :key="agency.id"
        class="agency-row"
        @click="onSelectAgency(agency.id)"
      >
        <img
          :src="agency.image"
          class="agency-avatar"
          :alt="`${agency.name} avatar`"
        />
        <p class="agency-name">{{ agency.name }}</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'

interface Agency {
  image: string
  name: string
  id: string
  selected?: boolean
}
export default Vue.extend({
  name: 'SwitchAgency',
  props: {
    options: {
      type: Array as PropType<Array<Agency>>,
      required: true,
    },
    selected: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      isShowPanel: false,
    }
  },
  computed: {
    agencySelected(): Agency | undefined {
      return this.options.find(({ id }) => this.selected === id)
    },
    otherAgencies(): Agency[] {
      return this.options.filter(({ id }) => this.selected !== id)
    },
  },
  methods: {
    onSelectAgency(agencyId: string) {
      this.isShowPanel = false
      this.$emit('select-agency', agencyId)
    },
  },
})
</script>

<style scoped lang="scss">
.switch-wrap {
  padding-top: 5px;
  position: relative;
}

.agency-avatar {
  width: 24px;
  height: 24px;
  margin-right: 5px;
}

.icon-arrow {
  width: 10px;
  height: 10px;
  cursor: pointer;
  margin-left: auto;
  filter: invert(100%) sepia(9%) saturate(1782%) hue-rotate(0deg)
    brightness(109%) contrast(108%);
}

.agency-name {
  color: #333;
  font-size: 12px;
  overflow: hidden;
  letter-spacing: 0;
  line-height: 18px;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-family: 'Hiragino Sans', sans-serif;
}

.agency-row {
  padding: 8px;
  display: flex;
  cursor: pointer;
  align-items: center;
}

.other-agency {
  opacity: 0;
  width: 100%;
  z-index: -1;
  bottom: 100%;
  border-radius: 3px;
  position: absolute;
  pointer-events: none;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  transition: all 0.15s ease;
  transform: translateY(10px);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);

  &.show {
    opacity: 1;
    z-index: 99;
    pointer-events: auto;
    transform: translateY(0);
  }

  .agency-row:hover {
    background: #f5f5f5;
  }
}

.agency-selected {
  cursor: pointer;
  border-radius: 3px;
  border: 1px solid #fff;

  .agency-name {
    color: #fff;
    max-width: calc(100% - 34px);
  }
}
</style>
