<template>
  <div class="language-options">
    <label v-for="lang in languages" :id="lang.code" :key="lang.code">
      <input v-model="selected" type="checkbox" :value="lang.code" />
      {{ lang.name }}
    </label>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: { value: { type: String, required: true } },
  data: () => ({
    languages: [
      { code: 'jp', name: '日本語' },
      { code: 'en', name: '英語' },
      { code: 'zh', name: '中国語' },
      { code: 'kr', name: '韓国語' },
    ],
  }),
  computed: {
    selected: {
      get() {
        return (this as any).value
      },
      set(value) {
        ;(this as any).$emit('input', value)
      },
    },
  },
})
</script>

<style lang="scss" scoped>
.language-options {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;

  & > *:not(:last-child) {
    margin-right: 15px;
  }

  label {
    display: flex;
    cursor: pointer;

    input[type='checkbox'] {
      width: 20px;
      height: 20px;
      border: 1px solid #ddd;
      margin-right: 5px;
    }
  }
}
</style>
