<template>
  <div class="business-close-options">
    <label v-for="day in days" :id="day.code" :key="day.code">
      <input v-model="selected" type="checkbox" :value="day.code" />
      {{ day.name }}曜日
    </label>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: { value: { type: Array, required: true } },
  data: () => ({
    days: [
      { code: 'mon', name: '月' },
      { code: 'tue', name: '火' },
      { code: 'wed', name: '水' },
      { code: 'thu', name: '木' },
      { code: 'fri', name: '金' },
      { code: 'sat', name: '土' },
      { code: 'sun', name: '日' },
    ],
  }),
  computed: {
    selected: {
      get() {
        return (this as any).value
      },
      set(value) {
        ;(this as any).$emit('input', value)
      },
    },
  },
})
</script>

<style lang="scss" scoped>
.business-close-options {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;

  & > *:not(:last-child) {
    margin-right: 15px;
  }

  label {
    display: flex;
    cursor: pointer;

    input[type='checkbox'] {
      width: 20px;
      height: 20px;
      border: 1px solid #ddd;
      margin-right: 5px;
    }
  }
}
</style>
