#import "../properties/property.fragment.gql"
#import "../properties/room.fragment.gql"
#import "../properties/baseAccess.fragment.gql"
#import "../properties/baseAddressDetail.fragment.gql"
#import "../properties/affiliate.fragment.gql"

query getInquiry($id: MongoID!) {
  inquiryById(_id: $id) {
    _id
    name
    phone
    email
    memo
    purpose
    agencyId
    roomCode
    preferredLanguage
    whenToMoveIn
    property {
      ...InquiryPropertyFragment
    }
    utm {
      utm_source
    }
    affiliate {
      code
    }
    affiliateDetail {
      ...BaseAffiliate
    }
    createdAt
    updatedAt
  }
}

fragment InquiryPropertyFragment on Property {
  ...BasePropertyFragment
  rooms {
    ...BaseRoom
  }
  prefecturePath
  addressDetail {
    ...BaseAddressDetail
  }
  access {
    ...BaseAccess
    stations {
      lineDetail {
        name
        en
      }
      stationDetail {
        name
        en
      }
    }
  }
  agency {
    public {
      name
    }
  }
}
