query getInquiries($page: Int, $sort: SortFindManyInquiryInput, $filter: FilterFindManyInquiryInput) {
  inquiries(perPage: 30, page: $page, sort: $sort, filter: $filter) {
    count
    pageInfo {
      currentPage
      perPage
      itemCount
      hasNextPage
      hasPreviousPage
    }
    items {
      _id
      name
      email
      unread
      monthlyFee
      serviceFee
      createdAt
      property {
        _id
        buildingName
      }
    }
  }
}
