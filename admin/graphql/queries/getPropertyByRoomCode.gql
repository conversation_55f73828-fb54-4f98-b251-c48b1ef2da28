#import "../properties/baseAddressDetail.fragment.gql"
#import "../properties/room.fragment.gql"

fragment BaseAgency on Agency {
  _id
  updatedAt
  createdAt
  basic {
    companyName
    license {
      licenseType
      number
      issueNumber
      expiryDate {
        from
        until
      }
    }
  }
  public {
    name
    imageUrl
    address {
      postalCode
      prefecture
      city
      streetAddress
      buildingName
    }
    phone
    email
    availableLanguages
    businessHour {
      start
      end
    }
    closeDays
    website
  }
}

query getPropertyByRoomCode($roomCode: String!) {
  property(filter: { rooms: [{ code: $roomCode }] }) {
    _id
    buildingName
    buildingType
    builtDate
    maxFloor
    structure
    languageComment
    address {
      line
      coord
    }
    images {
      collections
    }
    addressDetail {
      ...BaseAddressDetail
    }
    access {
      stations {
        lineId
        stationId
        lineName
        stationName
        distance {
          bus
          walk
        }
      }
    }
    rooms {
      ...BaseRoom
    }
    agency {
      ...BaseAgency
    }
  }
}
