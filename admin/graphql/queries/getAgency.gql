query getAgency($id: MongoID!) {
  agencyById(_id: $id) {
    _id
    createdAt
    updatedAt
    serviceFee {
      feeType
      value
    }
    basic {
      companyName
      license {
        haveLicense
        licenseType
        number
        issueNumber
        expiryDate {
          from
          until
        }
      }
    }
    public {
      name
      imageUrl
      address {
        postalCode
        prefecture
        city
        chome
        streetAddress
        buildingName
      }
      phone
      email
      availableLanguages
      businessHour {
        start
        end
      }
      closeDays
      website
    }
  }
}
