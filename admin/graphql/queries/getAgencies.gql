query getAgencies($filter: FilterFindManyAgencyInput, $perPage: Int = 1000, $page: Int = 1) {
  agencies(perPage: $perPage, page: $page, filter: $filter) {
    count
    pageInfo {
      currentPage
      perPage
      itemCount
      hasNextPage
      hasPreviousPage
    }
    items {
      _id
      createdAt
      updatedAt
      inquiryCount
      publishedRoomCount
      totalRoomCount
      serviceFee {
        feeType
        value
      }
      basic {
        companyName
        license {
          licenseType
          number
          issueNumber
          expiryDate {
            from
            until
          }
        }
      },
      public {
        name
        imageUrl
        address {
          postalCode
          prefecture
          city
          streetAddress
          buildingName
        },
        phone
        email
        availableLanguages
        businessHour {
          start
          end
        },
        closeDays
        website
      }
    }
  }
}
