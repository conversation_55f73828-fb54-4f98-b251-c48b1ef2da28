query getForms(
  $perPage: Int = 60
  $page: Int
  $sort: SortFindManyFormInput
  $filter: FilterFindManyFormInput
) {
  forms(perPage: $perPage, page: $page, sort: $sort, filter: $filter) {
    count
    pageInfo {
      currentPage
      perPage
      itemCount
      hasNextPage
      hasPreviousPage
    }
    items {
      _id
      firstName
      lastName
      email
      createdAt
      formType
    }
  }
}
