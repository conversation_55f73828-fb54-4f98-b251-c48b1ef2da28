#import "../properties/affiliate.fragment.gql"

query getFormById($id: MongoID!) {
  formById(_id: $id) {
    _id
    firstName
    lastName
    phone
    email
    memo
    formType
    preferredLanguage
    formRent {
      occupation
      visaStatus
      rentBudget
      floorPlan
      size
      desiredLocation
      stationDistance
      whenToMoveIn
      otherPreferences
    }
    formBuy {
      purpose
      propertyType
      desiredAreas
      timeHorizon
      paymentMethod
      budget
    }
    formSell {
      propertyType
      nearestStation
      desiredPrice
    }
    formTour {
      purpose
      propertyType
      budget
    }
    utm {
      utm_source
    }
    affiliate {
      code
    }
    affiliateDetail {
      ...BaseAffiliate
    }
    createdAt
    updatedAt
  }
}
