query getProperties(
  $page: Int
  $filter: FilterFindManyPropertyInput
  $sort: SortFindManyPropertyInput
) {
  properties(perPage: 50, page: $page, filter: $filter, sort: $sort) {
    count
    pageInfo {
      currentPage
      perPage
      itemCount
      hasPreviousPage
      hasNextPage
    }
    items {
      _id
      buildingName
      updatedAt
      prefecturePath
      rooms {
        code
        roomName
        visibility
        numberOfRoom
        layout
        monthlyFee
        availability {
          now
        }
        inquiryCount
        pageViews
      }
    }
  }
}
