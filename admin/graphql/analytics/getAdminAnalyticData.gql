query getAdminAnalyticData($inhouseAgencyIds: [String], $agencyIdsWithoutFee: [String], $agencyIdsWithFee: [String], $startDate: Date!, $endDate: Date!) {
  inhouseAnalytics: analytics(filter: { agencyIds: $inhouseAgencyIds, startDate: $startDate, endDate: $endDate }) {
    uniqueInquiry
    totalInquiry
    totalPageView
    averageInquiredRentFee
    totalServiceFee
    form {
      unique
      total
    }
  }

  withoutFeeAnalytics: analytics(filter: { agencyIds: $agencyIdsWithoutFee, startDate: $startDate, endDate: $endDate }) {
    uniqueInquiry
    totalInquiry
    totalPageView
    averageInquiredRentFee
    totalServiceFee
  }

  withFeeAnalytics: analytics(filter: { agencyIds: $agencyIdsWithFee, startDate: $startDate, endDate: $endDate }) {
    uniqueInquiry
    totalInquiry
    totalPageView
    averageInquiredRentFee
    totalServiceFee
  }
}
