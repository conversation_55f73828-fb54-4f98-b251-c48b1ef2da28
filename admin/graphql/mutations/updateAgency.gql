mutation updateAgency($id: MongoID!, $record: UpdateByIdAgencyInput!) {
  updateAgency(_id: $id, record: $record) {
    record {
      _id
      createdAt
      updatedAt
      basic {
        companyName
        license {
          licenseType
          number
          issueNumber
          expiryDate {
            from
            until
          }
        }
      }
      public {
        name
        imageUrl
        address {
          postalCode
          prefecture
          city
          chome
          streetAddress
          buildingName
        }
        phone
        email
        availableLanguages
        businessHour {
          start
          end
        }
        closeDays
        website
      }
      serviceFee {
        feeType
        value
      }
    }
  }
}
