mutation createAgency($record: CreateOneAgencyInput!) {
  createAgency(record: $record) {
    record {
      _id
      createdAt
      updatedAt
      basic {
        companyName
        license {
          licenseType
          number
          issueNumber
          expiryDate {
            from
            until
          }
        }
      },
      public {
        name
        imageUrl
        address {
          postalCode
          prefecture
          city
          streetAddress
          buildingName
        },
        phone
        email
        availableLanguages
        businessHour {
          start
          end
        },
        closeDays
        website
      }
    }
  }
}
