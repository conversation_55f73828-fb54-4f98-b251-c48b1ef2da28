
declare module '*/getStationById.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const GetStationById: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getAgencyIdsBasedOnFees.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getAgencyIdsBasedOnFee: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getAdminAnalyticData.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getAdminAnalyticData: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getAnalyticData.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const analytics: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getChomes.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const chomes: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getCities.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const cities: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getPrefectures.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const prefectures: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getForm.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getFormById: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getForms.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getForms: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getInquiries.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getInquiries: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getInquiry.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getInquiry: DocumentNode;
export const InquiryPropertyFragment: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getUnReadInquiries.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getUnReadInquiries: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/changePassword.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const changePassword: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/createAgency.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const createAgency: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/createOrUpdateFeatureProperties.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const createOrUpdateFeatured: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/deleteAgency.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const deleteAgency: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/newPassword.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const newPassword: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/readInquiry.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const readInquiry: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/resetPassword.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const resetPassword: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/signIn.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const signIn: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/signup.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const signUp: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/updateAgency.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const updateAgency: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/updateUser.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const updateUser: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/baseAccess.fragment.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const BaseAccess: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/baseAddressDetail.fragment.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const BaseAddressDetail: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/cloneRoom.mutation.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const cloneRoom: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/createProperty.mutation.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const createProperty: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/deleteProperty.mutation.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const deleteProperty: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/deleteRoom.mutation.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const deleteRoom: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getLocations.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const GetLocations: DocumentNode;
export const AreaFields: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getProperties.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getProperties: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getProperty.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getProperty: DocumentNode;
export const PropertyInputFragment: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getServiceLines.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const GetServiceLines: DocumentNode;

  export default defaultDocument;
}

declare module '*/getStations.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const GetStations: DocumentNode;

  export default defaultDocument;
}

declare module '*/property.fragment.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const BasePropertyFragment: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/room.fragment.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const BaseRoom: DocumentNode;
export const Fee: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/updateProperty.mutation.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const updateProperty: DocumentNode;
export const PropertyFragment: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/updateRoomVisibility.mutation.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const updateRoomVisibility: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getAgencies.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getAgencies: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getAgency.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getAgency: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getPropertyByRoomCode.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const BaseAgency: DocumentNode;
export const getPropertyByRoomCode: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getS3UploadSignedUrl.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  

  export default defaultDocument;
}
    

declare module '*/getSettingFeaturedProperties.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getFeatured: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getUser.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const GetUser: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/rejImportResultById.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getRejImportResultById: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/rejImportResults.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getRejImportResults: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getInhouseAgencyIds.query.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getInhouseAgencyIds: DocumentNode;

  export default defaultDocument;
}


declare module '*/getAffiliates.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getAffiliates: DocumentNode;

  export default defaultDocument;
}


declare module '*/createAffiliate.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const createAffiliate: DocumentNode;

  export default defaultDocument;
}
