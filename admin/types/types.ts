export type Maybe<T> = T | null;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  /** The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */
  JSON: any;
  /** The `ID` scalar type represents a unique MongoDB identifier in collection. MongoDB by default use 12-byte ObjectId value (https://docs.mongodb.com/manual/reference/bson-types/#objectid). But MongoDB also may accepts string or integer as correct values for _id field. */
  MongoID: any;
  Date: any;
  /** The string representation of JavaScript regexp. You may provide it with flags "/^abc.*\/i" or without flags like "^abc.*". More info about RegExp characters and flags: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions */
  RegExpAsString: any;
};

export type ValidationError = ErrorInterface & {
  __typename?: 'ValidationError';
  /** Combined error message from all validators */
  message?: Maybe<Scalars['String']>;
  /** List of validator errors */
  errors?: Maybe<Array<ValidatorError>>;
};

export type ErrorInterface = {
  /** Generic error message */
  message?: Maybe<Scalars['String']>;
};

export type ValidatorError = {
  __typename?: 'ValidatorError';
  /** Validation error message */
  message?: Maybe<Scalars['String']>;
  /** Source of the validation error from the model path */
  path?: Maybe<Scalars['String']>;
  /** Field value which occurs the validation error */
  value?: Maybe<Scalars['JSON']>;
  /** Input record idx in array which occurs the validation error. This `idx` is useful for createMany operation. For singular operations it always be 0. For *Many operations `idx` represents record index in array received from user. */
  idx: Scalars['Int'];
};


export type MongoError = ErrorInterface & {
  __typename?: 'MongoError';
  /** MongoDB error message */
  message?: Maybe<Scalars['String']>;
  /** MongoDB error code */
  code?: Maybe<Scalars['Int']>;
};

export type RuntimeError = ErrorInterface & {
  __typename?: 'RuntimeError';
  /** Runtime error message */
  message?: Maybe<Scalars['String']>;
};

export type Query = {
  __typename?: 'Query';
  user: User;
  agencyById?: Maybe<Agency>;
  agencies?: Maybe<AgencyPagination>;
  propertyById?: Maybe<Property>;
  properties?: Maybe<PropertyPagination>;
  property?: Maybe<Property>;
  featuredProperties?: Maybe<Array<Maybe<Property>>>;
  tags?: Maybe<Array<Maybe<TagCount>>>;
  availableLocations?: Maybe<Array<Maybe<Area>>>;
  searchLocations?: Maybe<Array<Maybe<Area>>>;
  cityNameById?: Maybe<Scalars['String']>;
  prefectureByName?: Maybe<Prefecture>;
  prefectures: Array<Prefecture>;
  cityById?: Maybe<City>;
  cities: Array<City>;
  chomes: Array<Chome>;
  s3UploadSignedUrl?: Maybe<S3UploadSignedUrl>;
  inquiryById?: Maybe<Inquiry>;
  inquiries?: Maybe<InquiryPagination>;
  inquiryCount?: Maybe<Scalars['Int']>;
  serviceLines: Array<ServiceLine>;
  stations: Array<Station>;
  stationById?: Maybe<Station>;
  setting?: Maybe<Setting>;
  apiVersion?: Maybe<Scalars['String']>;
  analytics?: Maybe<Analytic>;
  formById?: Maybe<Form>;
  forms?: Maybe<FormPagination>;
  rejImportResults?: Maybe<RejImportResultPagination>;
  rejImportResultById?: Maybe<RejImportResult>;
};


export type QueryAgencyByIdArgs = {
  _id: Scalars['MongoID'];
};


export type QueryAgenciesArgs = {
  page?: Maybe<Scalars['Int']>;
  perPage?: Maybe<Scalars['Int']>;
  filter?: Maybe<FilterFindManyAgencyInput>;
  sort?: Maybe<SortFindManyAgencyInput>;
};


export type QueryPropertyByIdArgs = {
  _id: Scalars['MongoID'];
};


export type QueryPropertiesArgs = {
  page?: Maybe<Scalars['Int']>;
  perPage?: Maybe<Scalars['Int']>;
  filter?: Maybe<FilterFindManyPropertyInput>;
  sort?: Maybe<SortFindManyPropertyInput>;
};


export type QueryPropertyArgs = {
  filter?: Maybe<FilterFindOnePropertyInput>;
  skip?: Maybe<Scalars['Int']>;
  sort?: Maybe<SortFindOnePropertyInput>;
};


export type QueryCityNameByIdArgs = {
  id: Scalars['String'];
};


export type QueryPrefectureByNameArgs = {
  keyword: Scalars['String'];
};


export type QueryPrefecturesArgs = {
  filter?: Maybe<FilterFindManyPrefectureInput>;
  skip?: Maybe<Scalars['Int']>;
  limit?: Maybe<Scalars['Int']>;
  sort?: Maybe<SortFindManyPrefectureInput>;
};


export type QueryCityByIdArgs = {
  _id: Scalars['String'];
};


export type QueryCitiesArgs = {
  filter?: Maybe<FilterFindManyCityInput>;
  skip?: Maybe<Scalars['Int']>;
  limit?: Maybe<Scalars['Int']>;
  sort?: Maybe<SortFindManyCityInput>;
};


export type QueryChomesArgs = {
  filter?: Maybe<FilterFindManyChomeInput>;
  skip?: Maybe<Scalars['Int']>;
  limit?: Maybe<Scalars['Int']>;
  sort?: Maybe<SortFindManyChomeInput>;
};


export type QueryS3UploadSignedUrlArgs = {
  type: Scalars['String'];
  path?: Maybe<Scalars['String']>;
};


export type QueryInquiryByIdArgs = {
  _id: Scalars['MongoID'];
};


export type QueryInquiriesArgs = {
  page?: Maybe<Scalars['Int']>;
  perPage?: Maybe<Scalars['Int']>;
  filter?: Maybe<FilterFindManyInquiryInput>;
  sort?: Maybe<SortFindManyInquiryInput>;
};


export type QueryInquiryCountArgs = {
  filter?: Maybe<FilterCountInquiryInput>;
};


export type QueryServiceLinesArgs = {
  filter?: Maybe<FilterFindManyServiceLineInput>;
  skip?: Maybe<Scalars['Int']>;
  limit?: Maybe<Scalars['Int']>;
  sort?: Maybe<SortFindManyServiceLineInput>;
};


export type QueryStationsArgs = {
  filter?: Maybe<FilterFindManyStationInput>;
  skip?: Maybe<Scalars['Int']>;
  limit?: Maybe<Scalars['Int']>;
  sort?: Maybe<SortFindManyStationInput>;
};


export type QueryStationByIdArgs = {
  _id: Scalars['String'];
};


export type QuerySettingArgs = {
  filter?: Maybe<FilterFindOneSettingInput>;
  skip?: Maybe<Scalars['Int']>;
  sort?: Maybe<SortFindOneSettingInput>;
};


export type QueryAnalyticsArgs = {
  filter?: Maybe<FilterAnalyticInput>;
};


export type QueryFormByIdArgs = {
  _id: Scalars['MongoID'];
};


export type QueryFormsArgs = {
  page?: Maybe<Scalars['Int']>;
  perPage?: Maybe<Scalars['Int']>;
  filter?: Maybe<FilterFindManyFormInput>;
  sort?: Maybe<SortFindManyFormInput>;
};


export type QueryRejImportResultsArgs = {
  page?: Maybe<Scalars['Int']>;
  perPage?: Maybe<Scalars['Int']>;
  filter?: Maybe<FilterFindManyRejImportResultInput>;
  sort?: Maybe<SortFindManyRejImportResultInput>;
};


export type QueryRejImportResultByIdArgs = {
  _id: Scalars['MongoID'];
};

export type User = {
  __typename?: 'User';
  email?: Maybe<Scalars['String']>;
  firstName?: Maybe<Scalars['String']>;
  lastName?: Maybe<Scalars['String']>;
  locale?: Maybe<Scalars['String']>;
  roles?: Maybe<Array<Maybe<Scalars['String']>>>;
  agencyId?: Maybe<Scalars['String']>;
  account?: Maybe<UserAccount>;
  _id: Scalars['MongoID'];
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export type UserAccount = {
  __typename?: 'UserAccount';
  facebook?: Maybe<UserAccountFacebook>;
  verification?: Maybe<UserAccountVerification>;
};

export type UserAccountFacebook = {
  __typename?: 'UserAccountFacebook';
  userId?: Maybe<Scalars['String']>;
};

export type UserAccountVerification = {
  __typename?: 'UserAccountVerification';
  verified?: Maybe<Scalars['Boolean']>;
};



/** An entity which own and create the properties listing */
export type Agency = {
  __typename?: 'Agency';
  basic?: Maybe<AgencyBasic>;
  public?: Maybe<AgencyPublic>;
  serviceFee?: Maybe<AgencyServiceFee>;
  _id: Scalars['MongoID'];
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
  /** How many inquiries made to the agency */
  inquiryCount?: Maybe<Scalars['Int']>;
  /** How many published room under the agency */
  publishedRoomCount?: Maybe<Scalars['Int']>;
  /** How many room under the agency */
  totalRoomCount?: Maybe<Scalars['Int']>;
  /** The Localized of the property address information */
  addressDetail?: Maybe<AgencyPublicAddress>;
};

export type AgencyBasic = {
  __typename?: 'AgencyBasic';
  companyName: Scalars['String'];
  license?: Maybe<AgencyBasicLicense>;
};

export type AgencyBasicLicense = {
  __typename?: 'AgencyBasicLicense';
  haveLicense?: Maybe<Scalars['Boolean']>;
  licenseType?: Maybe<EnumAgencyBasicLicenseLicenseType>;
  number?: Maybe<Scalars['String']>;
  issueNumber?: Maybe<Scalars['String']>;
  expiryDate?: Maybe<AgencyBasicLicenseExpiryDate>;
};

export enum EnumAgencyBasicLicenseLicenseType {
  MinisterLitt = 'minister_litt',
  GovernorTokyo = 'governor_tokyo',
  Na = 'na'
}

export type AgencyBasicLicenseExpiryDate = {
  __typename?: 'AgencyBasicLicenseExpiryDate';
  from?: Maybe<Scalars['String']>;
  until?: Maybe<Scalars['String']>;
};

export type AgencyPublic = {
  __typename?: 'AgencyPublic';
  name?: Maybe<Scalars['String']>;
  imageUrl?: Maybe<Scalars['String']>;
  address?: Maybe<AgencyPublicAddress>;
  phone?: Maybe<Scalars['String']>;
  email: Scalars['String'];
  availableLanguages?: Maybe<Array<Maybe<Scalars['String']>>>;
  businessHour?: Maybe<AgencyPublicBusinessHour>;
  closeDays?: Maybe<Array<Maybe<Scalars['String']>>>;
  website?: Maybe<Scalars['String']>;
};

export type AgencyPublicAddress = {
  __typename?: 'AgencyPublicAddress';
  postalCode?: Maybe<Scalars['String']>;
  prefecture?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  chome?: Maybe<Scalars['String']>;
  streetAddress?: Maybe<Scalars['String']>;
  buildingName?: Maybe<Scalars['String']>;
};

export type AgencyPublicBusinessHour = {
  __typename?: 'AgencyPublicBusinessHour';
  start?: Maybe<Scalars['String']>;
  end?: Maybe<Scalars['String']>;
};

export type AgencyServiceFee = {
  __typename?: 'AgencyServiceFee';
  feeType?: Maybe<EnumAgencyServiceFeeFeeType>;
  value?: Maybe<Scalars['Float']>;
};

export enum EnumAgencyServiceFeeFeeType {
  Percentage = 'percentage',
  Fixed = 'fixed'
}

/** List of items with pagination. */
export type AgencyPagination = {
  __typename?: 'AgencyPagination';
  /** Total object count. */
  count?: Maybe<Scalars['Int']>;
  /** Array of objects. */
  items?: Maybe<Array<Agency>>;
  /** Information to aid in pagination. */
  pageInfo: PaginationInfo;
};

export type PaginationInfo = {
  __typename?: 'PaginationInfo';
  currentPage: Scalars['Int'];
  perPage: Scalars['Int'];
  pageCount?: Maybe<Scalars['Int']>;
  itemCount?: Maybe<Scalars['Int']>;
  hasNextPage?: Maybe<Scalars['Boolean']>;
  hasPreviousPage?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyAgencyInput = {
  basic?: Maybe<FilterFindManyAgencyBasicInput>;
  public?: Maybe<FilterFindManyAgencyPublicInput>;
  serviceFee?: Maybe<FilterFindManyAgencyServiceFeeInput>;
  /** List of *indexed* fields that can be filtered via operators. */
  _operators?: Maybe<FilterFindManyAgencyOperatorsInput>;
  OR?: Maybe<Array<FilterFindManyAgencyInput>>;
  AND?: Maybe<Array<FilterFindManyAgencyInput>>;
};

export type FilterFindManyAgencyBasicInput = {
  companyName?: Maybe<Scalars['String']>;
  license?: Maybe<FilterFindManyAgencyBasicLicenseInput>;
};

export type FilterFindManyAgencyBasicLicenseInput = {
  haveLicense?: Maybe<Scalars['Boolean']>;
  licenseType?: Maybe<EnumAgencyBasicLicenseLicenseType>;
  number?: Maybe<Scalars['String']>;
  issueNumber?: Maybe<Scalars['String']>;
  expiryDate?: Maybe<FilterFindManyAgencyBasicLicenseExpiryDateInput>;
};

export type FilterFindManyAgencyBasicLicenseExpiryDateInput = {
  from?: Maybe<Scalars['String']>;
  until?: Maybe<Scalars['String']>;
};

export type FilterFindManyAgencyPublicInput = {
  name?: Maybe<Scalars['String']>;
  imageUrl?: Maybe<Scalars['String']>;
  address?: Maybe<FilterFindManyAgencyPublicAddressInput>;
  phone?: Maybe<Scalars['String']>;
  email?: Maybe<Scalars['String']>;
  availableLanguages?: Maybe<Array<Maybe<Scalars['String']>>>;
  businessHour?: Maybe<FilterFindManyAgencyPublicBusinessHourInput>;
  closeDays?: Maybe<Array<Maybe<Scalars['String']>>>;
  website?: Maybe<Scalars['String']>;
};

export type FilterFindManyAgencyPublicAddressInput = {
  postalCode?: Maybe<Scalars['String']>;
  prefecture?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  chome?: Maybe<Scalars['String']>;
  streetAddress?: Maybe<Scalars['String']>;
  buildingName?: Maybe<Scalars['String']>;
};

export type FilterFindManyAgencyPublicBusinessHourInput = {
  start?: Maybe<Scalars['String']>;
  end?: Maybe<Scalars['String']>;
};

export type FilterFindManyAgencyServiceFeeInput = {
  feeType?: Maybe<EnumAgencyServiceFeeFeeType>;
  value?: Maybe<Scalars['Float']>;
};

/** For performance reason this type contains only *indexed* fields. */
export type FilterFindManyAgencyOperatorsInput = {
  public?: Maybe<FilterFindManyAgencyPublicOperatorsInput>;
};

export type FilterFindManyAgencyPublicOperatorsInput = {
  email?: Maybe<FilterFindManyAgencyPublicEmailOperatorsInput>;
};

export type FilterFindManyAgencyPublicEmailOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};


export enum SortFindManyAgencyInput {
  IdAsc = '_ID_ASC',
  IdDesc = '_ID_DESC',
  PublicEmailAsc = 'PUBLIC__EMAIL_ASC',
  PublicEmailDesc = 'PUBLIC__EMAIL_DESC'
}

/** The entity which can be rented */
export type Property = {
  __typename?: 'Property';
  buildingName?: Maybe<Scalars['String']>;
  agencyId: Scalars['MongoID'];
  location?: Maybe<PropertyLocation>;
  address: PropertyAddress;
  buildingType: EnumPropertyBuildingType;
  builtDate: Scalars['Date'];
  maxFloor: Scalars['Float'];
  structure: EnumPropertyStructure;
  images: PropertyImages;
  access?: Maybe<PropertyAccess>;
  rooms: Array<Maybe<PropertyRooms>>;
  highestMonthlyPrice?: Maybe<Scalars['Float']>;
  lowestMonthlyPrice?: Maybe<Scalars['Float']>;
  pageViews?: Maybe<Scalars['JSON']>;
  nearestStationDistance?: Maybe<Scalars['Float']>;
  rejMetadata?: Maybe<PropertyRejMetadata>;
  _id: Scalars['MongoID'];
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
  agency?: Maybe<Agency>;
  /** The Localized of the property address information */
  addressDetail?: Maybe<PropertyAddress>;
  /** Url friendly version of the prefecture name */
  prefecturePath?: Maybe<Scalars['String']>;
  languageComment?:Scalars['JSON'] | undefined;
};

export type PropertyLocation = {
  __typename?: 'PropertyLocation';
  position?: Maybe<PropertyLocationPosition>;
  addressLine?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  region: Scalars['String'];
  prefecture: Scalars['String'];
  cityArea: Scalars['String'];
  city: Scalars['String'];
  area: Scalars['String'];
  chome: Scalars['String'];
};

export type PropertyLocationPosition = {
  __typename?: 'PropertyLocationPosition';
  lat?: Maybe<Scalars['Float']>;
  long?: Maybe<Scalars['Float']>;
};

export type PropertyAddress = {
  __typename?: 'PropertyAddress';
  coord?: Maybe<Array<Maybe<Scalars['Float']>>>;
  line?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  prefecture: Scalars['String'];
  city: Scalars['String'];
  chome: Scalars['String'];
};

export enum EnumPropertyBuildingType {
  Apartment = 'apartment',
  House = 'house',
  ShareHouse = 'share_house',
  MonthlyApartment = 'monthly_apartment'
}

export enum EnumPropertyStructure {
  Wooden = 'wooden',
  LightSteel = 'light_steel',
  HeavySteel = 'heavy_steel',
  Steel = 'steel',
  ReinforceConcrete = 'reinforce_concrete',
  SteelFramedReinforcedConcreteBlock = 'steel_framed_reinforced_concrete_block',
  Other = 'other'
}

export type PropertyImages = {
  __typename?: 'PropertyImages';
  collections: Array<Maybe<Scalars['String']>>;
};

export type PropertyAccess = {
  __typename?: 'PropertyAccess';
  stations?: Maybe<Array<Maybe<PropertyAccessStations>>>;
};

export type PropertyAccessStations = {
  __typename?: 'PropertyAccessStations';
  lineId?: Maybe<Scalars['String']>;
  stationId?: Maybe<Scalars['String']>;
  distance?: Maybe<PropertyAccessStationsDistance>;
  /** @deprecated Use `stationName` to obtain the name of station */
  stationDetail?: Maybe<Station>;
  /** @deprecated Use `lineName` to obtain the name of line */
  lineDetail?: Maybe<ServiceLine>;
  /** Localized name of the train station */
  stationName?: Maybe<Scalars['String']>;
  /** Localized name of the train service line */
  lineName?: Maybe<Scalars['String']>;
};

export type PropertyAccessStationsDistance = {
  __typename?: 'PropertyAccessStationsDistance';
  bus?: Maybe<Scalars['Float']>;
  walk?: Maybe<Scalars['Float']>;
};

/** The station is a place where the train transit */
export type Station = {
  __typename?: 'Station';
  _id: Scalars['String'];
  name: Scalars['String'];
  /** @deprecated Will be removed in favor of `localeName` field and header `accept-language` */
  en?: Maybe<Scalars['String']>;
  zh_cn?: Maybe<Scalars['String']>;
  zh_tw?: Maybe<Scalars['String']>;
  ko_kr?: Maybe<Scalars['String']>;
  location?: Maybe<StationLocation>;
  prefId: Scalars['String'];
  lineIds?: Maybe<Array<Maybe<Scalars['String']>>>;
  localeName?: Maybe<Scalars['String']>;
  serviceLines: Array<Maybe<ServiceLine>>;
  code: Maybe<Scalars['String']>;
};

export type StationLocation = {
  __typename?: 'StationLocation';
  lat?: Maybe<Scalars['Float']>;
  long?: Maybe<Scalars['Float']>;
};

/** The train service line */
export type ServiceLine = {
  __typename?: 'ServiceLine';
  _id: Scalars['String'];
  name: Scalars['String'];
  /** @deprecated Will be removed in favor of `localeName` field and header `accept-language` */
  en?: Maybe<Scalars['String']>;
  zh_cn?: Maybe<Scalars['String']>;
  zh_tw?: Maybe<Scalars['String']>;
  ko_kr?: Maybe<Scalars['String']>;
  stationIds?: Maybe<Array<Maybe<Scalars['String']>>>;
  sort?: Maybe<Scalars['Float']>;
  localeName?: Maybe<Scalars['String']>;
  stations: Array<Maybe<Station>>;
  items:  Array<Maybe<Station>>;
  code: Maybe<Scalars['String']>;
};

export type PropertyRooms = {
  __typename?: 'PropertyRooms';
  code?: Maybe<Scalars['String']>;
  roomName?: Maybe<Scalars['String']>;
  numberOfRoom: Scalars['Float'];
  visibility: EnumPropertyRoomsVisibility;
  companyManagement?: Maybe<Scalars['String']>;
  telephone?: Maybe<Scalars['String']>;
  images: PropertyRoomsImages;
  availability: PropertyRoomsAvailability;
  layout?: Maybe<Scalars['String']>;
  contractLength: Scalars['Float'];
  transactionType?: Maybe<EnumPropertyRoomsTransactionType>;
  contractType: EnumPropertyRoomsContractType;
  size: Scalars['Float'];
  mainWindow: EnumPropertyRoomsMainWindow;
  floor: Scalars['String'];
  tags?: Maybe<Array<Maybe<Scalars['String']>>>;
  facilities?: Maybe<PropertyRoomsFacilities>;
  monthlyFee?: Maybe<Scalars['Float']>;
  roomLayout?: Maybe<Scalars['String']>;
  fees: PropertyRoomsFees;
  pageViews?: Maybe<Scalars['JSON']>;
  inquiryCount?: Maybe<Scalars['Float']>;
  rejMetadata?: Maybe<PropertyRoomsRejMetadata>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
  /** How many today page view for the room */
  todayPageView?: Maybe<Scalars['Int']>;
};

export enum EnumPropertyRoomsVisibility {
  Public = 'public',
  Private = 'private',
  Full = 'full'
}

export type PropertyRoomsImages = {
  __typename?: 'PropertyRoomsImages';
  featured: Scalars['String'];
  floorPlan: Scalars['String'];
  collections: Array<Maybe<Scalars['String']>>;
};

export type PropertyRoomsAvailability = {
  __typename?: 'PropertyRoomsAvailability';
  status: EnumPropertyRoomsAvailabilityStatus;
  now?: Maybe<Scalars['Boolean']>;
  enquire?: Maybe<Scalars['Boolean']>;
};

export enum EnumPropertyRoomsAvailabilityStatus {
  Available = 'available',
  AvailableSoon = 'available_soon',
  Renovation = 'renovation',
  UnderConstruction = 'under_construction'
}

export enum EnumPropertyRoomsTransactionType {
  Agency = 'agency',
  Owner = 'owner',
  Agent = 'agent'
}

export enum EnumPropertyRoomsContractType {
  Standard = 'standard',
  FixedTerm = 'fixed_term'
}

export enum EnumPropertyRoomsMainWindow {
  East = 'east',
  Southeast = 'southeast',
  South = 'south',
  Southwest = 'southwest',
  West = 'west',
  Northwest = 'northwest',
  Northeast = 'northeast',
  North = 'north',
  Unknown = 'unknown'
}

export type PropertyRoomsFacilities = {
  __typename?: 'PropertyRoomsFacilities';
  basic?: Maybe<Array<Maybe<Scalars['String']>>>;
  building?: Maybe<Array<Maybe<Scalars['String']>>>;
  other?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type PropertyRoomsFees = {
  __typename?: 'PropertyRoomsFees';
  monthly: PropertyRoomsFeesMonthly;
  moveIn: PropertyRoomsFeesMoveIn;
  others?: Maybe<Array<Maybe<PropertyRoomsFeesOthers>>>;
};

export type PropertyRoomsFeesMonthly = {
  __typename?: 'PropertyRoomsFeesMonthly';
  rentFee?: Maybe<Scalars['Float']>;
  maintenanceFee?: Maybe<Scalars['Float']>;
};

export type PropertyRoomsFeesMoveIn = {
  __typename?: 'PropertyRoomsFeesMoveIn';
  deposit: PropertyRoomsFeesMoveInDeposit;
  keyMoney: PropertyRoomsFeesMoveInDeposit;
  agencyFee: PropertyRoomsFeesMoveInDeposit;
  guarantorFee: PropertyRoomsFeesMoveInDeposit;
  contractRenewalFee: PropertyRoomsFeesMoveInDeposit;
};

export type PropertyRoomsFeesMoveInDeposit = {
  __typename?: 'PropertyRoomsFeesMoveInDeposit';
  feeType: EnumPropertyRoomsFeesMoveInDepositFeeType;
  value: Scalars['Float'];
};

export enum EnumPropertyRoomsFeesMoveInDepositFeeType {
  FixedPrice = 'fixed_price',
  MonthlyMultiplier = 'monthly_multiplier'
}

export type PropertyRoomsFeesOthers = {
  __typename?: 'PropertyRoomsFeesOthers';
  feeType?: Maybe<EnumPropertyRoomsFeesOthersFeeType>;
  value?: Maybe<Scalars['Float']>;
  chargeTiming?: Maybe<EnumPropertyRoomsFeesOthersChargeTiming>;
};

export enum EnumPropertyRoomsFeesOthersFeeType {
  KeyReplacement = 'key_replacement',
  Support_24Hours = 'support_24_hours',
  Cleaning = 'cleaning',
  Administrative = 'administrative',
  Disinfection = 'disinfection',
  FireInsurance = 'fire_insurance',
  Community = 'community',
  Other = 'other',
  Na = 'na'
}

export enum EnumPropertyRoomsFeesOthersChargeTiming {
  MoveOut = 'move_out',
  MoveIn = 'move_in',
  Monthly = 'monthly',
  Yearly = 'yearly',
  Na = 'na'
}

export type PropertyRoomsRejMetadata = {
  __typename?: 'PropertyRoomsRejMetadata';
  propertyId?: Maybe<Scalars['String']>;
};

export type PropertyRejMetadata = {
  __typename?: 'PropertyRejMetadata';
  buildingId?: Maybe<Scalars['String']>;
};

/** List of items with pagination. */
export type PropertyPagination = {
  __typename?: 'PropertyPagination';
  /** Total object count. */
  count?: Maybe<Scalars['Int']>;
  /** Array of objects. */
  items?: Maybe<Array<Property>>;
  /** Information to aid in pagination. */
  pageInfo: PaginationInfo;
};

export type FilterFindManyPropertyInput = {
  buildingName?: Maybe<Scalars['String']>;
  agencyId?: Maybe<Scalars['MongoID']>;
  location?: Maybe<FilterFindManyPropertyLocationInput>;
  address?: Maybe<FilterFindManyPropertyAddressInput>;
  buildingType?: Maybe<EnumPropertyBuildingType>;
  builtDate?: Maybe<Scalars['Date']>;
  maxFloor?: Maybe<Scalars['Float']>;
  structure?: Maybe<EnumPropertyStructure>;
  images?: Maybe<FilterFindManyPropertyImagesInput>;
  access?: Maybe<FilterFindManyPropertyAccessInput>;
  rooms?: Maybe<Array<Maybe<FilterFindManyPropertyRoomsInput>>>;
  highestMonthlyPrice?: Maybe<Scalars['Float']>;
  lowestMonthlyPrice?: Maybe<Scalars['Float']>;
  pageViews?: Maybe<Scalars['JSON']>;
  nearestStationDistance?: Maybe<Scalars['Float']>;
  rejMetadata?: Maybe<FilterFindManyPropertyRejMetadataInput>;
  /** List of *indexed* fields that can be filtered via operators. */
  _operators?: Maybe<FilterFindManyPropertyOperatorsInput>;
  OR?: Maybe<Array<FilterFindManyPropertyInput>>;
  AND?: Maybe<Array<FilterFindManyPropertyInput>>;
  _include_rooms_tags?: Maybe<Array<Maybe<Scalars['String']>>>;
  languageComment?: Scalars['JSON'] | undefined;
};

export type FilterFindManyPropertyLocationInput = {
  position?: Maybe<FilterFindManyPropertyLocationPositionInput>;
  addressLine?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  region?: Maybe<Scalars['String']>;
  prefecture?: Maybe<Scalars['String']>;
  cityArea?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  area?: Maybe<Scalars['String']>;
  chome?: Maybe<Scalars['String']>;
};

export type FilterFindManyPropertyLocationPositionInput = {
  lat?: Maybe<Scalars['Float']>;
  long?: Maybe<Scalars['Float']>;
};

export type FilterFindManyPropertyAddressInput = {
  coord?: Maybe<Array<Maybe<Scalars['Float']>>>;
  line?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  prefecture?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  chome?: Maybe<Scalars['String']>;
};

export type FilterFindManyPropertyImagesInput = {
  collections?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type FilterFindManyPropertyAccessInput = {
  stations?: Maybe<Array<Maybe<FilterFindManyPropertyAccessStationsInput>>>;
};

export type FilterFindManyPropertyAccessStationsInput = {
  lineId?: Maybe<Scalars['String']>;
  stationId?: Maybe<Scalars['String']>;
  distance?: Maybe<FilterFindManyPropertyAccessStationsDistanceInput>;
};

export type FilterFindManyPropertyAccessStationsDistanceInput = {
  bus?: Maybe<Scalars['Float']>;
  walk?: Maybe<Scalars['Float']>;
};

export type FilterFindManyPropertyRoomsInput = {
  code?: Maybe<Scalars['String']>;
  roomName?: Maybe<Scalars['String']>;
  numberOfRoom?: Maybe<Scalars['Float']>;
  visibility?: Maybe<EnumPropertyRoomsVisibility>;
  companyManagement?: Maybe<Scalars['String']>;
  telephone?: Maybe<Scalars['String']>;
  images?: Maybe<FilterFindManyPropertyRoomsImagesInput>;
  availability?: Maybe<FilterFindManyPropertyRoomsAvailabilityInput>;
  layout?: Maybe<Scalars['String']>;
  contractLength?: Maybe<Scalars['Float']>;
  transactionType?: Maybe<EnumPropertyRoomsTransactionType>;
  contractType?: Maybe<EnumPropertyRoomsContractType>;
  size?: Maybe<Scalars['Float']>;
  mainWindow?: Maybe<EnumPropertyRoomsMainWindow>;
  floor?: Maybe<Scalars['String']>;
  tags?: Maybe<Array<Maybe<Scalars['String']>>>;
  facilities?: Maybe<FilterFindManyPropertyRoomsFacilitiesInput>;
  monthlyFee?: Maybe<Scalars['Float']>;
  roomLayout?: Maybe<Scalars['String']>;
  fees?: Maybe<FilterFindManyPropertyRoomsFeesInput>;
  pageViews?: Maybe<Scalars['JSON']>;
  inquiryCount?: Maybe<Scalars['Float']>;
  rejMetadata?: Maybe<FilterFindManyPropertyRoomsRejMetadataInput>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export type FilterFindManyPropertyRoomsImagesInput = {
  featured?: Maybe<Scalars['String']>;
  floorPlan?: Maybe<Scalars['String']>;
  collections?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type FilterFindManyPropertyRoomsAvailabilityInput = {
  status?: Maybe<EnumPropertyRoomsAvailabilityStatus>;
  now?: Maybe<Scalars['Boolean']>;
  enquire?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFacilitiesInput = {
  basic?: Maybe<Array<Maybe<Scalars['String']>>>;
  building?: Maybe<Array<Maybe<Scalars['String']>>>;
  other?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type FilterFindManyPropertyRoomsFeesInput = {
  monthly?: Maybe<FilterFindManyPropertyRoomsFeesMonthlyInput>;
  moveIn?: Maybe<FilterFindManyPropertyRoomsFeesMoveInInput>;
  others?: Maybe<Array<Maybe<FilterFindManyPropertyRoomsFeesOthersInput>>>;
};

export type FilterFindManyPropertyRoomsFeesMonthlyInput = {
  rentFee?: Maybe<Scalars['Float']>;
  maintenanceFee?: Maybe<Scalars['Float']>;
};

export type FilterFindManyPropertyRoomsFeesMoveInInput = {
  deposit?: Maybe<FilterFindManyPropertyRoomsFeesMoveInDepositInput>;
  keyMoney?: Maybe<FilterFindManyPropertyRoomsFeesMoveInDepositInput>;
  agencyFee?: Maybe<FilterFindManyPropertyRoomsFeesMoveInDepositInput>;
  guarantorFee?: Maybe<FilterFindManyPropertyRoomsFeesMoveInDepositInput>;
  contractRenewalFee?: Maybe<FilterFindManyPropertyRoomsFeesMoveInDepositInput>;
};

export type FilterFindManyPropertyRoomsFeesMoveInDepositInput = {
  feeType: EnumPropertyRoomsFeesMoveInDepositFeeType;
  value: Scalars['Float'];
};

export type FilterFindManyPropertyRoomsFeesOthersInput = {
  feeType?: Maybe<EnumPropertyRoomsFeesOthersFeeType>;
  value?: Maybe<Scalars['Float']>;
  chargeTiming?: Maybe<EnumPropertyRoomsFeesOthersChargeTiming>;
};

export type FilterFindManyPropertyRoomsRejMetadataInput = {
  propertyId?: Maybe<Scalars['String']>;
};

export type FilterFindManyPropertyRejMetadataInput = {
  buildingId?: Maybe<Scalars['String']>;
};

/** For performance reason this type contains only *indexed* fields. */
export type FilterFindManyPropertyOperatorsInput = {
  buildingName?: Maybe<FilterFindManyPropertyBuildingNameOperatorsInput>;
  agencyId?: Maybe<FilterFindManyPropertyAgencyIdOperatorsInput>;
  location?: Maybe<FilterFindManyPropertyLocationOperatorsInput>;
  address?: Maybe<FilterFindManyPropertyAddressOperatorsInput>;
  buildingType?: Maybe<FilterFindManyPropertyBuildingTypeOperatorsInput>;
  builtDate?: Maybe<FilterFindManyPropertyBuiltDateOperatorsInput>;
  maxFloor?: Maybe<FilterFindManyPropertyMaxFloorOperatorsInput>;
  structure?: Maybe<FilterFindManyPropertyStructureOperatorsInput>;
  images?: Maybe<FilterFindManyPropertyImagesOperatorsInput>;
  access?: Maybe<FilterFindManyPropertyAccessOperatorsInput>;
  rooms?: Maybe<FilterFindManyPropertyRoomsOperatorsInput>;
  highestMonthlyPrice?: Maybe<FilterFindManyPropertyHighestMonthlyPriceOperatorsInput>;
  lowestMonthlyPrice?: Maybe<FilterFindManyPropertyLowestMonthlyPriceOperatorsInput>;
  pageViews?: Maybe<FilterFindManyPropertyPageViewsOperatorsInput>;
  nearestStationDistance?: Maybe<FilterFindManyPropertyNearestStationDistanceOperatorsInput>;
  rejMetadata?: Maybe<FilterFindManyPropertyRejMetadataOperatorsInput>;
  languageComment?: Maybe<FilterFindManyPropertyLanguageCommentOperatorsInput>;
};

export type FilterFindManyPropertyLanguageCommentOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyBuildingNameOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyAgencyIdOperatorsInput = {
  gt?: Maybe<Scalars['MongoID']>;
  gte?: Maybe<Scalars['MongoID']>;
  lt?: Maybe<Scalars['MongoID']>;
  lte?: Maybe<Scalars['MongoID']>;
  ne?: Maybe<Scalars['MongoID']>;
  in?: Maybe<Array<Maybe<Scalars['MongoID']>>>;
  nin?: Maybe<Array<Maybe<Scalars['MongoID']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyLocationOperatorsInput = {
  position?: Maybe<FilterFindManyPropertyLocationPositionOperatorsInput>;
  addressLine?: Maybe<FilterFindManyPropertyLocationAddressLineOperatorsInput>;
  postalCode?: Maybe<FilterFindManyPropertyLocationPostalCodeOperatorsInput>;
  region?: Maybe<FilterFindManyPropertyLocationRegionOperatorsInput>;
  prefecture?: Maybe<FilterFindManyPropertyLocationPrefectureOperatorsInput>;
  cityArea?: Maybe<FilterFindManyPropertyLocationCityAreaOperatorsInput>;
  city?: Maybe<FilterFindManyPropertyLocationCityOperatorsInput>;
  area?: Maybe<FilterFindManyPropertyLocationAreaOperatorsInput>;
  chome?: Maybe<FilterFindManyPropertyLocationChomeOperatorsInput>;
};

export type FilterFindManyPropertyLocationPositionOperatorsInput = {
  lat?: Maybe<FilterFindManyPropertyLocationPositionLatOperatorsInput>;
  long?: Maybe<FilterFindManyPropertyLocationPositionLongOperatorsInput>;
};

export type FilterFindManyPropertyLocationPositionLatOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyLocationPositionLongOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyLocationAddressLineOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyLocationPostalCodeOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyLocationRegionOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyLocationPrefectureOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyLocationCityAreaOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyLocationCityOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyLocationAreaOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyLocationChomeOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyAddressOperatorsInput = {
  coord?: Maybe<FilterFindManyPropertyAddressCoordOperatorsInput>;
  line?: Maybe<FilterFindManyPropertyAddressLineOperatorsInput>;
  postalCode?: Maybe<FilterFindManyPropertyAddressPostalCodeOperatorsInput>;
  prefecture?: Maybe<FilterFindManyPropertyAddressPrefectureOperatorsInput>;
  city?: Maybe<FilterFindManyPropertyAddressCityOperatorsInput>;
  chome?: Maybe<FilterFindManyPropertyAddressChomeOperatorsInput>;
};

export type FilterFindManyPropertyAddressCoordOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyAddressLineOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyAddressPostalCodeOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyAddressPrefectureOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyAddressCityOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyAddressChomeOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyBuildingTypeOperatorsInput = {
  gt?: Maybe<EnumPropertyBuildingType>;
  gte?: Maybe<EnumPropertyBuildingType>;
  lt?: Maybe<EnumPropertyBuildingType>;
  lte?: Maybe<EnumPropertyBuildingType>;
  ne?: Maybe<EnumPropertyBuildingType>;
  in?: Maybe<Array<Maybe<EnumPropertyBuildingType>>>;
  nin?: Maybe<Array<Maybe<EnumPropertyBuildingType>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyBuiltDateOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyMaxFloorOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyStructureOperatorsInput = {
  gt?: Maybe<EnumPropertyStructure>;
  gte?: Maybe<EnumPropertyStructure>;
  lt?: Maybe<EnumPropertyStructure>;
  lte?: Maybe<EnumPropertyStructure>;
  ne?: Maybe<EnumPropertyStructure>;
  in?: Maybe<Array<Maybe<EnumPropertyStructure>>>;
  nin?: Maybe<Array<Maybe<EnumPropertyStructure>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyImagesOperatorsInput = {
  collections?: Maybe<FilterFindManyPropertyImagesCollectionsOperatorsInput>;
};

export type FilterFindManyPropertyImagesCollectionsOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyAccessOperatorsInput = {
  stations?: Maybe<FilterFindManyPropertyAccessStationsOperatorsInput>;
};

export type FilterFindManyPropertyAccessStationsOperatorsInput = {
  lineId?: Maybe<FilterFindManyPropertyAccessStationsLineIdOperatorsInput>;
  stationId?: Maybe<FilterFindManyPropertyAccessStationsStationIdOperatorsInput>;
  distance?: Maybe<FilterFindManyPropertyAccessStationsDistanceOperatorsInput>;
};

export type FilterFindManyPropertyAccessStationsLineIdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyAccessStationsStationIdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyAccessStationsDistanceOperatorsInput = {
  bus?: Maybe<FilterFindManyPropertyAccessStationsDistanceBusOperatorsInput>;
  walk?: Maybe<FilterFindManyPropertyAccessStationsDistanceWalkOperatorsInput>;
};

export type FilterFindManyPropertyAccessStationsDistanceBusOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyAccessStationsDistanceWalkOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsOperatorsInput = {
  code?: Maybe<FilterFindManyPropertyRoomsCodeOperatorsInput>;
  roomName?: Maybe<FilterFindManyPropertyRoomsRoomNameOperatorsInput>;
  numberOfRoom?: Maybe<FilterFindManyPropertyRoomsNumberOfRoomOperatorsInput>;
  visibility?: Maybe<FilterFindManyPropertyRoomsVisibilityOperatorsInput>;
  companyManagement?: Maybe<FilterFindManyPropertyRoomsCompanyManagementOperatorsInput>;
  telephone?: Maybe<FilterFindManyPropertyRoomsTelephoneOperatorsInput>;
  images?: Maybe<FilterFindManyPropertyRoomsImagesOperatorsInput>;
  availability?: Maybe<FilterFindManyPropertyRoomsAvailabilityOperatorsInput>;
  layout?: Maybe<FilterFindManyPropertyRoomsLayoutOperatorsInput>;
  contractLength?: Maybe<FilterFindManyPropertyRoomsContractLengthOperatorsInput>;
  transactionType?: Maybe<FilterFindManyPropertyRoomsTransactionTypeOperatorsInput>;
  contractType?: Maybe<FilterFindManyPropertyRoomsContractTypeOperatorsInput>;
  size?: Maybe<FilterFindManyPropertyRoomsSizeOperatorsInput>;
  mainWindow?: Maybe<FilterFindManyPropertyRoomsMainWindowOperatorsInput>;
  floor?: Maybe<FilterFindManyPropertyRoomsFloorOperatorsInput>;
  tags?: Maybe<FilterFindManyPropertyRoomsTagsOperatorsInput>;
  facilities?: Maybe<FilterFindManyPropertyRoomsFacilitiesOperatorsInput>;
  monthlyFee?: Maybe<FilterFindManyPropertyRoomsMonthlyFeeOperatorsInput>;
  roomLayout?: Maybe<FilterFindManyPropertyRoomsRoomLayoutOperatorsInput>;
  fees?: Maybe<FilterFindManyPropertyRoomsFeesOperatorsInput>;
  pageViews?: Maybe<FilterFindManyPropertyRoomsPageViewsOperatorsInput>;
  inquiryCount?: Maybe<FilterFindManyPropertyRoomsInquiryCountOperatorsInput>;
  rejMetadata?: Maybe<FilterFindManyPropertyRoomsRejMetadataOperatorsInput>;
  updatedAt?: Maybe<FilterFindManyPropertyRoomsUpdatedAtOperatorsInput>;
  createdAt?: Maybe<FilterFindManyPropertyRoomsCreatedAtOperatorsInput>;
};

export type FilterFindManyPropertyRoomsCodeOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsRoomNameOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsNumberOfRoomOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsVisibilityOperatorsInput = {
  gt?: Maybe<EnumPropertyRoomsVisibility>;
  gte?: Maybe<EnumPropertyRoomsVisibility>;
  lt?: Maybe<EnumPropertyRoomsVisibility>;
  lte?: Maybe<EnumPropertyRoomsVisibility>;
  ne?: Maybe<EnumPropertyRoomsVisibility>;
  in?: Maybe<Array<Maybe<EnumPropertyRoomsVisibility>>>;
  nin?: Maybe<Array<Maybe<EnumPropertyRoomsVisibility>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsCompanyManagementOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsTelephoneOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsImagesOperatorsInput = {
  featured?: Maybe<FilterFindManyPropertyRoomsImagesFeaturedOperatorsInput>;
  floorPlan?: Maybe<FilterFindManyPropertyRoomsImagesFloorPlanOperatorsInput>;
  collections?: Maybe<FilterFindManyPropertyRoomsImagesCollectionsOperatorsInput>;
};

export type FilterFindManyPropertyRoomsImagesFeaturedOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsImagesFloorPlanOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsImagesCollectionsOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsAvailabilityOperatorsInput = {
  status?: Maybe<FilterFindManyPropertyRoomsAvailabilityStatusOperatorsInput>;
  now?: Maybe<FilterFindManyPropertyRoomsAvailabilityNowOperatorsInput>;
  enquire?: Maybe<FilterFindManyPropertyRoomsAvailabilityEnquireOperatorsInput>;
};

export type FilterFindManyPropertyRoomsAvailabilityStatusOperatorsInput = {
  gt?: Maybe<EnumPropertyRoomsAvailabilityStatus>;
  gte?: Maybe<EnumPropertyRoomsAvailabilityStatus>;
  lt?: Maybe<EnumPropertyRoomsAvailabilityStatus>;
  lte?: Maybe<EnumPropertyRoomsAvailabilityStatus>;
  ne?: Maybe<EnumPropertyRoomsAvailabilityStatus>;
  in?: Maybe<Array<Maybe<EnumPropertyRoomsAvailabilityStatus>>>;
  nin?: Maybe<Array<Maybe<EnumPropertyRoomsAvailabilityStatus>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsAvailabilityNowOperatorsInput = {
  gt?: Maybe<Scalars['Boolean']>;
  gte?: Maybe<Scalars['Boolean']>;
  lt?: Maybe<Scalars['Boolean']>;
  lte?: Maybe<Scalars['Boolean']>;
  ne?: Maybe<Scalars['Boolean']>;
  in?: Maybe<Array<Maybe<Scalars['Boolean']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Boolean']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsAvailabilityEnquireOperatorsInput = {
  gt?: Maybe<Scalars['Boolean']>;
  gte?: Maybe<Scalars['Boolean']>;
  lt?: Maybe<Scalars['Boolean']>;
  lte?: Maybe<Scalars['Boolean']>;
  ne?: Maybe<Scalars['Boolean']>;
  in?: Maybe<Array<Maybe<Scalars['Boolean']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Boolean']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsLayoutOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsContractLengthOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsTransactionTypeOperatorsInput = {
  gt?: Maybe<EnumPropertyRoomsTransactionType>;
  gte?: Maybe<EnumPropertyRoomsTransactionType>;
  lt?: Maybe<EnumPropertyRoomsTransactionType>;
  lte?: Maybe<EnumPropertyRoomsTransactionType>;
  ne?: Maybe<EnumPropertyRoomsTransactionType>;
  in?: Maybe<Array<Maybe<EnumPropertyRoomsTransactionType>>>;
  nin?: Maybe<Array<Maybe<EnumPropertyRoomsTransactionType>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsContractTypeOperatorsInput = {
  gt?: Maybe<EnumPropertyRoomsContractType>;
  gte?: Maybe<EnumPropertyRoomsContractType>;
  lt?: Maybe<EnumPropertyRoomsContractType>;
  lte?: Maybe<EnumPropertyRoomsContractType>;
  ne?: Maybe<EnumPropertyRoomsContractType>;
  in?: Maybe<Array<Maybe<EnumPropertyRoomsContractType>>>;
  nin?: Maybe<Array<Maybe<EnumPropertyRoomsContractType>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsSizeOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsMainWindowOperatorsInput = {
  gt?: Maybe<EnumPropertyRoomsMainWindow>;
  gte?: Maybe<EnumPropertyRoomsMainWindow>;
  lt?: Maybe<EnumPropertyRoomsMainWindow>;
  lte?: Maybe<EnumPropertyRoomsMainWindow>;
  ne?: Maybe<EnumPropertyRoomsMainWindow>;
  in?: Maybe<Array<Maybe<EnumPropertyRoomsMainWindow>>>;
  nin?: Maybe<Array<Maybe<EnumPropertyRoomsMainWindow>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFloorOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsTagsOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFacilitiesOperatorsInput = {
  basic?: Maybe<FilterFindManyPropertyRoomsFacilitiesBasicOperatorsInput>;
  building?: Maybe<FilterFindManyPropertyRoomsFacilitiesBuildingOperatorsInput>;
  other?: Maybe<FilterFindManyPropertyRoomsFacilitiesOtherOperatorsInput>;
};

export type FilterFindManyPropertyRoomsFacilitiesBasicOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFacilitiesBuildingOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFacilitiesOtherOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsMonthlyFeeOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsRoomLayoutOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesOperatorsInput = {
  monthly?: Maybe<FilterFindManyPropertyRoomsFeesMonthlyOperatorsInput>;
  moveIn?: Maybe<FilterFindManyPropertyRoomsFeesMoveInOperatorsInput>;
  others?: Maybe<FilterFindManyPropertyRoomsFeesOthersOperatorsInput>;
};

export type FilterFindManyPropertyRoomsFeesMonthlyOperatorsInput = {
  rentFee?: Maybe<FilterFindManyPropertyRoomsFeesMonthlyRentFeeOperatorsInput>;
  maintenanceFee?: Maybe<FilterFindManyPropertyRoomsFeesMonthlyMaintenanceFeeOperatorsInput>;
};

export type FilterFindManyPropertyRoomsFeesMonthlyRentFeeOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesMonthlyMaintenanceFeeOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesMoveInOperatorsInput = {
  deposit?: Maybe<FilterFindManyPropertyRoomsFeesMoveInDepositOperatorsInput>;
  keyMoney?: Maybe<FilterFindManyPropertyRoomsFeesMoveInKeyMoneyOperatorsInput>;
  agencyFee?: Maybe<FilterFindManyPropertyRoomsFeesMoveInAgencyFeeOperatorsInput>;
  guarantorFee?: Maybe<FilterFindManyPropertyRoomsFeesMoveInGuarantorFeeOperatorsInput>;
  contractRenewalFee?: Maybe<FilterFindManyPropertyRoomsFeesMoveInContractRenewalFeeOperatorsInput>;
};

export type FilterFindManyPropertyRoomsFeesMoveInDepositOperatorsInput = {
  feeType?: Maybe<FilterFindManyPropertyRoomsFeesMoveInDepositFeeTypeOperatorsInput>;
  value?: Maybe<FilterFindManyPropertyRoomsFeesMoveInDepositValueOperatorsInput>;
};

export type FilterFindManyPropertyRoomsFeesMoveInDepositFeeTypeOperatorsInput = {
  gt?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  gte?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  lt?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  lte?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  ne?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  in?: Maybe<Array<Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>>>;
  nin?: Maybe<Array<Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesMoveInDepositValueOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesMoveInKeyMoneyOperatorsInput = {
  feeType?: Maybe<FilterFindManyPropertyRoomsFeesMoveInKeyMoneyFeeTypeOperatorsInput>;
  value?: Maybe<FilterFindManyPropertyRoomsFeesMoveInKeyMoneyValueOperatorsInput>;
};

export type FilterFindManyPropertyRoomsFeesMoveInKeyMoneyFeeTypeOperatorsInput = {
  gt?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  gte?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  lt?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  lte?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  ne?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  in?: Maybe<Array<Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>>>;
  nin?: Maybe<Array<Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesMoveInKeyMoneyValueOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesMoveInAgencyFeeOperatorsInput = {
  feeType?: Maybe<FilterFindManyPropertyRoomsFeesMoveInAgencyFeeFeeTypeOperatorsInput>;
  value?: Maybe<FilterFindManyPropertyRoomsFeesMoveInAgencyFeeValueOperatorsInput>;
};

export type FilterFindManyPropertyRoomsFeesMoveInAgencyFeeFeeTypeOperatorsInput = {
  gt?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  gte?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  lt?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  lte?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  ne?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  in?: Maybe<Array<Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>>>;
  nin?: Maybe<Array<Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesMoveInAgencyFeeValueOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesMoveInGuarantorFeeOperatorsInput = {
  feeType?: Maybe<FilterFindManyPropertyRoomsFeesMoveInGuarantorFeeFeeTypeOperatorsInput>;
  value?: Maybe<FilterFindManyPropertyRoomsFeesMoveInGuarantorFeeValueOperatorsInput>;
};

export type FilterFindManyPropertyRoomsFeesMoveInGuarantorFeeFeeTypeOperatorsInput = {
  gt?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  gte?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  lt?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  lte?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  ne?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  in?: Maybe<Array<Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>>>;
  nin?: Maybe<Array<Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesMoveInGuarantorFeeValueOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesMoveInContractRenewalFeeOperatorsInput = {
  feeType?: Maybe<FilterFindManyPropertyRoomsFeesMoveInContractRenewalFeeFeeTypeOperatorsInput>;
  value?: Maybe<FilterFindManyPropertyRoomsFeesMoveInContractRenewalFeeValueOperatorsInput>;
};

export type FilterFindManyPropertyRoomsFeesMoveInContractRenewalFeeFeeTypeOperatorsInput = {
  gt?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  gte?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  lt?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  lte?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  ne?: Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>;
  in?: Maybe<Array<Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>>>;
  nin?: Maybe<Array<Maybe<EnumPropertyRoomsFeesMoveInDepositFeeType>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesMoveInContractRenewalFeeValueOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesOthersOperatorsInput = {
  feeType?: Maybe<FilterFindManyPropertyRoomsFeesOthersFeeTypeOperatorsInput>;
  value?: Maybe<FilterFindManyPropertyRoomsFeesOthersValueOperatorsInput>;
  chargeTiming?: Maybe<FilterFindManyPropertyRoomsFeesOthersChargeTimingOperatorsInput>;
};

export type FilterFindManyPropertyRoomsFeesOthersFeeTypeOperatorsInput = {
  gt?: Maybe<EnumPropertyRoomsFeesOthersFeeType>;
  gte?: Maybe<EnumPropertyRoomsFeesOthersFeeType>;
  lt?: Maybe<EnumPropertyRoomsFeesOthersFeeType>;
  lte?: Maybe<EnumPropertyRoomsFeesOthersFeeType>;
  ne?: Maybe<EnumPropertyRoomsFeesOthersFeeType>;
  in?: Maybe<Array<Maybe<EnumPropertyRoomsFeesOthersFeeType>>>;
  nin?: Maybe<Array<Maybe<EnumPropertyRoomsFeesOthersFeeType>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesOthersValueOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsFeesOthersChargeTimingOperatorsInput = {
  gt?: Maybe<EnumPropertyRoomsFeesOthersChargeTiming>;
  gte?: Maybe<EnumPropertyRoomsFeesOthersChargeTiming>;
  lt?: Maybe<EnumPropertyRoomsFeesOthersChargeTiming>;
  lte?: Maybe<EnumPropertyRoomsFeesOthersChargeTiming>;
  ne?: Maybe<EnumPropertyRoomsFeesOthersChargeTiming>;
  in?: Maybe<Array<Maybe<EnumPropertyRoomsFeesOthersChargeTiming>>>;
  nin?: Maybe<Array<Maybe<EnumPropertyRoomsFeesOthersChargeTiming>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsPageViewsOperatorsInput = {
  gt?: Maybe<Scalars['JSON']>;
  gte?: Maybe<Scalars['JSON']>;
  lt?: Maybe<Scalars['JSON']>;
  lte?: Maybe<Scalars['JSON']>;
  ne?: Maybe<Scalars['JSON']>;
  in?: Maybe<Array<Maybe<Scalars['JSON']>>>;
  nin?: Maybe<Array<Maybe<Scalars['JSON']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsInquiryCountOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsRejMetadataOperatorsInput = {
  propertyId?: Maybe<FilterFindManyPropertyRoomsRejMetadataPropertyIdOperatorsInput>;
};

export type FilterFindManyPropertyRoomsRejMetadataPropertyIdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsUpdatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRoomsCreatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyHighestMonthlyPriceOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyLowestMonthlyPriceOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyPageViewsOperatorsInput = {
  gt?: Maybe<Scalars['JSON']>;
  gte?: Maybe<Scalars['JSON']>;
  lt?: Maybe<Scalars['JSON']>;
  lte?: Maybe<Scalars['JSON']>;
  ne?: Maybe<Scalars['JSON']>;
  in?: Maybe<Array<Maybe<Scalars['JSON']>>>;
  nin?: Maybe<Array<Maybe<Scalars['JSON']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyNearestStationDistanceOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyPropertyRejMetadataOperatorsInput = {
  buildingId?: Maybe<FilterFindManyPropertyRejMetadataBuildingIdOperatorsInput>;
};

export type FilterFindManyPropertyRejMetadataBuildingIdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export enum SortFindManyPropertyInput {
  IdAsc = '_ID_ASC',
  IdDesc = '_ID_DESC',
  AgencyidAsc = 'AGENCYID_ASC',
  AgencyidDesc = 'AGENCYID_DESC',
  HighestmonthlypriceAsc = 'HIGHESTMONTHLYPRICE_ASC',
  HighestmonthlypriceDesc = 'HIGHESTMONTHLYPRICE_DESC',
  LowestmonthlypriceAsc = 'LOWESTMONTHLYPRICE_ASC',
  LowestmonthlypriceDesc = 'LOWESTMONTHLYPRICE_DESC',
  PageviewsAsc = 'PAGEVIEWS_ASC',
  PageviewsDesc = 'PAGEVIEWS_DESC',
  NeareststationdistanceAsc = 'NEARESTSTATIONDISTANCE_ASC',
  NeareststationdistanceDesc = 'NEARESTSTATIONDISTANCE_DESC',
  RejmetadataBuildingidAsc = 'REJMETADATA__BUILDINGID_ASC',
  RejmetadataBuildingidDesc = 'REJMETADATA__BUILDINGID_DESC',
  CreatedatAsc = 'CREATEDAT_ASC',
  CreatedatDesc = 'CREATEDAT_DESC',
  UpdatedatAsc = 'UPDATEDAT_ASC',
  UpdatedatDesc = 'UPDATEDAT_DESC',
  HighestViewWeekly = 'HIGHEST_VIEW_WEEKLY',
  InquirycountAsc = 'INQUIRYCOUNT_ASC',
  InquirycountDesc = 'INQUIRYCOUNT_DESC'
}

export type FilterFindOnePropertyInput = {
  buildingName?: Maybe<Scalars['String']>;
  agencyId?: Maybe<Scalars['MongoID']>;
  location?: Maybe<FilterFindOnePropertyLocationInput>;
  address?: Maybe<FilterFindOnePropertyAddressInput>;
  buildingType?: Maybe<EnumPropertyBuildingType>;
  builtDate?: Maybe<Scalars['Date']>;
  maxFloor?: Maybe<Scalars['Float']>;
  structure?: Maybe<EnumPropertyStructure>;
  images?: Maybe<FilterFindOnePropertyImagesInput>;
  access?: Maybe<FilterFindOnePropertyAccessInput>;
  rooms?: Maybe<Array<Maybe<FilterFindOnePropertyRoomsInput>>>;
  highestMonthlyPrice?: Maybe<Scalars['Float']>;
  lowestMonthlyPrice?: Maybe<Scalars['Float']>;
  pageViews?: Maybe<Scalars['JSON']>;
  nearestStationDistance?: Maybe<Scalars['Float']>;
  rejMetadata?: Maybe<FilterFindOnePropertyRejMetadataInput>;
  /** List of *indexed* fields that can be filtered via operators. */
  _operators?: Maybe<FilterFindOnePropertyOperatorsInput>;
  OR?: Maybe<Array<FilterFindOnePropertyInput>>;
  AND?: Maybe<Array<FilterFindOnePropertyInput>>;
  languageComment?: Scalars['JSON'] | undefined;
};

export type FilterFindOnePropertyLocationInput = {
  position?: Maybe<FilterFindOnePropertyLocationPositionInput>;
  addressLine?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  region?: Maybe<Scalars['String']>;
  prefecture?: Maybe<Scalars['String']>;
  cityArea?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  area?: Maybe<Scalars['String']>;
  chome?: Maybe<Scalars['String']>;
};

export type FilterFindOnePropertyLocationPositionInput = {
  lat?: Maybe<Scalars['Float']>;
  long?: Maybe<Scalars['Float']>;
};

export type FilterFindOnePropertyAddressInput = {
  coord?: Maybe<Array<Maybe<Scalars['Float']>>>;
  line?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  prefecture?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  chome?: Maybe<Scalars['String']>;
};

export type FilterFindOnePropertyImagesInput = {
  collections?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type FilterFindOnePropertyAccessInput = {
  stations?: Maybe<Array<Maybe<FilterFindOnePropertyAccessStationsInput>>>;
};

export type FilterFindOnePropertyAccessStationsInput = {
  lineId?: Maybe<Scalars['String']>;
  stationId?: Maybe<Scalars['String']>;
  distance?: Maybe<FilterFindOnePropertyAccessStationsDistanceInput>;
};

export type FilterFindOnePropertyAccessStationsDistanceInput = {
  bus?: Maybe<Scalars['Float']>;
  walk?: Maybe<Scalars['Float']>;
};

export type FilterFindOnePropertyRoomsInput = {
  code?: Maybe<Scalars['String']>;
  roomName?: Maybe<Scalars['String']>;
  numberOfRoom?: Maybe<Scalars['Float']>;
  visibility?: Maybe<EnumPropertyRoomsVisibility>;
  companyManagement?: Maybe<Scalars['String']>;
  telephone?: Maybe<Scalars['String']>;
  images?: Maybe<FilterFindOnePropertyRoomsImagesInput>;
  availability?: Maybe<FilterFindOnePropertyRoomsAvailabilityInput>;
  layout?: Maybe<Scalars['String']>;
  contractLength?: Maybe<Scalars['Float']>;
  transactionType?: Maybe<EnumPropertyRoomsTransactionType>;
  contractType?: Maybe<EnumPropertyRoomsContractType>;
  size?: Maybe<Scalars['Float']>;
  mainWindow?: Maybe<EnumPropertyRoomsMainWindow>;
  floor?: Maybe<Scalars['String']>;
  tags?: Maybe<Array<Maybe<Scalars['String']>>>;
  facilities?: Maybe<FilterFindOnePropertyRoomsFacilitiesInput>;
  monthlyFee?: Maybe<Scalars['Float']>;
  roomLayout?: Maybe<Scalars['String']>;
  fees?: Maybe<FilterFindOnePropertyRoomsFeesInput>;
  pageViews?: Maybe<Scalars['JSON']>;
  inquiryCount?: Maybe<Scalars['Float']>;
  rejMetadata?: Maybe<FilterFindOnePropertyRoomsRejMetadataInput>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export type FilterFindOnePropertyRoomsImagesInput = {
  featured?: Maybe<Scalars['String']>;
  floorPlan?: Maybe<Scalars['String']>;
  collections?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type FilterFindOnePropertyRoomsAvailabilityInput = {
  status?: Maybe<EnumPropertyRoomsAvailabilityStatus>;
  now?: Maybe<Scalars['Boolean']>;
  enquire?: Maybe<Scalars['Boolean']>;
};

export type FilterFindOnePropertyRoomsFacilitiesInput = {
  basic?: Maybe<Array<Maybe<Scalars['String']>>>;
  building?: Maybe<Array<Maybe<Scalars['String']>>>;
  other?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type FilterFindOnePropertyRoomsFeesInput = {
  monthly?: Maybe<FilterFindOnePropertyRoomsFeesMonthlyInput>;
  moveIn?: Maybe<FilterFindOnePropertyRoomsFeesMoveInInput>;
  others?: Maybe<Array<Maybe<FilterFindOnePropertyRoomsFeesOthersInput>>>;
};

export type FilterFindOnePropertyRoomsFeesMonthlyInput = {
  rentFee?: Maybe<Scalars['Float']>;
  maintenanceFee?: Maybe<Scalars['Float']>;
};

export type FilterFindOnePropertyRoomsFeesMoveInInput = {
  deposit?: Maybe<FilterFindOnePropertyRoomsFeesMoveInDepositInput>;
  keyMoney?: Maybe<FilterFindOnePropertyRoomsFeesMoveInDepositInput>;
  agencyFee?: Maybe<FilterFindOnePropertyRoomsFeesMoveInDepositInput>;
  guarantorFee?: Maybe<FilterFindOnePropertyRoomsFeesMoveInDepositInput>;
  contractRenewalFee?: Maybe<FilterFindOnePropertyRoomsFeesMoveInDepositInput>;
};

export type FilterFindOnePropertyRoomsFeesMoveInDepositInput = {
  feeType: EnumPropertyRoomsFeesMoveInDepositFeeType;
  value: Scalars['Float'];
};

export type FilterFindOnePropertyRoomsFeesOthersInput = {
  feeType?: Maybe<EnumPropertyRoomsFeesOthersFeeType>;
  value?: Maybe<Scalars['Float']>;
  chargeTiming?: Maybe<EnumPropertyRoomsFeesOthersChargeTiming>;
};

export type FilterFindOnePropertyRoomsRejMetadataInput = {
  propertyId?: Maybe<Scalars['String']>;
};

export type FilterFindOnePropertyRejMetadataInput = {
  buildingId?: Maybe<Scalars['String']>;
};

/** For performance reason this type contains only *indexed* fields. */
export type FilterFindOnePropertyOperatorsInput = {
  agencyId?: Maybe<FilterFindOnePropertyAgencyIdOperatorsInput>;
  rooms?: Maybe<FilterFindOnePropertyRoomsOperatorsInput>;
  highestMonthlyPrice?: Maybe<FilterFindOnePropertyHighestMonthlyPriceOperatorsInput>;
  lowestMonthlyPrice?: Maybe<FilterFindOnePropertyLowestMonthlyPriceOperatorsInput>;
  pageViews?: Maybe<FilterFindOnePropertyPageViewsOperatorsInput>;
  nearestStationDistance?: Maybe<FilterFindOnePropertyNearestStationDistanceOperatorsInput>;
  rejMetadata?: Maybe<FilterFindOnePropertyRejMetadataOperatorsInput>;
};

export type FilterFindOnePropertyAgencyIdOperatorsInput = {
  gt?: Maybe<Scalars['MongoID']>;
  gte?: Maybe<Scalars['MongoID']>;
  lt?: Maybe<Scalars['MongoID']>;
  lte?: Maybe<Scalars['MongoID']>;
  ne?: Maybe<Scalars['MongoID']>;
  in?: Maybe<Array<Maybe<Scalars['MongoID']>>>;
  nin?: Maybe<Array<Maybe<Scalars['MongoID']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindOnePropertyRoomsOperatorsInput = {
  code?: Maybe<FilterFindOnePropertyRoomsCodeOperatorsInput>;
};

export type FilterFindOnePropertyRoomsCodeOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindOnePropertyHighestMonthlyPriceOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindOnePropertyLowestMonthlyPriceOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindOnePropertyPageViewsOperatorsInput = {
  gt?: Maybe<Scalars['JSON']>;
  gte?: Maybe<Scalars['JSON']>;
  lt?: Maybe<Scalars['JSON']>;
  lte?: Maybe<Scalars['JSON']>;
  ne?: Maybe<Scalars['JSON']>;
  in?: Maybe<Array<Maybe<Scalars['JSON']>>>;
  nin?: Maybe<Array<Maybe<Scalars['JSON']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindOnePropertyNearestStationDistanceOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindOnePropertyRejMetadataOperatorsInput = {
  buildingId?: Maybe<FilterFindOnePropertyRejMetadataBuildingIdOperatorsInput>;
};

export type FilterFindOnePropertyRejMetadataBuildingIdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export enum SortFindOnePropertyInput {
  IdAsc = '_ID_ASC',
  IdDesc = '_ID_DESC',
  AgencyidAsc = 'AGENCYID_ASC',
  AgencyidDesc = 'AGENCYID_DESC',
  HighestmonthlypriceAsc = 'HIGHESTMONTHLYPRICE_ASC',
  HighestmonthlypriceDesc = 'HIGHESTMONTHLYPRICE_DESC',
  LowestmonthlypriceAsc = 'LOWESTMONTHLYPRICE_ASC',
  LowestmonthlypriceDesc = 'LOWESTMONTHLYPRICE_DESC',
  PageviewsAsc = 'PAGEVIEWS_ASC',
  PageviewsDesc = 'PAGEVIEWS_DESC',
  NeareststationdistanceAsc = 'NEARESTSTATIONDISTANCE_ASC',
  NeareststationdistanceDesc = 'NEARESTSTATIONDISTANCE_DESC',
  RejmetadataBuildingidAsc = 'REJMETADATA__BUILDINGID_ASC',
  RejmetadataBuildingidDesc = 'REJMETADATA__BUILDINGID_DESC',
  CreatedatAsc = 'CREATEDAT_ASC',
  CreatedatDesc = 'CREATEDAT_DESC',
  UpdatedatAsc = 'UPDATEDAT_ASC',
  UpdatedatDesc = 'UPDATEDAT_DESC'
}

export type TagCount = {
  __typename?: 'TagCount';
  id: Scalars['String'];
  count: Scalars['Int'];
};

/** Structured location information for a property */
export type Area = {
  __typename?: 'Area';
  _id: Scalars['String'];
  name: Scalars['String'];
  /** @deprecated Will be removed in favor of `name` field and header `accept-language` */
  en?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
  items?: Maybe<Array<Maybe<Area>>>;
  /** Determine the total number of property on the area */
  propertyCounts?: Maybe<Scalars['Int']>;
};

/** First level juridiction of Japan */
export type Prefecture = {
  __typename?: 'Prefecture';
  _id: Scalars['String'];
  jp: Scalars['String'];
  kana?: Maybe<Scalars['String']>;
  en?: Maybe<Scalars['String']>;
  zh_cn?: Maybe<Scalars['String']>;
  zh_tw?: Maybe<Scalars['String']>;
  ko_kr?: Maybe<Scalars['String']>;
  /** Determine the total number of property on the area */
  propertyCounts?: Maybe<Scalars['Int']>;
};

export type FilterFindManyPrefectureInput = {
  _id?: Maybe<Scalars['String']>;
  jp?: Maybe<Scalars['String']>;
  kana?: Maybe<Scalars['String']>;
  en?: Maybe<Scalars['String']>;
  zh_cn?: Maybe<Scalars['String']>;
  zh_tw?: Maybe<Scalars['String']>;
  ko_kr?: Maybe<Scalars['String']>;
  /** List of *indexed* fields that can be filtered via operators. */
  _operators?: Maybe<FilterFindManyPrefectureOperatorsInput>;
  OR?: Maybe<Array<FilterFindManyPrefectureInput>>;
  AND?: Maybe<Array<FilterFindManyPrefectureInput>>;
};

/** For performance reason this type contains only *indexed* fields. */
export type FilterFindManyPrefectureOperatorsInput = {
  _id?: Maybe<FilterFindManyPrefecture_IdOperatorsInput>;
};

export type FilterFindManyPrefecture_IdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export enum SortFindManyPrefectureInput {
  IdAsc = '_ID_ASC',
  IdDesc = '_ID_DESC'
}

/** Second level juridiction of Japan */
export type City = {
  __typename?: 'City';
  _id: Scalars['String'];
  jp: Scalars['String'];
  kana?: Maybe<Scalars['String']>;
  en?: Maybe<Scalars['String']>;
  zh_cn?: Maybe<Scalars['String']>;
  zh_tw?: Maybe<Scalars['String']>;
  ko_kr?: Maybe<Scalars['String']>;
  prefId?: Maybe<Scalars['String']>;
  /** Determine the total number of property on the area */
  propertyCounts?: Maybe<Scalars['Int']>;
};

export type FilterFindManyCityInput = {
  _id?: Maybe<Scalars['String']>;
  jp?: Maybe<Scalars['String']>;
  kana?: Maybe<Scalars['String']>;
  en?: Maybe<Scalars['String']>;
  zh_cn?: Maybe<Scalars['String']>;
  zh_tw?: Maybe<Scalars['String']>;
  ko_kr?: Maybe<Scalars['String']>;
  prefId?: Maybe<Scalars['String']>;
  /** List of *indexed* fields that can be filtered via operators. */
  _operators?: Maybe<FilterFindManyCityOperatorsInput>;
  OR?: Maybe<Array<FilterFindManyCityInput>>;
  AND?: Maybe<Array<FilterFindManyCityInput>>;
};

/** For performance reason this type contains only *indexed* fields. */
export type FilterFindManyCityOperatorsInput = {
  _id?: Maybe<FilterFindManyCity_IdOperatorsInput>;
};

export type FilterFindManyCity_IdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export enum SortFindManyCityInput {
  IdAsc = '_ID_ASC',
  IdDesc = '_ID_DESC'
}

/** Last level juridiction of Japan */
export type Chome = {
  __typename?: 'Chome';
  _id: Scalars['String'];
  jp: Scalars['String'];
  kana?: Maybe<Scalars['String']>;
  en?: Maybe<Scalars['String']>;
  zh_cn?: Maybe<Scalars['String']>;
  zh_tw?: Maybe<Scalars['String']>;
  ko_kr?: Maybe<Scalars['String']>;
  prefId?: Maybe<Scalars['String']>;
  cityId?: Maybe<Scalars['String']>;
  extraName?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  coord?: Maybe<Array<Maybe<Scalars['Float']>>>;
};

export type FilterFindManyChomeInput = {
  _id?: Maybe<Scalars['String']>;
  jp?: Maybe<Scalars['String']>;
  kana?: Maybe<Scalars['String']>;
  en?: Maybe<Scalars['String']>;
  zh_cn?: Maybe<Scalars['String']>;
  zh_tw?: Maybe<Scalars['String']>;
  ko_kr?: Maybe<Scalars['String']>;
  prefId?: Maybe<Scalars['String']>;
  cityId?: Maybe<Scalars['String']>;
  extraName?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  coord?: Maybe<Array<Maybe<Scalars['Float']>>>;
  /** List of *indexed* fields that can be filtered via operators. */
  _operators?: Maybe<FilterFindManyChomeOperatorsInput>;
  OR?: Maybe<Array<FilterFindManyChomeInput>>;
  AND?: Maybe<Array<FilterFindManyChomeInput>>;
};

/** For performance reason this type contains only *indexed* fields. */
export type FilterFindManyChomeOperatorsInput = {
  _id?: Maybe<FilterFindManyChome_IdOperatorsInput>;
};

export type FilterFindManyChome_IdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export enum SortFindManyChomeInput {
  IdAsc = '_ID_ASC',
  IdDesc = '_ID_DESC'
}

export type S3UploadSignedUrl = {
  __typename?: 'S3UploadSignedUrl';
  url?: Maybe<Scalars['String']>;
};

/** The entity which can be rented */
export type Inquiry = {
  __typename?: 'Inquiry';
  name: Scalars['String'];
  phone?: Maybe<Scalars['String']>;
  email: Scalars['String'];
  locale: EnumInquiryLocale;
  memo?: Maybe<Scalars['String']>;
  preferredLanguage: Array<Maybe<EnumInquiryPreferredLanguage>>;
  whenToMoveIn: Maybe<EnumFormFormRentWhenToMoveIn>;
  purpose: Array<Maybe<EnumInquiryPurpose>>;
  unread?: Maybe<Scalars['Boolean']>;
  agencyId: Scalars['String'];
  propertyId: Scalars['String'];
  roomCode: Scalars['String'];
  utm?: Maybe<InquiryUtm>;
  monthlyFee: Scalars['Float'];
  serviceFee: Scalars['Float'];
  _id: Scalars['MongoID'];
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
  property?: Maybe<Property>;
};

export enum EnumInquiryLocale {
  EnUs = 'en_US',
  ZhCn = 'zh_CN',
  ZhTw = 'zh_TW',
  KoKr = 'ko_KR',
  JpJp = 'jp_JP',
  EsEs = 'es_ES',
  FrFr = 'fr_FR',
}

export enum EnumInquiryPreferredLanguage {
  En = 'en',
  Jp = 'jp',
  Zh = 'zh',
  Kr = 'kr',
  Es = 'es',
  Fr = 'fr'
}

export enum EnumInquiryPurpose {
  CheckAvailability = 'check_availability',
  RequestTour = 'request_tour',
  VisitOffice = 'visit_office',
  CheckOtherProperty = 'check_other_property',
  Other = 'other'
}

export type InquiryUtm = {
  __typename?: 'InquiryUtm';
  utm_source?: Maybe<Scalars['String']>;
  utm_medium?: Maybe<Scalars['String']>;
  utm_campaign?: Maybe<Scalars['String']>;
  utm_term?: Maybe<Scalars['String']>;
  utm_content?: Maybe<Scalars['String']>;
  gclid?: Maybe<Scalars['String']>;
  fbclid?: Maybe<Scalars['String']>;
};

/** List of items with pagination. */
export type InquiryPagination = {
  __typename?: 'InquiryPagination';
  /** Total object count. */
  count?: Maybe<Scalars['Int']>;
  /** Array of objects. */
  items?: Maybe<Array<Inquiry>>;
  /** Information to aid in pagination. */
  pageInfo: PaginationInfo;
};

export type FilterFindManyInquiryInput = {
  name?: Maybe<Scalars['String']>;
  phone?: Maybe<Scalars['String']>;
  email?: Maybe<Scalars['String']>;
  locale?: Maybe<EnumInquiryLocale>;
  memo?: Maybe<Scalars['String']>;
  preferredLanguage?: Maybe<Array<Maybe<EnumInquiryPreferredLanguage>>>;
  whenToMoveIn?: Maybe<EnumFormFormRentWhenToMoveIn>;
  purpose?: Maybe<Array<Maybe<EnumInquiryPurpose>>>;
  unread?: Maybe<Scalars['Boolean']>;
  agencyId?: Maybe<Scalars['String']>;
  propertyId?: Maybe<Scalars['String']>;
  roomCode?: Maybe<Scalars['String']>;
  utm?: Maybe<FilterFindManyInquiryUtmInput>;
  monthlyFee?: Maybe<Scalars['Float']>;
  serviceFee?: Maybe<Scalars['Float']>;
  _id?: Maybe<Scalars['MongoID']>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
  /** List of *indexed* fields that can be filtered via operators. */
  _operators?: Maybe<FilterFindManyInquiryOperatorsInput>;
  OR?: Maybe<Array<FilterFindManyInquiryInput>>;
  AND?: Maybe<Array<FilterFindManyInquiryInput>>;
};

export type FilterFindManyInquiryUtmInput = {
  utm_source?: Maybe<Scalars['String']>;
  utm_medium?: Maybe<Scalars['String']>;
  utm_campaign?: Maybe<Scalars['String']>;
  utm_term?: Maybe<Scalars['String']>;
  utm_content?: Maybe<Scalars['String']>;
  gclid?: Maybe<Scalars['String']>;
  fbclid?: Maybe<Scalars['String']>;
};

/** For performance reason this type contains only *indexed* fields. */
export type FilterFindManyInquiryOperatorsInput = {
  name?: Maybe<FilterFindManyInquiryNameOperatorsInput>;
  phone?: Maybe<FilterFindManyInquiryPhoneOperatorsInput>;
  email?: Maybe<FilterFindManyInquiryEmailOperatorsInput>;
  locale?: Maybe<FilterFindManyInquiryLocaleOperatorsInput>;
  memo?: Maybe<FilterFindManyInquiryMemoOperatorsInput>;
  preferredLanguage?: Maybe<FilterFindManyInquiryPreferredLanguageOperatorsInput>;
  whenToMoveIn: Maybe<FilterFindManyFormFormRentWhenToMoveInOperatorsInput>;
  purpose?: Maybe<FilterFindManyInquiryPurposeOperatorsInput>;
  unread?: Maybe<FilterFindManyInquiryUnreadOperatorsInput>;
  agencyId?: Maybe<FilterFindManyInquiryAgencyIdOperatorsInput>;
  propertyId?: Maybe<FilterFindManyInquiryPropertyIdOperatorsInput>;
  roomCode?: Maybe<FilterFindManyInquiryRoomCodeOperatorsInput>;
  utm?: Maybe<FilterFindManyInquiryUtmOperatorsInput>;
  monthlyFee?: Maybe<FilterFindManyInquiryMonthlyFeeOperatorsInput>;
  serviceFee?: Maybe<FilterFindManyInquiryServiceFeeOperatorsInput>;
  _id?: Maybe<FilterFindManyInquiry_IdOperatorsInput>;
  updatedAt?: Maybe<FilterFindManyInquiryUpdatedAtOperatorsInput>;
  createdAt?: Maybe<FilterFindManyInquiryCreatedAtOperatorsInput>;
};

export type FilterFindManyInquiryNameOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryPhoneOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryEmailOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryLocaleOperatorsInput = {
  gt?: Maybe<EnumInquiryLocale>;
  gte?: Maybe<EnumInquiryLocale>;
  lt?: Maybe<EnumInquiryLocale>;
  lte?: Maybe<EnumInquiryLocale>;
  ne?: Maybe<EnumInquiryLocale>;
  in?: Maybe<Array<Maybe<EnumInquiryLocale>>>;
  nin?: Maybe<Array<Maybe<EnumInquiryLocale>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryMemoOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryPreferredLanguageOperatorsInput = {
  gt?: Maybe<EnumInquiryPreferredLanguage>;
  gte?: Maybe<EnumInquiryPreferredLanguage>;
  lt?: Maybe<EnumInquiryPreferredLanguage>;
  lte?: Maybe<EnumInquiryPreferredLanguage>;
  ne?: Maybe<EnumInquiryPreferredLanguage>;
  in?: Maybe<Array<Maybe<EnumInquiryPreferredLanguage>>>;
  nin?: Maybe<Array<Maybe<EnumInquiryPreferredLanguage>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryPurposeOperatorsInput = {
  gt?: Maybe<EnumInquiryPurpose>;
  gte?: Maybe<EnumInquiryPurpose>;
  lt?: Maybe<EnumInquiryPurpose>;
  lte?: Maybe<EnumInquiryPurpose>;
  ne?: Maybe<EnumInquiryPurpose>;
  in?: Maybe<Array<Maybe<EnumInquiryPurpose>>>;
  nin?: Maybe<Array<Maybe<EnumInquiryPurpose>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryUnreadOperatorsInput = {
  gt?: Maybe<Scalars['Boolean']>;
  gte?: Maybe<Scalars['Boolean']>;
  lt?: Maybe<Scalars['Boolean']>;
  lte?: Maybe<Scalars['Boolean']>;
  ne?: Maybe<Scalars['Boolean']>;
  in?: Maybe<Array<Maybe<Scalars['Boolean']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Boolean']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryAgencyIdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryPropertyIdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryRoomCodeOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryUtmOperatorsInput = {
  utm_source?: Maybe<FilterFindManyInquiryUtmUtm_SourceOperatorsInput>;
  utm_medium?: Maybe<FilterFindManyInquiryUtmUtm_MediumOperatorsInput>;
  utm_campaign?: Maybe<FilterFindManyInquiryUtmUtm_CampaignOperatorsInput>;
  utm_term?: Maybe<FilterFindManyInquiryUtmUtm_TermOperatorsInput>;
  utm_content?: Maybe<FilterFindManyInquiryUtmUtm_ContentOperatorsInput>;
  gclid?: Maybe<FilterFindManyInquiryUtmGclidOperatorsInput>;
  fbclid?: Maybe<FilterFindManyInquiryUtmFbclidOperatorsInput>;
};

export type FilterFindManyInquiryUtmUtm_SourceOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryUtmUtm_MediumOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryUtmUtm_CampaignOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryUtmUtm_TermOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryUtmUtm_ContentOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryUtmGclidOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryUtmFbclidOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryMonthlyFeeOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryServiceFeeOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiry_IdOperatorsInput = {
  gt?: Maybe<Scalars['MongoID']>;
  gte?: Maybe<Scalars['MongoID']>;
  lt?: Maybe<Scalars['MongoID']>;
  lte?: Maybe<Scalars['MongoID']>;
  ne?: Maybe<Scalars['MongoID']>;
  in?: Maybe<Array<Maybe<Scalars['MongoID']>>>;
  nin?: Maybe<Array<Maybe<Scalars['MongoID']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryUpdatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyInquiryCreatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export enum SortFindManyInquiryInput {
  IdAsc = '_ID_ASC',
  IdDesc = '_ID_DESC',
  UnreadAsc = 'UNREAD_ASC',
  UnreadDesc = 'UNREAD_DESC',
  AgencyidAsc = 'AGENCYID_ASC',
  AgencyidDesc = 'AGENCYID_DESC',
  RoomcodeAsc = 'ROOMCODE_ASC',
  RoomcodeDesc = 'ROOMCODE_DESC',
  MonthlyfeeAsc = 'MONTHLYFEE_ASC',
  MonthlyfeeDesc = 'MONTHLYFEE_DESC',
  ServicefeeAsc = 'SERVICEFEE_ASC',
  ServicefeeDesc = 'SERVICEFEE_DESC',
  CreatedatAsc = 'CREATEDAT_ASC',
  CreatedatDesc = 'CREATEDAT_DESC',
  UpdatedatAsc = 'UPDATEDAT_ASC',
  UpdatedatDesc = 'UPDATEDAT_DESC'
}

export type FilterCountInquiryInput = {
  name?: Maybe<Scalars['String']>;
  phone?: Maybe<Scalars['String']>;
  email?: Maybe<Scalars['String']>;
  locale?: Maybe<EnumInquiryLocale>;
  memo?: Maybe<Scalars['String']>;
  preferredLanguage?: Maybe<Array<Maybe<EnumInquiryPreferredLanguage>>>;
  purpose?: Maybe<Array<Maybe<EnumInquiryPurpose>>>;
  whenToMoveIn: Maybe<EnumFormFormRentWhenToMoveIn>;
  unread?: Maybe<Scalars['Boolean']>;
  agencyId?: Maybe<Scalars['String']>;
  propertyId?: Maybe<Scalars['String']>;
  roomCode?: Maybe<Scalars['String']>;
  utm?: Maybe<FilterCountInquiryUtmInput>;
  monthlyFee?: Maybe<Scalars['Float']>;
  serviceFee?: Maybe<Scalars['Float']>;
  _id?: Maybe<Scalars['MongoID']>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
  /** List of *indexed* fields that can be filtered via operators. */
  _operators?: Maybe<FilterCountInquiryOperatorsInput>;
  OR?: Maybe<Array<FilterCountInquiryInput>>;
  AND?: Maybe<Array<FilterCountInquiryInput>>;
};

export type FilterCountInquiryUtmInput = {
  utm_source?: Maybe<Scalars['String']>;
  utm_medium?: Maybe<Scalars['String']>;
  utm_campaign?: Maybe<Scalars['String']>;
  utm_term?: Maybe<Scalars['String']>;
  utm_content?: Maybe<Scalars['String']>;
  gclid?: Maybe<Scalars['String']>;
  fbclid?: Maybe<Scalars['String']>;
};

/** For performance reason this type contains only *indexed* fields. */
export type FilterCountInquiryOperatorsInput = {
  unread?: Maybe<FilterCountInquiryUnreadOperatorsInput>;
  agencyId?: Maybe<FilterCountInquiryAgencyIdOperatorsInput>;
  roomCode?: Maybe<FilterCountInquiryRoomCodeOperatorsInput>;
  monthlyFee?: Maybe<FilterCountInquiryMonthlyFeeOperatorsInput>;
  serviceFee?: Maybe<FilterCountInquiryServiceFeeOperatorsInput>;
  _id?: Maybe<FilterCountInquiry_IdOperatorsInput>;
  updatedAt?: Maybe<FilterCountInquiryUpdatedAtOperatorsInput>;
  createdAt?: Maybe<FilterCountInquiryCreatedAtOperatorsInput>;
};

export type FilterCountInquiryUnreadOperatorsInput = {
  gt?: Maybe<Scalars['Boolean']>;
  gte?: Maybe<Scalars['Boolean']>;
  lt?: Maybe<Scalars['Boolean']>;
  lte?: Maybe<Scalars['Boolean']>;
  ne?: Maybe<Scalars['Boolean']>;
  in?: Maybe<Array<Maybe<Scalars['Boolean']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Boolean']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterCountInquiryAgencyIdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterCountInquiryRoomCodeOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterCountInquiryMonthlyFeeOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterCountInquiryServiceFeeOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterCountInquiry_IdOperatorsInput = {
  gt?: Maybe<Scalars['MongoID']>;
  gte?: Maybe<Scalars['MongoID']>;
  lt?: Maybe<Scalars['MongoID']>;
  lte?: Maybe<Scalars['MongoID']>;
  ne?: Maybe<Scalars['MongoID']>;
  in?: Maybe<Array<Maybe<Scalars['MongoID']>>>;
  nin?: Maybe<Array<Maybe<Scalars['MongoID']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterCountInquiryUpdatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterCountInquiryCreatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyServiceLineInput = {
  _id?: Maybe<Scalars['String']>;
  name?: Maybe<Scalars['String']>;
  en?: Maybe<Scalars['String']>;
  zh_cn?: Maybe<Scalars['String']>;
  zh_tw?: Maybe<Scalars['String']>;
  ko_kr?: Maybe<Scalars['String']>;
  stationIds?: Maybe<Array<Maybe<Scalars['String']>>>;
  sort?: Maybe<Scalars['Float']>;
  /** List of *indexed* fields that can be filtered via operators. */
  _operators?: Maybe<FilterFindManyServiceLineOperatorsInput>;
  OR?: Maybe<Array<FilterFindManyServiceLineInput>>;
  AND?: Maybe<Array<FilterFindManyServiceLineInput>>;
};

/** For performance reason this type contains only *indexed* fields. */
export type FilterFindManyServiceLineOperatorsInput = {
  _id?: Maybe<FilterFindManyServiceLine_IdOperatorsInput>;
  sort?: Maybe<FilterFindManyServiceLineSortOperatorsInput>;
};

export type FilterFindManyServiceLine_IdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyServiceLineSortOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export enum SortFindManyServiceLineInput {
  IdAsc = '_ID_ASC',
  IdDesc = '_ID_DESC',
  SortAsc = 'SORT_ASC',
  SortDesc = 'SORT_DESC'
}

export type FilterFindManyStationInput = {
  _id?: Maybe<Scalars['String']>;
  name?: Maybe<Scalars['String']>;
  en?: Maybe<Scalars['String']>;
  zh_cn?: Maybe<Scalars['String']>;
  zh_tw?: Maybe<Scalars['String']>;
  ko_kr?: Maybe<Scalars['String']>;
  location?: Maybe<FilterFindManyStationLocationInput>;
  prefId?: Maybe<Scalars['String']>;
  lineIds?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** List of *indexed* fields that can be filtered via operators. */
  _operators?: Maybe<FilterFindManyStationOperatorsInput>;
  OR?: Maybe<Array<FilterFindManyStationInput>>;
  AND?: Maybe<Array<FilterFindManyStationInput>>;
};

export type FilterFindManyStationLocationInput = {
  lat?: Maybe<Scalars['Float']>;
  long?: Maybe<Scalars['Float']>;
};

/** For performance reason this type contains only *indexed* fields. */
export type FilterFindManyStationOperatorsInput = {
  _id?: Maybe<FilterFindManyStation_IdOperatorsInput>;
  prefId?: Maybe<FilterFindManyStationPrefIdOperatorsInput>;
};

export type FilterFindManyStation_IdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyStationPrefIdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export enum SortFindManyStationInput {
  IdAsc = '_ID_ASC',
  IdDesc = '_ID_DESC',
  PrefidAsc = 'PREFID_ASC',
  PrefidDesc = 'PREFID_DESC'
}

/** Set the featured properties */
export type Setting = {
  __typename?: 'Setting';
  featured?: Maybe<Array<Maybe<Scalars['String']>>>;
  inHouseAgencyIds?: Maybe<Array<Maybe<Scalars['String']>>>;
  leopalaceAgencyId?: Maybe<Scalars['String']>;
  _id: Scalars['MongoID'];
};

export type FilterFindOneSettingInput = {
  featured?: Maybe<Array<Maybe<Scalars['String']>>>;
  inHouseAgencyIds?: Maybe<Array<Maybe<Scalars['String']>>>;
  leopalaceAgencyId?: Maybe<Scalars['String']>;
  _id?: Maybe<Scalars['MongoID']>;
  /** List of *indexed* fields that can be filtered via operators. */
  _operators?: Maybe<FilterFindOneSettingOperatorsInput>;
  OR?: Maybe<Array<FilterFindOneSettingInput>>;
  AND?: Maybe<Array<FilterFindOneSettingInput>>;
};

/** For performance reason this type contains only *indexed* fields. */
export type FilterFindOneSettingOperatorsInput = {
  _id?: Maybe<FilterFindOneSetting_IdOperatorsInput>;
};

export type FilterFindOneSetting_IdOperatorsInput = {
  gt?: Maybe<Scalars['MongoID']>;
  gte?: Maybe<Scalars['MongoID']>;
  lt?: Maybe<Scalars['MongoID']>;
  lte?: Maybe<Scalars['MongoID']>;
  ne?: Maybe<Scalars['MongoID']>;
  in?: Maybe<Array<Maybe<Scalars['MongoID']>>>;
  nin?: Maybe<Array<Maybe<Scalars['MongoID']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export enum SortFindOneSettingInput {
  IdAsc = '_ID_ASC',
  IdDesc = '_ID_DESC'
}

export type Analytic = {
  __typename?: 'Analytic';
  uniqueInquiry?: Maybe<Scalars['Int']>;
  totalInquiry?: Maybe<Scalars['Int']>;
  totalPageView?: Maybe<Scalars['Int']>;
  totalServiceFee?: Maybe<Scalars['Int']>;
  averageInquiredRentFee?: Maybe<Scalars['Int']>;
  form?: Maybe<AnalyticForm>;
};

export type AnalyticForm = {
  __typename?: 'AnalyticForm';
  unique?: Maybe<Scalars['Int']>;
  total?: Maybe<Scalars['Int']>;
};

export type FilterAnalyticInput = {
  startDate: Scalars['Date'];
  endDate: Scalars['Date'];
  agencyId?: Maybe<Scalars['String']>;
  agencyIds?: Maybe<Array<Maybe<Scalars['String']>>>;
};

/** The entity which can be rented */
export type Form = {
  __typename?: 'Form';
  firstName: Scalars['String'];
  lastName?: Maybe<Scalars['String']>;
  email: Scalars['String'];
  phone?: Maybe<Scalars['String']>;
  locale: EnumFormLocale;
  preferredLanguage: Array<Maybe<EnumFormPreferredLanguage>>;
  whenToMoveIn?: Maybe<EnumFormFormRentWhenToMoveIn>;
  memo?: Maybe<Scalars['String']>;
  formType: EnumFormFormType;
  formRent?: Maybe<FormFormRent>;
  formBuy?: Maybe<FormFormBuy>;
  formSell?: Maybe<FormFormSell>;
  formTour?: Maybe<FormFormTour>;
  utm?: Maybe<FormUtm>;
  _id: Scalars['MongoID'];
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export enum EnumFormLocale {
  EnUs = 'en_US',
  ZhCn = 'zh_CN',
  ZhTw = 'zh_TW',
  KoKr = 'ko_KR',
  JpJp = 'jp_JP'
}

export enum EnumFormPreferredLanguage {
  En = 'en',
  Jp = 'jp',
  Zh = 'zh',
  Kr = 'kr'
}

export enum EnumFormFormType {
  Rent = 'rent',
  Buy = 'buy',
  Sell = 'sell',
  Tour = 'tour'
}

export type FormFormRent = {
  __typename?: 'FormFormRent';
  occupation: EnumFormFormRentOccupation;
  visaStatus?: Maybe<EnumFormFormRentVisaStatus>;
  rentBudget?: Maybe<Scalars['String']>;
  floorPlan: Scalars['String'];
  size: Scalars['Float'];
  desiredLocation: Scalars['String'];
  stationDistance: Scalars['Float'];
  whenToMoveIn: EnumFormFormRentWhenToMoveIn;
  otherPreferences?: Maybe<Array<Maybe<EnumFormFormRentOtherPreferences>>>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export enum EnumFormFormRentOccupation {
  CompanyEmployee = 'company_employee',
  PartTimer = 'part_timer',
  Student = 'student',
  Unemployed = 'unemployed',
  Other = 'other'
}

export enum EnumFormFormRentVisaStatus {
  Student = 'student',
  WorkVisa = 'work_visa',
  LongTermResident = 'long_term_resident',
  PermanentResident = 'permanent_resident',
  JapaneseNationality = 'japanese_nationality',
  Other = 'other'
}

export enum EnumFormFormRentWhenToMoveIn {
  Asap = 'asap',
  In_1Or_2Weeks = 'in_1_or_2_weeks',
  InAMonth = 'in_a_month',
  In_2Months = 'in_2_months',
  OnceFindOne = 'once_find_one',
  Other = 'other'
}

export enum EnumFormFormRentOtherPreferences {
  Not_1stFloor = 'not_1st_floor',
  SeparateBathToilet = 'separate_bath_toilet',
  AutoLockEntrance = 'auto_lock_entrance',
  WashStand = 'wash_stand',
  BicyclePark = 'bicycle_park',
  PetFriendly = 'pet_friendly',
  PackageDeliveryBox = 'package_delivery_box',
  Elevator = 'elevator',
  Balcony = 'balcony',
  NoKeyMoney = 'no_key_money',
  NoDeposit = 'no_deposit',
  Other = 'other'
}

export type FormFormBuy = {
  __typename?: 'FormFormBuy';
  purpose: EnumFormFormBuyPurpose;
  propertyType: EnumFormFormBuyPropertyType;
  desiredAreas: Scalars['String'];
  budget: Scalars['String'];
  paymentMethod: EnumFormFormBuyPaymentMethod;
  timeHorizon: Scalars['String'];
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export enum EnumFormFormBuyPurpose {
  OwnUse = 'own_use',
  Investment = 'investment',
  OwnUseInvestment = 'own_use_investment'
}

export enum EnumFormFormBuyPropertyType {
  MansionApartment = 'mansion_apartment',
  House = 'house',
  ResidentialBuilding = 'residential_building',
  OfficeBuilding = 'office_building',
  Land = 'land',
  Other = 'other'
}

export enum EnumFormFormBuyPaymentMethod {
  Cash = 'cash',
  Loan = 'loan',
  Other = 'other'
}

export type FormFormTour = {
  __typename?: 'FormFormTour';
  purpose: EnumFormFormTourPurpose;
  propertyType: EnumFormFormTourPropertyType;
  budget: Scalars['String'];
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export enum EnumFormFormTourPurpose {
  OwnUse = 'own_use',
  Investment = 'investment',
  OwnUseInvestment = 'own_use_investment'
}

export enum EnumFormFormTourPropertyType {
  MansionApartment = 'mansion_apartment',
  House = 'house',
  ResidentialBuilding = 'residential_building',
  OfficeBuilding = 'office_building',
  Land = 'land',
  Other = 'other'
}

export type FormFormSell = {
  __typename?: 'FormFormSell';
  nearestStation: Scalars['String'];
  propertyType: EnumFormFormSellPropertyType;
  desiredPrice?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export enum EnumFormFormSellPropertyType {
  MansionApartment = 'mansion_apartment',
  House = 'house',
  ResidentialBuilding = 'residential_building',
  OfficeBuilding = 'office_building',
  Land = 'land',
  Other = 'other'
}

export type FormUtm = {
  __typename?: 'FormUtm';
  utm_source?: Maybe<Scalars['String']>;
  utm_medium?: Maybe<Scalars['String']>;
  utm_campaign?: Maybe<Scalars['String']>;
  utm_term?: Maybe<Scalars['String']>;
  utm_content?: Maybe<Scalars['String']>;
  gclid?: Maybe<Scalars['String']>;
  fbclid?: Maybe<Scalars['String']>;
};

/** List of items with pagination. */
export type FormPagination = {
  __typename?: 'FormPagination';
  /** Total object count. */
  count?: Maybe<Scalars['Int']>;
  /** Array of objects. */
  items?: Maybe<Array<Form>>;
  /** Information to aid in pagination. */
  pageInfo: PaginationInfo;
};

export type FilterFindManyFormInput = {
  firstName?: Maybe<Scalars['String']>;
  lastName?: Maybe<Scalars['String']>;
  email?: Maybe<Scalars['String']>;
  phone?: Maybe<Scalars['String']>;
  locale?: Maybe<EnumFormLocale>;
  preferredLanguage?: Maybe<Array<Maybe<EnumFormPreferredLanguage>>>;
  whenToMoveIn?: Maybe<EnumFormFormRentWhenToMoveIn>;
  memo?: Maybe<Scalars['String']>;
  formType?: Maybe<EnumFormFormType>;
  formRent?: Maybe<FilterFindManyFormFormRentInput>;
  formBuy?: Maybe<FilterFindManyFormFormBuyInput>;
  formSell?: Maybe<FilterFindManyFormFormSellInput>;
  formTour?: Maybe<FilterFindManyFormFormTourInput>;
  utm?: Maybe<FilterFindManyFormUtmInput>;
  _id?: Maybe<Scalars['MongoID']>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
  /** List of *indexed* fields that can be filtered via operators. */
  _operators?: Maybe<FilterFindManyFormOperatorsInput>;
  OR?: Maybe<Array<FilterFindManyFormInput>>;
  AND?: Maybe<Array<FilterFindManyFormInput>>;
};

export type FilterFindManyFormFormRentInput = {
  occupation?: Maybe<EnumFormFormRentOccupation>;
  visaStatus?: Maybe<EnumFormFormRentVisaStatus>;
  rentBudget?: Maybe<Scalars['String']>;
  floorPlan?: Maybe<Scalars['String']>;
  size?: Maybe<Scalars['Float']>;
  desiredLocation?: Maybe<Scalars['String']>;
  stationDistance?: Maybe<Scalars['Float']>;
  whenToMoveIn?: Maybe<EnumFormFormRentWhenToMoveIn>;
  otherPreferences?: Maybe<Array<Maybe<EnumFormFormRentOtherPreferences>>>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export type FilterFindManyFormFormBuyInput = {
  purpose?: Maybe<EnumFormFormBuyPurpose>;
  propertyType?: Maybe<EnumFormFormBuyPropertyType>;
  desiredAreas?: Maybe<Scalars['String']>;
  budget?: Maybe<Scalars['String']>;
  paymentMethod?: Maybe<EnumFormFormBuyPaymentMethod>;
  timeHorizon?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export type FilterFindManyFormFormTourInput = {
  purpose?: Maybe<EnumFormFormTourPurpose>;
  propertyType?: Maybe<EnumFormFormTourPropertyType>;
  budget?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export type FilterFindManyFormFormSellInput = {
  nearestStation?: Maybe<Scalars['String']>;
  propertyType?: Maybe<EnumFormFormSellPropertyType>;
  desiredPrice?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export type FilterFindManyFormUtmInput = {
  utm_source?: Maybe<Scalars['String']>;
  utm_medium?: Maybe<Scalars['String']>;
  utm_campaign?: Maybe<Scalars['String']>;
  utm_term?: Maybe<Scalars['String']>;
  utm_content?: Maybe<Scalars['String']>;
  gclid?: Maybe<Scalars['String']>;
  fbclid?: Maybe<Scalars['String']>;
};

/** For performance reason this type contains only *indexed* fields. */
export type FilterFindManyFormOperatorsInput = {
  firstName?: Maybe<FilterFindManyFormFirstNameOperatorsInput>;
  lastName?: Maybe<FilterFindManyFormLastNameOperatorsInput>;
  email?: Maybe<FilterFindManyFormEmailOperatorsInput>;
  phone?: Maybe<FilterFindManyFormPhoneOperatorsInput>;
  locale?: Maybe<FilterFindManyFormLocaleOperatorsInput>;
  preferredLanguage?: Maybe<FilterFindManyFormPreferredLanguageOperatorsInput>;
  whenToMoveIn: Maybe<FilterFindManyFormFormRentWhenToMoveInOperatorsInput>;
  memo?: Maybe<FilterFindManyFormMemoOperatorsInput>;
  formType?: Maybe<FilterFindManyFormFormTypeOperatorsInput>;
  formRent?: Maybe<FilterFindManyFormFormRentOperatorsInput>;
  formBuy?: Maybe<FilterFindManyFormFormBuyOperatorsInput>;
  formSell?: Maybe<FilterFindManyFormFormSellOperatorsInput>;
  formTour?: Maybe<FilterFindManyFormFormTourOperatorsInput>;
  utm?: Maybe<FilterFindManyFormUtmOperatorsInput>;
  _id?: Maybe<FilterFindManyForm_IdOperatorsInput>;
  updatedAt?: Maybe<FilterFindManyFormUpdatedAtOperatorsInput>;
  createdAt?: Maybe<FilterFindManyFormCreatedAtOperatorsInput>;
};

export type FilterFindManyFormFirstNameOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormLastNameOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormEmailOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormPhoneOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormLocaleOperatorsInput = {
  gt?: Maybe<EnumFormLocale>;
  gte?: Maybe<EnumFormLocale>;
  lt?: Maybe<EnumFormLocale>;
  lte?: Maybe<EnumFormLocale>;
  ne?: Maybe<EnumFormLocale>;
  in?: Maybe<Array<Maybe<EnumFormLocale>>>;
  nin?: Maybe<Array<Maybe<EnumFormLocale>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormPreferredLanguageOperatorsInput = {
  gt?: Maybe<EnumFormPreferredLanguage>;
  gte?: Maybe<EnumFormPreferredLanguage>;
  lt?: Maybe<EnumFormPreferredLanguage>;
  lte?: Maybe<EnumFormPreferredLanguage>;
  ne?: Maybe<EnumFormPreferredLanguage>;
  in?: Maybe<Array<Maybe<EnumFormPreferredLanguage>>>;
  nin?: Maybe<Array<Maybe<EnumFormPreferredLanguage>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormMemoOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormTypeOperatorsInput = {
  gt?: Maybe<EnumFormFormType>;
  gte?: Maybe<EnumFormFormType>;
  lt?: Maybe<EnumFormFormType>;
  lte?: Maybe<EnumFormFormType>;
  ne?: Maybe<EnumFormFormType>;
  in?: Maybe<Array<Maybe<EnumFormFormType>>>;
  nin?: Maybe<Array<Maybe<EnumFormFormType>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormRentOperatorsInput = {
  occupation?: Maybe<FilterFindManyFormFormRentOccupationOperatorsInput>;
  visaStatus?: Maybe<FilterFindManyFormFormRentVisaStatusOperatorsInput>;
  rentBudget?: Maybe<FilterFindManyFormFormRentRentBudgetOperatorsInput>;
  floorPlan?: Maybe<FilterFindManyFormFormRentFloorPlanOperatorsInput>;
  size?: Maybe<FilterFindManyFormFormRentSizeOperatorsInput>;
  desiredLocation?: Maybe<FilterFindManyFormFormRentDesiredLocationOperatorsInput>;
  stationDistance?: Maybe<FilterFindManyFormFormRentStationDistanceOperatorsInput>;
  whenToMoveIn?: Maybe<FilterFindManyFormFormRentWhenToMoveInOperatorsInput>;
  otherPreferences?: Maybe<FilterFindManyFormFormRentOtherPreferencesOperatorsInput>;
  updatedAt?: Maybe<FilterFindManyFormFormRentUpdatedAtOperatorsInput>;
  createdAt?: Maybe<FilterFindManyFormFormRentCreatedAtOperatorsInput>;
};

export type FilterFindManyFormFormRentOccupationOperatorsInput = {
  gt?: Maybe<EnumFormFormRentOccupation>;
  gte?: Maybe<EnumFormFormRentOccupation>;
  lt?: Maybe<EnumFormFormRentOccupation>;
  lte?: Maybe<EnumFormFormRentOccupation>;
  ne?: Maybe<EnumFormFormRentOccupation>;
  in?: Maybe<Array<Maybe<EnumFormFormRentOccupation>>>;
  nin?: Maybe<Array<Maybe<EnumFormFormRentOccupation>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormRentVisaStatusOperatorsInput = {
  gt?: Maybe<EnumFormFormRentVisaStatus>;
  gte?: Maybe<EnumFormFormRentVisaStatus>;
  lt?: Maybe<EnumFormFormRentVisaStatus>;
  lte?: Maybe<EnumFormFormRentVisaStatus>;
  ne?: Maybe<EnumFormFormRentVisaStatus>;
  in?: Maybe<Array<Maybe<EnumFormFormRentVisaStatus>>>;
  nin?: Maybe<Array<Maybe<EnumFormFormRentVisaStatus>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormRentRentBudgetOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormRentFloorPlanOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormRentSizeOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormRentDesiredLocationOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormRentStationDistanceOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormRentWhenToMoveInOperatorsInput = {
  gt?: Maybe<EnumFormFormRentWhenToMoveIn>;
  gte?: Maybe<EnumFormFormRentWhenToMoveIn>;
  lt?: Maybe<EnumFormFormRentWhenToMoveIn>;
  lte?: Maybe<EnumFormFormRentWhenToMoveIn>;
  ne?: Maybe<EnumFormFormRentWhenToMoveIn>;
  in?: Maybe<Array<Maybe<EnumFormFormRentWhenToMoveIn>>>;
  nin?: Maybe<Array<Maybe<EnumFormFormRentWhenToMoveIn>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormRentOtherPreferencesOperatorsInput = {
  gt?: Maybe<EnumFormFormRentOtherPreferences>;
  gte?: Maybe<EnumFormFormRentOtherPreferences>;
  lt?: Maybe<EnumFormFormRentOtherPreferences>;
  lte?: Maybe<EnumFormFormRentOtherPreferences>;
  ne?: Maybe<EnumFormFormRentOtherPreferences>;
  in?: Maybe<Array<Maybe<EnumFormFormRentOtherPreferences>>>;
  nin?: Maybe<Array<Maybe<EnumFormFormRentOtherPreferences>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormRentUpdatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormRentCreatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormBuyOperatorsInput = {
  purpose?: Maybe<FilterFindManyFormFormBuyPurposeOperatorsInput>;
  propertyType?: Maybe<FilterFindManyFormFormBuyPropertyTypeOperatorsInput>;
  desiredAreas?: Maybe<FilterFindManyFormFormBuyDesiredAreasOperatorsInput>;
  budget?: Maybe<FilterFindManyFormFormBuyBudgetOperatorsInput>;
  paymentMethod?: Maybe<FilterFindManyFormFormBuyPaymentMethodOperatorsInput>;
  timeHorizon?: Maybe<FilterFindManyFormFormBuyTimeHorizonOperatorsInput>;
  updatedAt?: Maybe<FilterFindManyFormFormBuyUpdatedAtOperatorsInput>;
  createdAt?: Maybe<FilterFindManyFormFormBuyCreatedAtOperatorsInput>;
};

export type FilterFindManyFormFormBuyPurposeOperatorsInput = {
  gt?: Maybe<EnumFormFormBuyPurpose>;
  gte?: Maybe<EnumFormFormBuyPurpose>;
  lt?: Maybe<EnumFormFormBuyPurpose>;
  lte?: Maybe<EnumFormFormBuyPurpose>;
  ne?: Maybe<EnumFormFormBuyPurpose>;
  in?: Maybe<Array<Maybe<EnumFormFormBuyPurpose>>>;
  nin?: Maybe<Array<Maybe<EnumFormFormBuyPurpose>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormBuyPropertyTypeOperatorsInput = {
  gt?: Maybe<EnumFormFormBuyPropertyType>;
  gte?: Maybe<EnumFormFormBuyPropertyType>;
  lt?: Maybe<EnumFormFormBuyPropertyType>;
  lte?: Maybe<EnumFormFormBuyPropertyType>;
  ne?: Maybe<EnumFormFormBuyPropertyType>;
  in?: Maybe<Array<Maybe<EnumFormFormBuyPropertyType>>>;
  nin?: Maybe<Array<Maybe<EnumFormFormBuyPropertyType>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormBuyDesiredAreasOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormBuyBudgetOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormBuyPaymentMethodOperatorsInput = {
  gt?: Maybe<EnumFormFormBuyPaymentMethod>;
  gte?: Maybe<EnumFormFormBuyPaymentMethod>;
  lt?: Maybe<EnumFormFormBuyPaymentMethod>;
  lte?: Maybe<EnumFormFormBuyPaymentMethod>;
  ne?: Maybe<EnumFormFormBuyPaymentMethod>;
  in?: Maybe<Array<Maybe<EnumFormFormBuyPaymentMethod>>>;
  nin?: Maybe<Array<Maybe<EnumFormFormBuyPaymentMethod>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormBuyTimeHorizonOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormBuyUpdatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormBuyCreatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormTourOperatorsInput = {
  purpose?: Maybe<FilterFindManyFormFormTourPurposeOperatorsInput>;
  propertyType?: Maybe<FilterFindManyFormFormTourPropertyTypeOperatorsInput>;
  budget?: Maybe<FilterFindManyFormFormTourBudgetOperatorsInput>;
  updatedAt?: Maybe<FilterFindManyFormFormTourUpdatedAtOperatorsInput>;
  createdAt?: Maybe<FilterFindManyFormFormTourCreatedAtOperatorsInput>;
};

export type FilterFindManyFormFormTourPurposeOperatorsInput = {
  gt?: Maybe<EnumFormFormTourPurpose>;
  gte?: Maybe<EnumFormFormTourPurpose>;
  lt?: Maybe<EnumFormFormTourPurpose>;
  lte?: Maybe<EnumFormFormTourPurpose>;
  ne?: Maybe<EnumFormFormTourPurpose>;
  in?: Maybe<Array<Maybe<EnumFormFormTourPurpose>>>;
  nin?: Maybe<Array<Maybe<EnumFormFormTourPurpose>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormTourPropertyTypeOperatorsInput = {
  gt?: Maybe<EnumFormFormTourPropertyType>;
  gte?: Maybe<EnumFormFormTourPropertyType>;
  lt?: Maybe<EnumFormFormTourPropertyType>;
  lte?: Maybe<EnumFormFormTourPropertyType>;
  ne?: Maybe<EnumFormFormTourPropertyType>;
  in?: Maybe<Array<Maybe<EnumFormFormTourPropertyType>>>;
  nin?: Maybe<Array<Maybe<EnumFormFormTourPropertyType>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormTourDesiredAreasOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormTourBudgetOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormTourTimeHorizonOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormTourUpdatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormTourCreatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormSellOperatorsInput = {
  nearestStation?: Maybe<FilterFindManyFormFormSellNearestStationOperatorsInput>;
  propertyType?: Maybe<FilterFindManyFormFormSellPropertyTypeOperatorsInput>;
  desiredPrice?: Maybe<FilterFindManyFormFormSellDesiredPriceOperatorsInput>;
  updatedAt?: Maybe<FilterFindManyFormFormSellUpdatedAtOperatorsInput>;
  createdAt?: Maybe<FilterFindManyFormFormSellCreatedAtOperatorsInput>;
};

export type FilterFindManyFormFormSellNearestStationOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormSellPropertyTypeOperatorsInput = {
  gt?: Maybe<EnumFormFormSellPropertyType>;
  gte?: Maybe<EnumFormFormSellPropertyType>;
  lt?: Maybe<EnumFormFormSellPropertyType>;
  lte?: Maybe<EnumFormFormSellPropertyType>;
  ne?: Maybe<EnumFormFormSellPropertyType>;
  in?: Maybe<Array<Maybe<EnumFormFormSellPropertyType>>>;
  nin?: Maybe<Array<Maybe<EnumFormFormSellPropertyType>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormSellDesiredPriceOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormSellUpdatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormFormSellCreatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormUtmOperatorsInput = {
  utm_source?: Maybe<FilterFindManyFormUtmUtm_SourceOperatorsInput>;
  utm_medium?: Maybe<FilterFindManyFormUtmUtm_MediumOperatorsInput>;
  utm_campaign?: Maybe<FilterFindManyFormUtmUtm_CampaignOperatorsInput>;
  utm_term?: Maybe<FilterFindManyFormUtmUtm_TermOperatorsInput>;
  utm_content?: Maybe<FilterFindManyFormUtmUtm_ContentOperatorsInput>;
  gclid?: Maybe<FilterFindManyFormUtmGclidOperatorsInput>;
  fbclid?: Maybe<FilterFindManyFormUtmFbclidOperatorsInput>;
};

export type FilterFindManyFormUtmUtm_SourceOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormUtmUtm_MediumOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormUtmUtm_CampaignOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormUtmUtm_TermOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormUtmUtm_ContentOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormUtmGclidOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormUtmFbclidOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyForm_IdOperatorsInput = {
  gt?: Maybe<Scalars['MongoID']>;
  gte?: Maybe<Scalars['MongoID']>;
  lt?: Maybe<Scalars['MongoID']>;
  lte?: Maybe<Scalars['MongoID']>;
  ne?: Maybe<Scalars['MongoID']>;
  in?: Maybe<Array<Maybe<Scalars['MongoID']>>>;
  nin?: Maybe<Array<Maybe<Scalars['MongoID']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormUpdatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyFormCreatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export enum SortFindManyFormInput {
  IdAsc = '_ID_ASC',
  IdDesc = '_ID_DESC',
  CreatedatAsc = 'CREATEDAT_ASC',
  CreatedatDesc = 'CREATEDAT_DESC',
  UpdatedatAsc = 'UPDATEDAT_ASC',
  UpdatedatDesc = 'UPDATEDAT_DESC'
}

/** List of items with pagination. */
export type RejImportResultPagination = {
  __typename?: 'REJImportResultPagination';
  /** Total object count. */
  count?: Maybe<Scalars['Int']>;
  /** Array of objects. */
  items?: Maybe<Array<RejImportResult>>;
  /** Information to aid in pagination. */
  pageInfo: PaginationInfo;
};

/** The result log of the rej import */
export type RejImportResult = {
  __typename?: 'REJImportResult';
  agencyId: Scalars['String'];
  processedRow: Scalars['Float'];
  importedRow: Scalars['Float'];
  removedProperty: Scalars['Float'];
  generalErrors?: Maybe<Array<Maybe<Scalars['String']>>>;
  parsingErrors?: Maybe<Array<Maybe<RejImportResultParsingErrors>>>;
  databaseErrors?: Maybe<Array<Maybe<RejImportResultDatabaseErrors>>>;
  fileModifiedDate: Scalars['Date'];
  fileSize: Scalars['Float'];
  _id: Scalars['MongoID'];
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export type RejImportResultParsingErrors = {
  __typename?: 'REJImportResultParsingErrors';
  propertyId: Scalars['String'];
  items?: Maybe<Array<Maybe<RejImportResultParsingErrorsItems>>>;
};

export type RejImportResultParsingErrorsItems = {
  __typename?: 'REJImportResultParsingErrorsItems';
  colName: Scalars['String'];
  message: Scalars['String'];
};

export type RejImportResultDatabaseErrors = {
  __typename?: 'REJImportResultDatabaseErrors';
  propertyId: Scalars['String'];
  message?: Maybe<Scalars['String']>;
};

export type FilterFindManyRejImportResultInput = {
  agencyId?: Maybe<Scalars['String']>;
  processedRow?: Maybe<Scalars['Float']>;
  importedRow?: Maybe<Scalars['Float']>;
  removedProperty?: Maybe<Scalars['Float']>;
  generalErrors?: Maybe<Array<Maybe<Scalars['String']>>>;
  parsingErrors?: Maybe<Array<Maybe<FilterFindManyRejImportResultParsingErrorsInput>>>;
  databaseErrors?: Maybe<Array<Maybe<FilterFindManyRejImportResultDatabaseErrorsInput>>>;
  fileModifiedDate?: Maybe<Scalars['Date']>;
  fileSize?: Maybe<Scalars['Float']>;
  _id?: Maybe<Scalars['MongoID']>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
  /** List of *indexed* fields that can be filtered via operators. */
  _operators?: Maybe<FilterFindManyRejImportResultOperatorsInput>;
  OR?: Maybe<Array<FilterFindManyRejImportResultInput>>;
  AND?: Maybe<Array<FilterFindManyRejImportResultInput>>;
};

export type FilterFindManyRejImportResultParsingErrorsInput = {
  propertyId?: Maybe<Scalars['String']>;
  items?: Maybe<Array<Maybe<FilterFindManyRejImportResultParsingErrorsItemsInput>>>;
};

export type FilterFindManyRejImportResultParsingErrorsItemsInput = {
  colName?: Maybe<Scalars['String']>;
  message?: Maybe<Scalars['String']>;
};

export type FilterFindManyRejImportResultDatabaseErrorsInput = {
  propertyId?: Maybe<Scalars['String']>;
  message?: Maybe<Scalars['String']>;
};

/** For performance reason this type contains only *indexed* fields. */
export type FilterFindManyRejImportResultOperatorsInput = {
  agencyId?: Maybe<FilterFindManyRejImportResultAgencyIdOperatorsInput>;
  processedRow?: Maybe<FilterFindManyRejImportResultProcessedRowOperatorsInput>;
  importedRow?: Maybe<FilterFindManyRejImportResultImportedRowOperatorsInput>;
  removedProperty?: Maybe<FilterFindManyRejImportResultRemovedPropertyOperatorsInput>;
  generalErrors?: Maybe<FilterFindManyRejImportResultGeneralErrorsOperatorsInput>;
  parsingErrors?: Maybe<FilterFindManyRejImportResultParsingErrorsOperatorsInput>;
  databaseErrors?: Maybe<FilterFindManyRejImportResultDatabaseErrorsOperatorsInput>;
  fileModifiedDate?: Maybe<FilterFindManyRejImportResultFileModifiedDateOperatorsInput>;
  fileSize?: Maybe<FilterFindManyRejImportResultFileSizeOperatorsInput>;
  _id?: Maybe<FilterFindManyRejImportResult_IdOperatorsInput>;
  updatedAt?: Maybe<FilterFindManyRejImportResultUpdatedAtOperatorsInput>;
  createdAt?: Maybe<FilterFindManyRejImportResultCreatedAtOperatorsInput>;
};

export type FilterFindManyRejImportResultAgencyIdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyRejImportResultProcessedRowOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyRejImportResultImportedRowOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyRejImportResultRemovedPropertyOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyRejImportResultGeneralErrorsOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyRejImportResultParsingErrorsOperatorsInput = {
  propertyId?: Maybe<FilterFindManyRejImportResultParsingErrorsPropertyIdOperatorsInput>;
  items?: Maybe<FilterFindManyRejImportResultParsingErrorsItemsOperatorsInput>;
};

export type FilterFindManyRejImportResultParsingErrorsPropertyIdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyRejImportResultParsingErrorsItemsOperatorsInput = {
  colName?: Maybe<FilterFindManyRejImportResultParsingErrorsItemsColNameOperatorsInput>;
  message?: Maybe<FilterFindManyRejImportResultParsingErrorsItemsMessageOperatorsInput>;
};

export type FilterFindManyRejImportResultParsingErrorsItemsColNameOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyRejImportResultParsingErrorsItemsMessageOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyRejImportResultDatabaseErrorsOperatorsInput = {
  propertyId?: Maybe<FilterFindManyRejImportResultDatabaseErrorsPropertyIdOperatorsInput>;
  message?: Maybe<FilterFindManyRejImportResultDatabaseErrorsMessageOperatorsInput>;
};

export type FilterFindManyRejImportResultDatabaseErrorsPropertyIdOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyRejImportResultDatabaseErrorsMessageOperatorsInput = {
  gt?: Maybe<Scalars['String']>;
  gte?: Maybe<Scalars['String']>;
  lt?: Maybe<Scalars['String']>;
  lte?: Maybe<Scalars['String']>;
  ne?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Maybe<Scalars['String']>>>;
  nin?: Maybe<Array<Maybe<Scalars['String']>>>;
  regex?: Maybe<Scalars['RegExpAsString']>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyRejImportResultFileModifiedDateOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyRejImportResultFileSizeOperatorsInput = {
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  ne?: Maybe<Scalars['Float']>;
  in?: Maybe<Array<Maybe<Scalars['Float']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Float']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyRejImportResult_IdOperatorsInput = {
  gt?: Maybe<Scalars['MongoID']>;
  gte?: Maybe<Scalars['MongoID']>;
  lt?: Maybe<Scalars['MongoID']>;
  lte?: Maybe<Scalars['MongoID']>;
  ne?: Maybe<Scalars['MongoID']>;
  in?: Maybe<Array<Maybe<Scalars['MongoID']>>>;
  nin?: Maybe<Array<Maybe<Scalars['MongoID']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyRejImportResultUpdatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export type FilterFindManyRejImportResultCreatedAtOperatorsInput = {
  gt?: Maybe<Scalars['Date']>;
  gte?: Maybe<Scalars['Date']>;
  lt?: Maybe<Scalars['Date']>;
  lte?: Maybe<Scalars['Date']>;
  ne?: Maybe<Scalars['Date']>;
  in?: Maybe<Array<Maybe<Scalars['Date']>>>;
  nin?: Maybe<Array<Maybe<Scalars['Date']>>>;
  exists?: Maybe<Scalars['Boolean']>;
};

export enum SortFindManyRejImportResultInput {
  IdAsc = '_ID_ASC',
  IdDesc = '_ID_DESC',
  CreatedatAsc = 'CREATEDAT_ASC',
  CreatedatDesc = 'CREATEDAT_DESC'
}

export type Mutation = {
  __typename?: 'Mutation';
  /** Create one document with mongoose defaults, setters, hooks and validation */
  createAgency?: Maybe<CreateOneAgencyPayload>;
  /** Update one document: 1) Retrieve one document by findById. 2) Apply updates to mongoose document. 3) Mongoose applies defaults, setters, hooks and validation. 4) And save it. */
  updateAgency?: Maybe<UpdateByIdAgencyPayload>;
  /** Remove one document: 1) Retrieve one document and remove with hooks via findByIdAndRemove. 2) Return removed document. */
  deleteAgency?: Maybe<RemoveByIdAgencyPayload>;
  /** Create one document with mongoose defaults, setters, hooks and validation */
  createProperty?: Maybe<CreateOnePropertyPayload>;
  /** Update one document: 1) Retrieve one document by findById. 2) Apply updates to mongoose document. 3) Mongoose applies defaults, setters, hooks and validation. 4) And save it. */
  updateProperty?: Maybe<UpdateByIdPropertyPayload>;
  /** Remove one document: 1) Retrieve one document and remove with hooks via findByIdAndRemove. 2) Return removed document. */
  deleteProperty?: Maybe<RemoveByIdPropertyPayload>;
  incrementPageView?: Maybe<Scalars['Boolean']>;
  cloneRoom?: Maybe<PropertyRooms>;
  updateRoomVisibility?: Maybe<Scalars['Boolean']>;
  deleteRoom?: Maybe<PropertyRooms>;
  /** Create one document with mongoose defaults, setters, hooks and validation */
  createInquiry?: Maybe<CreateOneInquiryPayload>;
  setInquiryRead?: Maybe<Scalars['Boolean']>;
  syncTrainLineStations?: Maybe<Scalars['JSON']>;
  sendToSupport?: Maybe<Scalars['Boolean']>;
  createOrUpdateFeatured?: Maybe<Array<Maybe<Scalars['String']>>>;
  importPropertyFromREJ?: Maybe<Scalars['JSON']>;
  startSyncREJImport?: Maybe<Scalars['JSON']>;
  stopSyncREJImport?: Maybe<Scalars['JSON']>;
  /** Create one document with mongoose defaults, setters, hooks and validation */
  sendForm?: Maybe<CreateOneFormPayload>;
  importAddress?: Maybe<Scalars['JSON']>;
  syncPropertyNewAddress?: Maybe<Scalars['JSON']>;
  signIn: AccessToken;
  signInWithFB: AccessToken;
  signUp: AccessToken;
  logout: Succeed;
  verifyRequest: Succeed;
  verify: AccessToken;
  resetPassword: Succeed;
  newPassword: AccessToken;
  changePassword: Succeed;
  updateUser: User;
  switchLocale: User;
};


export type MutationCreateAgencyArgs = {
  record: CreateOneAgencyInput;
};


export type MutationUpdateAgencyArgs = {
  _id: Scalars['MongoID'];
  record: UpdateByIdAgencyInput;
};


export type MutationDeleteAgencyArgs = {
  _id: Scalars['MongoID'];
};


export type MutationCreatePropertyArgs = {
  record: CreateOnePropertyInput;
};


export type MutationUpdatePropertyArgs = {
  _id: Scalars['MongoID'];
  record: UpdateByIdPropertyInput;
};


export type MutationDeletePropertyArgs = {
  _id: Scalars['MongoID'];
};


export type MutationIncrementPageViewArgs = {
  propertyId: Scalars['MongoID'];
  roomCode: Scalars['String'];
};


export type MutationCloneRoomArgs = {
  propertyId: Scalars['MongoID'];
  code: Scalars['String'];
};


export type MutationUpdateRoomVisibilityArgs = {
  propertyId: Scalars['MongoID'];
  code: Scalars['String'];
  value: EnumPropertyRoomsVisibility;
};


export type MutationDeleteRoomArgs = {
  propertyId: Scalars['MongoID'];
  code: Scalars['String'];
};


export type MutationCreateInquiryArgs = {
  record: CreateOneInquiryInput;
};


export type MutationSetInquiryReadArgs = {
  inquiryId: Scalars['MongoID'];
  status: Scalars['Boolean'];
};


export type MutationSendToSupportArgs = {
  input: SendToSupportInput;
};


export type MutationCreateOrUpdateFeaturedArgs = {
  featured?: Maybe<Array<Maybe<Scalars['String']>>>;
};


export type MutationImportPropertyFromRejArgs = {
  forceOverwrite?: Maybe<Scalars['Boolean']>;
};


export type MutationSendFormArgs = {
  record: CreateOneFormInput;
};


export type MutationSignInArgs = {
  email: Scalars['String'];
  password: Scalars['String'];
};


export type MutationSignInWithFbArgs = {
  token: Scalars['String'];
};


export type MutationSignUpArgs = {
  email: Scalars['String'];
  password: Scalars['String'];
  agencyId?: Maybe<Scalars['String']>;
};


export type MutationVerifyArgs = {
  token: Scalars['String'];
};


export type MutationResetPasswordArgs = {
  email: Scalars['String'];
};


export type MutationNewPasswordArgs = {
  token: Scalars['String'];
  newPassword: Scalars['String'];
};


export type MutationChangePasswordArgs = {
  currentPassword: Scalars['String'];
  newPassword: Scalars['String'];
};


export type MutationUpdateUserArgs = {
  email: Scalars['String'];
  firstName: Scalars['String'];
  lastName: Scalars['String'];
};


export type MutationSwitchLocaleArgs = {
  locale: Locale;
};

export type CreateOneAgencyPayload = {
  __typename?: 'CreateOneAgencyPayload';
  /** Document ID */
  recordId?: Maybe<Scalars['MongoID']>;
  /** Created document */
  record?: Maybe<Agency>;
  /** Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response. */
  error?: Maybe<ErrorInterface>;
};

export type CreateOneAgencyInput = {
  basic?: Maybe<AgencyBasicInput>;
  public?: Maybe<AgencyPublicInput>;
  serviceFee?: Maybe<AgencyServiceFeeInput>;
};

export type AgencyBasicInput = {
  companyName: Scalars['String'];
  license?: Maybe<AgencyBasicLicenseInput>;
};

export type AgencyBasicLicenseInput = {
  haveLicense?: Maybe<Scalars['Boolean']>;
  licenseType?: Maybe<EnumAgencyBasicLicenseLicenseType>;
  number?: Maybe<Scalars['String']>;
  issueNumber?: Maybe<Scalars['String']>;
  expiryDate?: Maybe<AgencyBasicLicenseExpiryDateInput>;
};

export type AgencyBasicLicenseExpiryDateInput = {
  from?: Maybe<Scalars['String']>;
  until?: Maybe<Scalars['String']>;
};

export type AgencyPublicInput = {
  name?: Maybe<Scalars['String']>;
  imageUrl?: Maybe<Scalars['String']>;
  address?: Maybe<AgencyPublicAddressInput>;
  phone?: Maybe<Scalars['String']>;
  email: Scalars['String'];
  availableLanguages?: Maybe<Array<Maybe<Scalars['String']>>>;
  businessHour?: Maybe<AgencyPublicBusinessHourInput>;
  closeDays?: Maybe<Array<Maybe<Scalars['String']>>>;
  website?: Maybe<Scalars['String']>;
};

export type AgencyPublicAddressInput = {
  postalCode?: Maybe<Scalars['String']>;
  prefecture?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  chome?: Maybe<Scalars['String']>;
  streetAddress?: Maybe<Scalars['String']>;
  buildingName?: Maybe<Scalars['String']>;
};

export type AgencyPublicBusinessHourInput = {
  start?: Maybe<Scalars['String']>;
  end?: Maybe<Scalars['String']>;
};

export type AgencyServiceFeeInput = {
  feeType?: Maybe<EnumAgencyServiceFeeFeeType>;
  value?: Maybe<Scalars['Float']>;
};

export type UpdateByIdAgencyPayload = {
  __typename?: 'UpdateByIdAgencyPayload';
  /** Document ID */
  recordId?: Maybe<Scalars['MongoID']>;
  /** Updated document */
  record?: Maybe<Agency>;
  /** Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response. */
  error?: Maybe<ErrorInterface>;
};

export type UpdateByIdAgencyInput = {
  basic?: Maybe<UpdateByIdAgencyBasicInput>;
  public?: Maybe<UpdateByIdAgencyPublicInput>;
  serviceFee?: Maybe<UpdateByIdAgencyServiceFeeInput>;
};

export type UpdateByIdAgencyBasicInput = {
  companyName?: Maybe<Scalars['String']>;
  license?: Maybe<UpdateByIdAgencyBasicLicenseInput>;
};

export type UpdateByIdAgencyBasicLicenseInput = {
  haveLicense?: Maybe<Scalars['Boolean']>;
  licenseType?: Maybe<EnumAgencyBasicLicenseLicenseType>;
  number?: Maybe<Scalars['String']>;
  issueNumber?: Maybe<Scalars['String']>;
  expiryDate?: Maybe<UpdateByIdAgencyBasicLicenseExpiryDateInput>;
};

export type UpdateByIdAgencyBasicLicenseExpiryDateInput = {
  from?: Maybe<Scalars['String']>;
  until?: Maybe<Scalars['String']>;
};

export type UpdateByIdAgencyPublicInput = {
  name?: Maybe<Scalars['String']>;
  imageUrl?: Maybe<Scalars['String']>;
  address?: Maybe<UpdateByIdAgencyPublicAddressInput>;
  phone?: Maybe<Scalars['String']>;
  email?: Maybe<Scalars['String']>;
  availableLanguages?: Maybe<Array<Maybe<Scalars['String']>>>;
  businessHour?: Maybe<UpdateByIdAgencyPublicBusinessHourInput>;
  closeDays?: Maybe<Array<Maybe<Scalars['String']>>>;
  website?: Maybe<Scalars['String']>;
};

export type UpdateByIdAgencyPublicAddressInput = {
  postalCode?: Maybe<Scalars['String']>;
  prefecture?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  chome?: Maybe<Scalars['String']>;
  streetAddress?: Maybe<Scalars['String']>;
  buildingName?: Maybe<Scalars['String']>;
};

export type UpdateByIdAgencyPublicBusinessHourInput = {
  start?: Maybe<Scalars['String']>;
  end?: Maybe<Scalars['String']>;
};

export type UpdateByIdAgencyServiceFeeInput = {
  feeType?: Maybe<EnumAgencyServiceFeeFeeType>;
  value?: Maybe<Scalars['Float']>;
};

export type RemoveByIdAgencyPayload = {
  __typename?: 'RemoveByIdAgencyPayload';
  /** Document ID */
  recordId?: Maybe<Scalars['MongoID']>;
  /** Removed document */
  record?: Maybe<Agency>;
  /** Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response. */
  error?: Maybe<ErrorInterface>;
};

export type CreateOnePropertyPayload = {
  __typename?: 'CreateOnePropertyPayload';
  /** Document ID */
  recordId?: Maybe<Scalars['MongoID']>;
  /** Created document */
  record?: Maybe<Property>;
  /** Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response. */
  error?: Maybe<ErrorInterface>;
};

export type CreateOnePropertyInput = {
  buildingName?: Maybe<Scalars['String']>;
  agencyId: Scalars['MongoID'];
  location?: Maybe<PropertyLocationInput>;
  address: PropertyAddressInput;
  buildingType: EnumPropertyBuildingType;
  builtDate: Scalars['Date'];
  maxFloor: Scalars['Float'];
  structure: EnumPropertyStructure;
  images: PropertyImagesInput;
  access?: Maybe<PropertyAccessInput>;
  rooms: Array<Maybe<RoomInput>>;
  highestMonthlyPrice?: Maybe<Scalars['Float']>;
  lowestMonthlyPrice?: Maybe<Scalars['Float']>;
  pageViews?: Maybe<Scalars['JSON']>;
  nearestStationDistance?: Maybe<Scalars['Float']>;
  rejMetadata?: Maybe<PropertyRejMetadataInput>;
  languageComment?: Scalars['JSON'] | undefined;
};

export type PropertyLocationInput = {
  position?: Maybe<PropertyLocationPositionInput>;
  addressLine?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  region: Scalars['String'];
  prefecture: Scalars['String'];
  cityArea: Scalars['String'];
  city: Scalars['String'];
  area: Scalars['String'];
  chome: Scalars['String'];
};

export type PropertyLocationPositionInput = {
  lat?: Maybe<Scalars['Float']>;
  long?: Maybe<Scalars['Float']>;
};

export type PropertyAddressInput = {
  coord?: Maybe<Array<Maybe<Scalars['Float']>>>;
  line?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  prefecture: Scalars['String'];
  city: Scalars['String'];
  chome: Scalars['String'];
};

export type PropertyImagesInput = {
  collections: Array<Maybe<Scalars['String']>>;
};

export type PropertyAccessInput = {
  stations?: Maybe<Array<Maybe<PropertyAccessStationsInput>>>;
};

export type PropertyAccessStationsInput = {
  lineId?: Maybe<Scalars['String']>;
  stationId?: Maybe<Scalars['String']>;
  distance?: Maybe<PropertyAccessStationsDistanceInput>;
};

export type PropertyAccessStationsDistanceInput = {
  bus?: Maybe<Scalars['Float']>;
  walk?: Maybe<Scalars['Float']>;
};

export type RoomInput = {
  roomName?: Maybe<Scalars['String']>;
  numberOfRoom: Scalars['Float'];
  visibility: EnumPropertyRoomsVisibility;
  companyManagement?: Maybe<Scalars['String']>;
  telephone?: Maybe<Scalars['String']>;
  images: PropertyRoomsImagesInput;
  availability: PropertyRoomsAvailabilityInput;
  layout?: Maybe<Scalars['String']>;
  contractLength: Scalars['Float'];
  transactionType?: Maybe<EnumPropertyRoomsTransactionType>;
  contractType: EnumPropertyRoomsContractType;
  size: Scalars['Float'];
  mainWindow: EnumPropertyRoomsMainWindow;
  floor: Scalars['String'];
  tags?: Maybe<Array<Maybe<Scalars['String']>>>;
  facilities?: Maybe<PropertyRoomsFacilitiesInput>;
  roomLayout?: Maybe<Scalars['String']>;
  fees: PropertyRoomsFeesInput;
  pageViews?: Maybe<Scalars['JSON']>;
  inquiryCount?: Maybe<Scalars['Float']>;
  rejMetadata?: Maybe<PropertyRoomsRejMetadataInput>;
};

export type PropertyRoomsImagesInput = {
  featured: Scalars['String'];
  floorPlan: Scalars['String'];
  collections: Array<Maybe<Scalars['String']>>;
};

export type PropertyRoomsAvailabilityInput = {
  status: EnumPropertyRoomsAvailabilityStatus;
  now?: Maybe<Scalars['Boolean']>;
  enquire?: Maybe<Scalars['Boolean']>;
};

export type PropertyRoomsFacilitiesInput = {
  basic?: Maybe<Array<Maybe<Scalars['String']>>>;
  building?: Maybe<Array<Maybe<Scalars['String']>>>;
  other?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type PropertyRoomsFeesInput = {
  monthly: PropertyRoomsFeesMonthlyInput;
  moveIn: PropertyRoomsFeesMoveInInput;
  others?: Maybe<Array<Maybe<PropertyRoomsFeesOthersInput>>>;
};

export type PropertyRoomsFeesMonthlyInput = {
  rentFee?: Maybe<Scalars['Float']>;
  maintenanceFee?: Maybe<Scalars['Float']>;
};

export type PropertyRoomsFeesMoveInInput = {
  deposit: PropertyRoomsFeesMoveInDepositInput;
  keyMoney: PropertyRoomsFeesMoveInDepositInput;
  agencyFee: PropertyRoomsFeesMoveInDepositInput;
  guarantorFee: PropertyRoomsFeesMoveInDepositInput;
  contractRenewalFee: PropertyRoomsFeesMoveInDepositInput;
};

export type PropertyRoomsFeesMoveInDepositInput = {
  feeType: EnumPropertyRoomsFeesMoveInDepositFeeType;
  value: Scalars['Float'];
};

export type PropertyRoomsFeesOthersInput = {
  feeType?: Maybe<EnumPropertyRoomsFeesOthersFeeType>;
  value?: Maybe<Scalars['Float']>;
  chargeTiming?: Maybe<EnumPropertyRoomsFeesOthersChargeTiming>;
};

export type PropertyRoomsRejMetadataInput = {
  propertyId?: Maybe<Scalars['String']>;
};

export type PropertyRejMetadataInput = {
  buildingId?: Maybe<Scalars['String']>;
};

export type UpdateByIdPropertyPayload = {
  __typename?: 'UpdateByIdPropertyPayload';
  /** Document ID */
  recordId?: Maybe<Scalars['MongoID']>;
  /** Updated document */
  record?: Maybe<Property>;
  /** Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response. */
  error?: Maybe<ErrorInterface>;
};

export type UpdateByIdPropertyInput = {
  buildingName?: Maybe<Scalars['String']>;
  agencyId?: Maybe<Scalars['MongoID']>;
  location?: Maybe<UpdateByIdPropertyLocationInput>;
  address?: Maybe<UpdateByIdPropertyAddressInput>;
  buildingType?: Maybe<EnumPropertyBuildingType>;
  builtDate?: Maybe<Scalars['Date']>;
  maxFloor?: Maybe<Scalars['Float']>;
  structure?: Maybe<EnumPropertyStructure>;
  images?: Maybe<UpdateByIdPropertyImagesInput>;
  access?: Maybe<UpdateByIdPropertyAccessInput>;
  rooms?: Maybe<Array<Maybe<UpdateByIdPropertyRoomsInput>>>;
  highestMonthlyPrice?: Maybe<Scalars['Float']>;
  lowestMonthlyPrice?: Maybe<Scalars['Float']>;
  pageViews?: Maybe<Scalars['JSON']>;
  nearestStationDistance?: Maybe<Scalars['Float']>;
  rejMetadata?: Maybe<UpdateByIdPropertyRejMetadataInput>;
  languageComment?: Scalars['JSON'] | undefined;
};

export type UpdateByIdPropertyLocationInput = {
  position?: Maybe<UpdateByIdPropertyLocationPositionInput>;
  addressLine?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  region?: Maybe<Scalars['String']>;
  prefecture?: Maybe<Scalars['String']>;
  cityArea?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  area?: Maybe<Scalars['String']>;
  chome?: Maybe<Scalars['String']>;
};

export type UpdateByIdPropertyLocationPositionInput = {
  lat?: Maybe<Scalars['Float']>;
  long?: Maybe<Scalars['Float']>;
};

export type UpdateByIdPropertyAddressInput = {
  coord?: Maybe<Array<Maybe<Scalars['Float']>>>;
  line?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  prefecture?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  chome?: Maybe<Scalars['String']>;
};

export type UpdateByIdPropertyImagesInput = {
  collections?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type UpdateByIdPropertyAccessInput = {
  stations?: Maybe<Array<Maybe<UpdateByIdPropertyAccessStationsInput>>>;
};

export type UpdateByIdPropertyAccessStationsInput = {
  lineId?: Maybe<Scalars['String']>;
  stationId?: Maybe<Scalars['String']>;
  distance?: Maybe<UpdateByIdPropertyAccessStationsDistanceInput>;
};

export type UpdateByIdPropertyAccessStationsDistanceInput = {
  bus?: Maybe<Scalars['Float']>;
  walk?: Maybe<Scalars['Float']>;
};

export type UpdateByIdPropertyRoomsInput = {
  code?: Maybe<Scalars['String']>;
  roomName?: Maybe<Scalars['String']>;
  numberOfRoom?: Maybe<Scalars['Float']>;
  visibility?: Maybe<EnumPropertyRoomsVisibility>;
  companyManagement?: Maybe<Scalars['String']>;
  telephone?: Maybe<Scalars['String']>;
  images?: Maybe<UpdateByIdPropertyRoomsImagesInput>;
  availability?: Maybe<UpdateByIdPropertyRoomsAvailabilityInput>;
  layout?: Maybe<Scalars['String']>;
  contractLength?: Maybe<Scalars['Float']>;
  transactionType?: Maybe<EnumPropertyRoomsTransactionType>;
  contractType?: Maybe<EnumPropertyRoomsContractType>;
  size?: Maybe<Scalars['Float']>;
  mainWindow?: Maybe<EnumPropertyRoomsMainWindow>;
  floor?: Maybe<Scalars['String']>;
  tags?: Maybe<Array<Maybe<Scalars['String']>>>;
  facilities?: Maybe<UpdateByIdPropertyRoomsFacilitiesInput>;
  monthlyFee?: Maybe<Scalars['Float']>;
  roomLayout?: Maybe<Scalars['String']>;
  fees?: Maybe<UpdateByIdPropertyRoomsFeesInput>;
  pageViews?: Maybe<Scalars['JSON']>;
  inquiryCount?: Maybe<Scalars['Float']>;
  rejMetadata?: Maybe<UpdateByIdPropertyRoomsRejMetadataInput>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export type UpdateByIdPropertyRoomsImagesInput = {
  featured?: Maybe<Scalars['String']>;
  floorPlan?: Maybe<Scalars['String']>;
  collections?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type UpdateByIdPropertyRoomsAvailabilityInput = {
  status?: Maybe<EnumPropertyRoomsAvailabilityStatus>;
  now?: Maybe<Scalars['Boolean']>;
  enquire?: Maybe<Scalars['Boolean']>;
};

export type UpdateByIdPropertyRoomsFacilitiesInput = {
  basic?: Maybe<Array<Maybe<Scalars['String']>>>;
  building?: Maybe<Array<Maybe<Scalars['String']>>>;
  other?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type UpdateByIdPropertyRoomsFeesInput = {
  monthly?: Maybe<UpdateByIdPropertyRoomsFeesMonthlyInput>;
  moveIn?: Maybe<UpdateByIdPropertyRoomsFeesMoveInInput>;
  others?: Maybe<Array<Maybe<UpdateByIdPropertyRoomsFeesOthersInput>>>;
};

export type UpdateByIdPropertyRoomsFeesMonthlyInput = {
  rentFee?: Maybe<Scalars['Float']>;
  maintenanceFee?: Maybe<Scalars['Float']>;
};

export type UpdateByIdPropertyRoomsFeesMoveInInput = {
  deposit?: Maybe<UpdateByIdPropertyRoomsFeesMoveInDepositInput>;
  keyMoney?: Maybe<UpdateByIdPropertyRoomsFeesMoveInDepositInput>;
  agencyFee?: Maybe<UpdateByIdPropertyRoomsFeesMoveInDepositInput>;
  guarantorFee?: Maybe<UpdateByIdPropertyRoomsFeesMoveInDepositInput>;
  contractRenewalFee?: Maybe<UpdateByIdPropertyRoomsFeesMoveInDepositInput>;
};

export type UpdateByIdPropertyRoomsFeesMoveInDepositInput = {
  feeType: EnumPropertyRoomsFeesMoveInDepositFeeType;
  value: Scalars['Float'];
};

export type UpdateByIdPropertyRoomsFeesOthersInput = {
  feeType?: Maybe<EnumPropertyRoomsFeesOthersFeeType>;
  value?: Maybe<Scalars['Float']>;
  chargeTiming?: Maybe<EnumPropertyRoomsFeesOthersChargeTiming>;
};

export type UpdateByIdPropertyRoomsRejMetadataInput = {
  propertyId?: Maybe<Scalars['String']>;
};

export type UpdateByIdPropertyRejMetadataInput = {
  buildingId?: Maybe<Scalars['String']>;
};

export type RemoveByIdPropertyPayload = {
  __typename?: 'RemoveByIdPropertyPayload';
  /** Document ID */
  recordId?: Maybe<Scalars['MongoID']>;
  /** Removed document */
  record?: Maybe<Property>;
  /** Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response. */
  error?: Maybe<ErrorInterface>;
};

export type CreateOneInquiryPayload = {
  __typename?: 'CreateOneInquiryPayload';
  /** Document ID */
  recordId?: Maybe<Scalars['MongoID']>;
  /** Created document */
  record?: Maybe<Inquiry>;
  /** Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response. */
  error?: Maybe<ErrorInterface>;
};

export type CreateOneInquiryInput = {
  name: Scalars['String'];
  phone?: Maybe<Scalars['String']>;
  email: Scalars['String'];
  locale: EnumInquiryLocale;
  memo?: Maybe<Scalars['String']>;
  preferredLanguage: Array<Maybe<EnumInquiryPreferredLanguage>>;
  purpose: Array<Maybe<EnumInquiryPurpose>>;
  unread?: Maybe<Scalars['Boolean']>;
  propertyId: Scalars['String'];
  roomCode: Scalars['String'];
  whenToMoveIn: Maybe<EnumFormFormRentWhenToMoveIn>;
  utm?: Maybe<InquiryUtmInput>;
};

export type InquiryUtmInput = {
  utm_source?: Maybe<Scalars['String']>;
  utm_medium?: Maybe<Scalars['String']>;
  utm_campaign?: Maybe<Scalars['String']>;
  utm_term?: Maybe<Scalars['String']>;
  utm_content?: Maybe<Scalars['String']>;
  gclid?: Maybe<Scalars['String']>;
  fbclid?: Maybe<Scalars['String']>;
};

export type SendToSupportInput = {
  name: Scalars['String'];
  email: Scalars['String'];
  message: Scalars['String'];
  token: Scalars['String'];
};

export type CreateOneFormPayload = {
  __typename?: 'CreateOneFormPayload';
  /** Document ID */
  recordId?: Maybe<Scalars['MongoID']>;
  /** Created document */
  record?: Maybe<Form>;
  /** Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response. */
  error?: Maybe<ErrorInterface>;
};

export type CreateOneFormInput = {
  firstName: Scalars['String'];
  lastName?: Maybe<Scalars['String']>;
  email: Scalars['String'];
  phone?: Maybe<Scalars['String']>;
  locale: EnumFormLocale;
  preferredLanguage: Array<Maybe<EnumFormPreferredLanguage>>;
  whenToMoveIn: Maybe<EnumFormFormRentWhenToMoveIn>;
  memo?: Maybe<Scalars['String']>;
  formType: EnumFormFormType;
  formRent?: Maybe<FormFormRentInput>;
  formBuy?: Maybe<FormFormBuyInput>;
  formSell?: Maybe<FormFormSellInput>;
  formTour?: Maybe<FormFormTourInput>;
  utm?: Maybe<FormUtmInput>;
};

export type FormFormRentInput = {
  occupation: EnumFormFormRentOccupation;
  visaStatus?: Maybe<EnumFormFormRentVisaStatus>;
  rentBudget?: Maybe<Scalars['String']>;
  floorPlan: Scalars['String'];
  size: Scalars['Float'];
  desiredLocation: Scalars['String'];
  stationDistance: Scalars['Float'];
  whenToMoveIn: EnumFormFormRentWhenToMoveIn;
  otherPreferences?: Maybe<Array<Maybe<EnumFormFormRentOtherPreferences>>>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export type FormFormBuyInput = {
  purpose: EnumFormFormBuyPurpose;
  propertyType: EnumFormFormBuyPropertyType;
  desiredAreas: Scalars['String'];
  budget: Scalars['String'];
  paymentMethod: EnumFormFormBuyPaymentMethod;
  timeHorizon: Scalars['String'];
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export type FormFormTourInput = {
  purpose: EnumFormFormTourPurpose;
  propertyType: EnumFormFormTourPropertyType;
  budget: Scalars['String'];
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export type FormFormSellInput = {
  nearestStation: Scalars['String'];
  propertyType: EnumFormFormSellPropertyType;
  desiredPrice?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export type FormUtmInput = {
  utm_source?: Maybe<Scalars['String']>;
  utm_medium?: Maybe<Scalars['String']>;
  utm_campaign?: Maybe<Scalars['String']>;
  utm_term?: Maybe<Scalars['String']>;
  utm_content?: Maybe<Scalars['String']>;
  gclid?: Maybe<Scalars['String']>;
  fbclid?: Maybe<Scalars['String']>;
};

export type AccessToken = {
  __typename?: 'AccessToken';
  accessToken: Scalars['String'];
};

export type Succeed = {
  __typename?: 'Succeed';
  succeed: Scalars['Boolean'];
};

export enum Locale {
  En = 'en',
  Ge = 'ge'
}

export type GetStationByIdQueryVariables = Exact<{
  id: Scalars['String'];
}>;


export type GetStationByIdQuery = (
  { __typename?: 'Query' }
  & { stationById?: Maybe<(
    { __typename?: 'Station' }
    & Pick<Station, '_id' | 'name' | 'en'>
  )> }
);

export type GetAgencyIdsBasedOnFeeQueryVariables = Exact<{ [key: string]: never; }>;


export type GetAgencyIdsBasedOnFeeQuery = (
  { __typename?: 'Query' }
  & { agencyIdsWithFee?: Maybe<(
    { __typename?: 'AgencyPagination' }
    & { items?: Maybe<Array<(
      { __typename?: 'Agency' }
      & Pick<Agency, '_id'>
    )>> }
  )>, agencyIdsWithoutFee?: Maybe<(
    { __typename?: 'AgencyPagination' }
    & { items?: Maybe<Array<(
      { __typename?: 'Agency' }
      & Pick<Agency, '_id'>
    )>> }
  )> }
);

export type GetAdminAnalyticDataQueryVariables = Exact<{
  inhouseAgencyIds?: Maybe<Array<Maybe<Scalars['String']>> | Maybe<Scalars['String']>>;
  agencyIdsWithoutFee?: Maybe<Array<Maybe<Scalars['String']>> | Maybe<Scalars['String']>>;
  agencyIdsWithFee?: Maybe<Array<Maybe<Scalars['String']>> | Maybe<Scalars['String']>>;
  startDate: Scalars['Date'];
  endDate: Scalars['Date'];
}>;


export type GetAdminAnalyticDataQuery = (
  { __typename?: 'Query' }
  & { inhouseAnalytics?: Maybe<(
    { __typename?: 'Analytic' }
    & Pick<Analytic, 'uniqueInquiry' | 'totalInquiry' | 'totalPageView' | 'averageInquiredRentFee' | 'totalServiceFee'>
    & { form?: Maybe<(
      { __typename?: 'AnalyticForm' }
      & Pick<AnalyticForm, 'unique' | 'total'>
    )> }
  )>, withoutFeeAnalytics?: Maybe<(
    { __typename?: 'Analytic' }
    & Pick<Analytic, 'uniqueInquiry' | 'totalInquiry' | 'totalPageView' | 'averageInquiredRentFee' | 'totalServiceFee'>
  )>, withFeeAnalytics?: Maybe<(
    { __typename?: 'Analytic' }
    & Pick<Analytic, 'uniqueInquiry' | 'totalInquiry' | 'totalPageView' | 'averageInquiredRentFee' | 'totalServiceFee'>
  )> }
);

export type AnalyticsQueryVariables = Exact<{
  filter?: Maybe<FilterAnalyticInput>;
}>;


export type AnalyticsQuery = (
  { __typename?: 'Query' }
  & { analytics?: Maybe<(
    { __typename?: 'Analytic' }
    & Pick<Analytic, 'uniqueInquiry' | 'totalInquiry' | 'totalPageView' | 'averageInquiredRentFee' | 'totalServiceFee'>
    & { form?: Maybe<(
      { __typename?: 'AnalyticForm' }
      & Pick<AnalyticForm, 'unique' | 'total'>
    )> }
  )> }
);

export type ChomesQueryVariables = Exact<{
  cityId: Scalars['String'];
  prefId: Scalars['String'];
}>;


export type ChomesQuery = (
  { __typename?: 'Query' }
  & { chomes: Array<(
    { __typename?: 'Chome' }
    & Pick<Chome, '_id' | 'en' | 'jp'>
  )> }
);

export type CitiesQueryVariables = Exact<{
  prefId: Scalars['String'];
}>;


export type CitiesQuery = (
  { __typename?: 'Query' }
  & { cities: Array<(
    { __typename?: 'City' }
    & Pick<City, '_id' | 'en' | 'jp'>
  )> }
);

export type PrefecturesQueryVariables = Exact<{ [key: string]: never; }>;


export type PrefecturesQuery = (
  { __typename?: 'Query' }
  & { prefectures: Array<(
    { __typename?: 'Prefecture' }
    & Pick<Prefecture, '_id' | 'en' | 'jp'>
  )> }
);

export type GetFormByIdQueryVariables = Exact<{
  id: Scalars['MongoID'];
}>;


export type GetFormByIdQuery = (
  { __typename?: 'Query' }
  & { formById?: Maybe<(
    { __typename?: 'Form' }
    & Pick<Form, '_id' | 'firstName' | 'lastName' | 'phone' | 'email' | 'memo' | 'formType' | 'whenToMoveIn' | 'preferredLanguage' | 'createdAt' | 'updatedAt'>
    & { formRent?: Maybe<(
      { __typename?: 'FormFormRent' }
      & Pick<FormFormRent, 'occupation' | 'visaStatus' | 'rentBudget' | 'floorPlan' | 'size' | 'desiredLocation' | 'stationDistance' | 'whenToMoveIn' | 'otherPreferences'>
    )>, formBuy?: Maybe<(
      { __typename?: 'FormFormBuy' }
      & Pick<FormFormBuy, 'purpose' | 'propertyType' | 'desiredAreas' | 'timeHorizon' | 'paymentMethod' | 'budget'>
    )>, formSell?: Maybe<(
      { __typename?: 'FormFormSell' }
      & Pick<FormFormSell, 'propertyType' | 'nearestStation' | 'desiredPrice'>
    )>, formTour?: Maybe<(
      { __typename?: 'FormFormTour' }
      & Pick<FormFormTour, 'purpose' | 'propertyType' | 'budget'>
    )>, utm?: Maybe<(
      { __typename?: 'FormUtm' }
      & Pick<FormUtm, 'utm_source'>
    )> }
  )> }
);

export type GetFormsQueryVariables = Exact<{
  perPage?: Maybe<Scalars['Int']>;
  page?: Maybe<Scalars['Int']>;
  sort?: Maybe<SortFindManyFormInput>;
  filter?: Maybe<FilterFindManyFormInput>;
}>;


export type GetFormsQuery = (
  { __typename?: 'Query' }
  & { forms?: Maybe<(
    { __typename?: 'FormPagination' }
    & Pick<FormPagination, 'count'>
    & { pageInfo: (
      { __typename?: 'PaginationInfo' }
      & Pick<PaginationInfo, 'currentPage' | 'perPage' | 'itemCount' | 'hasNextPage' | 'hasPreviousPage'>
    ), items?: Maybe<Array<(
      { __typename?: 'Form' }
      & Pick<Form, '_id' | 'firstName' | 'lastName' | 'email' | 'createdAt' | 'formType'>
    )>> }
  )> }
);

export type GetInquiriesQueryVariables = Exact<{
  page?: Maybe<Scalars['Int']>;
  sort?: Maybe<SortFindManyInquiryInput>;
  filter?: Maybe<FilterFindManyInquiryInput>;
}>;


export type GetInquiriesQuery = (
  { __typename?: 'Query' }
  & { inquiries?: Maybe<(
    { __typename?: 'InquiryPagination' }
    & Pick<InquiryPagination, 'count'>
    & { pageInfo: (
      { __typename?: 'PaginationInfo' }
      & Pick<PaginationInfo, 'currentPage' | 'perPage' | 'itemCount' | 'hasNextPage' | 'hasPreviousPage'>
    ), items?: Maybe<Array<(
      { __typename?: 'Inquiry' }
      & Pick<Inquiry, '_id' | 'name' | 'email' | 'unread' | 'monthlyFee' | 'serviceFee' | 'createdAt'>
      & { property?: Maybe<(
        { __typename?: 'Property' }
        & Pick<Property, '_id' | 'buildingName'>
      )> }
    )>> }
  )> }
);

export type GetInquiryQueryVariables = Exact<{
  id: Scalars['MongoID'];
}>;


export type GetInquiryQuery = (
  { __typename?: 'Query' }
  & { inquiryById?: Maybe<(
    { __typename?: 'Inquiry' }
    & Pick<Inquiry, '_id' | 'name' | 'phone' | 'email' | 'memo' | 'purpose' | 'agencyId' | 'roomCode' | 'preferredLanguage' | 'whenToMoveIn' | 'createdAt' | 'updatedAt'>
    & { property?: Maybe<(
      { __typename?: 'Property' }
      & InquiryPropertyFragmentFragment
    )>, utm?: Maybe<(
      { __typename?: 'InquiryUtm' }
      & Pick<InquiryUtm, 'utm_source'>
    )> }
  )> }
);

export type InquiryPropertyFragmentFragment = (
  { __typename?: 'Property' }
  & Pick<Property, 'prefecturePath'>
  & { rooms: Array<Maybe<(
    { __typename?: 'PropertyRooms' }
    & BaseRoomFragment
  )>>, addressDetail?: Maybe<(
    { __typename?: 'PropertyAddress' }
    & BaseAddressDetailFragment
  )>, access?: Maybe<(
    { __typename?: 'PropertyAccess' }
    & { stations?: Maybe<Array<Maybe<(
      { __typename?: 'PropertyAccessStations' }
      & { lineDetail?: Maybe<(
        { __typename?: 'ServiceLine' }
        & Pick<ServiceLine, 'name' | 'en'>
      )>, stationDetail?: Maybe<(
        { __typename?: 'Station' }
        & Pick<Station, 'name' | 'en'>
      )> }
    )>>> }
    & BaseAccessFragment
  )>, agency?: Maybe<(
    { __typename?: 'Agency' }
    & { public?: Maybe<(
      { __typename?: 'AgencyPublic' }
      & Pick<AgencyPublic, 'name'>
    )> }
  )> }
  & BasePropertyFragmentFragment
);

export type GetUnReadInquiriesQueryVariables = Exact<{
  agencyId?: Maybe<Scalars['String']>;
}>;


export type GetUnReadInquiriesQuery = (
  { __typename?: 'Query' }
  & { adminInquiryCount: Query['inquiryCount'] }
);

export type ChangePasswordMutationVariables = Exact<{
  old: Scalars['String'];
  new: Scalars['String'];
}>;


export type ChangePasswordMutation = (
  { __typename?: 'Mutation' }
  & { changePassword: (
    { __typename?: 'Succeed' }
    & Pick<Succeed, 'succeed'>
  ) }
);

export type CreateAgencyMutationVariables = Exact<{
  record: CreateOneAgencyInput;
}>;


export type CreateAgencyMutation = (
  { __typename?: 'Mutation' }
  & { createAgency?: Maybe<(
    { __typename?: 'CreateOneAgencyPayload' }
    & { record?: Maybe<(
      { __typename?: 'Agency' }
      & Pick<Agency, '_id' | 'createdAt' | 'updatedAt'>
      & { basic?: Maybe<(
        { __typename?: 'AgencyBasic' }
        & Pick<AgencyBasic, 'companyName'>
        & { license?: Maybe<(
          { __typename?: 'AgencyBasicLicense' }
          & Pick<AgencyBasicLicense, 'licenseType' | 'number' | 'issueNumber'>
          & { expiryDate?: Maybe<(
            { __typename?: 'AgencyBasicLicenseExpiryDate' }
            & Pick<AgencyBasicLicenseExpiryDate, 'from' | 'until'>
          )> }
        )> }
      )>, public?: Maybe<(
        { __typename?: 'AgencyPublic' }
        & Pick<AgencyPublic, 'name' | 'imageUrl' | 'phone' | 'email' | 'availableLanguages' | 'closeDays' | 'website'>
        & { address?: Maybe<(
          { __typename?: 'AgencyPublicAddress' }
          & Pick<AgencyPublicAddress, 'postalCode' | 'prefecture' | 'city' | 'streetAddress' | 'buildingName'>
        )>, businessHour?: Maybe<(
          { __typename?: 'AgencyPublicBusinessHour' }
          & Pick<AgencyPublicBusinessHour, 'start' | 'end'>
        )> }
      )> }
    )> }
  )> }
);

export type CreateOrUpdateFeaturedMutationVariables = Exact<{
  featured?: Maybe<Array<Maybe<Scalars['String']>> | Maybe<Scalars['String']>>;
}>;


export type CreateOrUpdateFeaturedMutation = (
  { __typename?: 'Mutation' }
  & Pick<Mutation, 'createOrUpdateFeatured'>
);

export type DeleteAgencyMutationVariables = Exact<{
  id: Scalars['MongoID'];
}>;


export type DeleteAgencyMutation = (
  { __typename?: 'Mutation' }
  & { deleteAgency?: Maybe<(
    { __typename?: 'RemoveByIdAgencyPayload' }
    & { record?: Maybe<(
      { __typename?: 'Agency' }
      & Pick<Agency, '_id'>
    )> }
  )> }
);

export type NewPasswordMutationVariables = Exact<{
  token: Scalars['String'];
  new: Scalars['String'];
}>;


export type NewPasswordMutation = (
  { __typename?: 'Mutation' }
  & { newPassword: (
    { __typename?: 'AccessToken' }
    & Pick<AccessToken, 'accessToken'>
  ) }
);

export type ReadInquiryMutationVariables = Exact<{
  inquiryId: Scalars['MongoID'];
}>;


export type ReadInquiryMutation = (
  { __typename?: 'Mutation' }
  & Pick<Mutation, 'setInquiryRead'>
);

export type ResetPasswordMutationVariables = Exact<{
  email: Scalars['String'];
}>;


export type ResetPasswordMutation = (
  { __typename?: 'Mutation' }
  & { resetPassword: (
    { __typename?: 'Succeed' }
    & Pick<Succeed, 'succeed'>
  ) }
);

export type SignInMutationVariables = Exact<{
  email: Scalars['String'];
  password: Scalars['String'];
}>;


export type SignInMutation = (
  { __typename?: 'Mutation' }
  & { signIn: (
    { __typename?: 'AccessToken' }
    & Pick<AccessToken, 'accessToken'>
  ) }
);

export type SignUpMutationVariables = Exact<{
  email: Scalars['String'];
  password: Scalars['String'];
}>;


export type SignUpMutation = (
  { __typename?: 'Mutation' }
  & { signUp: (
    { __typename?: 'AccessToken' }
    & Pick<AccessToken, 'accessToken'>
  ) }
);

export type UpdateAgencyMutationVariables = Exact<{
  id: Scalars['MongoID'];
  record: UpdateByIdAgencyInput;
}>;


export type UpdateAgencyMutation = (
  { __typename?: 'Mutation' }
  & { updateAgency?: Maybe<(
    { __typename?: 'UpdateByIdAgencyPayload' }
    & { record?: Maybe<(
      { __typename?: 'Agency' }
      & Pick<Agency, '_id' | 'createdAt' | 'updatedAt'>
      & { basic?: Maybe<(
        { __typename?: 'AgencyBasic' }
        & Pick<AgencyBasic, 'companyName'>
        & { license?: Maybe<(
          { __typename?: 'AgencyBasicLicense' }
          & Pick<AgencyBasicLicense, 'licenseType' | 'number' | 'issueNumber'>
          & { expiryDate?: Maybe<(
            { __typename?: 'AgencyBasicLicenseExpiryDate' }
            & Pick<AgencyBasicLicenseExpiryDate, 'from' | 'until'>
          )> }
        )> }
      )>, public?: Maybe<(
        { __typename?: 'AgencyPublic' }
        & Pick<AgencyPublic, 'name' | 'imageUrl' | 'phone' | 'email' | 'availableLanguages' | 'closeDays' | 'website'>
        & { address?: Maybe<(
          { __typename?: 'AgencyPublicAddress' }
          & Pick<AgencyPublicAddress, 'postalCode' | 'prefecture' | 'city' | 'chome' | 'streetAddress' | 'buildingName'>
        )>, businessHour?: Maybe<(
          { __typename?: 'AgencyPublicBusinessHour' }
          & Pick<AgencyPublicBusinessHour, 'start' | 'end'>
        )> }
      )>, serviceFee?: Maybe<(
        { __typename?: 'AgencyServiceFee' }
        & Pick<AgencyServiceFee, 'feeType' | 'value'>
      )> }
    )> }
  )> }
);

export type UpdateUserMutationVariables = Exact<{
  email: Scalars['String'];
  firstName: Scalars['String'];
  lastName: Scalars['String'];
}>;


export type UpdateUserMutation = (
  { __typename?: 'Mutation' }
  & { updateUser: (
    { __typename?: 'User' }
    & Pick<User, '_id' | 'createdAt' | 'email' | 'firstName' | 'lastName' | 'locale' | 'updatedAt'>
    & { account?: Maybe<(
      { __typename?: 'UserAccount' }
      & { verification?: Maybe<(
        { __typename?: 'UserAccountVerification' }
        & Pick<UserAccountVerification, 'verified'>
      )> }
    )> }
  ) }
);

export type BaseAccessFragment = (
  { __typename?: 'PropertyAccess' }
  & { stations?: Maybe<Array<Maybe<(
    { __typename?: 'PropertyAccessStations' }
    & Pick<PropertyAccessStations, 'lineId' | 'stationId'>
    & { distance?: Maybe<(
      { __typename?: 'PropertyAccessStationsDistance' }
      & Pick<PropertyAccessStationsDistance, 'bus' | 'walk'>
    )> }
  )>>> }
);

export type BaseAddressDetailFragment = (
  { __typename?: 'PropertyAddress' }
  & Pick<PropertyAddress, 'prefecture' | 'city' | 'chome'>
);

export type CloneRoomMutationVariables = Exact<{
  propertyId: Scalars['MongoID'];
  code: Scalars['String'];
}>;


export type CloneRoomMutation = (
  { __typename?: 'Mutation' }
  & { cloneRoom?: Maybe<(
    { __typename?: 'PropertyRooms' }
    & BaseRoomFragment
  )> }
);

export type CreatePropertyMutationVariables = Exact<{
  input: CreateOnePropertyInput;
}>;


export type CreatePropertyMutation = (
  { __typename?: 'Mutation' }
  & { createProperty?: Maybe<(
    { __typename?: 'CreateOnePropertyPayload' }
    & { record?: Maybe<(
      { __typename?: 'Property' }
      & Pick<Property, '_id'>
    )> }
  )> }
);

export type DeletePropertyMutationVariables = Exact<{
  id: Scalars['MongoID'];
}>;


export type DeletePropertyMutation = (
  { __typename?: 'Mutation' }
  & { deleteProperty?: Maybe<(
    { __typename?: 'RemoveByIdPropertyPayload' }
    & { record?: Maybe<(
      { __typename?: 'Property' }
      & Pick<Property, '_id'>
    )> }
  )> }
);

export type DeleteRoomMutationVariables = Exact<{
  propertyId: Scalars['MongoID'];
  code: Scalars['String'];
}>;


export type DeleteRoomMutation = (
  { __typename?: 'Mutation' }
  & { deleteRoom?: Maybe<(
    { __typename?: 'PropertyRooms' }
    & BaseRoomFragment
  )> }
);

export type GetLocationsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetLocationsQuery = (
  { __typename?: 'Query' }
  & { locations?: Maybe<Array<Maybe<(
    { __typename?: 'Area' }
    & { items?: Maybe<Array<Maybe<(
      { __typename?: 'Area' }
      & { items?: Maybe<Array<Maybe<(
        { __typename?: 'Area' }
        & Pick<Area, 'postalCode'>
        & { items?: Maybe<Array<Maybe<(
          { __typename?: 'Area' }
          & AreaFieldsFragment
        )>>> }
        & AreaFieldsFragment
      )>>> }
      & AreaFieldsFragment
    )>>> }
    & AreaFieldsFragment
  )>>> }
);

export type AreaFieldsFragment = (
  { __typename?: 'Area' }
  & Pick<Area, 'name' | 'en'>
  & { code: Area['_id'] }
);

export type GetPropertiesQueryVariables = Exact<{
  page?: Maybe<Scalars['Int']>;
  filter?: Maybe<FilterFindManyPropertyInput>;
  sort?: Maybe<SortFindManyPropertyInput>;
}>;


export type GetPropertiesQuery = (
  { __typename?: 'Query' }
  & { properties?: Maybe<(
    { __typename?: 'PropertyPagination' }
    & Pick<PropertyPagination, 'count'>
    & { pageInfo: (
      { __typename?: 'PaginationInfo' }
      & Pick<PaginationInfo, 'currentPage' | 'perPage' | 'itemCount' | 'hasPreviousPage' | 'hasNextPage'>
    ), items?: Maybe<Array<(
      { __typename?: 'Property' }
      & Pick<Property, '_id' | 'buildingName' | 'updatedAt' | 'prefecturePath'>
      & { rooms: Array<Maybe<(
        { __typename?: 'PropertyRooms' }
        & Pick<PropertyRooms, 'code' | 'roomName' | 'visibility' | 'numberOfRoom' | 'layout' | 'monthlyFee' | 'inquiryCount' | 'pageViews'>
        & { availability: (
          { __typename?: 'PropertyRoomsAvailability' }
          & Pick<PropertyRoomsAvailability, 'now'>
        ) }
      )>> }
    )>> }
  )> }
);

export type GetPropertyQueryVariables = Exact<{
  id: Scalars['MongoID'];
}>;


export type GetPropertyQuery = (
  { __typename?: 'Query' }
  & { propertyById?: Maybe<(
    { __typename?: 'Property' }
    & PropertyInputFragmentFragment
  )> }
);

export type PropertyInputFragmentFragment = (
  { __typename?: 'Property' }
  & { rooms: Array<Maybe<(
    { __typename?: 'PropertyRooms' }
    & BaseRoomFragment
  )>> }
  & BasePropertyFragmentFragment
);

export type GetServiceLinesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetServiceLinesQuery = (
  { __typename?: 'Query' }
  & { serviceLines: Array<(
    { __typename?: 'ServiceLine' }
    & Pick<ServiceLine, 'name' | 'en'>
    & { code: ServiceLine['_id'] }
    & { items: Array<Maybe<(
      { __typename?: 'Station' }
      & Pick<Station, 'name' | 'en'>
      & { code: Station['_id'] }
    )>> }
  )> }
);

export type GetStationsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetStationsQuery = (
  { __typename?: 'Query' }
  & { stations: Array<(
    { __typename?: 'Station' }
    & Pick<Station, 'name' | 'en'>
    & { code: Station['_id'] }
    & { items: Array<Maybe<(
      { __typename?: 'ServiceLine' }
      & Pick<ServiceLine, 'name' | 'en'>
      & { code: ServiceLine['_id'] }
    )>> }
  )> }
);

export type BasePropertyFragmentFragment = (
  { __typename?: 'Property' }
  & Pick<Property, '_id' | 'agencyId' | 'buildingName' | 'buildingType' | 'builtDate' | 'maxFloor' | 'structure' | 'languageComment' >
  & { address: (
    { __typename?: 'PropertyAddress' }
    & Pick<PropertyAddress, 'line' | 'postalCode' | 'prefecture' | 'city' | 'chome' | 'coord'>
  ), images: (
    { __typename?: 'PropertyImages' }
    & Pick<PropertyImages, 'collections'>
  ), access?: Maybe<(
    { __typename?: 'PropertyAccess' }
    & { stations?: Maybe<Array<Maybe<(
      { __typename?: 'PropertyAccessStations' }
      & Pick<PropertyAccessStations, 'lineId' | 'stationId'>
      & { distance?: Maybe<(
        { __typename?: 'PropertyAccessStationsDistance' }
        & Pick<PropertyAccessStationsDistance, 'walk' | 'bus'>
      )> }
    )>>> }
  )> }
);

export type BaseRoomFragment = (
  { __typename?: 'PropertyRooms' }
  & Pick<PropertyRooms, 'code' | 'createdAt' | 'updatedAt' | 'roomName' | 'size' | 'floor' | 'layout' | 'numberOfRoom' | 'mainWindow' | 'contractType' | 'transactionType' | 'contractLength' | 'visibility' | 'tags' | 'companyManagement' | 'telephone'>
  & { availability: (
    { __typename?: 'PropertyRoomsAvailability' }
    & Pick<PropertyRoomsAvailability, 'status' | 'now' | 'enquire'>
  ), facilities?: Maybe<(
    { __typename?: 'PropertyRoomsFacilities' }
    & Pick<PropertyRoomsFacilities, 'basic' | 'building' | 'other'>
  )>, images: (
    { __typename?: 'PropertyRoomsImages' }
    & Pick<PropertyRoomsImages, 'featured' | 'floorPlan' | 'collections'>
  ), fees: (
    { __typename?: 'PropertyRoomsFees' }
    & { monthly: (
      { __typename?: 'PropertyRoomsFeesMonthly' }
      & Pick<PropertyRoomsFeesMonthly, 'rentFee' | 'maintenanceFee'>
    ), moveIn: (
      { __typename?: 'PropertyRoomsFeesMoveIn' }
      & { agencyFee: (
        { __typename?: 'PropertyRoomsFeesMoveInDeposit' }
        & FeeFragment
      ), guarantorFee: (
        { __typename?: 'PropertyRoomsFeesMoveInDeposit' }
        & FeeFragment
      ), keyMoney: (
        { __typename?: 'PropertyRoomsFeesMoveInDeposit' }
        & FeeFragment
      ), contractRenewalFee: (
        { __typename?: 'PropertyRoomsFeesMoveInDeposit' }
        & FeeFragment
      ), deposit: (
        { __typename?: 'PropertyRoomsFeesMoveInDeposit' }
        & FeeFragment
      ) }
    ), others?: Maybe<Array<Maybe<(
      { __typename?: 'PropertyRoomsFeesOthers' }
      & Pick<PropertyRoomsFeesOthers, 'feeType' | 'chargeTiming' | 'value'>
    )>>> }
  ) }
);

export type FeeFragment = (
  { __typename?: 'PropertyRoomsFeesMoveInDeposit' }
  & Pick<PropertyRoomsFeesMoveInDeposit, 'feeType' | 'value'>
);

export type UpdatePropertyMutationVariables = Exact<{
  id: Scalars['MongoID'];
  record: UpdateByIdPropertyInput;
}>;


export type UpdatePropertyMutation = (
  { __typename?: 'Mutation' }
  & { updateProperty?: Maybe<(
    { __typename?: 'UpdateByIdPropertyPayload' }
    & { record?: Maybe<(
      { __typename?: 'Property' }
      & PropertyFragmentFragment
    )> }
  )> }
);

export type PropertyFragmentFragment = (
  { __typename?: 'Property' }
  & { agency?: Maybe<(
    { __typename?: 'Agency' }
    & { public?: Maybe<(
      { __typename?: 'AgencyPublic' }
      & Pick<AgencyPublic, 'name'>
    )> }
  )> }
  & BasePropertyFragmentFragment
);

export type UpdateRoomVisibilityMutationVariables = Exact<{
  propertyId: Scalars['MongoID'];
  code: Scalars['String'];
  value: EnumPropertyRoomsVisibility;
}>;


export type UpdateRoomVisibilityMutation = (
  { __typename?: 'Mutation' }
  & Pick<Mutation, 'updateRoomVisibility'>
);

export type GetAgenciesQueryVariables = Exact<{
  filter?: Maybe<FilterFindManyAgencyInput>;
  perPage?: Maybe<Scalars['Int']>;
  page?: Maybe<Scalars['Int']>;
}>;


export type GetAgenciesQuery = (
  { __typename?: 'Query' }
  & { agencies?: Maybe<(
    { __typename?: 'AgencyPagination' }
    & Pick<AgencyPagination, 'count'>
    & { pageInfo: (
      { __typename?: 'PaginationInfo' }
      & Pick<PaginationInfo, 'currentPage' | 'perPage' | 'itemCount' | 'hasNextPage' | 'hasPreviousPage'>
    ), items?: Maybe<Array<(
      { __typename?: 'Agency' }
      & Pick<Agency, '_id' | 'createdAt' | 'updatedAt' | 'inquiryCount' | 'publishedRoomCount' | 'totalRoomCount'>
      & { serviceFee?: Maybe<(
        { __typename?: 'AgencyServiceFee' }
        & Pick<AgencyServiceFee, 'feeType' | 'value'>
      )>, basic?: Maybe<(
        { __typename?: 'AgencyBasic' }
        & Pick<AgencyBasic, 'companyName'>
        & { license?: Maybe<(
          { __typename?: 'AgencyBasicLicense' }
          & Pick<AgencyBasicLicense, 'licenseType' | 'number' | 'issueNumber'>
          & { expiryDate?: Maybe<(
            { __typename?: 'AgencyBasicLicenseExpiryDate' }
            & Pick<AgencyBasicLicenseExpiryDate, 'from' | 'until'>
          )> }
        )> }
      )>, public?: Maybe<(
        { __typename?: 'AgencyPublic' }
        & Pick<AgencyPublic, 'name' | 'imageUrl' | 'phone' | 'email' | 'availableLanguages' | 'closeDays' | 'website'>
        & { address?: Maybe<(
          { __typename?: 'AgencyPublicAddress' }
          & Pick<AgencyPublicAddress, 'postalCode' | 'prefecture' | 'city' | 'streetAddress' | 'buildingName'>
        )>, businessHour?: Maybe<(
          { __typename?: 'AgencyPublicBusinessHour' }
          & Pick<AgencyPublicBusinessHour, 'start' | 'end'>
        )> }
      )> }
    )>> }
  )> }
);

export type GetAgencyQueryVariables = Exact<{
  id: Scalars['MongoID'];
}>;


export type GetAgencyQuery = (
  { __typename?: 'Query' }
  & { agencyById?: Maybe<(
    { __typename?: 'Agency' }
    & Pick<Agency, '_id' | 'createdAt' | 'updatedAt'>
    & { serviceFee?: Maybe<(
      { __typename?: 'AgencyServiceFee' }
      & Pick<AgencyServiceFee, 'feeType' | 'value'>
    )>, basic?: Maybe<(
      { __typename?: 'AgencyBasic' }
      & Pick<AgencyBasic, 'companyName'>
      & { license?: Maybe<(
        { __typename?: 'AgencyBasicLicense' }
        & Pick<AgencyBasicLicense, 'haveLicense' | 'licenseType' | 'number' | 'issueNumber'>
        & { expiryDate?: Maybe<(
          { __typename?: 'AgencyBasicLicenseExpiryDate' }
          & Pick<AgencyBasicLicenseExpiryDate, 'from' | 'until'>
        )> }
      )> }
    )>, public?: Maybe<(
      { __typename?: 'AgencyPublic' }
      & Pick<AgencyPublic, 'name' | 'imageUrl' | 'phone' | 'email' | 'availableLanguages' | 'closeDays' | 'website'>
      & { address?: Maybe<(
        { __typename?: 'AgencyPublicAddress' }
        & Pick<AgencyPublicAddress, 'postalCode' | 'prefecture' | 'city' | 'chome' | 'streetAddress' | 'buildingName'>
      )>, businessHour?: Maybe<(
        { __typename?: 'AgencyPublicBusinessHour' }
        & Pick<AgencyPublicBusinessHour, 'start' | 'end'>
      )> }
    )> }
  )> }
);

export type BaseAgencyFragment = (
  { __typename?: 'Agency' }
  & Pick<Agency, '_id' | 'updatedAt' | 'createdAt'>
  & { basic?: Maybe<(
    { __typename?: 'AgencyBasic' }
    & Pick<AgencyBasic, 'companyName'>
    & { license?: Maybe<(
      { __typename?: 'AgencyBasicLicense' }
      & Pick<AgencyBasicLicense, 'licenseType' | 'number' | 'issueNumber'>
      & { expiryDate?: Maybe<(
        { __typename?: 'AgencyBasicLicenseExpiryDate' }
        & Pick<AgencyBasicLicenseExpiryDate, 'from' | 'until'>
      )> }
    )> }
  )>, public?: Maybe<(
    { __typename?: 'AgencyPublic' }
    & Pick<AgencyPublic, 'name' | 'imageUrl' | 'phone' | 'email' | 'availableLanguages' | 'closeDays' | 'website'>
    & { address?: Maybe<(
      { __typename?: 'AgencyPublicAddress' }
      & Pick<AgencyPublicAddress, 'postalCode' | 'prefecture' | 'city' | 'streetAddress' | 'buildingName'>
    )>, businessHour?: Maybe<(
      { __typename?: 'AgencyPublicBusinessHour' }
      & Pick<AgencyPublicBusinessHour, 'start' | 'end'>
    )> }
  )> }
);

export type GetPropertyByRoomCodeQueryVariables = Exact<{
  roomCode: Scalars['String'];
}>;


export type GetPropertyByRoomCodeQuery = (
  { __typename?: 'Query' }
  & { property?: Maybe<(
    { __typename?: 'Property' }
    & Pick<Property, '_id' | 'buildingName' | 'buildingType' | 'builtDate' | 'maxFloor' | 'structure' | 'languageComment'>
    & { address: (
      { __typename?: 'PropertyAddress' }
      & Pick<PropertyAddress, 'line' | 'coord'>
    ), images: (
      { __typename?: 'PropertyImages' }
      & Pick<PropertyImages, 'collections'>
    ), addressDetail?: Maybe<(
      { __typename?: 'PropertyAddress' }
      & BaseAddressDetailFragment
    )>, access?: Maybe<(
      { __typename?: 'PropertyAccess' }
      & { stations?: Maybe<Array<Maybe<(
        { __typename?: 'PropertyAccessStations' }
        & Pick<PropertyAccessStations, 'lineId' | 'stationId' | 'lineName' | 'stationName'>
        & { distance?: Maybe<(
          { __typename?: 'PropertyAccessStationsDistance' }
          & Pick<PropertyAccessStationsDistance, 'bus' | 'walk'>
        )> }
      )>>> }
    )>, rooms: Array<Maybe<(
      { __typename?: 'PropertyRooms' }
      & BaseRoomFragment
    )>>, agency?: Maybe<(
      { __typename?: 'Agency' }
      & BaseAgencyFragment
    )> }
  )> }
);

export type Unnamed_1_QueryVariables = Exact<{
  type: Scalars['String'];
  path: Scalars['String'];
}>;


export type Unnamed_1_Query = (
  { __typename?: 'Query' }
  & { s3UploadSignedUrl?: Maybe<(
    { __typename?: 'S3UploadSignedUrl' }
    & Pick<S3UploadSignedUrl, 'url'>
  )> }
);

export type GetFeaturedQueryVariables = Exact<{ [key: string]: never; }>;


export type GetFeaturedQuery = (
  { __typename?: 'Query' }
  & { setting?: Maybe<(
    { __typename?: 'Setting' }
    & Pick<Setting, 'featured'>
  )> }
);

export type GetUserQueryVariables = Exact<{ [key: string]: never; }>;


export type GetUserQuery = (
  { __typename?: 'Query' }
  & { user: (
    { __typename?: 'User' }
    & Pick<User, 'email' | 'createdAt' | 'updatedAt'>
  ) }
);

export type GetRejImportResultByIdQueryVariables = Exact<{
  id: Scalars['MongoID'];
}>;


export type GetRejImportResultByIdQuery = (
  { __typename?: 'Query' }
  & { rejImportResultById?: Maybe<(
    { __typename?: 'REJImportResult' }
    & Pick<RejImportResult, '_id' | 'createdAt' | 'processedRow' | 'importedRow' | 'removedProperty' | 'agencyId'>
    & { parsingErrors?: Maybe<Array<Maybe<(
      { __typename?: 'REJImportResultParsingErrors' }
      & Pick<RejImportResultParsingErrors, 'propertyId'>
      & { items?: Maybe<Array<Maybe<(
        { __typename?: 'REJImportResultParsingErrorsItems' }
        & Pick<RejImportResultParsingErrorsItems, 'colName' | 'message'>
      )>>> }
    )>>> }
  )> }
);

export type GetRejImportResultsQueryVariables = Exact<{
  page?: Maybe<Scalars['Int']>;
}>;


export type GetRejImportResultsQuery = (
  { __typename?: 'Query' }
  & { rejImportResults?: Maybe<(
    { __typename?: 'REJImportResultPagination' }
    & Pick<RejImportResultPagination, 'count'>
    & { pageInfo: (
      { __typename?: 'PaginationInfo' }
      & Pick<PaginationInfo, 'currentPage' | 'perPage' | 'itemCount' | 'hasNextPage' | 'hasPreviousPage'>
    ), items?: Maybe<Array<(
      { __typename?: 'REJImportResult' }
      & Pick<RejImportResult, '_id' | 'createdAt' | 'processedRow' | 'importedRow' | 'removedProperty'>
    )>> }
  )> }
);

export type GetInhouseAgencyIdsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetInhouseAgencyIdsQuery = (
  { __typename?: 'Query' }
  & { setting?: Maybe<(
    { __typename?: 'Setting' }
    & Pick<Setting, 'inHouseAgencyIds'>
  )> }
);

export type Affiliate = {
  __typename?: 'Affiliate';
  _id: Scalars['MongoID'];
  name: Scalars['String'];
  code: Scalars['String'];
  updatedAt?: Maybe<Scalars['Date']>;
  createdAt?: Maybe<Scalars['Date']>;
};

export type AffiliatePagination = {
  __typename?: 'AffiliatePagination';
  count?: Maybe<Scalars['Int']>;
  items?: Maybe<Array<Affiliate>>;
  pageInfo: PaginationInfo;
};
