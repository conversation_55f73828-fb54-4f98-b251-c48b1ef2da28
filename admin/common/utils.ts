import get from 'lodash/get'
import minBy from 'lodash/minBy'
import find from 'lodash/find'
import {
  Maybe,
  Property,
  PropertyAccessStations,
  PropertyRooms,
} from '~/types/types'
import { Option } from '~/types'

const formatter = new Intl.NumberFormat('ja-JP', {
  style: 'currency',
  currency: 'JPY',
})

export const getNearestStation = (
  stations: Array<Maybe<PropertyAccessStations>>
) => {
  // Remove access which doesn't have stationId
  const filteredStations = stations.filter(
    (station) => station?.stationId && station?.stationId !== ''
  )
  return minBy(filteredStations, 'distance.walk')
}

export const getStationAccessMultiple = (
  stationsMultiple: Array<PropertyAccessStations>
): Array<string | null> => {
  if (!stationsMultiple) return []
  return stationsMultiple
    .map(function (station: PropertyAccessStations) {
      if (!station.lineId || !station.stationId) return null
      const showWalk = get(station, 'distance.walk', 0) > 0
      const minute = station.distance![showWalk ? 'walk' : 'bus']
      return `${station.lineName} ${station.stationName} Sta. ${minute} min by walk`
    })
    .filter((item) => item)
}

// FIXME: check it, is it deprecated?
// export const getLocationDetailCard = (detail: PropertyLocation) => {
//   return `${detail.city}, ${detail.prefecture}`
// }

// export const getLocationDetail = (detail: PropertyLocation) => {
//   return `${detail.area}, ${detail.city}, ${detail.prefecture}`
// }

export const getPropertyName = (property: Property) => {
  const nearest = getNearestStation(property?.access?.stations || [])
  const stationName = nearest?.stationName?.concat(' Sta. ')
  return `${stationName || ''}`
}

export const getRoomName = (property: Property, roomCode: string) => {
  const nearest = getNearestStation(property?.access?.stations || [])
  const room = find(property.rooms, { code: roomCode })
  const stationName = nearest?.stationName?.concat(' Sta. ')
  return `${stationName || ''}${getRoomLayout(room)} ${room?.roomName}`
}

export const getRoomLayout = (room?: PropertyRooms | null) =>
  `${room?.numberOfRoom}${room?.layout}`

export const restoreCityCode = (code: string): string => {
  const cityMatch = code ? code.match(/(r\d+)(p\d+)(ca\d+)(c\d+)/) : null

  if (cityMatch) {
    return cityMatch.slice(1).join('__')
  }

  return code
}

export const formatPrice = (price: number): string => {
  return formatter.format(price)
}

export const flatIntoDict = (acc: any, item: Option): object => {
  if (item.items) {
    return {
      ...acc,
      [item.code]: item.name,
      ...item.items.reduce(flatIntoDict, {}),
    }
  }

  return {
    ...acc,
    [item.code]: item.name,
  }
}
