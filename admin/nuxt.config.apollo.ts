import { onError } from '@apollo/client/link/error'
import { ApolloLink } from '@apollo/client/core/'
import { Context } from '@nuxt/types'
import get from 'lodash/get'
import omitDeep from 'omit-deep-lodash'
import { getMainDefinition } from 'apollo-utilities'
import { OperationDefinitionNode } from 'graphql'

export default (context: Context) => {
  /**
   * Global error handler,
   * when we get authorized message, it means the session is invalid
   * Logout then redirect to login page
   */
  const errorLink = onError(({ graphQLErrors }) => {
    const errorMessage = get(graphQLErrors, '[0].message', '')
    const isUnauthorized = errorMessage.includes('authorized')
    if (!isUnauthorized) return
    context.$apolloHelpers.onLogout().then(() => {
      context.app.$accessor.reset()
      context.redirect('/')
    })
  })

  /**
   * Used to remove __typename in mutation variables input
   * refer to https://github.com/apollographql/apollo-feature-requests/issues/6#issuecomment-576687277
   */
  const cleanTypenameLink = new ApolloLink((operation, forward) => {
    const keysToOmit = ['__typename']
    const def = getMainDefinition(operation.query)
    if (def && (<OperationDefinitionNode>def).operation === 'mutation') {
      operation.variables = omitDeep(operation.variables, keysToOmit)
    }
    return forward ? forward(operation) : null
  })

  return {
    link: ApolloLink.from([errorLink, cleanTypenameLink]),
    httpEndpoint: context.$config.gqlUrl,
    authenticationType: 'Bearer',
  }
}
