{"name": "admin", "version": "1.8.1", "private": true, "scripts": {"dev": "nuxt", "docker-local": "yarn && yarn dev", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "gql": "graphql-codegen --config config.yml", "lint:ts": "eslint --ext \".ts,.vue\" .", "lint:style": "stylelint \"**/*.{vue,css,scss}\"", "lint": "yarn lint:ts && yarn lint:style"}, "engines": {"node": "14.16.0"}, "dependencies": {"@nuxtjs/apollo": "^4.0.1-rc.5", "@nuxtjs/toast": "^3.3.1", "apollo-utilities": "^1.3.4", "core-js": "^3.8.3", "dayjs": "^1.10.4", "flat": "**************:InboundPlatform/common-flat.git#pass-val", "gmap-vue": "^1.5.0", "html2canvas": "^1.0.0-rc.7", "jspdf": "^2.3.1", "lodash": "^4.17.21", "mitt": "^2.1.0", "nuxt": "^2.14.12", "nuxt-typed-vuex": "^0.2.0", "omit-deep-lodash": "^1.1.5", "qrcode": "^1.4.4", "uuid": "^8.3.2", "v-click-outside": "^3.1.2", "vue-clickaway2": "^2.3.2", "vue-html2pdf": "^1.8.0", "vue-qrcode": "^0.4.0", "vue-select": "^3.11.2", "vue2-datepicker": "^3.9.2", "vuedraggable": "^2.24.3", "vuex-persist": "^3.1.3", "yup": "^0.32.9"}, "devDependencies": {"@fortawesome/free-solid-svg-icons": "^5.15.3", "@graphql-codegen/cli": "^1.21.2", "@graphql-codegen/typescript": "^1.21.1", "@graphql-codegen/typescript-graphql-files-modules": "^1.18.1", "@graphql-codegen/typescript-operations": "^1.17.15", "@nuxt/types": "^2.14.12", "@nuxt/typescript-build": "^2.0.4", "@nuxtjs/dotenv": "^1.4.1", "@nuxtjs/eslint-config-typescript": "^5.0.0", "@nuxtjs/eslint-module": "^3.0.2", "@nuxtjs/fontawesome": "^1.1.2", "@nuxtjs/stylelint-module": "^4.0.0", "@types/flat": "^5.0.1", "@types/lodash": "^4.14.168", "@types/omit-deep-lodash": "^1.1.1", "@types/uuid": "^8.3.0", "@types/vue-clickaway": "^2.2.0", "@types/vue-select": "^3.11.1", "@types/vue2-datepicker": "^3.3.1", "babel-eslint": "^10.1.0", "eslint": "^7.18.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-nuxt": "^2.0.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.5.0", "fibers": "^5.0.0", "prettier": "^2.2.1", "sass": "^1.32.8", "sass-loader": "10", "stylelint": "^13.12.0", "stylelint-config-prettier": "^8.0.2", "stylelint-config-standard": "^20.0.0", "stylelint-scss": "^3.19.0"}}