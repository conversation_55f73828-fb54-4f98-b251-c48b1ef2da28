import get from 'lodash/get'
import {
  EnumPropertyBuildingType as BuildingType,
  EnumPropertyRoomsVisibility as Visibility,
  EnumPropertyStructure as Structure,
  EnumPropertyRoomsTransactionType as TransactionType,
  EnumPropertyRoomsAvailabilityStatus as AvailabilityStatus,
  EnumPropertyRoomsContractType as ContractType,
  EnumPropertyRoomsFeesMoveInDepositFeeType as MoveInFeeType,
  EnumPropertyRoomsMainWindow as MainWindow,
  EnumPropertyRoomsFeesOthersFeeType as OthersFeeType,
  EnumPropertyRoomsFeesOthersChargeTiming as OthersChargeTiming,
} from './types/types'

export const dictionary = {
  property: {
    buildingType: {
      [BuildingType.Apartment]: 'アパート／マンション',
      [BuildingType.House]: '一軒家',
      [BuildingType.ShareHouse]: 'シェアハウス',
      [BuildingType.MonthlyApartment]: 'マンスリーマンション',
    },
    structure: {
      [Structure.Wooden]: '木造',
      [Structure.LightSteel]: '軽量鉄骨',
      [Structure.HeavySteel]: '鉄筋コンクリート',
      [Structure.Steel]: '鉄骨造',
      [Structure.ReinforceConcrete]: '鉄筋鉄骨コンクリート',
      [Structure.SteelFramedReinforcedConcreteBlock]: 'ブロック,',
      [Structure.Other]: 'その他',
    },
  },
  room: {
    visibility: {
      [Visibility.Public]: '公開中',
      [Visibility.Full]: '成約済み',
      [Visibility.Private]: '非公開(非推奨)',
    },
    mainWindow: {
      [MainWindow.Unknown]: '不明',
      [MainWindow.East]: '東',
      [MainWindow.Southeast]: '南東',
      [MainWindow.South]: '南',
      [MainWindow.Southwest]: '南西',
      [MainWindow.West]: '西',
      [MainWindow.Northwest]: '北西',
      [MainWindow.North]: '北',
      [MainWindow.Northeast]: '北東',
    },
    availabilityStatus: {
      [AvailabilityStatus.Available]: '空室',
      [AvailabilityStatus.AvailableSoon]: '退去予定',
      [AvailabilityStatus.Renovation]: '改装中',
      [AvailabilityStatus.UnderConstruction]: '建築中',
    },
    transactionType: {
      [TransactionType.Agency]: '仲介',
      [TransactionType.Owner]: '貸主',
      [TransactionType.Agent]: '代理',
    },
    contractType: {
      [ContractType.Standard]: '普通賃貸借契約',
      [ContractType.FixedTerm]: '定期借家契約',
    },
    feeType: {
      [MoveInFeeType.MonthlyMultiplier]: 'ヶ月',
      [MoveInFeeType.FixedPrice]: '円',
    },
    otherFeeType: {
      [OthersFeeType.KeyReplacement]: '鍵交換費用',
      [OthersFeeType.Support_24Hours]: '24時間サポート',
      [OthersFeeType.Cleaning]: 'クリーニング代',
      [OthersFeeType.Administrative]: '事務手数料',
      [OthersFeeType.Disinfection]: '除菌消臭代',
      [OthersFeeType.FireInsurance]: '火災保険',
      [OthersFeeType.Community]: '環境維持費',
      [OthersFeeType.Other]: 'その他',
    },
    otherFeeChargeTiming: {
      [OthersChargeTiming.MoveIn]: '初期時',
      [OthersChargeTiming.MoveOut]: '退去時',
      [OthersChargeTiming.Monthly]: '月額',
      [OthersChargeTiming.Yearly]: '年間',
    },
  },
}

interface Option {
  value: string
  label: string
}

export function getOptions(path: string): Option[] {
  const indexes = get(dictionary, path)
  return Object.keys(indexes).map((key) => ({
    value: key,
    label: indexes[key],
  }))
}
