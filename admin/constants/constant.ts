import {
  EnumAgencyBasicLicenseLicenseType,
  EnumPropertyBuildingType as BuildingType,
  EnumPropertyRoomsMainWindow as MainWindow,
  EnumPropertyStructure as Structure,
} from '~/types/types'

export const mainWindow = {
  [MainWindow.East]: 'East',
  [MainWindow.Southeast]: 'Southeast',
  [MainWindow.South]: 'South',
  [MainWindow.Southwest]: 'Southwest',
  [MainWindow.West]: 'West',
  [MainWindow.Northwest]: 'Northwest',
  [MainWindow.North]: 'North',
  [MainWindow.Northeast]: 'Northeast',
}

export const buildingType = {
  [BuildingType.Apartment]: 'Apartment',
  [BuildingType.House]: 'House',
  [BuildingType.ShareHouse]: 'Share House',
  [BuildingType.MonthlyApartment]: 'Monthly Apartment',
}

export const structure = {
  [Structure.Wooden]: 'Wooden',
  [Structure.LightSteel]: 'Light Steel',
  [Structure.HeavySteel]: 'Heavy Steel',
  [Structure.ReinforceConcrete]: 'Reinforce Concrete',
  [Structure.SteelFramedReinforcedConcreteBlock]:
    'Steel Framed Reinforced Concrete Block',
  [Structure.Other]: 'Other',
  [Structure.Steel]: 'Steel',
}

export const facilities = {
  free_wifi: 'Free Internet',
  air_conditioner: 'Air Conditioner',
  floor_heating_system: 'Floor Heating System',
  bath_toilet_separated: 'Bath / Toilet Separated',
  wash_basin: 'Wash Basin',
  bath_reheating_system: 'Bath Reheating System',
  bath_dehumidifier: 'Bath Dehumidifier',
  jacuzzi: 'Jacuzzi',
  mist_sauna: 'Mist Sauna',
  bidet: 'Washlet Toilet',
  washing_machine: 'Washing Machine',
  washing_machine_space: 'Washing Machine Inside',
  electric_stove: 'Induction Stove',
  gas_stove: 'Gas Stove',
  builtin_kitchen: 'Built-in Kitchen',
  counter_kitchen: 'Counter Kitchen',
  electric_water_heater: 'Electric Water Heater',
  gas_water_heater: 'Gas Water Heater',
  loft: 'Loft',
  walkin_closet: 'Walk-in Closet',
  auto_lock: 'Self Locking Entrance',
  parking: 'Parking lot',
  motorbike_parking: 'Moterbike Parking ',
  biycle_parking: 'Bicycle Parking ',
  delivery_box: 'Delivery Box',
  elevator: 'Elevator',
  concierge: 'Concierge',
  balcony: 'Balcony',
  communal_garden: 'Communal Garden',
  private_garden: 'Private Garden',
  allow_pet: 'Pet Allowed',
  allow_musical_instrument: 'Musical Instruments Allowed',
  allow_room_sharing: 'Room Sharing Allowed',
  corner_room: 'Corner Room',
  barrier_free: 'Barrior Free',
}

export const licenseType = {
  [EnumAgencyBasicLicenseLicenseType.MinisterLitt]:
    'Licensed by Minister of Land, Infrastructure, Transport, and Tourism ({licenseNumber}) {issueNumber}',
  [EnumAgencyBasicLicenseLicenseType.GovernorTokyo]:
    'Licensed by Governor of Tokyo License ({licenseNumber}) {issueNumber}',
}

export enum FilterKey {
  custom = 'custom',
  today = 'today',
  yesterday = 'yesterday',
  last7Days = 'last7Days',
  last4Weeks = 'last4Weeks',
  last3Months = 'last3Months',
  last12Months = 'last12Months',
}
