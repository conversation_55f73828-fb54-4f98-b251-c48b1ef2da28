import {
  EnumFormFormRentOccupation as Occupation,
  EnumFormFormRentVisaStatus as VisaStatus,
  EnumFormFormRentWhenToMoveIn as WhenToMoveIn,
  EnumFormFormRentOtherPreferences as OtherPreferences,
  EnumFormFormBuyPurpose as Purpose,
  EnumFormFormBuyPaymentMethod as PaymentMethod,
  EnumFormFormBuyPropertyType as PropertyType,
} from '~/types/types'

export const languages = {
  en: 'English',
  jp: '日本語',
  zh: '中文',
  kr: '韓国語',
}

export const occupation = {
  [Occupation.CompanyEmployee]: 'Company Employee',
  [Occupation.PartTimer]: 'Part-Timer',
  [Occupation.Student]: 'Student',
  [Occupation.Unemployed]: 'Unemployed',
  [Occupation.Other]: 'Other',
}

export const visaStatus = {
  [VisaStatus.WorkVisa]: 'Work Visa',
  [VisaStatus.Student]: 'Student',
  [VisaStatus.LongTermResident]: 'Long Term Resident',
  [VisaStatus.PermanentResident]: 'Permanent Resident',
  [VisaStatus.JapaneseNationality]: 'Japanese Nationality',
  [VisaStatus.Other]: 'Other',
}

export const rentBudget = {
  num_in_k: '¥{num},000',
  over_num_in_k: '¥{num},000 or more',
}

export const whenToMoveIn = {
  [WhenToMoveIn.Asap]: 'As soon as possible',
  [WhenToMoveIn.In_1Or_2Weeks]: 'In 1 week - 2 weeks',
  [WhenToMoveIn.InAMonth]: 'In a month',
  [WhenToMoveIn.In_2Months]: 'In 2 months',
  [WhenToMoveIn.OnceFindOne]: 'When I find a nice one',
  [WhenToMoveIn.Other]: 'Other',
}

export const otherPreferences = {
  [OtherPreferences.Not_1stFloor]: 'Not the first floor',
  [OtherPreferences.SeparateBathToilet]: 'Bath and Toilet separated',
  [OtherPreferences.AutoLockEntrance]: 'Auto locking entrance',
  [OtherPreferences.WashStand]: 'Wash stand',
  [OtherPreferences.BicyclePark]: 'Bicycle parking space',
  [OtherPreferences.PetFriendly]: 'Pet friendly',
  [OtherPreferences.PackageDeliveryBox]: 'Package Delivery Box',
  [OtherPreferences.Elevator]: 'Elevator',
  [OtherPreferences.Balcony]: 'Balcony',
  [OtherPreferences.NoKeyMoney]: 'No key money',
  [OtherPreferences.NoDeposit]: 'No deposit',
  [OtherPreferences.Other]: 'Other',
}

export const purpose = {
  [Purpose.OwnUse]: 'Own use',
  [Purpose.Investment]: 'Investment',
  [Purpose.OwnUseInvestment]: 'Own use & Investment',
}

export const propertyType = {
  [PropertyType.MansionApartment]: 'Mansion/Apartment',
  [PropertyType.House]: 'House',
  [PropertyType.ResidentialBuilding]: 'Residential Building',
  [PropertyType.OfficeBuilding]: 'Office Building',
  [PropertyType.Land]: 'Land',
  [PropertyType.Other]: 'Other',
}

export const paymentMethod = {
  [PaymentMethod.Cash]: 'Cash',
  [PaymentMethod.Loan]: 'Loan',
  [PaymentMethod.Other]: 'Other',
}
