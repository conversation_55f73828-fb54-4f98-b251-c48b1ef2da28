import * as Yup from 'yup'

export type ComponentType = 'select' | 'text' | 'image' | 'date'
export const required = (type: ComponentType = 'text') => {
  const messages = {
    select: 'オプションを一つ選択して下さい。',
    text: '入力が必要な項目です。',
    image: '画像を一つ指定してください。',
    date: '日時を選択してください。',
  }
  return Yup.string().required(messages[type])
}

export const optional = Yup.string().optional()

export const number = Yup.number()
  .typeError('有効な数値を入力してください。')
  .min(0, '0以上の数字を入力してください。')
export const requiredNumber = number.required('入力が必要な項目です。')
export const optionalNumber = number
  .transform((value, originalValue) =>
    String(originalValue).trim() === '' ? null : value
  )
  .nullable()

export const postalCode = Yup.string().matches(
  /^[0-9]{3}-?[0-9]{4}$/,
  '郵便番号は"-"(ハイフン)を除く7桁の数字で入力してください。'
)
export const requiredPostalCode = postalCode.required('入力が必要な項目です。')
export const optionalPostalCode = Yup.lazy((value) =>
  !value ? Yup.string() : postalCode
)

const phonePattern = /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/
export const phoneNumber = Yup.string()
  .matches(phonePattern, '有効な電話番号を入力してください。')
  .min(10)
  .max(11)

export const atLeastOneString = Yup.array()
  .of(Yup.string())
  .test({
    name: 'test-lang',
    message: '一つ以上チェックしてください。',
    test: (choices) => Array.isArray(choices) && choices.length > 0,
  })
  .required()

export const email = Yup.string().email(
  '有効なメールアドレスを入力してください。'
)
export const requiredEmail = email.required('入力が必要な項目です。')

export const website = Yup.string().url('有効なURLを入力してください。')
export const requiredWebsite = website.required('入力が必要な項目です。')
