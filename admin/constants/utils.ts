import { get } from 'lodash'
import dayjs from 'dayjs'
import { PropertyAddress } from '~/types/types'

export const getNearestStation = (
  stations: any[],
  propertyCompare: 'walk' | 'bus' = 'walk'
): any | null => {
  if (stations.length > 0) {
    let station = stations[0]
    stations.forEach((item) => {
      if (
        item.distance[propertyCompare] > 0 &&
        item.distance[propertyCompare] < station.distance[propertyCompare]
      ) {
        station = { ...item }
      }
    })

    return station
  }
  return null
}

export const getStationAccess = (stations: any): string => {
  const nearestStation: any | null = getNearestStation(stations)
  if (nearestStation) {
    const lineDetail = get(nearestStation, 'lineDetail.en', '')
    const stationDetail = get(nearestStation, 'stationDetail.en', '')
    const time = get(nearestStation, 'distance.walk', '')
    return `${lineDetail} ${stationDetail} Sta. ${time} min by walk`
  }

  return ''
}

export const getAddressDetail = (detail: PropertyAddress) => {
  if (!detail.chome) {
    return `${detail.city}, ${detail.prefecture}`
  }
  return `${detail.chome}, ${detail.city}, ${detail.prefecture}`
}

export const validDateRange = (date: string) =>
  /\d{4}-\d{2}-\d{2}~\d{4}-\d{2}-\d{2}/.test(date)

export const formatDate = (
  value: string | Date,
  format = 'YYYY-M-D H:mm'
): string => dayjs(value).format(format)

export const dateRangeValid = (filterDate: Date[]): boolean =>
  !!filterDate.length && filterDate.every((item: Date) => !!item)

export const getDateRangeFormatted = (
  dateSelected: Date[]
): { start: string; end: string } => {
  const format = 'YYYY-MM-DD'
  return {
    start: formatDate(dateSelected[0], format),
    end: formatDate(dateSelected[1], format),
  }
}

export const datePickerLang = {
  formatLocale: {
    months: [
      '一月',
      '二月',
      '三月',
      '四月',
      '五月',
      '六月',
      '七月',
      '八月',
      '九月',
      '十月',
      '十一月',
      '十二月',
    ],
    monthsShort: [
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    weekdays: [
      '日曜日',
      '月曜日',
      '火曜日',
      '水曜日',
      '木曜日',
      '金曜日',
      '土曜日',
    ],
    weekdaysShort: ['日', '月', '火', '水', '木', '金', '土'],
    weekdaysMin: ['日', '月', '火', '水', '木', '金', '土'],
    firstDayOfWeek: 0,
    firstWeekContainsDate: 6,
  },

  yearFormat: 'YYYY年',
  // the calendar title of month
  monthFormat: 'MM月',
  // the calendar title of month before year
  monthBeforeYear: false,
}
