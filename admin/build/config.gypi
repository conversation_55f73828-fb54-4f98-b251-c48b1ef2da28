# Do not edit. File was generated by node-gyp's "configure" step
{
  "target_defaults": {
    "cflags": [],
    "default_configuration": "Release",
    "defines": [],
    "include_dirs": [],
    "libraries": []
  },
  "variables": {
    "arch_triplet": "x86_64-linux-gnu",
    "asan": 0,
    "build_v8_with_gn": "false",
    "coverage": "false",
    "debug_nghttp2": "false",
    "enable_lto": "false",
    "enable_pgo_generate": "false",
    "enable_pgo_use": "false",
    "force_dynamic_crt": 1,
    "host_arch": "x64",
    "icu_gyp_path": "tools/icu/icu-system.gyp",
    "icu_small": "false",
    "icu_ver_major": "66",
    "llvm_version": 0,
    "node_byteorder": "little",
    "node_debug_lib": "false",
    "node_enable_d8": "false",
    "node_enable_v8_vtunejit": "false",
    "node_install_npm": "false",
    "node_module_version": 64,
    "node_no_browser_globals": "false",
    "node_prefix": "/usr",
    "node_relative_path": "lib/x86_64-linux-gnu/nodejs:share/nodejs:lib/nodejs",
    "node_release_urlbase": "",
    "node_shared": "true",
    "node_shared_cares": "true",
    "node_shared_http_parser": "false",
    "node_shared_libuv": "true",
    "node_shared_nghttp2": "true",
    "node_shared_openssl": "true",
    "node_shared_zlib": "true",
    "node_tag": "",
    "node_target_type": "shared_library",
    "node_use_bundled_v8": "true",
    "node_use_dtrace": "false",
    "node_use_etw": "false",
    "node_use_large_pages": "false",
    "node_use_openssl": "true",
    "node_use_pch": "false",
    "node_use_perfctr": "false",
    "node_use_v8_platform": "true",
    "node_with_ltcg": "false",
    "node_without_node_options": "false",
    "openssl_fips": "",
    "openssl_no_asm": 0,
    "shlib_suffix": "so.64",
    "target_arch": "x64",
    "v8_enable_gdbjit": 0,
    "v8_enable_i18n_support": 1,
    "v8_enable_inspector": 1,
    "v8_no_strict_aliasing": 1,
    "v8_optimized_debug": 0,
    "v8_promise_internal_field_count": 1,
    "v8_random_seed": 0,
    "v8_trace_maps": 0,
    "v8_typed_array_max_size_in_heap": 0,
    "v8_use_snapshot": "false",
    "want_separate_host_toolset": 0,
    "nodedir": "/usr/include/nodejs",
    "standalone_static_library": 1,
    "g": "true",
    "unsafe_perm": "true"
  }
}
