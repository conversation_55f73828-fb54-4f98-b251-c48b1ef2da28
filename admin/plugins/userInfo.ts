import { Plugin } from '@nuxt/types'

declare module 'vue/types/vue' {
  interface UserInfoJWTPayload {
    sub: string
    name: string
    ias: string
    exp: string
    roles: string[]
    agencyId?: string
  }
  interface Vue {
    $getUserInfo(): UserInfoJWTPayload
  }
}

function parseJwt(token: string) {
  const base64Url = token.split('.')[1]
  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
  const jsonPayload = decodeURIComponent(
    atob(base64)
      .split('')
      .map(function (c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
      })
      .join('')
  )

  return JSON.parse(jsonPayload)
}

const getUserInfo: Plugin = (context, inject) => {
  inject('getUserInfo', () => {
    const token = context.$apolloHelpers.getToken()
    return token && parseJwt(token)
  })
}

export default getUserInfo
