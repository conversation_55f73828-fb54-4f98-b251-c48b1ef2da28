import mapValues from 'lodash/mapValues'
import flatten from 'flat'
import * as Yup from 'yup'

type dict = { [key: string]: string | null }

/**
 * Create a flat dictionary, no matter how deep the fields
 * @param obj the object we want to transform
 * @returns flatten object
 */
export const createDict = (obj: any) => {
  const toEmpty = () => ''
  const options = { arrayBrackets: true } as any
  const flatObj = flatten(obj, options) as dict
  return mapValues(flatObj, toEmpty) as dict
}

type VueInstance = { [key: string]: any } & { errors: dict }

/**
 * A common utility function to validate and assign the errors on the fly
 * @param yupSchema the validation schema
 * @param value the value we want to validate
 * @param instance Vue instance which need `errors` field, so we can assign the error
 */
export const validateForm = async (
  yupSchema: Yup.ObjectSchema<any, any, any, any>,
  value: any,
  instance: VueInstance
) => {
  try {
    await yupSchema.validate(value, { abortEarly: false, recursive: true })
  } catch (error) {
    console.log(JSON.stringify(error), value)
    error.inner.forEach((validation: any) => {
      instance.errors[validation.path] = validation.message
    })
    throw error
  }
}

/**
 * Create a watcher function which responsible to trigger the validation
 * everytime the defined value is changed
 * @param fieldName the field name we want to watch
 * @param yupSchema the yup schema
 * @returns
 */
export const watchAndValidate = (
  fieldName: string,
  yupSchema: Yup.ObjectSchema<any, any, any, any>
) =>
  async function (this: VueInstance, newValue: string) {
    try {
      await Yup.reach(yupSchema, fieldName).validate(newValue)
      this.errors[fieldName] = null
    } catch (error) {
      this.errors[fieldName] = error.message
    }
  }

export const watchForm = (
  input: object,
  schema: Yup.ObjectSchema<any, any, any, any>
) =>
  Object.entries(flatten(input)).reduce(
    (acc, [index]) => ({
      ...acc,
      async [`form.${index}`](this: any, newValue: any) {
        this.$emit('input', this.form)
        const normalizedIndex = index.replace(/(\.(\d+?)\.)/, '[$2].')
        try {
          await Yup.reach(schema, index).validate(newValue)
          this.errors[normalizedIndex] = ''
        } catch (error) {
          this.errors[normalizedIndex] = error.message
        }
      },
    }),
    {}
  )
