import { Plugin } from '@nuxt/types'
import Vue from 'vue'
import mitt from 'mitt'

export enum ConfirmOptionsType {
  Primary = 'primary',
  Danger = 'danger',
}
export interface ConfirmOptions {
  type?: ConfirmOptionsType
  title?: String
  message: string
  okButton?: String
  cancelButton?: String
  okCallback?: () => void
  cancelCallback?: () => void
}

type confirmFunction = (options: ConfirmOptions) => void

declare module 'vue/types/vue' {
  interface Vue {
    $showConfirmDialog: confirmFunction
  }
}
export const eventBus = mitt()

const confirm: Plugin = (_ctx, inject) => {
  inject('showConfirmDialog', function (this: Vue, options: ConfirmOptions) {
    eventBus.emit('showConfirmDialog', options)
  })
}

export default confirm
