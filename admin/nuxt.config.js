require('dotenv').config()

export default {
  // Disable server-side rendering: https://go.nuxtjs.dev/ssr-mode
  ssr: false,

  // Target: https://go.nuxtjs.dev/config-target
  target: 'static',

  // Global page headers: https://go.nuxtjs.dev/config-head
  head: {
    title: 'Living Japan',
    htmlAttrs: {
      lang: 'en',
    },
    meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { hid: 'description', name: 'description', content: '' },
      { name: 'robots', content: 'noindex' },
    ],
    link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }],
  },

  loading: '~/components/Loading.vue',

  vue: {
    config: {
      ignoredElements: ['box', 'lines'],
    },
  },

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: ['@/assets/scss/main.scss'],

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: [
    '~/plugins/userInfo.ts',
    '~/plugins/vuedraggable.ts',
    '~/plugins/confirm-dialog.ts',
    '~/plugins/clickaway',
    { src: '~/plugins/vuex-persist.ts', ssr: false },
    { src: '~/plugins/vue2-google-map', ssr: true },
    { src: '~/plugins/datepicker.js', ssr: false },
  ],

  // Auto import components: https://go.nuxtjs.dev/config-components
  components: [
    '~/components',
    '~/components/tabs',
    '~/components/forms',
    '~/components/properties',
    '~/components/agencies',
  ],

  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  buildModules: [
    // https://go.nuxtjs.dev/typescript
    '@nuxt/typescript-build',
    // https://go.nuxtjs.dev/stylelint
    '@nuxtjs/stylelint-module',
    // https://github.com/nuxt-community/dotenv-module
    '@nuxtjs/dotenv',
    // https://www.npmjs.com/package/@nuxtjs/fontawesome
    '@nuxtjs/fontawesome',
    'nuxt-typed-vuex',
  ],

  fontawesome: {
    icons: {
      solid: [
        'faChevronDown',
        'faChevronUp',
        'faChevronRight',
        'faCaretDown',
        'faCaretRight',
        'faTimes',
        'faQuestionCircle',
        'faUserShield',
      ],
    },
  },

  // Modules: https://go.nuxtjs.dev/config-modules
  modules: ['@nuxtjs/apollo', '@nuxtjs/toast'],

  // Build Configuration: https://go.nuxtjs.dev/config-build
  build: {
    transpile: [/^gmap-vue($|\/)/],
  },

  router: {
    middleware: 'isAuth',
  },

  apollo: {
    clientConfigs: {
      default: '~/nuxt.config.apollo.ts',
    },
  },

  toast: {
    position: 'top-right',
    duration: 1500,
    iconPack: 'custom-class',
    action: {
      icon: 'icons-close',
      onClick: (__, toast) => toast.goAway(0),
    },
    register: [
      // Register custom toasts
      {
        name: 'commonError',
        message: 'Oops...Something went wrong. Please try again. ',
        options: {
          type: 'error',
        },
      },
    ],
  },

  publicRuntimeConfig: {
    cdnUrl: process.env.CDN_URL,
    webUrl: process.env.WEB_URL,
    gqlUrl: process.env.GQL_URL,
    gMapAPI: process.env.GMAP_APIKEY,
    version: require('./package.json').version,
  },
}
