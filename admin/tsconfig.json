{"compilerOptions": {"target": "ES2018", "module": "ESNext", "moduleResolution": "Node", "lib": ["ESNext", "ESNext.AsyncIterable", "DOM"], "resolveJsonModule": true, "esModuleInterop": true, "allowJs": true, "sourceMap": true, "strict": true, "noEmit": true, "experimentalDecorators": true, "baseUrl": ".", "paths": {"~/*": ["./*"], "@/*": ["./*"]}, "types": ["@types/node", "@nuxtjs/apollo", "@nuxtjs/toast", "@nuxt/types", "vue-apollo/types", "@types/vue2-datepicker"]}, "exclude": ["node_modules", ".nuxt", "dist"]}