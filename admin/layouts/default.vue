<template>
  <div class="wrapper">
    <Sidebar />
    <div class="page">
      <top-header />
      <Nuxt />
    </div>
    <confirm-dialogue />
  </div>
</template>

<style lang="scss">
@import '~/assets/scss/settings/variables';

@font-face {
  font-family: 'Noto Sans';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('~assets/fonts/NotoSans-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Noto Sans';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('~assets/fonts/NotoSans-Bold.ttf') format('truetype');
}

@font-face {
  font-family: 'Noto Sans JP';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('~assets/fonts/NotoSansJP-Regular.otf') format('opentype');
}

@font-face {
  font-family: 'Noto Sans JP';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('~assets/fonts/NotoSansJP-Bold.otf') format('opentype');
}

html {
  font-family: 'Noto Sans', 'Noto Sans JP', 'Source Sans Pro', -apple-system,
    BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  word-spacing: 1px;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
  color: #333;
}

input,
select,
option {
  font-family: 'Noto Sans', 'Noto Sans JP', 'Source Sans Pro', -apple-system,
    BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
}

a {
  text-decoration: none;
  color: #333;
  transition: 0.3s;

  &:hover {
    opacity: 0.8;
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40px;

  h1 {
    margin-bottom: 0 !important;
  }

  .buttons {
    font-weight: bold;

    button {
      border: 0;
      width: 110px;
      height: 34px;
      border-radius: 4px;
      color: $color_white;
      transition: 0.3s;
      font-size: 12px;

      &:hover {
        opacity: 0.7;
      }

      &.gray {
        background-color: $color_emperor;
      }
      &.blue {
        background-color: $color_matisse;
      }
      &.orange {
        background-color: $color_pumpkin;
      }
      &[disabled] {
        background-color: #ececec;
      }
    }
  }
}

.wrapper {
  display: flex;
  flex-direction: row;

  .page {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: stretch;
    flex: 1;
    position: relative;
    max-height: 100vh;
    overflow-y: auto;

    .container {
      display: flex;
      flex-direction: column;
      align-items: stretch;
      justify-content: stretch;
      background-color: #f2f4f6;
      flex: 1;
      padding: 58px;
      padding-top: 96px;

      h1 {
        display: flex;
        align-items: center;
        font-size: 24px;
        margin-bottom: 40px;
        justify-content: space-between;

        .button {
          text-decoration: none;
          font-size: 12px;
          font-weight: normal;
          padding: 4px 15px;
          border-radius: 4px;
          background: #34946f;
          color: #fff;
          margin-left: 20px;
        }
      }

      .content {
        flex: 1;
        border-radius: 5px;
        border: 2px solid #ececec;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        justify-content: stretch;
        padding: 20px;
        background-color: white;
        max-width: 600px;
      }
    }
  }
}

table {
  width: auto;
  background-color: white;
  border-top: 1px solid $color_alto;

  tr {
    width: auto;
    border: 0;
    transition: 0.3s;
    text-align: left;
    font-size: 14px;
    border-bottom: 1px solid $color_alto;

    &:hover {
      background-color: rgb(250, 250, 250);
      cursor: pointer;
    }

    th {
      font-weight: bold;
      padding: 15px 22px;
      width: auto;
    }

    td {
      padding: 8px 22px;
      width: auto;
      border-radius: 5px;
    }
  }
}

.section-title {
  display: flex;
  flex-direction: column;

  h2 {
    margin: 0;
    font-size: 18px;
    border-left: 4px solid #333;
    padding-left: 5px;
    margin-bottom: 15px;
  }

  p {
    font-size: 12px;
    color: #555;
    margin-bottom: 14px;
  }

  &::after {
    content: '';
    border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
  }
}

.form {
  &-input {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;

    &__label {
      font-size: 12px;
      color: #333;
      margin-bottom: 10px;
      font-weight: bold;
      display: flex;

      .optional {
        font-size: 8px;
        font-weight: normal;
        padding: 3px 8px;
        background-color: $color_dusty_gray;
        color: #fff;
        border-radius: 2px;
        margin-left: 5px;
      }
    }

    &__caption {
      color: $color_emperor;
      font-size: 12px;
      margin-bottom: 10px;
    }

    @mixin field {
      border: 1px solid #ddd;
      border-radius: 8px;
      outline: none;
      transition: 0.3s;
      height: 34px;
      font-size: 12px;

      &::placeholder {
        color: #aaa;
      }

      &:active,
      &:focus {
        border: 1px solid #555;
      }

      &.error {
        border-color: red;
      }
    }

    input {
      max-width: 600px;
      padding: 10px 12px;
      font-size: 12px;
      @include field;
    }

    select {
      font-size: 14px;
      padding: 2px 10px 0 8px;
      border-color: #555;
      @include field;
    }

    div.error {
      font-size: 12px;
      color: red;
      margin-top: 10px;
    }
  }
}

.submit {
  outline: 0;
  border: 0;
  background: #1b7fa7;
  border-radius: 4px;
  color: #eee;
  padding: 10px 22px;
  cursor: pointer;
  max-width: 240px;
  font-size: 14px;
  font-weight: bold;

  &:hover {
    opacity: 0.8;
  }
}

.shine {
  background: #f6f7f8;
  background-image: linear-gradient(
    to right,
    #f6f7f8 0%,
    #edeef1 20%,
    #f6f7f8 40%,
    #f6f7f8 100%
  );
  background-repeat: no-repeat;
  background-size: 800px 170px;
  display: inline-block;
  position: relative;
  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: forwards;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-name: placeholder_shimmer;
  -webkit-animation-timing-function: linear;
  animation-duration: 1s;
  animation-fill-mode: forward;
  animation-iteration-count: infinite;
  animation-name: placeholder_shimmer;
  animation-timing-function: linear;
}

@keyframes placeholder_shimmer {
  0% {
    background-position: -468px 0;
  }

  100% {
    background-position: 468px 0;
  }
}
</style>
