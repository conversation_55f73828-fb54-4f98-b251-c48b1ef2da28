<template>
  <div class="container">
    <h1>
      業者一覧 <NuxtLink class="button" to="/agencies/create">create</NuxtLink>
    </h1>
    <template v-if="noItem">
      <p>There is no agencies available.</p>
    </template>
    <template v-else>
      <pagination-bar v-bind="agencies.pageInfo" path="agencies" />
      <table>
        <tr>
          <th style="width: 50%">店舗名</th>
          <th>登録物件数</th>
          <th>公開物件数</th>
          <th>累計反響件数</th>
          <th>反響手数料率</th>
        </tr>
        <template v-if="isFetching">
          <tr v-for="row in 5" :key="row">
            <td v-for="col in 5" :key="col">
              <lines style="height: 15px; width: 100%" class="shine"></lines>
            </td>
          </tr>
        </template>
        <template v-else>
          <tr v-for="agency in agencies.items" :key="agency._id">
            <td class="td-link">
              <NuxtLink class="agencyList-item" :to="`/agencies/${agency._id}`">
                <strong>{{ get(agency, 'basic.companyName') }}</strong>
              </NuxtLink>
            </td>
            <td>{{ get(agency, 'totalRoomCount', 0) }}</td>
            <td>{{ get(agency, 'publishedRoomCount', 0) }}</td>
            <td>{{ get(agency, 'inquiryCount', 0) }}</td>
            <td>{{ getServiceFee(agency) }}</td>
          </tr>
        </template>
      </table>
      <pagination-bar v-bind="agencies.pageInfo" path="agencies" />
    </template>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import getAgenciesGql from '~/graphql/queries/getAgencies.gql'
import { Agency, AgencyPagination } from '~/types/types'
import { formatPrice } from '~/plugins/number.utils'

export default Vue.extend({
  middleware: ['isAdmin'],
  data: () => ({ agencies: {} as AgencyPagination, isFetching: true }),
  computed: {
    noItem() {
      return (this.agencies?.items?.length || 0) === 0
    },
    currentAgencyId() {
      return this.$accessor.currentAgencyId
    },
  },
  watch: {
    currentAgencyId(newValue) {
      if (newValue) {
        this.$router.push('/agencies/' + newValue)
      }
    },
  },
  watchQuery: ['page'],
  mounted() {
    if (this.$accessor.currentAgencyId) {
      this.$router.push('/agencies/' + this.$accessor.currentAgencyId)
    }
  },
  methods: {
    get,
    getServiceFee(agency: Agency) {
      if (agency.serviceFee?.feeType === 'percentage') {
        return `${agency.serviceFee?.value || '0'}%`
      }
      return formatPrice(agency.serviceFee?.value || 0)
    },
  },
  apollo: {
    agencies: {
      query: getAgenciesGql,
      fetchPolicy: 'cache-and-network',
      watchLoading(isLoading): void {
        this.isFetching = isLoading
      },
      variables(): { page: number; perPage: number } {
        const page = Number.parseInt(this.$route.query?.page as string) || 0
        return { page, perPage: 10 }
      },
    },
  },
})
</script>

<style lang="scss" scoped>
.container {
  overflow: hidden;
}

.td-link {
  display: flex;
  padding: 0;
  .agencyList-item {
    flex: 1;
    position: relative;
    padding: 8px 22px;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      display: block;
      width: 80vw;
    }
  }
}
</style>
