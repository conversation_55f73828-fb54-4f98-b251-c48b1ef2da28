<template>
  <div class="container">
    <h1>店舗情報</h1>
    <div v-if="notFound" class="not-found">
      Agency does not exist or already deleted
    </div>
    <div v-else class="form">
      <section>
        <div class="section-title">
          <h2>基本情報</h2>
        </div>
        <div class="service-fee">
          <form-input-text
            v-if="$accessor.isAdmin && !$accessor.currentAgencyId"
            v-model.number="form.serviceFee.value"
            type="number"
            label="反響手数料率"
            placeholder="0"
            suffix="%"
            :error="get(errors, 'serviceFee.value')"
          >
            <template slot="label">
              <tooltip
                text="administrator only"
                icon="user-shield"
                icon-color="green"
              />
            </template>
          </form-input-text>
        </div>
        <form-input-text
          v-model="form.basic.companyName"
          label="法人名"
          placeholder="株式会社インバウンドプラットフォーム"
          :error="get(errors, 'basic.companyName')"
        />
        <div class="form-input">
          <label class="form-input__label">宅地建物取引業者 免許証番号</label>
          <div class="horizontal">
            <select
              v-model="form.basic.license.licenseType"
              class="license-type"
              :disabled="haveNoLicense"
            >
              <option disabled value="">選択する</option>
              <option
                v-for="type in licenseTypes"
                :key="type.value"
                :value="type.value"
              >
                {{ type.label }}
              </option>
            </select>
            <div class="license-no">
              <span>(</span>
              <input
                v-model="form.basic.license.number"
                class="license-no"
                type="text"
                :disabled="haveNoLicense"
              /><span>)</span>
              <span>第</span>
            </div>
            <input
              v-model="form.basic.license.issueNumber"
              class="license-issue"
              type="text"
              :disabled="haveNoLicense"
            />
            号
          </div>
          <div v-if="get(errors, 'basic.license.licenseType')" class="error">
            {{ get(errors, 'basic.license.licenseType') }}
          </div>
          <div v-if="get(errors, 'basic.license.number')" class="error">
            {{ get(errors, 'basic.license.number') }}
          </div>
          <div v-if="get(errors, 'basic.license.issueNumber')" class="error">
            {{ get(errors, 'basic.license.issueNumber') }}
          </div>
        </div>
        <div class="form-input no-license">
          <label id="noLicense">
            <input v-model="haveNoLicense" type="checkbox" />
            免許証なし
          </label>
        </div>
        <div v-show="!haveNoLicense" class="form-input">
          <label class="form-input__label">免許有効期限</label>
          <div class="horizontal">
            <date-select
              v-model="form.basic.license.expiryDate.from"
              :error="get(errors, 'basic.license.expiryDate.from')"
              suffix="から"
              :is-disabled="haveNoLicense"
            />
            <date-select
              v-model="form.basic.license.expiryDate.until"
              :error="get(errors, 'basic.license.expiryDate.until')"
              :max-year="2030"
              :is-disabled="haveNoLicense"
            />
          </div>
        </div>
      </section>
      <section>
        <div class="section-title">
          <h2>公開情報</h2>
          <p>サイト上に公開され、お客様が閲覧できる情報です。</p>
        </div>
        <form-input-text
          v-model="form.public.name"
          label="店舗名"
          placeholder="Shop name"
          :error="get(errors, 'public.name')"
        />
        <div class="form-input">
          <label class="form-input__label">表示用アイコン</label>
          <avatar-upload
            v-model="form.public.imageUrl"
            :path="`agencies/${agencyId}/images`"
            :error="get(errors, 'public.imageUrl')"
          />
        </div>
        <form-input-text
          v-model="form.public.address.postalCode"
          label="郵便番号"
          placeholder="Postal Code"
          :error="get(errors, 'public.address.postalCode')"
        />
        <div class="form-input" style="max-width: 600px">
          <label class="form-input__label">住所</label>
          <address-picker
            placeholder="選択する"
            :prefecture="get(form, 'public.address.prefecture')"
            :city="get(form, 'public.address.city')"
            :chome="get(form, 'public.address.chome')"
            @on-picked="handleAddressPick($event)"
          />
          <div v-if="get(errors, 'public.address.prefecture')" class="error">
            {{ get(errors, 'public.address.prefecture') }}
          </div>
        </div>
        <form-input-text
          v-model="form.public.address.streetAddress"
          label="番地"
          placeholder=""
          :error="get(errors, 'public.address.streetAddress')"
        />
        <form-input-text
          v-model="form.public.address.buildingName"
          label="建物名/階数"
          placeholder="Building Name"
          :error="get(errors, 'public.address.buildingName')"
        />
        <form-input-text
          v-model="form.public.phone"
          label="電話番号"
          placeholder="03-1234-5678"
          :error="get(errors, 'public.phone')"
        />
        <form-input-text
          v-model="form.public.email"
          label="メールアドレス"
          placeholder="<EMAIL>"
          :error="get(errors, 'public.email')"
        />
        <div class="form-input">
          <label class="form-input__label">利用可能言語 </label>
          <language-options v-model="form.public.availableLanguages" />
          <div v-if="get(errors, 'public.availableLanguages')" class="error">
            {{ get(errors, 'public.availableLanguages') }}
          </div>
        </div>
        <div class="form-input">
          <label class="form-input__label">営業時間</label>
          <div class="horizontal">
            <hour-select
              v-model="form.public.businessHour.start"
              suffix="から"
            />
            <hour-select v-model="form.public.businessHour.end" />
          </div>
          <div v-if="get(errors, 'public.businessHour.end')" class="error">
            {{ get(errors, 'public.businessHour.end') }}
          </div>
        </div>
        <div class="form-input">
          <label class="form-input__label">
            定休日 <span class="optional">任意</span>
          </label>
          <business-close-options v-model="form.public.closeDays" />
        </div>
        <form-input-text
          v-model="form.public.website"
          is-optional
          label="ウェブサイト"
          placeholder="https://example.com"
          :error="get(errors, 'public.website')"
        />
      </section>
      <div class="buttons">
        <button
          class="submit"
          :disabled="$apollo.queries.agencyById.loading"
          @click.prevent="updateAgency"
        >
          保存する
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import isObject from 'lodash/isObject'
import cloneDeep from 'lodash/cloneDeep'
import set from 'lodash/set'
import isNil from 'lodash/isNil'
import * as Yup from 'yup'
import updateAgencyGql from '~/graphql/mutations/updateAgency.gql'
import getAgencyGql from '~/graphql/queries/getAgency.gql'
import LanguageOptions from '~/components/agencies/LanguageOptions.vue'
import BusinessCloseOptions from '~/components/agencies/BusinessCloseOptions.vue'
import HourSelect from '~/components/forms/HourSelect.vue'
import FormInputText from '~/components/forms/FormInputText.vue'
import DateSelect from '~/components/forms/DateSelect.vue'
import { createDict, validateForm, watchForm } from '~/plugins/form.utils'
import * as YupConstants from '~/constants/yup'
import {
  EnumAgencyBasicLicenseLicenseType,
  EnumAgencyBasicLicenseLicenseType as LicenseType,
  EnumAgencyServiceFeeFeeType,
  Maybe,
  UpdateByIdAgencyInput,
} from '~/types/types'

const licenseRule = (type: YupConstants.ComponentType) =>
  Yup.string().when(['haveLicense'], {
    is: (haveLicense: boolean) => haveLicense,
    then: YupConstants.required(type),
  })

const schema = Yup.object().shape({
  serviceFee: Yup.object({
    feeType: Yup.string().required(),
    value: Yup.number().required(),
  }),
  basic: Yup.object({
    companyName: YupConstants.required('text'),
    license: Yup.object({
      haveLicense: Yup.boolean(),
      licenseType: licenseRule('text'),
      number: licenseRule('text'),
      issueNumber: licenseRule('text'),
      expiryDate: Yup.object({
        from: licenseRule('date'),
        until: licenseRule('date'),
      }),
    }),
  }),
  public: Yup.object({
    name: YupConstants.required('text'),
    imageUrl: YupConstants.required('image'),
    address: Yup.object({
      postalCode: YupConstants.requiredPostalCode,
      prefecture: YupConstants.required('select'),
      city: YupConstants.required('text'),
      streetAddress: YupConstants.required('text'),
      buildingName: YupConstants.optional,
    }),
    phone: YupConstants.phoneNumber,
    email: YupConstants.requiredEmail,
    availableLanguages: YupConstants.atLeastOneString,
    businessHour: Yup.object({
      start: YupConstants.required('select'),
      end: YupConstants.required('select'),
    }),
    website: YupConstants.website,
  }),
})

const form: UpdateByIdAgencyInput = {
  basic: {
    companyName: '',
    license: {
      haveLicense: true,
      licenseType: '' as Maybe<EnumAgencyBasicLicenseLicenseType> | undefined,
      number: '',
      issueNumber: '',
      expiryDate: {
        from: '',
        until: '',
      },
    },
  },
  public: {
    name: '',
    imageUrl: '',
    address: {
      postalCode: '',
      prefecture: '',
      city: '',
      chome: '',
      streetAddress: '',
      buildingName: '',
    },
    phone: '',
    email: '',
    availableLanguages: [],
    businessHour: {
      start: '',
      end: '',
    },
    closeDays: [],
    website: '',
  },
  serviceFee: {
    feeType: EnumAgencyServiceFeeFeeType.Percentage,
    value: 3,
  },
}

export default Vue.extend({
  components: {
    LanguageOptions,
    BusinessCloseOptions,
    HourSelect,
    FormInputText,
    DateSelect,
  },
  asyncData(context) {
    return {
      paramAgencyId: context.params.id,
    }
  },
  data: () => ({
    form: cloneDeep(form),
    errors: createDict(form),
    errorMessage: null as null | string,
    isChanged: false,
    notFound: false,
    paramAgencyId: '',
    licenseTypes: [
      { value: LicenseType.MinisterLitt, label: '国土交通大臣' },
      { value: LicenseType.GovernorTokyo, label: '東京都知事' },
    ],
  }),
  computed: {
    agencyId() {
      if (this.$accessor.isAdmin) return this.paramAgencyId
      if (this.$accessor.viewedAs === this.paramAgencyId)
        return this.paramAgencyId
      return null
    },
    haveNoLicense: {
      get(): boolean {
        return !(this as any).form.basic.license.haveLicense
      },
      set(newValue: boolean) {
        ;(this as any).form.basic.license.haveLicense = !newValue
        if (!newValue) return
        // Clear the license fields
        const license = (this as any).form.basic.license
        license.licenseType = ''
        license.number = ''
        license.issueNumber = ''
        license.expiryDate.until = ''
        license.expiryDate.from = ''
      },
    },
  },
  apollo: {
    agencyById: {
      query: getAgencyGql,
      variables(): { id: string | null } {
        return { id: this.agencyId }
      },
      result({ data }): void {
        if (!data.agencyById) {
          this.notFound = true
          return
        }

        const assign = (base: any, field: string) => {
          const baseObj = get(base, field)

          if (isObject(baseObj) && !Array.isArray(baseObj)) {
            for (const childField in baseObj) {
              assign(base, `${field}.${childField}`)
            }
            return
          }

          const incomingData = get(data.agencyById, field.slice('form.'.length))
          if (isNil(incomingData)) return
          set(base.form, field.slice('form.'.length), incomingData)
        }

        assign(this, 'form')
      },
    },
  },
  watch: watchForm(form, schema),
  methods: {
    get,
    handleAddressPick(newValue: {
      prefecture: string
      city: string
      chome: string
    }) {
      Object.keys(newValue).forEach((key) => {
        set(
          this,
          `form.public.address.${key}`,
          newValue[key as keyof typeof newValue]
        )
      })
    },
    async updateAgency() {
      try {
        await validateForm(schema, this.form, this)
        const record = cloneDeep(this.form)
        const license = record.basic?.license
        if (license && !license.haveLicense) {
          license.licenseType = EnumAgencyBasicLicenseLicenseType.Na
        }
        const result = await this.$apollo.mutate({
          mutation: updateAgencyGql,
          variables: { id: this.agencyId, record },
        })

        const updatedData = get(result, 'data.updateAgency.record', null)

        if (updatedData) {
          const message = 'The information has been updated'
          const options = { duration: 10000 }
          this.$toast.success(message, options)
        }
      } catch (error) {
        const message = get(
          error,
          'networkError.result.errors[0].message',
          error.message
        )

        this.$toast.error(message, { duration: 5000 })
      }
    },
  },
})
</script>

<style lang="scss" scoped>
.service-fee {
  max-width: 120px;
}

.buttons {
  display: flex;
  justify-content: space-between;
}

.horizontal {
  display: flex;
  align-items: center;
  & > * {
    flex: unset;
  }
}

.license {
  &-type {
    margin-right: 10px;
  }

  &-no {
    display: flex;
    align-items: center;

    & > * {
      margin-right: 10px;
    }

    input {
      padding: 2px;
      max-width: 34px;
      text-align: center;
    }
  }
  &-issue {
    max-width: 150px;
  }
}

.no-license {
  display: flex;
  align-items: flex-start;
  font-size: 14px;
  color: #333;

  label {
    display: flex;
    cursor: pointer;

    input[type='checkbox'] {
      width: 20px;
      height: 20px;
      border: 1px solid #ddd;
      margin-right: 5px;
    }
  }
}
</style>
