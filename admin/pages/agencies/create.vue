<template>
  <div class="container">
    <div class="header">
      <h1>Create Agency</h1>
      <div v-if="!isCreated" class="buttons">
        <button class="orange" :disabled="isCreating" @click="createAgency">
          保存する
        </button>
      </div>
    </div>

    <section v-if="!isCreated" class="form">
      <form-input-text
        v-model="record.basic.companyName"
        :error="get(errors, 'basic.companyName')"
        label="company name"
        placeholder="Toyama Property, Inc."
      />
      <form-input-text
        v-model="record.public.email"
        :error="get(errors, 'public.email')"
        label="company email"
        placeholder="<EMAIL>"
      />
    </section>
    <section v-else class="form">
      <p class="notice">
        Account {{ record.basic.companyName }} has been created, please use
        below credential to login into the agency account
      </p>
      <form-input-text
        :value="record.public.email"
        label="agency account email"
        is-disabled
      />
      <form-input-text
        value="default__password__please__change__it__soon"
        label="agency account password"
        is-disabled
      />
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import cloneDeep from 'lodash/cloneDeep'
import flatten from 'flat'
import * as Yup from 'yup'
import createAgencyGql from '~/graphql/mutations/createAgency.gql'

const schema = Yup.object().shape({
  basic: Yup.object({
    companyName: Yup.string().required(),
  }),
  public: Yup.object({
    email: Yup.string().email().required(),
  }),
})

const input = {
  basic: {
    companyName: '',
  },
  public: {
    email: '',
  },
}

export default Vue.extend({
  middleware: ['isAdmin'],
  data: () => ({
    record: cloneDeep(input),
    errors: flatten(input) as { [id: string]: string },
    agencyId: null as null | string,
    isCreating: false,
    isCreated: false,
  }),
  computed: {
    currentAgencyId() {
      return this.$accessor.currentAgencyId
    },
  },
  watch: {
    currentAgencyId(newValue) {
      if (newValue) {
        this.$router.push('/')
      }
    },
  },
  mounted() {
    if (this.$accessor.currentAgencyId) {
      this.$router.push('/')
    }
  },
  methods: {
    get,
    async createAgency() {
      this.errors = flatten(input)
      try {
        await schema.validate(this.record, { abortEarly: false })
      } catch (error) {
        error.inner.forEach((validation: any) => {
          console.log(JSON.stringify(validation))
          this.errors[validation.path] = validation.message
        })
        return
      }

      this.isCreating = true
      try {
        const result = await this.$apollo.mutate({
          mutation: createAgencyGql,
          variables: {
            record: this.record,
          },
        })

        this.agencyId = get(result, 'data.createAgency.record._id', null)
        if (this.agencyId) {
          this.isCreated = true
        } else {
          this.$toast.global.commonError()
        }
      } catch (error) {
        const message = get(
          error,
          'networkError.result.errors[0].message',
          error.message
        )
        this.$toast.error(message, { duration: 5000 })
      }
      this.isCreating = false
    },
  },
})
</script>

<style lang="scss" scoped>
.notice {
  background: #ececec;
  padding: 12px;
  font-size: 12px;
  max-width: 600px;
  border-radius: 4px;
  margin-bottom: 20px;
}
</style>
