<template>
  <div class="container-common">
    <FormCommon>
      <notice
        v-if="errorMessage"
        :message="errorMessage"
        :type="'error__message'"
      ></notice>
      <form-input label="メールアドレス">
        <input
          v-model="form.email"
          type="email"
          placeholder="<EMAIL>"
        />
      </form-input>
      <form-input label="パスワード">
        <input
          v-model="form.password"
          type="password"
          placeholder="●●●●●●●●●●"
        />
      </form-input>
      <button @click.prevent="login">ログイン</button>
      <NuxtLink class="nuxt__link" to="/reset-password"
        >パスワードを忘れた方はこちら</NuxtLink
      >
    </FormCommon>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import signInGql from '~/graphql/mutations/signIn.gql'

export default Vue.extend({
  layout: 'blank',
  data: () => ({
    form: {
      email: '',
      password: '',
    },
    errorMessage: null,
  }),
  async mounted() {
    await this.$apolloHelpers.onLogout()
    this.$accessor.reset()
  },
  methods: {
    async login() {
      try {
        const result = await this.$apollo.mutate({
          mutation: signInGql,
          variables: this.form,
        })
        await this.$apolloHelpers.onLogin(result.data.signIn.accessToken)
        this.$router.push({ path: '/' })
      } catch (error) {
        const message = get(
          error,
          'networkError.result.errors[0].message',
          error.message
        )
        this.errorMessage = message
      }
    },
  },
})
</script>
