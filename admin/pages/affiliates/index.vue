<template>
  <div class="container">
    <h1>
      アフィリエイト一覧
      <NuxtLink class="button" to="/affiliates/create">追加</NuxtLink>
    </h1>
    <template v-if="noItem">
      <p>There is no affiliates available.</p>
    </template>
    <template v-else>
      <pagination-bar v-bind="affiliates.pageInfo" path="affiliates" />
      <table>
        <tr>
          <th>パートナー名</th>
          <th>アフィリエイトID</th>
          <th>作成日</th>
          <th></th>
        </tr>
        <template v-if="isFetching">
          <tr v-for="row in 5" :key="row">
            <td v-for="col in 5" :key="col">
              <lines style="height: 15px; width: 100%" class="shine"></lines>
            </td>
          </tr>
        </template>
        <template v-else>
          <tr v-for="affiliate in affiliates.items" :key="affiliate._id">
            <td>{{ get(affiliate, 'name') }}</td>
            <td>{{ get(affiliate, 'code') }}</td>
            <td>{{ formatDate(get(affiliate, 'createdAt')) }}</td>
            <td class="td-link">
              <a
                target="_blank"
                :href="`${$config.webUrl}/?aid=${affiliate.code}`"
              >
                <strong>リンク</strong>
              </a>
            </td>
          </tr>
        </template>
      </table>
      <pagination-bar v-bind="affiliates.pageInfo" path="affiliates" />
    </template>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import getAffiliatesGql from '~/graphql/affiliates/getAffiliates.gql'
import { AffiliatePagination } from '~/types/types'
import { formatDate } from '~/constants/utils'

export default Vue.extend({
  middleware: ['isAdmin'],
  data: () => ({ affiliates: {} as AffiliatePagination, isFetching: true }),
  computed: {
    noItem() {
      return (this.affiliates?.items?.length || 0) === 0
    },
    currentAffiliateId() {
      return this.$accessor.currentAffiliateId
    },
  },
  watch: {
    currentAffiliateId(newValue) {
      if (newValue) {
        this.$router.push('/affiliates/' + newValue)
      }
    },
  },
  watchQuery: ['page'],
  methods: {
    get,
    formatDate,
  },
  apollo: {
    affiliates: {
      query: getAffiliatesGql,
      fetchPolicy: 'cache-and-network',
      watchLoading(isLoading): void {
        this.isFetching = isLoading
      },
      variables(): { page: number; perPage: number } {
        const page = Number.parseInt(this.$route.query?.page as string) || 0
        return { page, perPage: 10 }
      },
    },
  },
})
</script>

<style lang="scss" scoped>
.container {
  overflow: hidden;
}

.td-link {
  display: flex;
  padding: 0;
  .affiliateList-item {
    flex: 1;
    position: relative;
    padding: 8px 22px;
    cursor: default;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      display: block;
      width: 80vw;
    }
  }
}

th,
td {
  cursor: auto;
}
</style>
