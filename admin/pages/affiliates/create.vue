<template>
  <div class="container">
    <div class="header">
      <h1>アフィリエイトを追加する</h1>
      <div v-if="!isCreated" class="buttons">
        <button class="orange" :disabled="isCreating" @click="createAffiliate">
          保存する
        </button>
      </div>
    </div>

    <section v-if="!isCreated" class="form">
      <form-input-text
        v-model="record.name"
        :error="get(errors, 'name')"
        label="パートナー名"
        placeholder="パートナー名"
      />
      <div class="field-affiliate">
        <form-input-text
          v-model="record.code"
          label="アフィリエイトID"
          is-disabled
        />
      </div>
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import random from 'lodash/random'
import cloneDeep from 'lodash/cloneDeep'
import flatten from 'flat'
import * as Yup from 'yup'
import createAffiliateGql from '~/graphql/affiliates/createAffiliate.gql'

const schema = Yup.object().shape({
  name: Yup.string().required(),
})

const input = {
  name: '',
  code: random(100000, 999999).toString(),
}

export default Vue.extend({
  middleware: ['isAdmin'],
  data: () => ({
    record: cloneDeep(input),
    errors: flatten(input) as { [id: string]: string },
    isCreating: false,
    isCreated: false,
  }),

  methods: {
    get,
    async createAffiliate() {
      this.errors = flatten(input)
      try {
        await schema.validate(this.record, { abortEarly: false })
      } catch (error) {
        error.inner.forEach((validation: any) => {
          this.errors[validation.path] = validation.message
        })
        return
      }

      this.isCreating = true
      try {
        const result = await this.$apollo.mutate({
          mutation: createAffiliateGql,
          variables: {
            record: this.record,
          },
        })

        const affiliateId = get(result, 'data.createAffiliate.record._id', null)
        if (affiliateId) {
          this.isCreated = true
          this.$router.push('/affiliates')
        } else {
          this.$toast.global.commonError()
        }
      } catch (error) {
        const message = get(
          error,
          'networkError.result.errors[0].message',
          error.message
        )
        this.$toast.error(message, { duration: 5000 })
      }
      this.isCreating = false
    },
  },
})
</script>

<style lang="scss" scoped>
.btn-gencode {
  background-color: rgb(58, 148, 58);
  border-radius: 4px;
  outline: none;
  border: none;
  color: #fff;
  padding: 3px 5px;
  height: 35px;
  &:hover {
    background-color: darkseagreen;
    transition: 0.5s;
  }
}
</style>
