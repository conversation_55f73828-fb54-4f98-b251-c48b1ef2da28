<template>
  <div class="container">
    <h1>
      物件管理
      <property-filter-button v-model="filters.visibility" />
      <search-widget v-model="searchText" />
      <NuxtLink class="nav-bar-event__btn" to="/properties/create"
        ><img
          class="nav-bar-event__plus"
          src="~/assets/images/add.svg"
        />物件を追加する</NuxtLink
      >
    </h1>
    <template v-if="isEmpty">
      <p>{{ notFoundMessage }}</p>
    </template>
    <template v-else>
      <pagination-bar
        v-bind="properties.pageInfo"
        path="properties"
        summary-title="物件一覧"
      />
      <table class="property-list">
        <tr>
          <th style="min-width: unset; width: 50px">操作</th>
          <th>
            最終更新日<NuxtLink class="button" :to="getaUrl.UPDATEDAT"
              ><img
                class="admin-sort"
                src="~/assets/images/properties/admin-sort.svg"
            /></NuxtLink>
          </th>
          <th>
            PV数<NuxtLink class="button" :to="getaUrl.PAGEVIEWS"
              ><img
                class="admin-sort"
                src="~/assets/images/properties/admin-sort.svg"
            /></NuxtLink>
          </th>
          <th>
            反響数<NuxtLink class="button" :to="getaUrl.INQUIRYCOUNT"
              ><img
                class="admin-sort"
                src="~/assets/images/properties/admin-sort.svg"
            /></NuxtLink>
          </th>
          <th>公開状態</th>
          <th>物件名</th>
          <th>部屋番号</th>
          <th>間取り</th>
          <th>賃料</th>
          <th>現況</th>
        </tr>
        <template v-if="$apollo.queries.properties.loading">
          <tr v-for="row in 10" :key="row">
            <td v-for="col in 10" :key="col">
              <lines style="height: 15px; width: 100%" class="shine"></lines>
            </td>
          </tr>
        </template>
        <template v-else>
          <tr v-for="item in items" :key="item.roomCode" class="table-row">
            <td>
              <property-row-menu
                :property-name="item.roomName"
                :prefecture-path="item.prefecturePath"
                :property-id="item.propertyId"
                :building-name="item.buildingName"
                :room-code="item.roomCode"
                @on-deleted="$apollo.queries.properties.refetch()"
                @on-cloned="$apollo.queries.properties.refetch()"
              />
            </td>
            <td>{{ item.updatedAt }}</td>
            <td>{{ item.viewCount }}</td>
            <td v-if="item.inquiryCount > 0">
              <nuxt-link :to="`/inquiries?roomCode=${item.roomCode}`">
                {{ item.inquiryCount }}
              </nuxt-link>
            </td>
            <td v-else>{{ item.inquiryCount }}</td>
            <td>
              <visibility-options
                :value="item.visibility"
                :property-id="item.propertyId"
                :room-code="item.roomCode"
                @on-updated="onRoomVisibilityChanged(item.roomCode, $event)"
              />
            </td>
            <td>{{ item.buildingName }}</td>
            <td>{{ item.roomName }}</td>
            <td>{{ item.layout }}</td>
            <td>{{ item.rent }}</td>
            <td>{{ item.status }}</td>
          </tr>
        </template>
      </table>
      <pagination-bar
        v-bind="properties.pageInfo"
        path="properties"
        summary-title="物件一覧"
      />
    </template>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import flatMap from 'lodash/flatMap'
import dayjs from 'dayjs'
import set from 'lodash/set'
import setWith from 'lodash/setWith'
import getPropertiesGql from '~/graphql/properties/getProperties.query.gql'
import {
  EnumPropertyRoomsVisibility,
  Property,
  PropertyPagination,
} from '~/types/types'
import { formatPrice } from '~/plugins/number.utils'

interface RowItem {
  propertyId: string
  roomCode: string
  updatedAt: string
  viewCount: number
  inquiryCount: number
  visibility: EnumPropertyRoomsVisibility
  buildingName: string | null
  roomName: string
  layout: string
  rent: string
  status: string
  prefecturePath: string
}

const aUrl = {
  UPDATEDAT: '?sort=UPDATEDAT_ASC',
  PAGEVIEWS: '?sort=PAGEVIEWS_DESC',
  INQUIRYCOUNT: '?sort=INQUIRYCOUNT_DESC',
}

export default Vue.extend({
  name: 'PropertyListPage',
  asyncData({ route }: any): Promise<object | void> | object | void {
    const searchText = route.query?.search || ''
    const visibility = route.query?.visibility || []
    const filters = {
      visibility: Array.isArray(visibility) ? visibility : [visibility],
    }
    return { searchText, filters }
  },
  data: () => ({
    items: [] as RowItem[],
    aUrl,
    properties: {} as PropertyPagination,
    searchText: '',
    filters: {
      visibility: [] as EnumPropertyRoomsVisibility[],
    },
  }),
  computed: {
    getaUrl() {
      return this.aUrl
    },
    notFoundMessage() {
      if (this.searchText) {
        return `「${this.searchText}」の検索結果はありません。`
      }
      return 'There is no properties available, create one.'
    },
    isEmpty(): boolean {
      const isFetching = this.$apollo.queries.properties
      const hasItem = this.properties?.items?.length
      return !isFetching && !hasItem
    },
  },
  watch: {
    searchText: {
      handler(newValue, oldValue) {
        if (oldValue !== newValue) {
          const query = {
            ...this.$route.query,
            search: newValue,
          } as {
            page?: string
            search?: string
            sort?: string
          }

          if (newValue.trim().length < 1) {
            delete query.search
          }

          if (query?.page) {
            delete query.page
          }

          this.$router.push({ query })
        }
      },
    },
    'filters.visibility'(newValue) {
      const query = {
        ...this.$route.query,
        visibility: newValue,
      }

      if (this.filters.visibility.length === 0) {
        delete query.visibility
      }

      this.$router.push({ query })
    },
  },
  watchQuery: ['page', 'search', 'visibility'],
  methods: {
    get,
    formatDate(value: string) {
      return dayjs(value).format('DD MMMM YYYY')
    },
    onRoomVisibilityChanged(
      roomCode: string,
      value: EnumPropertyRoomsVisibility
    ) {
      const selected = this.items.find((item) => item.roomCode === roomCode)
      if (!selected) return
      selected.visibility = value
    },
  },
  apollo: {
    properties: {
      query: getPropertiesGql,
      variables() {
        const page = Number.parseInt(this.$route.query?.page as string) || 0
        const sort = (this.$route.query?.sort as string) || 'UPDATEDAT_DESC'
        const search = {
          OR: [
            setWith({}, '_operators.buildingName.regex', this.searchText),
            setWith({}, 'rooms.code', this.searchText),
          ],
        }

        const { visibility } = this.filters
        if (visibility.length > 0) {
          set(search, 'OR[0]._operators.rooms.visibility.in', visibility)
        }

        const filter = this.$accessor.currentAgencyId
          ? {
              AND: [
                { agencyId: this.$accessor.currentAgencyId },
                { ...search },
              ],
            }
          : { ...search }

        for (const [key] of Object.entries(this.aUrl)) {
          const typedKey = key as keyof typeof aUrl
          if (sort.includes(key)) {
            this.aUrl[typedKey] =
              this.$route.path +
              '?sort=' +
              key +
              (sort.includes('ASC') ? '_DESC' : '_ASC')
            break
          }
        }
        return {
          page,
          filter,
          sort,
        }
      },
      fetchPolicy: 'network-only',
      result({ data }) {
        // FIXME: refactor this ugly code
        const getItems = (property: Property) =>
          (this.filters.visibility.length > 0
            ? property.rooms.filter(
                (room) =>
                  room?.visibility &&
                  this.filters.visibility.includes(room.visibility)
              )
            : property.rooms
          )
            .map((room, index) => ({
              propertyId: property._id,
              roomCode: room?.code || index.toString(),
              updatedAt: dayjs(property.updatedAt).format('YYYY-MM-DD HH:mm'),
              viewCount: get(room, 'pageViews.total', 0),
              inquiryCount: room?.inquiryCount || 0,
              visibility:
                room?.visibility || EnumPropertyRoomsVisibility.Public,
              buildingName: get(property, 'buildingName', '') || '',
              roomName: get(room, 'roomName', '') || '',
              layout: `${room?.numberOfRoom}${room?.layout}`,
              rent: formatPrice(room?.monthlyFee || 0),
              status: room?.availability.now ? '空室' : '退去予定',
              prefecturePath: property?.prefecturePath || '__pref__',
            }))
            .sort((item1, item2) => {
              const sort = this.$route.query?.sort as string
              if (!sort) return 1
              const sorts = sort.split('_')
              if (sorts[0] === 'INQUIRYCOUNT') {
                return sorts[1] === 'DESC'
                  ? item2.inquiryCount - item1.inquiryCount
                  : item1.inquiryCount - item2.inquiryCount
              }
              if (sorts[0] === 'PAGEVIEWS') {
                return sorts[1] === 'DESC'
                  ? item2.viewCount - item1.viewCount
                  : item1.viewCount - item2.viewCount
              }
              return 1
            })

        this.items = flatMap(data?.properties?.items || [], getItems)
      },
    },
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/settings/variables';

.container {
  h2 {
    margin-bottom: 12px;

    a {
      text-decoration: none;
      color: red;
    }
  }

  input {
    margin-bottom: 12px;
    padding: 12px;
  }

  button {
    outline: 0;
    border: 0;
    background: $color_black;
    color: #eee;
    padding: 12px 24px;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }
  }
}

.property-list {
  tr {
    cursor: unset;
  }

  tr td,
  tr th {
    padding: 7px 10px;
    min-width: 75px;
  }

  tr td {
    border: 1px solid $color_alto;

    &:first-child {
      border-left: 0;
    }

    &:last-child {
      border-right: 0;
    }
  }

  tr th:nth-child(2),
  tr td:nth-child(2),
  tr th:nth-child(9),
  tr td:nth-child(10) {
    min-width: 100px;
  }

  tr th:nth-child(5),
  tr td:nth-child(5),
  tr th:nth-child(6),
  tr td:nth-child(6) {
    min-width: 150px;
  }

  tr th:nth-child(7),
  tr td:nth-child(7) {
    max-width: 125px;
  }

  tr th:nth-child(3),
  tr td:nth-child(3),
  tr th:nth-child(4),
  tr td:nth-child(4),
  tr th:nth-child(8),
  tr td:nth-child(8) {
    max-width: 75px;
  }
}

.admin-sort {
  margin-left: 10px;
  width: 10px;
}

.nav-bar-event__btn {
  color: #fff;
  font-size: 14px;
  border-radius: 4px;
  background-color: #1f3a4f;
  display: flex;
  align-items: center;
  padding: 10px 20px;
  cursor: pointer;
}

.nav-bar-event__plus {
  height: 14px;
  width: 14px;
  filter: brightness(0) invert(1);
  margin-right: 5px;
}
</style>
