<template>
  <div class="container">
    <div class="header">
      <h1>新規物件の追加</h1>
      <div v-if="isButtonsVisible" class="buttons">
        <button class="orange" :disabled="isCreating" @click="handleOnSave">
          保存する
        </button>
      </div>
    </div>
    <div v-if="notFound" class="not-found">
      Property does not exist or already deleted
    </div>
    <div v-else-if="!isAuthorized" class="not-found">
      Sorry, you are not authorized to manage this property<br />
      Make sure you are logged in as the owner of this property
    </div>
    <property-form
      v-else
      ref="propertyForm"
      :action="action"
      :value="propertyById"
      :room-code="roomCode"
      :agency-id="agencyId"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import createPropertyGql from '~/graphql/properties/createProperty.mutation.gql'
import updatePropertyGql from '~/graphql/properties/updateProperty.mutation.gql'
import getPropertyGql from '~/graphql/properties/getProperty.query.gql'
import { Maybe, Property } from '~/types/types'

export default Vue.extend({
  asyncData(context) {
    return {
      action: context.params.id === 'create' ? 'create' : 'update',
      propertyId: context.params.id,
      roomCode: context.route.query?.roomCode,
    }
  },
  data: () => ({
    notFound: false,
    roomCode: null as null | string,
    propertyId: null as null | string,
    propertyById: null as Maybe<Property>,
    errorMessage: null as null | string,
    action: 'create' as 'create' | 'edit',
    isCreating: false,
  }),
  computed: {
    agencyId() {
      return this.$accessor.currentAgencyId || ''
    },
    isAuthorized() {
      if (!this.propertyById) return true
      const { currentAgencyId, isAdmin } = this.$accessor
      if (!currentAgencyId) return isAdmin
      return currentAgencyId === this.propertyById.agencyId
    },
    isButtonsVisible() {
      if (!this.isAuthorized) return false
      if (this.notFound) return false
      return true
    },
  },
  apollo: {
    propertyById: {
      query: getPropertyGql,
      fetchPolicy: 'network-only',
      skip(): boolean {
        return this.action === 'create' || !this.propertyId
      },
      variables(): { id: string | null } {
        return { id: this.propertyId }
      },
      result({ data }) {
        if (!data.propertyById) {
          this.notFound = true
        }
      },
      watchLoading(isLoading): void {
        this.$nextTick(() => {
          if (this.$nuxt && this.$nuxt.$loading) {
            isLoading
              ? this.$nuxt.$loading.start()
              : this.$nuxt.$loading.finish()
          }
        })
      },
    },
  },

  methods: {
    async proceed() {
      const options = {
        mutation:
          this.action === 'create' ? createPropertyGql : updatePropertyGql,
        variables: this.getVariables(),
      }
      const result = await this.$apollo.mutate(options)
      const isSuccess = !!get(result, `data.${this.action}Property.record._id`)
      if (!isSuccess) {
        this.$toast.global.commonError()
        return
      }

      const message =
        this.action === 'create'
          ? 'Property has been created'
          : 'Property has been updated'

      if (this.action === 'create') this.$nuxt.$router.push('/properties')
      this.$toast.success(message, { duration: 10000 })
    },

    async handleOnSave() {
      const isValid = await (this.$refs.propertyForm as any).validate()
      if (!isValid) {
        this.$toast.error('Please fill the required form')
        return
      }

      this.isCreating = true
      try {
        await this.proceed()
      } catch (error) {
        const message = get(
          error,
          'networkError.result.errors[0].message',
          error.message
        )
        // eslint-disable-next-line no-console
        console.error(message)
        this.$toast.global.commonError()
      }
      this.isCreating = false
    },

    getVariables() {
      const form = (this.$refs.propertyForm as any).getValue()
      const transformed = {
        ...form,
        agencyId: this.agencyId || form.agencyId,
        rooms: form.rooms.map((room: any) => ({
          ...room,
          availability: {
            ...room?.availability,
          },
        })),
      }

      if (this.action === 'create') return { input: transformed }
      else {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { _id: __, agency: ____, ...record } = transformed
        return { id: this.propertyId, record }
      }
    },
  },
})
</script>
