<template>
  <div class="pdf-export">
    <canvas
      ref="myCanvas"
      width="652"
      height="32"
      style="display: none"
    ></canvas>
    <vue-html2pdf
      v-if="property"
      ref="html2Pdf"
      :show-layout="showHtml"
      :float-layout="!showHtml"
      :enable-download="false"
      :preview-modal="!showHtml"
      :filename="roomCode"
      :manual-pagination="true"
      pdf-orientation="landscape"
      pdf-format="a4"
      pdf-content-width="1123px"
      :html-to-pdf-options="htmlToPdfOption"
      @hasDownloaded="closeWindow"
    >
      <section slot="pdf-content" class="pdf-container">
        <section class="pdf-item">
          <div class="pdf-wrap">
            <div class="pdf-row">
              <div class="pdf-images">
                <div
                  v-for="(image, index) in images"
                  :key="index"
                  class="picture"
                  :class="{
                    'less-image': images.length < 5,
                  }"
                >
                  <img :src="image" alt="" />
                </div>
              </div>
              <div class="pdf-info">
                <div class="property-type">For rent</div>
                <h1 class="building-name">
                  {{ buildingName }}
                </h1>
                <h2 class="property-address">
                  {{ roomName }}
                </h2>
                <table class="property-info-table">
                  <tr>
                    <th>Rent Fees</th>
                    <td colspan="3">
                      <span class="price">{{ propertyPrice }}</span>
                      <span
                        v-if="maintenanceFee !== 0"
                        class="price-maintenance"
                        >(Maintenance Fees)</span
                      >
                    </td>
                  </tr>
                  <tr>
                    <th>Deposit</th>
                    <td colspan="3">{{ depositFee }}</td>
                  </tr>
                  <tr>
                    <th>Key Money</th>
                    <td colspan="3">{{ keyMoney }}</td>
                  </tr>
                  <tr>
                    <th>Agency Fee</th>
                    <td colspan="3">{{ agencyFee }}</td>
                  </tr>
                  <tr v-if="stationsAccessByWalk.first">
                    <th :rowspan="stationsAccessByWalk.max">Access</th>
                    <td colspan="3">
                      <div class="text-ellipsis">
                        {{ stationsAccessByWalk.first }}
                      </div>
                    </td>
                  </tr>
                  <template v-if="stationsAccessByWalk.other">
                    <tr
                      v-for="(station, index) in stationsAccessByWalk.other"
                      :key="index"
                    >
                      <td colspan="3">
                        <div class="text-ellipsis">{{ station }}</div>
                      </td>
                    </tr>
                  </template>
                  <tr class="multi-col">
                    <th>Layout</th>
                    <td class="span-td">
                      {{ getRoomLayout(selectedRoom) }}
                    </td>
                    <th>Size</th>
                    <td>{{ get(selectedRoom, 'size', 0) }}m<sup>2</sup></td>
                  </tr>
                  <tr class="multi-col">
                    <th>Year Built</th>
                    <td class="span-td">{{ buildDate }}</td>
                    <th>Floor</th>
                    <td class="span-td">
                      {{ get(selectedRoom, 'floor', '') }}F /
                      {{ get(property, 'maxFloor', '') }}F
                    </td>
                  </tr>
                  <tr class="multi-col">
                    <th>Window Direction</th>
                    <td class="span-td">
                      {{ mainWindow }}
                    </td>
                    <th>Available from</th>
                    <td class="span-td">{{ availability }}</td>
                  </tr>
                  <tr class="multi-col">
                    <th>Type</th>
                    <td class="span-td">{{ buildingType }}</td>
                    <th>Structure</th>
                    <td class="span-td">{{ buildingStructure }}</td>
                  </tr>
                  <tr>
                    <th>Features</th>
                    <td colspan="3" class="features-cell">
                      <div class="features-holder">
                        <span
                          v-for="(facility, code) in facilities"
                          :key="code"
                          class="facility"
                          :class="{ selected: roomFacilities.includes(code) }"
                        >
                          {{ facility }}
                        </span>
                      </div>
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
        </section>
        <section class="pdf-item">
          <div class="pdf-footer">
            <div class="pdf-wrap">
              <div v-if="agency" class="pdf-row">
                <div class="agency-info">
                  <div class="pdf-row">
                    <picture class="agency-avatar">
                      <img :src="agency.img" alt="" />
                    </picture>
                    <div class="">
                      <h2 class="agency-name">{{ agency.name }}</h2>
                      <p class="agency-license">{{ agency.licence }}</p>
                      <div class="pdf-row">
                        <div class="business-hour">
                          <strong>Business Hours</strong><br />
                          {{ agency.businessHour }}
                        </div>
                        <div>
                          <img src="~/assets/images/icon-phone.svg" alt="" />
                        </div>
                        <div class="agency-phone">
                          <div class="phone-icon">
                            <img src="~/assets/images/icon-phone.png" alt="" />
                          </div>
                          <span>{{ agency.phone }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="property-info">
                  <div class="pdf-row">
                    <div class="property-info--left">
                      <img
                        src="~/assets/images/logo_white.png"
                        alt=""
                        class="logo"
                      />
                      <p>
                        <strong>CHECK THIS PROPERTY ONLINE NOW!</strong>
                      </p>
                      <p>
                        <a :href="propertyLink">{{ propertyLink }}</a>
                      </p>
                    </div>
                    <vue-qrcode class="qr-img" :value="propertyLink" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </section>
    </vue-html2pdf>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import VueHtml2pdf from 'vue-html2pdf'
import VueQrcode from 'vue-qrcode'
import {
  EnumPropertyRoomsFeesMoveInDepositFeeType as MoveInFeeType,
  PropertyRooms,
  PropertyRoomsFeesMoveInDeposit as FeesMoveIn,
  Agency,
  EnumPropertyRoomsFeesMoveInDepositFeeType,
} from '@/types/types'

import dayjs from 'dayjs'
import {
  getStationAccessMultiple,
  formatPrice,
  getRoomName,
  getRoomLayout,
} from '~/common/utils'
import getPropertyByRoomCode from '~/graphql/queries/getPropertyByRoomCode.gql'
import { Property } from '~/types/types'
import {
  buildingType,
  facilities,
  licenseType,
  mainWindow,
  structure,
} from '~/constants/constant'

interface AgencyData {
  img: string
  name: string
  phone: string
  licence: string
  businessHour: string
}

export default Vue.extend({
  name: 'Pdf',
  components: {
    VueHtml2pdf,
    VueQrcode,
  },
  layout: 'blank',
  asyncData({
    error,
    params,
    query,
  }: any): Promise<object | void> | object | void {
    if (params?.room_id) {
      const showHtml = query?.html === 'true'

      return {
        roomCode: params.room_id,
        showHtml,
      }
    }
    return error({ statusCode: 404, message: 'Not Found' })
  },
  data() {
    return {
      roomCode: '' as string,
      property: null as null | Property,
      downloaded: false as Boolean,
      facilities,
      showHtml: false,
      context2D: null as CanvasRenderingContext2D | null,
    }
  },
  computed: {
    buildingName(): string | null {
      if (this.property && this.context2D) {
        const name = get(this.property, 'buildingName', '') as string
        return this.fittingString(name, 250)
      }
      return ''
    },
    images(): string[] {
      let images: string[] = []
      if (this.property) {
        images = [
          get(this.property, 'rooms[0].images.floorPlan', null),
          get(this.property, 'rooms[0].images.featured', null),
          ...get(this.property, 'rooms[0].images.collections', []),
        ].filter((item) => item)

        const imageSlice = images.length < 5 ? 2 : 5
        images = images
          .slice(0, imageSlice)
          .map((item) => this.$config.cdnUrl + '/' + item)
      }
      return images
    },
    selectedRoom(): PropertyRooms {
      const rooms = this.property?.rooms || ([] as PropertyRooms[])
      return rooms.find((room) => room?.code === this.roomCode) as PropertyRooms
    },
    propertyPrice(): string {
      const rentFee = get(this.selectedRoom, 'fees.monthly.rentFee', 0)

      return `${formatPrice(rentFee)}${
        this.maintenanceFee ? ' +' + formatPrice(this.maintenanceFee) : ''
      }`
    },
    maintenanceFee(): number {
      return get(this.selectedRoom, 'fees.monthly.maintenanceFee', 0)
    },
    roomName(): string {
      if (this.property && this.context2D) {
        return this.fittingString(
          getRoomName(this.property, this.roomCode),
          270
        )
      }
      return ''
    },
    depositFee(): string {
      return this.getFee('deposit')
    },
    keyMoney(): string {
      return this.getFee('keyMoney')
    },
    agencyFee(): string {
      const fee = this.toFormattedMoveInFees('agencyFee')
      return fee === 0 ? formatPrice(fee) + ' ( Free)' : formatPrice(fee)
    },
    stationsAccessByWalk(): {
      max: number
      first?: string | null
      other?: (string | null)[]
    } {
      const stations = get(this.property, 'access.stations', [])
      const stationArray: (string | null)[] = getStationAccessMultiple(stations)
      const maxAccess = stationArray.length
      const [first, ...other] = stationArray
      return {
        max: maxAccess > 1 ? maxAccess : 2,
        first: this.fittingString(first as string, 330),
        other: other.length
          ? other.map((item) => this.fittingString(item as string, 330))
          : [''],
      }
    },
    buildDate(): string {
      const date = get(this.property, 'builtDate', new Date())
      return dayjs(date).format('YYYY')
    },
    mainWindow(): string {
      const window = get(this.selectedRoom, 'mainWindow', '')
      return get(mainWindow, window, '')
    },
    availability(): string {
      const availability = this.selectedRoom?.availability
      if (availability?.now) {
        return 'Now'
      }
      if (availability?.enquire) {
        return 'Please ask the agent'
      }
      return ''
    },
    buildingType(): string {
      const type = get(this.property, 'buildingType', '')
      return get(buildingType, type, '')
    },
    buildingStructure(): string {
      const propertyStructure = get(this.property, 'structure', '')
      return get(structure, propertyStructure, '')
    },
    roomFacilities(): Array<string> {
      const roomFacilities = get(this.selectedRoom, 'facilities', null)
      if (!roomFacilities) {
        return []
      }
      const facilitiesArr: Array<string> = []
      delete roomFacilities.__typename
      Object.keys(roomFacilities).forEach((item: string) => {
        facilitiesArr.push(...get(roomFacilities, item, []))
      })
      return facilitiesArr
    },
    agency(): AgencyData | null {
      if (this.property) {
        const agency: Agency | null = get(this.property, 'agency', null)
        if (agency) {
          return {
            img: this.$config.cdnUrl + get(agency, 'public.imageUrl', ''),
            name: get(agency, 'public.name', ''),
            phone: get(agency, 'public.phone', ''),
            licence: this.getLicense(agency),
            businessHour: `${get(
              agency,
              'public.businessHour.start',
              '00:00'
            )}AM - ${get(agency, 'public.businessHour.end', '00:00')}PM`,
          }
        }
      }
      return null
    },
    propertyLink(): string {
      return `${this.$config.webUrl}/rent/tokyo/${this.roomCode}`
    },
    htmlToPdfOption(): any {
      return {
        margin: 0,
        filename: this.roomCode + '.pdf',
        html2canvas: {
          allowTaint: false,
          useCORS: true,
          logging: true,
          scale: 2,
        },
        image: {
          type: 'jpeg',
          quality: 1,
        },
        jsPDF: {
          orientation: 'landscape',
        },
      }
    },
  },
  watch: {
    property(value: null | Property): void {
      if (value !== null && !this.downloaded) {
        this.$nextTick(() => {
          this.downloaded = true
          this.download()
        })
      }
    },
  },
  mounted() {
    const canvas = this.$refs.myCanvas as HTMLCanvasElement
    this.context2D = canvas.getContext('2d')
  },
  methods: {
    download() {
      const domRef: any = this.$refs.html2Pdf
      domRef.generatePdf()
    },
    closeWindow() {
      // TODO: Uncomment next line after debug...
      // window.close()
    },
    toFormattedMoveInFees(key: string): number {
      if (!this.selectedRoom?.fees) {
        // eslint-disable-next-line no-console
        console.error(
          'this.selectedRoom.fees does not exist. Pls fix on the database'
        )
        return 0
      }
      const baseFee = get(
        this.selectedRoom,
        `fees.moveIn.${key}`,
        {}
      ) as FeesMoveIn
      const type = baseFee?.feeType || MoveInFeeType.FixedPrice
      const baseValue = baseFee?.value || 0

      // If the type is a multiplier,
      // guarantor fee is calculated by (monthly rent fee + maintenance fee)
      // for the others, only use the monthly rent fee alone
      const baseMultiplierFee =
        key === 'guarantorFee'
          ? this.selectedRoom?.monthlyFee
          : this.selectedRoom?.fees?.monthly?.rentFee

      return type === MoveInFeeType.MonthlyMultiplier
        ? baseValue * (baseMultiplierFee || 0)
        : baseValue
    },
    getLicense(agency: Agency): string {
      const issueNumber = get(agency, 'basic.license.issueNumber', '')
      const licenseNumber = get(agency, 'basic.license.number', '')
      const agencyLicenseType = get(agency, 'basic.license.licenseType', '')
      return get(licenseType, agencyLicenseType, '')
        .replace('{licenseNumber}', licenseNumber)
        .replace('{issueNumber}', issueNumber)
    },
    get,
    getRoomLayout,
    fittingString(str: string, maxWidth: number): string {
      if (this.context2D) {
        let width = this.context2D.measureText(str as string).width
        const ellipsis = '…'
        const ellipsisWidth = this.context2D.measureText(ellipsis).width
        if (width <= maxWidth || width <= ellipsisWidth) {
          return str
        } else {
          let len = str.length
          while (width >= maxWidth - ellipsisWidth && len-- > 0) {
            str = str.substring(0, len)
            width = this.context2D.measureText(str).width
          }
          return str + ellipsis
        }
      }
      return str
    },
    getFee(key: string): string {
      const feeType = get(this.selectedRoom, `fees.moveIn.${key}.feeType`)
      const feeValue = get(this.selectedRoom, `fees.moveIn.${key}.value`)
      const fee = formatPrice(this.toFormattedMoveInFees(key))
      const suffix = feeValue > 1 ? 'months' : 'month'
      return feeType ===
        EnumPropertyRoomsFeesMoveInDepositFeeType.MonthlyMultiplier
        ? fee + ` (${feeValue} ${suffix})`
        : fee
    },
  },
  apollo: {
    property: {
      query: getPropertyByRoomCode,
      variables(): { roomCode: string | null } {
        return {
          roomCode: this.roomCode,
        }
      },
    },
  },
})
</script>

<style scoped lang="scss">
.pdf-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  padding-top: 20px;
  font-size: 14px;
  margin: 0;
  max-height: 794px; /* this is the magic number */

  .pdf-item:first-child {
    flex: 1;
  }
}

.features-holder {
  min-height: 186px !important;
  max-height: 186px;
  overflow-y: hidden;
}

.features-cell {
  line-height: 16px;
}

.pdf-row {
  display: flex;
  align-items: flex-start;
}

.pdf-wrap {
  padding: 0 15px 0 20px;
  //max-width: 1100px;
  margin-left: auto;
  margin-right: auto;
}

.pdf-footer {
  color: #fff;
  background: #2b4663;
  padding: 15px 20px;
  margin-top: 10px;

  .pdf-row {
    justify-content: space-between;
  }

  .logo {
    max-width: 130px;
    height: auto;
    margin-bottom: 5px;
  }
}

.agency-avatar {
  width: 85px;
  height: 85px;
  overflow: hidden;
  position: relative;
  margin-right: 12px;

  img {
    min-width: 100px;
    max-width: 100%;
    height: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.phone-icon {
  width: 34px;
  height: 34px;
  margin-right: 10px;

  img {
    width: 100%;
    height: 100%;
  }
}

.pdf-images {
  width: calc(40% - 20px);
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  height: 630px;
  margin-right: 20px;

  .picture {
    border: 1px solid #aaa;
    max-width: 100%;
    position: relative;
    padding-bottom: 30%;
    width: calc(50% - 4px);
    padding-left: 5px;

    &.less-image,
    &:first-child {
      width: 100%;
      padding-bottom: 60%;
    }

    img {
      position: absolute;
      top: 50%;
      left: 50%;
      max-width: 100%;
      height: 100%;
      transform: translate(-50%, -50%);
    }
  }
}

.pdf-info {
  width: 60%;
}

.property-type {
  display: inline-block;
  text-transform: uppercase;
  background-color: #d31474;
  padding: 8px 20px;
  height: 40px;
  color: #fff;
  font-family: Arial, sans-serif;
  font-size: 22px;
  font-weight: bold;
  letter-spacing: 0;
  margin-bottom: 4px;
}

.property-info-table {
  border-collapse: collapse;

  tr {
    border: none !important;
  }

  th {
    background-color: #eee;
    width: 175px;
  }

  th,
  td {
    padding: 5px 10px;
    border: 1px solid #aaa;
    border-radius: 0;
  }

  td {
    width: calc(100% - 175px);
    background: #fff;

    div {
      min-height: 19px;
    }
  }

  .multi-col {
    th,
    td {
      width: 25%;
    }
  }
}

.facility {
  font-size: 11px;
  border-radius: 3px;
  display: inline-block;
  margin-right: 7px;
  margin-bottom: 7px;
  color: #aaa;
  line-height: 8px;
  font-family: 'Noto Sans', sans-serif;

  &:not(:last-child) {
    &::after {
      content: '/';
    }
  }

  &.selected {
    font-weight: bold;
    color: #d31474;
  }
}

.building-name,
.property-address {
  font-size: 22px;
  line-height: 32px;
  overflow: hidden;
  white-space: nowrap;
}

.property-address {
  margin-bottom: 5px;
}

.agency-name {
  font-family: Arial, sans-serif;
  font-size: 18px;
  font-weight: bold;
  line-height: 22px;
  text-transform: uppercase;
}

.agency-license {
  font-family: 'Noto Sans', sans-serif;
  font-size: 11px;
  line-height: 19px;
}

.business-hour {
  font-family: 'Noto Sans', sans-serif;
  font-size: 16px;
}

.property-info--left {
  margin-right: 15px;
  font-family: Arial, sans-serif;
  font-size: 18px;
  line-height: 21px;

  p + p {
    margin-top: 2px;
  }

  a {
    color: #fff;
  }
}

.qr-img {
  width: 85px;
  height: 85px;
}

.agency-phone {
  margin-top: 8px;
  font-family: Arial, sans-serif;
  font-size: 40px;
  font-weight: bold;
  line-height: 35px;
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.agency-info {
  margin-right: 15px;
}
.price {
  font-size: 24px;
  font-weight: bold;
}

.price-maintenance {
  font-size: 16px;
  font-weight: bold;
}

.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;

  td & {
    width: 467px;
  }
}
</style>
<style lang="scss">
.pdf-export .vue-html2pdf .pdf-preview {
  width: 100vw !important;
  height: 100vh !important;
  top: 0 !important;
  button {
    display: none !important;
  }
}
</style>
