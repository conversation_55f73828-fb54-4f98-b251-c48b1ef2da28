<template>
  <div class="container">
    <h2>Sign Up</h2>
    <notice v-if="errorMessage" :message="errorMessage"></notice>
    <input v-model="form.email" type="email" placeholder="<EMAIL>" />
    <input v-model="form.password" type="password" placeholder="●●●●●●●●●●" />
    <button @click.prevent="signUp">Sign up</button>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import signupGql from '~/graphql/mutations/signup.gql'
import Notice from '~/components/Notice.vue'

export default Vue.extend({
  components: { Notice },
  layout: 'blank',
  data: () => ({
    form: {
      email: '',
      password: '',
    },
    errorMessage: null,
  }),
  async mounted() {
    await this.$apolloHelpers.onLogout()
  },
  methods: {
    async signUp() {
      try {
        const result = await this.$apollo.mutate({
          mutation: signupGql,
          variables: this.form,
        })
        const { accessToken } = result.data.signUp
        await this.$apolloHelpers.onLogin(accessToken)
        this.$router.push({ path: '/signin' })
      } catch (error) {
        const message = get(
          error,
          'networkError.result.errors[0].message',
          error.message
        )
        this.errorMessage = message
      }
    },
  },
})
</script>

<style lang="scss" scoped>
.container {
  margin: 0 auto;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  gap: 12px;

  input {
    padding: 12px;
  }

  button {
    outline: 0;
    border: 0;
    background: #000;
    color: #eee;
    padding: 12px 24px;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }
  }
}
</style>
