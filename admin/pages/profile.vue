<template>
  <div class="container">
    <h1>Profile</h1>
    <div class="content">
      <div class="row">
        <label>Email</label>
        {{ user && user.email }}
      </div>
      <div class="row">
        <label>First Name</label>
        {{ (user && user.firstName) || 'Unknown' }}
      </div>
      <div class="row">
        <label>Last Name</label>
        {{ (user && user.lastName) || 'Unknown' }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import getUserGql from '~/graphql/queries/getUser.gql'

export default Vue.extend({
  apollo: {
    user: { query: getUserGql },
  },
})
</script>
