<template>
  <div class="container">
    <h1>Change Password</h1>
    <div class="content form">
      <notice v-if="errorMessage" :message="errorMessage" class="error" />
      <notice
        v-else-if="isChanged"
        message="Password has been changed."
        class="success"
      />
      <label>Current Password</label>
      <input
        v-model="form.oldPassword"
        type="password"
        placeholder="●●●●●●●●●●"
      />
      <label>New Password</label>
      <input
        v-model="form.newPassword"
        type="password"
        placeholder="●●●●●●●●●●"
      />
      <label>New Password (retype)</label>
      <input
        v-model="form.retypePassword"
        type="password"
        placeholder="●●●●●●●●●●"
      />
      <button class="submit" @click.prevent="changePassword">Change</button>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import changePasswordGql from '~/graphql/mutations/changePassword.gql'

const errorsKeyMap = {
  oldPassword: 'Current password',
  newPassword: 'New password',
  retypePassword: 'Re-type password',
}

export default Vue.extend({
  middleware: ['isAuth'],
  data: () => ({
    form: {
      oldPassword: '',
      newPassword: '',
      retypePassword: '',
    },
    errorMessage: null as null | string,
    isLoading: true,
    isChanged: false,
  }),
  methods: {
    async changePassword() {
      if (this.validate()) {
        try {
          const result = await this.$apollo.mutate({
            mutation: changePasswordGql,
            variables: {
              old: this.form.oldPassword,
              new: this.form.newPassword,
            },
          })
          this.isChanged = result.data.changePassword.succeed
          this.form.oldPassword = ''
          this.form.newPassword = ''
          this.form.retypePassword = ''
        } catch (error) {
          this.errorMessage = get(
            error,
            'networkError.result.errors[0].message',
            error.message
          )
        }
      }
    },
    validate(): boolean {
      const message = []
      for (const key in this.form) {
        if (!get(this.form, key, '')) {
          message.push(get(errorsKeyMap, key, '') + ' is required')
        }
      }

      if (!message.length) {
        if (this.form.newPassword !== this.form.retypePassword) {
          message.push('Typed passwords are not same. Please check again.')
        }
      }

      this.errorMessage = message.join(', ')

      return !message.length
    },
  },
})
</script>

<style lang="scss" scoped>
label {
  font-size: 12px;
  color: #333;
  margin-bottom: 10px;
  font-weight: 700;
}

input {
  border-radius: 8px;
  padding: 10px 12px;
  border: 1px solid #ddd;
  outline: none;
  transition: 0.3s;
  height: 34px;
  font-size: 12px;
  margin-bottom: 10px;
}

.error {
  color: red;
  font-size: 13px;
  margin-bottom: 10px;
}

.success {
  color: #23aa75;
  font-size: 13px;
  margin-bottom: 10px;
}
</style>
