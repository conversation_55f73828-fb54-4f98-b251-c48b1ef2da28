<template>
  <div class="container-common">
    <FormCommon>
      <template v-if="isRequested">
        <p>We've sent the reset password email, please check your mail box.</p>
      </template>
      <template v-else>
        <notice
          v-if="errorMessage"
          :message="errorMessage"
          :type="'error__message'"
        ></notice>
        <form-input label="メールアドレス">
          <input
            v-model="form.email"
            type="email"
            placeholder="<EMAIL>"
          />
        </form-input>
        <button @click.prevent="reset">新しいパスワードを作成する</button>
        <NuxtLink class="nuxt__link" to="/">ログインに戻る</NuxtLink>
      </template>
    </FormCommon>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import signInGql from '~/graphql/mutations/resetPassword.gql'

export default Vue.extend({
  layout: 'blank',
  data: () => ({
    form: {
      email: '',
    },
    errorMessage: null,
    isRequested: false,
  }),
  methods: {
    async reset() {
      try {
        const result = await this.$apollo.mutate({
          mutation: signInGql,
          variables: this.form,
        })
        this.isRequested = result.data.resetPassword.succeed
      } catch (error) {
        const message = get(
          error,
          'networkError.result.errors[0].message',
          error.message
        )
        this.errorMessage = message
      }
    },
  },
})
</script>
