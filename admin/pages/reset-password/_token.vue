<template>
  <div class="container">
    <h2>Reset Password</h2>
    <template v-if="isChanged">
      <p>Password has been changed! We'll redirect you back shortly.</p>
    </template>
    <template v-else>
      <notice v-if="errorMessage" :message="errorMessage"></notice>
      <label>New Password</label>
      <input
        v-model="form.newPassword"
        type="password"
        placeholder="●●●●●●●●●●"
      />
      <label>New Password (retype)</label>
      <input
        v-model="form.retypePassword"
        type="password"
        placeholder="●●●●●●●●●●"
      />
      <button :disabled="!token" @click.prevent="changePassword">Change</button>
    </template>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import newPasswordGql from '~/graphql/mutations/newPassword.gql'

export default Vue.extend({
  layout: 'blank',
  asyncData(context: any) {
    return { token: context.params.token }
  },
  data: () => ({
    form: {
      newPassword: '',
      retypePassword: '',
    },
    errorMessage: null as null | string,
    token: null as null | string,
    isLoading: true,
    isNotValidToken: false,
    isChanged: false,
  }),
  methods: {
    async changePassword() {
      if (this.form.newPassword !== this.form.retypePassword) {
        this.errorMessage = 'Typed passwords are not same. Please check again.'
        return
      }

      try {
        const result = await this.$apollo.mutate({
          mutation: newPasswordGql,
          variables: {
            new: this.form.newPassword,
            token: this.token,
          },
        })
        const { accessToken } = result.data.newPassword
        await this.$apolloHelpers.onLogin(accessToken)
        await new Promise((resolve) => setTimeout(resolve, 3000))
        this.$router.push({ path: '/' })
      } catch (error) {
        const message = get(
          error,
          'networkError.result.errors[0].message',
          error.message
        )
        this.errorMessage = message
      }
    },
  },
})
</script>

<style lang="scss" scoped>
.container {
  margin: 0 auto;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  gap: 10px;
  label {
    font-size: 10px;
    color: #aaa;
    text-transform: uppercase;
  }
  input {
    padding: 14px;
  }

  button {
    outline: 0;
    border: 0;
    background: #000;
    color: #eee;
    padding: 12px 24px;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }
  }
}
</style>
