<template>
  <div class="container">
    <h1>REJ Import Log Detail</h1>
    <div v-if="!isAuthorized">
      Sorry, you are not authorized to see this page<br />
      Make sure you are logged in with correct authorization
    </div>
    <template v-else>
      <section v-if="rejImportResultById" class="user_info">
        <div class="section-title">
          <h2>Summary</h2>
        </div>
        <div class="row">
          <label>Date</label>
          <p class="value">{{ formatDate(rejImportResultById.createdAt) }}</p>
        </div>
        <div class="row">
          <label>Processed Row</label>
          <p class="value">{{ rejImportResultById.processedRow }}</p>
        </div>
        <div class="row">
          <label>Imported Row</label>
          <p class="value">{{ rejImportResultById.importedRow }}</p>
        </div>
        <div class="row">
          <label>Incompatible Row</label>
          <p class="value">
            {{ get(rejImportResultById, 'parsingErrors.length', 0) }}
          </p>
        </div>
        <div class="row">
          <label>Removed Property</label>
          <p class="value">{{ rejImportResultById.removedProperty }}</p>
        </div>
      </section>
      <section class="user_info">
        <div class="section-title">
          <h2>Incompatible Rows</h2>
        </div>
        <table>
          <tr>
            <th>REJ Property ID</th>
            <th>Column Name</th>
            <th>Error Message</th>
          </tr>
          <template v-for="error in rejImportResultById.parsingErrors">
            <tr
              v-for="(item, index) in error.items"
              :key="`${error.propertyId}_${index}`"
              class="table-row"
            >
              <td v-if="index === 0" :rowspan="error.items.length">
                {{ error.propertyId }}
              </td>
              <td>
                {{ item.colName }}
              </td>
              <td>{{ item.message }}</td>
            </tr>
          </template>
        </table>
      </section>
    </template>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import getREJImportResultById from '~/graphql/rej/rejImportResultById.query.gql'
import { Maybe, RejImportResult } from '~/types/types'
import { formatDate } from '~/constants/utils'

export default Vue.extend({
  asyncData(context: any) {
    return { rejImportId: context.params.id }
  },
  data: () => ({
    errorMessage: null as null | string,
    isChanged: false,
    rejImportId: '',
    rejImportResultById: {} as Maybe<RejImportResult>,
  }),
  computed: {
    isAuthorized() {
      if (!this.rejImportId) return true
      const { currentAgencyId, isAdmin } = this.$accessor
      if (!currentAgencyId) return isAdmin
      return currentAgencyId === this.rejImportResultById?.agencyId
    },
  },
  methods: { get, formatDate },
  apollo: {
    rejImportResultById: {
      query: getREJImportResultById,
      variables() {
        return { id: this.rejImportId }
      },
    },
  },
})
</script>
<style lang="scss" scoped>
@import '~/assets/scss/settings/variables';

.title {
  font-size: 24px;
}

section {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  max-width: 1200px;

  &:not(:last-child) {
    margin-bottom: 40px;
  }
}

.row {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;

  &:not(:last-child) {
    margin-bottom: 15px;
  }

  label {
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .value {
    font-size: 14px;
  }
}
</style>
