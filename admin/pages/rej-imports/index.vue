<template>
  <div class="container">
    <h1>REJ Imports</h1>
    <div v-if="!isAuthorized">
      Sorry, you are not authorized to see this page<br />
      Make sure you are logged in with correct authorization
    </div>
    <template v-else-if="isEmpty">
      <p>There is no log REJ imports yet.</p>
    </template>
    <template v-else>
      <pagination-bar v-bind="rejImportResults.pageInfo" path="rej-imports" />
      <table>
        <tr>
          <th>日付</th>
          <th>Processed Row</th>
          <th>Imported Row</th>
          <th>Removed Property</th>
        </tr>
        <template v-if="$apollo.queries.rejImportResults.loading">
          <tr v-for="row in 10" :key="row">
            <td v-for="col in 6" :key="col">
              <lines style="height: 15px; width: 100%" class="shine"></lines>
            </td>
          </tr>
        </template>
        <template v-else>
          <tr
            v-for="item in rejImportResults.items"
            :key="item._id"
            class="table-row"
            @click.prevent="handleOnItemClick(item._id)"
          >
            <td>{{ formatDate(item.createdAt) }}</td>
            <td>{{ item.processedRow }}</td>
            <td>{{ item.importedRow }}</td>
            <td>{{ item.removedProperty }}</td>
          </tr>
        </template>
      </table>
      <pagination-bar v-bind="rejImportResults.pageInfo" path="rej-imports" />
    </template>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import getREJImportResults from '~/graphql/rej/rejImportResults.query.gql'
import { RejImportResultPagination } from '~/types/types'
import { formatDate } from '~/constants/utils'

export default Vue.extend({
  name: 'REJImportResults',
  data: () => ({
    rejImportResults: {
      pageInfo: {
        currentPage: 0,
        perPage: 30,
      },
      items: [],
    } as RejImportResultPagination,
  }),
  computed: {
    isAuthorized(): boolean {
      const { currentAgencyId, isAdmin } = this.$accessor
      return !currentAgencyId && isAdmin
    },
    currentPage(): number {
      return Number.parseInt(this.$route.query?.page as string) || 0
    },

    isEmpty(): boolean {
      const isFetching = this.$apollo?.queries?.rejImportResults?.loading
      const hasItem = this.rejImportResults?.items?.length
      return !isFetching && !hasItem
    },
  },
  watchQuery: ['page'],
  methods: {
    get,
    formatDate,
    async handleOnItemClick(_id: string) {
      await this.$router.push({ path: `/rej-imports/${_id}` })
    },
  },
  apollo: {
    rejImportResults: {
      query: getREJImportResults,
      fetchPolicy: 'network-only',
      variables(): { page: number } {
        return {
          page: this.currentPage,
        }
      },
    },
  },
})
</script>

<style lang="scss" scoped>
.inquiry-unread {
  background: #fff2f2;
}
</style>
