<template>
  <div class="container">
    <h1>物件特集</h1>
    <p>物件IDを入力してください。</p>
    <div class="setting__area">
      <div class="horizontal">
        <form-input-text
          v-for="i in 8"
          :key="i"
          v-model="featured[i - 1]"
          :caption="`${i}`"
          is-optional
        />
      </div>
      <button class="button--blue" @click.prevent="onSaveSetting">
        保存する
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import fill from 'lodash/fill'
import getFeatured from '~/graphql/queries/getSettingFeaturedProperties.gql'
import createOrUpdateFeature from '~/graphql/mutations/createOrUpdateFeatureProperties.gql'
export default Vue.extend({
  name: 'Setting',
  middleware: ['isAdmin'],
  data() {
    return {
      featured: fill(Array(8), '') as string[],
    }
  },
  computed: {
    currentAgencyId() {
      return this.$accessor.currentAgencyId
    },
  },
  watch: {
    currentAgencyId(newValue) {
      if (newValue) {
        this.$router.push('/')
      }
    },
  },
  mounted() {
    if (this.$accessor.currentAgencyId) {
      this.$router.push('/')
    }
  },
  methods: {
    async onSaveSetting() {
      try {
        const result = await this.$apollo.mutate({
          mutation: createOrUpdateFeature,
          variables: {
            featured: this.featured.filter(
              (code) => code && code.trim() !== ''
            ),
          },
        })
        if (get(result, 'data.createOrUpdateFeatured', null)) {
          this.$toast.success('Save setting success', { duration: 5000 })
        } else {
          this.$toast.error(
            'Error during save setting. Please try again later!',
            { duration: 5000 }
          )
        }
      } catch (e) {
        this.$toast.error(e.message, { duration: 5000 })
      }
    },
  },
  apollo: {
    setting: {
      query: getFeatured,
      result({ data }) {
        const result = get(data, 'setting.featured', [])
        const length = result.length || 0
        if (length) {
          this.featured = [...result, ...fill(Array(8 - length), '')]
        }
      },
    },
  },
})
</script>

<style scoped lang="scss">
.setting__area {
  max-width: 880px;
  padding-top: 40px;
}

.button--blue {
  color: #fff;
  border: none;
  width: 100px;
  font-size: 14px;
  padding: 10px 22px;
  border-radius: 4px;
  display: inline-block;
  background-color: #1b7fa7;

  &:hover {
    background-color: rgba(#1b7fa7, 0.9);
  }
}
.horizontal {
  display: flex;
  flex-wrap: wrap;

  & > * {
    margin-right: 20px;
    flex: 1 1 calc(25% - 20px);
  }
}
</style>
