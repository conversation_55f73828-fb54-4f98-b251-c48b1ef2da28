<template>
  <div class="container">
    <h1>
      反響一覧 <template v-if="roomCode">(#{{ roomCode }})</template>
      <div class="inquiry-filter">
        <date-range-filter
          v-model="filterDate"
          :filter-default="FilterKey.custom"
          @input="onChangeFilterDate"
        />
      </div>
    </h1>
    <template v-if="isEmpty">
      <p>There is no inquiry available.</p>
    </template>
    <template v-else>
      <pagination-bar v-bind="inquiries.pageInfo" path="inquiries" />
      <table>
        <tr>
          <th>日付</th>
          <th>物件名</th>
          <th>名前</th>
          <th>連絡先</th>
          <th>賃料</th>
          <th>反響課金</th>
        </tr>
        <template v-if="$apollo.queries.inquiries.loading">
          <tr v-for="row in 10" :key="row">
            <td v-for="col in 6" :key="col">
              <lines style="height: 15px; width: 100%" class="shine"></lines>
            </td>
          </tr>
        </template>
        <template v-else>
          <tr
            v-for="item in items"
            :key="item._id"
            class="table-row"
            :class="item.unread ? 'inquiry-unread' : ''"
            @click.prevent="handleOnItemClick(item._id, item.type)"
          >
            <td>{{ formatDate(item.createdAt) }}</td>
            <td>{{ item.buildingName }}</td>
            <td>{{ item.name }}</td>
            <td>{{ item.email }}</td>
            <td>{{ item.monthlyFee }}</td>
            <td>{{ item.serviceFee }}</td>
          </tr>
        </template>
      </table>
      <pagination-bar v-bind="inquiries.pageInfo" path="inquiries" />
    </template>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import set from 'lodash/set'
import startCase from 'lodash/startCase'
import setWith from 'lodash/setWith'
import dayjs from 'dayjs'
import getInquiriesGql from '~/graphql/inquiries/getInquiries.query.gql'
import getFormsGql from '~/graphql/forms/getForms.query.gql'
import readInquiryGql from '~/graphql/mutations/readInquiry.gql'
import {
  FormPagination,
  InquiryPagination,
  SortFindManyInquiryInput,
} from '~/types/types'
import { formatPrice } from '~/plugins/number.utils'
import {
  dateRangeValid,
  formatDate,
  getDateRangeFormatted,
  validDateRange,
} from '~/constants/utils'
import { FilterKey } from '~/constants/constant'

enum ItemRowType {
  Inquiry,
  Form,
}

interface ItemRow {
  _id: string
  type: ItemRowType
  unread: boolean
  createdAt: string
  buildingName: string
  name: string
  email: string
  monthlyFee: string
  serviceFee: string
}

export default Vue.extend({
  name: 'InquiryListPage',
  asyncData({ query }): Promise<object | void> | object | void {
    if (query?.date && validDateRange(query.date as string)) {
      const date = query.date as string
      const [dateStart, dateEnd] = date.split('~')
      return {
        filterDate: [
          dayjs(dateStart, 'YYYY-MM-DD').toDate(),
          dayjs(dateEnd, 'YYYY-MM-DD').toDate(),
        ],
      }
    }

    return {
      filterDate: [],
    }
  },
  data: () => ({
    inquiries: {} as InquiryPagination,
    forms: {} as FormPagination,
    sort: SortFindManyInquiryInput.CreatedatDesc,
    filterDate: [] as Date[],
    FilterKey,
  }),
  computed: {
    currentPage() {
      return Number.parseInt(this.$route.query?.page as string) || 0
    },
    roomCode() {
      return this.$route.query?.roomCode || null
    },
    isEmpty(): boolean {
      const isFetching = this.$apollo.queries.inquiries.loading
      const hasItem = this.inquiries?.items?.length
      return !isFetching && !hasItem
    },
    items(): ItemRow[] {
      const inquiries =
        this.inquiries?.items?.map((item) => ({
          _id: item._id,
          type: ItemRowType.Inquiry,
          unread: !!item.unread,
          createdAt: item.createdAt,
          buildingName: get(item, 'property.buildingName', '——'),
          name: item.name,
          email: item.email,
          monthlyFee: this.formatPrice(item.monthlyFee),
          serviceFee: this.formatPrice(item.serviceFee),
        })) || []

      const forms =
        this.forms?.items?.map((item) => ({
          _id: item._id,
          type: ItemRowType.Form,
          unread: false,
          createdAt: item.createdAt,
          buildingName: startCase(item.formType),
          name: `${item.firstName} ${item.lastName}`,
          email: item.email,
          monthlyFee: '——',
          serviceFee: '——',
        })) || []

      return inquiries
        .concat(this.$accessor.isAdminOrInhouseAgency ? forms : [])
        .sort((a, b) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))
    },
    inquiriesDateRange() {
      const sortedInquiries = (this.inquiries?.items || [])
        .slice()
        .sort((a, b) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))
      if (sortedInquiries.length < 3) return {}

      const start = sortedInquiries[0].createdAt
      const end = sortedInquiries[sortedInquiries.length - 1].createdAt
      const gte = setWith(
        {},
        '_operators.createdAt.gte',
        dayjs(end).add(1, 'day').toISOString()
      )

      if (this.currentPage < 2) return gte
      return {
        AND: [
          setWith(
            {},
            '_operators.createdAt.lte',
            dayjs(start).add(6, 'hours').toISOString()
          ),
          gte,
        ],
      }
    },
  },
  watchQuery: ['page', 'date', 'roomCode'],
  methods: {
    get,
    formatPrice,
    async handleOnItemClick(_id: string, type: ItemRowType) {
      if (type === ItemRowType.Form) {
        this.$router.push({ path: `/forms/${_id}` })
        return
      }
      const options = {
        mutation: readInquiryGql,
        variables: {
          inquiryId: _id,
        },
      }
      const result = await this.$apollo.mutate(options)
      const isSuccess = !!get(result, `data.setInquiryRead`)
      if (isSuccess) {
        await this.$store.dispatch('fetchReadInquiry')
      }
      this.$router.push({ path: `/inquiries/${_id}` })
    },
    getFilterDate() {
      if (dateRangeValid(this.filterDate)) {
        const { start, end } = getDateRangeFormatted(this.filterDate)
        return {
          AND: [
            setWith({}, '_operators.createdAt.gte', dayjs(start).toISOString()),
            setWith(
              {},
              '_operators.createdAt.lte',
              dayjs(end).add(1, 'day').toISOString()
            ),
          ],
        }
      }
      return null
    },
    onChangeFilterDate(dateSelected: Date[]) {
      const isDateValid = dateRangeValid(dateSelected)
      const { start, end } = getDateRangeFormatted(dateSelected)
      const query = {
        ...this.$route.query,
        page: undefined, // reset the page
        date: isDateValid ? `${start}~${end}` : undefined,
      }
      this.$router.push({ query })
    },
    formatDate,
    dateRangeValid,
  },
  apollo: {
    inquiries: {
      query: getInquiriesGql,
      fetchPolicy: 'network-only',
      variables(): { page: number; sort: string; filter: any } {
        const filterDate = this.getFilterDate()
        const filter = filterDate || {}

        if (this.$accessor.currentAgencyId) {
          set(filter, 'agencyId', this.$accessor.currentAgencyId)
        }

        if (this.roomCode) {
          set(filter, 'roomCode', this.roomCode)
        }
        return {
          page: this.currentPage,
          sort: this.sort,
          filter,
        }
      },
    },
    forms: {
      query: getFormsGql,
      fetchPolicy: 'network-only',
      variables(): { page: number; sort: string; filter: any } {
        const filterDate = this.getFilterDate()
        return {
          page: filterDate ? this.currentPage : 1,
          filter: filterDate || this.inquiriesDateRange,
          sort: this.sort,
        }
      },
      skip(): boolean {
        return !this.$accessor.isAdminOrInhouseAgency || !this.inquiries?.items
      },
    },
  },
})
</script>

<style lang="scss" scoped>
.inquiry-unread {
  background: #fff2f2;
}
</style>
