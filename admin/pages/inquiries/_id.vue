<template>
  <div class="container">
    <h1>お問い合わせ内容</h1>
    <div v-if="!isAuthorized">
      Sorry, you are not authorized to manage this property<br />
      Make sure you are logged in as the owner of this property
    </div>
    <template v-else>
      <section
        v-if="
          get(inquiryById, 'utm.utm_source') ||
          get(inquiryById, 'affiliate.code')
        "
        class="user_info"
      >
        <div class="section-title">
          <h2>TRACKING</h2>
        </div>
        <div v-if="inquiryById.utm.utm_source" class="row">
          <label>UTM Source</label>
          <p class="value">{{ inquiryById.utm.utm_source }}</p>
        </div>
        <div v-if="inquiryById.affiliate.code" class="row">
          <label>パートナー名</label>
          <p class="value">{{ inquiryById.affiliateDetail.name }}</p>
        </div>
      </section>
      <section v-if="inquiryById" class="user_info">
        <div class="section-title">
          <h2>お客様情報</h2>
        </div>
        <div class="row">
          <label>お名前</label>
          <p class="value">{{ inquiryById.name }}</p>
        </div>
        <div class="row">
          <label>メールアドレス</label>
          <p class="value">{{ inquiryById.email }}</p>
        </div>
        <div class="row">
          <label>電話番号</label>
          <p class="value">{{ inquiryById.phone }}</p>
        </div>
        <div v-if="preferredLanguage" class="row">
          <label>ご希望の言語</label>
          <p class="value">{{ preferredLanguage }}</p>
        </div>
        <div v-if="inquiryById.whenToMoveIn" class="row">
          <label>When To Move In</label>
          <p class="value">
            {{ translate(inquiryById.whenToMoveIn, constants.whenToMoveIn) }}
          </p>
        </div>
        <div class="row">
          <label>問い合わせ内容</label>
          <p class="value" v-html="purposeText"></p>
        </div>
        <div v-if="inquiryById.memo" class="row">
          <label>メモ</label>
          <p class="value">{{ inquiryById.memo }}</p>
        </div>
      </section>
      <section class="user_info">
        <div class="section-title">
          <h2>問い合わせ物件</h2>
        </div>
        <inquiry-room
          v-if="chosenRoom"
          :property-id="get(inquiryById, 'property._id')"
          :room-name="get(inquiryById, 'property.buildingName', '')"
          :room="chosenRoom"
          :prefecture-path="
            get(inquiryById, 'property.prefecturePath', '__pref__')
          "
          :address-detail="get(inquiryById, 'property.addressDetail', {})"
          :access="get(inquiryById, 'property.access.stations', {})"
        />
      </section>
    </template>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import getInquiryGql from '~/graphql/inquiries/getInquiry.query.gql'
import InquiryRoom from '~/components/inquiries/InquiryRoom.vue'
import { EnumInquiryPurpose, Inquiry, Maybe } from '~/types/types'
import * as constants from '~/constants/form'

const purposeDict = {
  check_availability: {
    en: 'I want to know availability',
    jp: '空室状況を確認したい',
  },
  request_tour: {
    en: 'I want to take a room tour',
    jp: '内見をしたい',
  },
  visit_office: {
    en: 'I want to visit your store',
    jp: '店舗に来店して相談したい',
  },
  check_other_property: {
    en: 'I also want to know other property information',
    jp: '他の物件情報も知りたい',
  },
  other: {
    en: 'Other',
    jp: 'その他',
  },
}

const availableLanguages = {
  en: '英語',
  jp: '日本語',
  zh: '中国語',
  kr: '韓国語',
}

export default Vue.extend({
  components: { InquiryRoom },
  asyncData(context: any) {
    return { inquiryId: context.params.id }
  },
  data: () => ({
    errorMessage: null as null | string,
    isChanged: false,
    inquiryId: '',
    inquiryById: {} as Maybe<Inquiry>,
    constants,
  }),
  computed: {
    preferredLanguage(): string {
      const list = this.inquiryById?.preferredLanguage || []
      return list
        .map(
          (key) => availableLanguages[key as keyof typeof availableLanguages]
        )
        .join('、')
    },
    chosenRoom() {
      const roomCode = this.inquiryById?.roomCode
      return this.inquiryById?.property?.rooms?.find(
        (room) => room?.code === roomCode
      )
    },
    isAuthorized() {
      if (!this.inquiryById) return true
      const { currentAgencyId, isAdmin } = this.$accessor
      if (!currentAgencyId) return isAdmin
      return currentAgencyId === this.inquiryById.agencyId
    },
    purposeText() {
      const purposes: Maybe<EnumInquiryPurpose>[] | [] = get(
        this.inquiryById,
        'purpose',
        []
      )
      const purposeText = purposes.map((item: Maybe<EnumInquiryPurpose>) => {
        return [
          get(purposeDict, item + '.jp', ''),
          get(purposeDict, item + '.en', ''),
        ].join(' - ')
      })
      return purposeText.join('<br>')
    },
  },
  methods: {
    get,
    translate(value: string | null = null, constant: any) {
      return value && constant ? constant[value] : null
    },
  },
  apollo: {
    inquiryById: {
      query: getInquiryGql,
      variables() {
        return { id: this.inquiryId }
      },
    },
  },
})
</script>
<style lang="scss" scoped>
@import '~/assets/scss/settings/variables';

.title {
  font-size: 24px;
}

section {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  max-width: 500px;

  &:not(:last-child) {
    margin-bottom: 40px;
  }
}

.row {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;

  &:not(:last-child) {
    margin-bottom: 15px;
  }

  label {
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .value {
    font-size: 14px;
  }
}
</style>
