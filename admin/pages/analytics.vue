<template>
  <div class="container">
    <h1>
      アナリティクス
      <div class="inquiry-filter">
        <date-range-filter
          v-model="filterDate"
          :filter-default="FilterKey.last7Days"
          @input="onChangeFilterDate"
        />
      </div>
    </h1>

    <div v-if="isViewingAsAdmin" class="section-title">
      <h2>全体</h2>
    </div>
    <div class="analytic">
      <div class="analytic-grid">
        <template v-if="analytics.length">
          <analytic-widget
            v-for="(data, index) in analytics"
            :key="index"
            :title="data.title"
            :content="data.content"
            :extra="data.extra"
            class="analytic-grid-item"
          />
        </template>
        <template v-else>
          <analytic-widget v-for="i in 5" :key="i" class="analytic-grid-item">
            <template #title>
              <lines style="height: 27px; width: 100%" class="shine"></lines>
            </template>
            <template #content>
              <lines style="height: 22px; width: 100%" class="shine"></lines>
            </template>
          </analytic-widget>
        </template>
      </div>
    </div>

    <!-- Extra analytics only visible to admin -->
    <template v-if="isViewingAsAdmin">
      <div v-for="extra in adminAnalytics" :key="extra.sectionTitle">
        <div class="section-title">
          <h2>{{ extra.sectionTitle }}</h2>
        </div>
        <div class="analytic">
          <div class="analytic-grid">
            <template v-if="analytics.length">
              <analytic-widget
                v-for="(data, index) in extra.items"
                :key="index"
                :title="data.title"
                :content="data.content"
                :extra="data.extra"
                class="analytic-grid-item"
              />
            </template>
            <template v-else>
              <analytic-widget
                v-for="i in 5"
                :key="i"
                class="analytic-grid-item"
              >
                <template #title>
                  <lines
                    style="height: 27px; width: 100%"
                    class="shine"
                  ></lines>
                </template>
                <template #content>
                  <lines
                    style="height: 22px; width: 100%"
                    class="shine"
                  ></lines>
                </template>
              </analytic-widget>
            </template>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import dayjs from 'dayjs'
import get from 'lodash/get'
import setWith from 'lodash/setWith'
import xor from 'lodash/xor'
import getAnalytic from '~/graphql/analytics/getAnalyticData.gql'
import getAdminAnalyticData from '~/graphql/analytics/getAdminAnalyticData.gql'
import getAgencyIdsBasedOnFees from '~/graphql/agencies/getAgencyIdsBasedOnFees.gql'
import {
  dateRangeValid,
  getDateRangeFormatted,
  validDateRange,
} from '~/constants/utils'
import { FilterKey } from '~/constants/constant'

const analyticMap = {
  uniqueInquiry: {
    title: '反響UU数',
  },
  totalInquiry: {
    title: '反響件数',
  },
  totalPageView: {
    title: 'PV数',
  },
  averageInquiredRentFee: {
    title: '反響物件の平均賃料',
    prefix: '¥',
  },
  totalServiceFee: {
    title: '合計費用',
    prefix: '¥',
  },
}

const formatter = new Intl.NumberFormat()

interface Analytic {
  title: string
  content: string
  extra?: { label: string; value: string }[]
}

interface AdminAnalyticSection {
  sectionTitle: string
  items: Analytic[]
}

export default Vue.extend({
  name: 'Analytics',
  asyncData({ query }): Promise<object | void> | object | void {
    if (query?.date && validDateRange(query.date as string)) {
      const date = query.date as string
      const [dateStart, dateEnd] = date.split('~')
      return {
        filterDate: [
          dayjs(dateStart, 'YYYY-MM-DD').toDate(),
          dayjs(dateEnd, 'YYYY-MM-DD').toDate(),
        ],
      }
    }
    return {}
  },
  data() {
    return {
      FilterKey,
      filterDate: [] as Date[],
      analytics: [] as Analytic[],
      adminAnalytics: [] as AdminAnalyticSection[],
    }
  },
  computed: {
    isViewingAsAdmin() {
      return this.$accessor.viewedAs === 'administrator'
    },
  },
  watchQuery: ['date'],
  methods: {
    dateRangeValid,
    onChangeFilterDate(dateSelected: Date[]) {
      const isDateValid = dateRangeValid(dateSelected)
      const { start, end } = getDateRangeFormatted(dateSelected)
      const query = {
        ...this.$route.query,
        date: isDateValid ? `${start}~${end}` : undefined,
      }
      this.$router.push({ query })
    },
    transform(data: any, withForm = true): Analytic[] {
      return Object.keys(analyticMap).reduce((acc: Analytic[], key: string) => {
        const hasExtrasData = ['uniqueInquiry', 'totalInquiry'].includes(key)
        const formKey = key.replace('Inquiry', '')
        const formValue = hasExtrasData
          ? Number.parseInt(get(data.form, formKey, 0))
          : 0

        const prefix = get(analyticMap, `${key}.prefix`, '')
        const value = Number.parseInt(get(data, key, 0))
        const formattedOriginalValue = formatter.format(value)
        const formattedFormValue = formatter.format(formValue)
        const formattedValue = formatter.format(value + formValue)
        return [
          ...acc,
          {
            title: get(analyticMap, `${key}.title`, ''),
            content: `${prefix}${formattedValue}`,
            extra:
              hasExtrasData && this.$accessor.isAdminOrInhouseAgency && withForm
                ? [
                    { label: 'Inquiry', value: formattedOriginalValue },
                    {
                      label: 'Form',
                      value: formattedFormValue,
                    },
                  ]
                : [],
          },
        ]
      }, [])
    },
  },
  apollo: {
    analytics: {
      query: getAnalytic,
      fetchPolicy: 'network-only',
      variables() {
        const { start, end } = getDateRangeFormatted(this.filterDate)
        const filter = {
          startDate: dayjs(start).toISOString(),
          endDate: dayjs(end).toISOString(),
        }
        if (this.$accessor.currentAgencyId) {
          setWith(filter, 'agencyId', this.$accessor.currentAgencyId)
        }

        return { filter }
      },
      result({ data }) {
        this.analytics = this.transform(data?.analytics)
      },
    },
    agencyIdsWithoutFee: {
      query: getAgencyIdsBasedOnFees,
      fetchPolicy: 'network-only',
      result({ data }) {
        this.$accessor.setAgencyIds({
          withoutFee: data.agencyIdsWithoutFee?.items?.map(
            (item: any) => item?._id
          ),
          withFee: data.agencyIdsWithFee?.items?.map((item: any) => item?._id),
        })
      },
      skip(): boolean {
        return !this.isViewingAsAdmin
      },
    },
    inhouseAnalytics: {
      query: getAdminAnalyticData,
      fetchPolicy: 'network-only',
      variables(): any {
        const { start, end } = getDateRangeFormatted(this.filterDate)
        return {
          startDate: dayjs(start).toISOString(),
          endDate: dayjs(end).toISOString(),
          inhouseAgencyIds: this.$accessor.inhouseAgencyIds,
          agencyIdsWithoutFee: xor(
            this.$accessor.agencyIds.withoutFee,
            this.$accessor.inhouseAgencyIds
          ),
          agencyIdsWithFee: this.$accessor.agencyIds.withFee,
        }
      },
      result({ data }) {
        this.adminAnalytics = [
          {
            sectionTitle: 'Living Japan',
            items: this.transform(data?.inhouseAnalytics),
          },
          {
            sectionTitle: '成約型',
            items: this.transform(data?.withoutFeeAnalytics, false),
          },
          {
            sectionTitle: '広告型',
            items: this.transform(data?.withFeeAnalytics, false),
          },
        ]
      },
      skip(): boolean {
        return !this.isViewingAsAdmin
      },
    },
  },
})
</script>

<style scoped lang="scss">
.analytic {
  margin-bottom: 40px;
}

.analytic-grid {
  gap: 20px;
  display: flex;
  flex-wrap: wrap;
}

.analytic-grid-item {
  @media (min-width: 768px) {
    width: calc(50% - (20px / 2));
  }

  @media (min-width: 1024px) {
    width: calc(33.3% - (40px / 3));
  }

  @media (min-width: 1200px) {
    width: calc(25% - (60px / 4));
  }
}

@media (max-width: 768px) {
  .wrapper .page .container h1 {
    flex-wrap: wrap;
  }
  .inquiry-filter {
    width: 100%;
    margin-top: 10px;
  }
}
</style>
