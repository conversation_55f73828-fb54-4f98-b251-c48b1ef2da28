<template>
  <div class="container">
    <h1>お問い合わせ内容</h1>
    <div v-if="!$accessor.isAdmin">
      Sorry, you are not authorized to manage this property<br />
      Make sure you are logged in as the owner of this property
    </div>
    <template v-else>
      <section
        v-if="
          get(formById, 'utm.utm_source') || get(formById, 'affiliate.code')
        "
        class="user_info"
      >
        <div class="section-title">
          <h2>TRACKING</h2>
        </div>
        <div v-if="formById.utm.utm_source" class="row">
          <label>UTM Source</label>
          <p class="value">{{ formById.utm.utm_source }}</p>
        </div>
        <div v-if="formById.affiliate.code" class="row">
          <label>パートナー名</label>
          <p class="value">{{ formById.affiliateDetail.name }}</p>
        </div>
      </section>
      <section v-if="formById" class="user_info">
        <div class="section-title">
          <h2>お客様情報</h2>
        </div>
        <div class="row">
          <label>お名前</label>
          <p class="value">{{ formById.firstName }} {{ formById.lastName }}</p>
        </div>
        <div class="row">
          <label>メールアドレス</label>
          <p class="value">{{ formById.email }}</p>
        </div>
        <div v-if="formById.phone" class="row">
          <label>電話番号</label>
          <p class="value">{{ formById.phone }}</p>
        </div>
        <div v-if="preferredLanguage" class="row">
          <label>ご希望の言語</label>
          <p class="value">{{ preferredLanguage }}</p>
        </div>
        <br />
        <div class="section-title">
          <h2>{{ capitalize(formById.formType) }} Form Response</h2>
        </div>
        <template v-if="formById.formRent">
          <div class="row">
            <label>Occupation</label>
            <p class="value">
              {{
                translate(formById.formRent.occupation, constants.occupation)
              }}
            </p>
          </div>
          <div class="row">
            <label>Visa Status</label>
            <p class="value">
              {{
                translate(formById.formRent.visaStatus, constants.visaStatus)
              }}
            </p>
          </div>
          <div class="row">
            <label>Rent Budget</label>
            <p class="value">
              {{
                formById.formRent.rentBudget &&
                formById.formRent.rentBudget.includes('over')
                  ? 'Over ¥300,000'
                  : `¥${formById.formRent.rentBudget.replace('K', ',000')}`
              }}
            </p>
          </div>
          <div class="row">
            <label>Floor Plan</label>
            <p class="value">
              {{
                formById.formRent.floorPlan.includes('more')
                  ? '3LDK or more'
                  : formById.formRent.floorPlan
              }}
            </p>
          </div>
          <div class="row">
            <label>Min Size</label>
            <p class="value">
              {{ formById.formRent.size }}㎡{{
                formById.formRent.size > 99 ? ' or more' : ''
              }}
            </p>
          </div>
          <div class="row">
            <label>Desired Location</label>
            <p class="value">{{ formById.formRent.desiredLocation }}</p>
          </div>
          <div class="row">
            <label>Station Distance</label>
            <p class="value">
              {{
                0 > formById.formRent.stationDistance
                  ? "I don't care"
                  : `${formById.formRent.stationDistance} min walk`
              }}
            </p>
          </div>
          <div class="row">
            <label>When to move in</label>
            <p class="value">
              {{
                translate(
                  formById.formRent.whenToMoveIn,
                  constants.whenToMoveIn
                )
              }}
            </p>
          </div>
          <div class="row">
            <label>Other Preferences</label>
            <p class="value" v-html="otherPreferences" />
          </div>
        </template>
        <template v-else-if="formById.formBuy">
          <div class="row">
            <label>Purpose</label>
            <p class="value">
              {{ translate(formById.formBuy.purpose, constants.purpose) }}
            </p>
          </div>
          <div class="row">
            <label>Property Type</label>
            <p class="value">
              {{
                translate(formById.formBuy.propertyType, constants.propertyType)
              }}
            </p>
          </div>
          <div class="row">
            <label>Desired Areas</label>
            <p class="value">{{ formById.formBuy.desiredAreas }}</p>
          </div>
          <div class="row">
            <label>Time Horizon</label>
            <p class="value">{{ formById.formBuy.timeHorizon }}</p>
          </div>
          <div class="row">
            <label>Payment Method</label>
            <p class="value">
              {{
                translate(
                  formById.formBuy.paymentMethod,
                  constants.paymentMethod
                )
              }}
            </p>
          </div>
          <div class="row">
            <label>Budget</label>
            <p class="value">
              {{
                formById.formBuy.budget.includes('over')
                  ? '1B JPY or more'
                  : `${formById.formBuy.budget} JPY`
              }}
            </p>
          </div></template
        >
        <template v-else-if="formById.formTour">
          <div class="row">
            <label>Purpose</label>
            <p class="value">
              {{ translate(formById.formTour.purpose, constants.purpose) }}
            </p>
          </div>
          <div class="row">
            <label>Property Type</label>
            <p class="value">
              {{
                translate(
                  formById.formTour.propertyType,
                  constants.propertyType
                )
              }}
            </p>
          </div>
          <div class="row">
            <label>Budget</label>
            <p class="value">
              {{
                formById.formTour.budget.includes('over')
                  ? '1B JPY or more'
                  : `${formById.formTour.budget} JPY`
              }}
            </p>
          </div>
        </template>
        <template v-else-if="formById.formSell">
          <div class="row">
            <label>Property Type</label>
            <p class="value">
              {{
                translate(
                  formById.formSell.propertyType,
                  constants.propertyType
                )
              }}
            </p>
          </div>
          <div class="row">
            <label>Nearest Station</label>
            <p class="value">{{ formById.formSell.nearestStation }}</p>
          </div>
          <div class="row">
            <label>Desired Price</label>
            <p class="value">{{ formById.formSell.desiredPrice }}</p>
          </div>
        </template>
        <div v-if="formById.memo" class="row">
          <label>メモ</label>
          <p class="value">{{ formById.memo }}</p>
        </div>
      </section>
    </template>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import capitalize from 'lodash/capitalize'
import getFormGql from '~/graphql/forms/getForm.query.gql'
import { EnumFormFormRentOtherPreferences, Form, Maybe } from '~/types/types'
import * as constants from '~/constants/form'

export default Vue.extend({
  asyncData(context: any) {
    return { formId: context.params.id }
  },
  data: () => ({
    errorMessage: null as null | string,
    isChanged: false,
    formId: '',
    formById: {} as Maybe<Form>,
    constants,
  }),
  computed: {
    preferredLanguage(): string {
      const list = this.formById?.preferredLanguage || []
      return list
        .map(
          (key) => constants.languages[key as keyof typeof constants.languages]
        )
        .join('、')
    },
    otherPreferences() {
      const texts = this.formById?.formRent?.otherPreferences?.map(
        (item: Maybe<EnumFormFormRentOtherPreferences>) =>
          item && `· ${constants.otherPreferences[item]}`
      )
      return texts && texts.join('<br />')
    },
  },
  methods: {
    get,
    capitalize,
    translate(value: string | null = null, constant: any) {
      return value && constant ? constant[value] : null
    },
  },
  apollo: {
    formById: {
      query: getFormGql,
      variables() {
        return { id: this.formId }
      },
    },
  },
})
</script>
<style lang="scss" scoped>
@import '~/assets/scss/settings/variables';

.title {
  font-size: 24px;
}

section {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  max-width: 500px;

  &:not(:last-child) {
    margin-bottom: 40px;
  }
}

.row {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;

  &:not(:last-child) {
    margin-bottom: 15px;
  }

  label {
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .value {
    font-size: 14px;
  }
}
</style>
