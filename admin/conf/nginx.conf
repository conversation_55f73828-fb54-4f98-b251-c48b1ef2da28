server {
    listen   80; ## listen for ipv4; this line is default and implied
    listen   [::]:80 default ipv6only=on; ## listen for ipv6

    root /usr/share/nginx/html;
    index index.html;

    server_tokens  off; # disable the Server nginx header

    server_name _; # all hostnames

    # enable gzip
    gzip on;
    gzip_disable "msie6";

    gzip_comp_level 6;
    gzip_min_length 1100;
    gzip_buffers 16 8k;
    gzip_proxied any;
    gzip_types
        text/plain
        text/css
        text/js
        text/xml
        text/javascript
        application/javascript
        application/x-javascript
        application/json
        application/xml
        application/rss+xml
        image/svg+xml;

    # http to https redirect
    if ($http_x_forwarded_proto != https) {
      return 301 https://$host$request_uri;
    }

    location / {
        try_files $uri /index.html; # redirect all request to index.html
    }
}
