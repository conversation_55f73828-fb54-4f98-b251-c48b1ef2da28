import {
  getAccessorType,
  getterTree,
  actionTree,
  mutationTree,
} from 'typed-vuex'
import getUnReadInquiries from '~/graphql/inquiries/getUnReadInquiries.query.gql'
import getInhouseAgencyIds from '~/graphql/settings/getInhouseAgencyIds.query.gql'
import {
  Prefecture,
  City,
  Chome,
  GetInhouseAgencyIdsQuery,
  Maybe,
} from '~/types/types'

export const state = () => ({
  countReadInquiries: 0,
  viewedAs: null as string | null,
  isAdmin: false,
  inhouseAgencyIds: [] as string[], // FIXME: move this later to agencyIds obj below
  agencyIds: {
    withFee: [] as string[],
    withoutFee: [] as string[],
  },
  prefectureMap: {} as { [id: string]: Prefecture },
  cityMap: {} as { [id: string]: City },
  chomeMap: {} as { [id: string]: Chome },
})

interface AgencyIds {
  withFee: string[]
  withoutFee: string[]
}

export type RootState = ReturnType<typeof state>

export const getters = getterTree(state, {
  getCountReadInquiries(state) {
    return state.countReadInquiries
  },
  currentAgencyId(state) {
    if (state.viewedAs === 'administrator') return null
    return state.viewedAs
  },
  isAdminOrInhouseAgency(state) {
    const { viewedAs, inhouseAgencyIds } = state
    return (
      viewedAs === 'administrator' || inhouseAgencyIds.includes(viewedAs || '')
    )
  },
  getAddress(state) {
    return (args: { prefecture: string; city: string; chome: string }) => {
      const prefecture = state.prefectureMap[args.prefecture]
      const city = state.prefectureMap[args.city]
      const chome = state.prefectureMap[args.chome]
      if (!prefecture || !city || !chome) return ''
      return `${prefecture.jp}${city.jp}${chome.jp}`
    }
  },
})

export const mutations = mutationTree(state, {
  fetchReadInquiry(state, payload) {
    state.countReadInquiries = payload.data.adminInquiryCount
  },
  setViewedAs(state, payload: string | null) {
    state.viewedAs = payload
  },
  setAdmin(state, payload: boolean) {
    state.isAdmin = payload
  },
  setAgencyIds(state, payload: AgencyIds) {
    state.agencyIds = payload
  },
  reset(state) {
    state.isAdmin = false
    state.viewedAs = null
    state.countReadInquiries = 0
  },
  setInhouseAgencyIds(state, payload: Maybe<string>[]) {
    state.inhouseAgencyIds = payload.filter((id) => id !== null) as string[]
  },
  setPrefectureMap(state, payload: Prefecture[]) {
    state.prefectureMap = {
      ...state.prefectureMap,
      ...payload.reduce(
        (acc, pref) => ({
          ...acc,
          [pref._id]: pref,
        }),
        {}
      ),
    }
  },
  setCityMap(state, payload: City[]) {
    state.prefectureMap = {
      ...state.prefectureMap,
      ...payload.reduce(
        (acc, pref) => ({
          ...acc,
          [pref._id]: pref,
        }),
        {}
      ),
    }
  },
  setChomeMap(state, payload: Chome[]) {
    state.prefectureMap = {
      ...state.prefectureMap,
      ...payload.reduce(
        (acc, pref) => ({
          ...acc,
          [pref._id]: pref,
        }),
        {}
      ),
    }
  },
})

export const actions = actionTree(
  { state, getters, mutations },
  {
    async fetchReadInquiry({ commit, getters }) {
      const client = this.app.apolloProvider.defaultClient
      const result = await client.mutate({
        mutation: getUnReadInquiries,
        variables: getters.currentAgencyId
          ? { agencyId: getters.currentAgencyId }
          : {},
      })

      commit('fetchReadInquiry', result)
    },
    async fetchInhouseAgencyIds({ commit }) {
      const client = this.app.apolloProvider.defaultClient
      const result = await client.query<GetInhouseAgencyIdsQuery>({
        query: getInhouseAgencyIds,
      })

      commit(
        'setInhouseAgencyIds',
        result.data?.setting?.inHouseAgencyIds || []
      )
    },
  }
)

export const accessorType = getAccessorType({
  state,
  getters,
  mutations,
  actions,
  // Uncomment below lines once we have submodule
  // more reference: https://typed-vuex.roe.dev/getting-started-nuxt
  // modules: {
  //   // The key (submodule) needs to match the Nuxt namespace (e.g. ~/store/submodule.ts)
  //   submodule,
  // },
})
