version: '3'
services:
  admin:
    build: 
      dockerfile: Dockerfile.local
      context: admin
    environment:
      HOST: 0.0.0.0
    ports:
      - '3001:3000'
    volumes: 
      - ./admin:/usr/src/admin
  web:
    build: 
      dockerfile: Dockerfile.local
      context: web
    environment:
      HOST: 0.0.0.0
    ports:
      - '3000:3000'
    volumes: 
      - ./web:/usr/src/web
  api:
    build: 
      dockerfile: Dockerfile.local
      context: api
    ports:
      - '8000:8000'
    volumes: 
      - ./api:/usr/src/api
  mongo:
    image: mongo:4.4.6
    ports:
      - '27017:27017'
  mongo_seed:
    image: mongo:4.4.6
    links:
      - mongo
    volumes:
      - ./seeds:/seeds
    working_dir: /seeds
    command: ./import.sh
  redis:
    image: redis:alpine
    ports:
      - '6379:6379'
