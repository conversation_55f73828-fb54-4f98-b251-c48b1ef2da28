<template>
  <div class="wrapper">
    <TheHeader class="sticky" />
    <main class="site-main">
      <div class="thanks">
        <div class="container">
          <div class="content">
            <h1>{{ $t('contact.thankTitle') }}</h1>
            <p>
              {{ $t('contact.messages.success') }}
            </p>
            <button>
              <NuxtLink :to="localePath('/')">
                {{ $t('common.topPage') }}
              </NuxtLink>
            </button>
          </div>
        </div>
      </div>
    </main>
    <TheFooter />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { MetaInfo } from 'vue-meta/types/vue-meta.d'

export default Vue.extend({
  name: 'Thanks',
  data() {
    return {
      titleHeading: 'Recommended Properties' as String,
    }
  },
  head(): MetaInfo {
    return {
      meta: [{ hid: 'robots', name: 'robots', content: 'noindex' }],
    }
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.content {
  margin-top: calc-rem(50px);
  margin-bottom: calc-rem(80px);
  text-align: center;

  @include bp('sp') {
    margin-bottom: calc-rem(60px);
    margin-top: calc-rem(28px);
  }
}
.content h1 {
  color: #16ab29;
  font-size: calc-rem(32px);
  font-weight: bold;
  line-height: calc-rem(43px);
  margin-bottom: calc-rem(10px);

  @include bp('sp') {
    font-size: calc-rem(22px);
  }
}

.content p {
  color: #333;
  font-size: calc-rem(16px);
  line-height: calc-rem(22px);
  margin-bottom: calc-rem(30px);

  @include bp('sp') {
    margin-bottom: calc-rem(20px);
  }
}

.content button {
  border-radius: calc-rem(4px);
  background-color: #333;
  border: none;
  padding: 7px 20px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 20%);
}
.nuxt-link-active {
  color: #fff;
  font-weight: bold;
  line-height: calc-rem(22px);
}
</style>
