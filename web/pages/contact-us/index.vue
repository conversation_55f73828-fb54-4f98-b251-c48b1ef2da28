<template>
  <div class="wrapper">
    <TheHeader class="sticky" :with-banner="false" />
    <main class="site-main">
      <div class="container">
        <h1 class="page-title">{{ page.title }}</h1>

        <form @submit.prevent="handleSubmit">
          <div v-if="errorMessage" class="error-message">
            <the-alert
              class="alert--danger"
              :content="errorMessage"
              :dismiss-able="true"
            />
          </div>
          <div class="input" :class="{ error: errors.name }">
            <label>{{ $t('contact.label.name') }} </label>
            <input
              v-model="form.name"
              type="text"
              @blur="validateField('name')"
            />
            <p v-if="errors.name" class="error">{{ errors.name }}</p>
          </div>
          <div class="input" :class="{ error: errors.email }">
            <label>{{ $t('contact.label.email') }} </label>
            <input
              v-model="form.email"
              type="email"
              @blur="validateField('email')"
            />
            <p v-if="errors.email" class="error">{{ errors.email }}</p>
          </div>
          <div class="input" :class="{ error: errors.message }">
            <label>{{ $t('contact.label.message') }} </label>
            <textarea
              v-model="form.message"
              rows="8"
              @blur="validateField('message')"
            ></textarea>
            <p v-if="errors.message" class="error">{{ errors.message }}</p>
          </div>
          <div class="input" :class="{ error: errors.token }">
            <recaptcha
              @error="onError"
              @success="onSuccess"
              @expired="onExpired"
            />
            <p v-if="errors.token" class="error">{{ errors.token }}</p>
          </div>
          <BaseButton
            :disabled="isSubmitting"
            type="submit"
            :class="{ 'button--blue': !isSubmitting }"
          >
            {{ $t('contact.button') }}
          </BaseButton>
        </form>
      </div>
    </main>
    <TheFooter />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { MetaInfo } from 'vue-meta/types/vue-meta.d'
import * as Yup from 'yup'
import get from 'lodash/get'
import sendToSupport from '~/apollo/mutations/sendToSupport.graphql'

interface Page {
  title: string
  body: any
}

const initialValues = {
  name: '',
  email: '',
  message: '',
  token: '',
}

export default Vue.extend({
  name: 'ContactUs',
  asyncData({ app }: any) {
    return {
      title: typeof app.head === 'function' ? app.head().title : app.head.title,
    }
  },
  data() {
    const schema = Yup.object().shape({
      name: Yup.string().required(this.$t('contact.validate.name.required')),
      email: Yup.string()
        .email(this.$t('contact.validate.email.type'))
        .required(this.$t('contact.validate.email.required')),
      token: Yup.string().required(
        this.$t('contact.validate.recaptcha.required')
      ),
      message: Yup.string().required(
        this.$t('contact.validate.message.required')
      ),
    })

    const page: Page = {
      title: this.$t('contact.pageTitle').toString(),
      body: '',
    }

    return {
      schema,
      page,
      title: '',
      form: initialValues,
      errors: {
        name: null,
        email: null,
        message: null,
        token: null,
      },
      errorMessage: null as null | string,
      isSubmitting: false,
    }
  },
  head(): MetaInfo {
    return {
      title: this.page.title + ' - ' + this.title,
      meta: [{ hid: 'robots', name: 'robots', content: 'noindex' }],
    }
  },
  methods: {
    async validateField(fieldName: keyof typeof initialValues) {
      this.errors[fieldName] = null
      try {
        await this.schema.validateAt(fieldName, this.form)
      } catch (error) {
        this.errors[fieldName] = error.message
      }
    },
    async validateOnSubmit() {
      this.clearError()
      try {
        await this.schema.validate(this.form, { abortEarly: false })
        return true
      } catch (errors) {
        if (!errors.inner)
          alert(this.$t('contact.messages.somethingWrong').toString())
        errors.inner?.forEach((error: Yup.ValidationError) => {
          if (!error?.path) return
          this.$set(this.errors, error.path as any, error.message)
        })
        return false
      }
    },
    clearError() {
      Object.keys(this.errors).forEach((key) => {
        this.errors[key as keyof typeof initialValues] = null
      })
    },
    async handleSubmit() {
      const valid = await this.validateOnSubmit()
      if (!valid) return

      this.isSubmitting = true
      try {
        const result = await this.$apollo.mutate({
          mutation: sendToSupport,
          variables: { input: this.form },
        })
        if (result.data.sendToSupport) {
          await this.$router.push(this.localePath('/contact-us/thanks'))
        } else {
          this.errorMessage = this.$t('contact.messages.failed').toString()
        }
        this.$recaptcha.reset()
      } catch (error) {
        this.errorMessage = get(
          error,
          'networkError.result.errors[0].message',
          error.message
        )
      }
      this.isSubmitting = false
    },
    onError(error: any) {
      console.warn(error)
      this.validateField('token')
    },
    onSuccess(token: string) {
      this.form.token = token
      this.validateField('token')
    },
    onExpired() {
      this.validateField('token')
    },
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.page-title {
  margin-bottom: 30px;
  text-align: center;
}

form {
  display: flex;
  flex-direction: column;
  max-width: 600px;
  margin: auto;

  .error-message {
    margin-bottom: 20px;
  }

  .input {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;

    label {
      font-size: 14px;
      color: $color_gray_light8;
      padding-bottom: 10px;
    }

    input,
    textarea {
      color: $color_base;
      width: 100%;
      font-size: 14px;
      letter-spacing: 0;
      border-radius: 5px;
      border: 1px solid $color_gray_light3;
      background-color: $color_white;
      padding: 7px 10px;

      &:focus {
        border: 1px solid $color_base;
      }
    }

    &.error {
      color: red;

      input,
      textarea {
        border-color: red;
      }

      p.error {
        font-size: 12px;
        margin: 0;
        margin-top: 2px;
      }
    }
  }

  button[type='submit'] {
    max-width: 200px;
    align-self: flex-end;
  }
}
</style>
