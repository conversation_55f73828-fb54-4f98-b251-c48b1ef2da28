<template>
  <div class="wrapper">
    <TheHeader class="sticky" />
    <main class="site-main">
      <div class="container">
        <h1 class="page-title">{{ page.title }}</h1>
        <nuxt-content :document="page" />
      </div>
    </main>
    <TheFooter />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { MetaInfo } from 'vue-meta/types/vue-meta.d'

interface Page {
  title: string
  body: any
}

const page: Page = {
  title: '',
  body: '',
}

export default Vue.extend({
  name: 'TermsPartner',
  async asyncData({ $content, app }: any) {
    const locale = app?.i18n?.locale || 'en'
    const page = await $content(`${locale}/terms-partners`).fetch()
    return {
      title: typeof app.head === 'function' ? app.head().title : app.head.title,
      page,
    }
  },
  data() {
    return {
      page,
      title: '',
    }
  },
  head(): MetaInfo {
    return {
      title: this.page.title + ' - ' + this.title,
      meta: [{ hid: 'robots', name: 'robots', content: 'noindex' }],
    }
  },
})
</script>

<style lang="scss" scoped>
.container {
  font-family: 'Noto Sans', 'Noto Sans JP', 'Noto Sans TC', sans-serif;
}

.nuxt-content {
  word-break: break-word;

  h3 {
    margin-top: 30px;
  }

  ol {
    li {
      list-style: unset;
    }
  }

  .last {
    ul {
      margin-left: 0;

      li {
        list-style: none;
      }
    }

    ol {
      padding-left: 20px;

      li {
        list-style: decimal;
      }
    }
  }
}

.page-title {
  font-size: 24px;
  margin-bottom: 30px;
}

.site-main {
  padding-top: 120px;
}
</style>
