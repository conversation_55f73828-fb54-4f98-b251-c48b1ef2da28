<template>
  <div class="wrapper">
    <TheHeader class="sticky" />
    <main class="site-main">
      <div class="container">
        <h1 class="page-title">{{ page.title }}</h1>
        <nuxt-content :document="page" />
      </div>
    </main>
    <TheFooter />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

interface Page {
  title: string
  body: any
}

const page: Page = {
  title: '',
  body: '',
}

export default Vue.extend({
  name: 'PrivacyPolicy',
  async asyncData({ $content, app }: any) {
    const locale = app?.i18n?.locale || 'en'
    const page = await $content(`${locale}/privacy-policy`).fetch()
    return {
      title: typeof app.head === 'function' ? app.head().title : app.head.title,
      page,
    }
  },
  data() {
    return {
      page,
      title: '',
    }
  },
  head(): { title: string } {
    return {
      title: this.page.title + ' - ' + this.title,
    }
  },
})
</script>

<style lang="scss">
.nuxt-content {
  word-break: break-word;

  p + h3 {
    margin-top: 40px;
  }

  ul {
    margin-bottom: 20px;

    li {
      list-style: disc;
    }

    ul {
      li {
        list-style: circle;
      }
    }
  }
}

.page-title {
  margin-bottom: 30px;
}
</style>
