<template>
  <div class="wrapper">
    <TheHeader class="sticky" />
    <main class="site-main">
      <div class="big-banner"></div>
      <div class="container">
        <div class="content">
          <div class="section-title">
            <h3>HOW TO GET REWARDED</h3>
            <div class="section-title-border" />
          </div>
          <div class="steps-container">
            <div v-for="step in steps" :key="step.index" class="step">
              <div class="step-header">
                <div class="icon">
                  <img :src="step.image" />
                </div>
                <div class="step-header-title">
                  <h4>STEP {{ step.index }}</h4>
                  <h5>{{ step.name }}</h5>
                </div>
              </div>
              <p class="step-content" v-html="step.description" />
            </div>
          </div>
          <div class="section-title">
            <h3>REFERRAL PROGRAM</h3>
            <div class="section-title-border" />
          </div>
          <table class="info">
            <tr v-for="row in info" :key="row.title">
              <td>{{ row.title }}</td>
              <td v-html="row.content"></td>
            </tr>
          </table>
          <div class="disclaimer">
            <p>* Amazon.co.jp is not a sponsor of this campaign.</p>
            <p>
              * ”Amazon”, “Amazon.co.jp” and their logos are registered
              trademarks of Amazon.com, Inc. and its affiliates.
            </p>
          </div>
        </div>
      </div>
    </main>
    <TheFooter />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { MetaInfo } from 'vue-meta/types/vue-meta.d'

export default Vue.extend({
  name: 'Campaign',
  data: () => ({
    steps: [
      {
        index: 1,
        name: 'Refer a friend',
        image: require('~/assets/images/referral/step-1.svg'),
        description:
          'Your referral must inform Inbound Platform Corp your name as a referral before signing a lease contract. You can also refer a friend directly at <a href="mailto:<EMAIL>"><EMAIL></a> by email. ',
      },
      {
        index: 2,
        name: 'Sign a lease contract',
        image: require('~/assets/images/referral/step-2.svg'),

        description:
          'Sign a lease contract with Inbound Platform Crop when you find the best one to move in.',
      },
      {
        index: 3,
        name: 'Get Rewarded',
        image: require('~/assets/images/referral/step-3.svg'),

        description: "We'll send the reward to you and your referral's email.",
      },
    ],
    info: [
      {
        title: 'Overview',
        content:
          'When a referral sign a lease contract with Living Japan, you and your referral receive the special reward of Amazon JP Gift Card from us.',
      },
      {
        title: 'Prize',
        content: `<ul class="campaign-list">
          <li>¥20,000 Amazon JP Gift Card to you</li>
          <li>¥20,000 Amazon JP Gift Card to your referral</li>
        </ul>
        `,
      },
      {
        title: 'Note',
        content: `<ul class="campaign-list">
          <li>The reward content may change. Please visit <a href="https://livingjapan.com/referral-program">www.livingjapan.com/referral-program</a> to check the latest reward content.</li>
          <li>Your referral must sign a lease contract through the agent service of Inbound Platform Corp. Both parties can NOT get rewarded if signing a contract with another company.</li>
          <li>Your referral must inform Inbound Platform Corp your name as a referral before signing a lease contract.</li>
        </ul>
          `,
      },
    ],
  }),
  head(): MetaInfo {
    return {
      meta: [{ hid: 'robots', name: 'robots', content: 'noindex' }],
    }
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.content {
  display: flex;
  flex-direction: column;
  font-family: sans-serif;
}

.site-main {
  padding-top: 67px;
  overflow-x: hidden;
}

.big-banner {
  background: url(~/assets/images/referral/banner.png), #18af94;
  height: 580px;
  margin: 0 -158px;
  background-size: 900px 499px;
  background-repeat: no-repeat;
  background-position: center;

  @include bp(sp) {
    background: url(~/assets/images/referral/banner.png), #18af94;
    height: 230px;
    background-size: 320px 177px;
    background-repeat: no-repeat;
    background-position: center;
    margin: 0 -20px;
    margin-top: -20px;
  }
}

.section-title {
  margin-top: 40px;
  margin-bottom: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;

  @include bp(sp) {
    margin-bottom: 30px;
  }

  h3 {
    font-family: sans-serif;
    color: #4e5a62;
    font-size: 20px;
    letter-spacing: 0;
    text-align: center;
    margin-bottom: 15px;
  }

  &-border {
    height: 2px;
    width: 100px;
    background-color: #18af94;
  }
}

.disclaimer {
  color: #4e5a62;
  font-size: 13px;
  margin: 20px auto;
  width: 100%;
  max-width: 860px;

  p {
    margin-bottom: 0;
  }

  @include bp(sp) {
    font-size: 12px;
  }
}

.steps-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 80px 0;

  @include bp(sp) {
    flex-direction: column;
    margin: 0;
  }

  .step {
    width: 300px;
    height: 280px;
    border: 1px solid #f6f6f8;
    background-color: #fff;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
    margin: 0 20px;
    padding: 80px 25px 0;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;

    @include bp(sp) {
      width: 100%;
      padding: 20px;
      height: unset;
      margin-bottom: 20px;
    }

    &-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      position: relative;

      @include bp(sp) {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
      }

      .icon {
        position: absolute;
        top: -205%;
        left: 0;
        width: 300px;
        height: 120px;
        margin-left: -20px;
        display: flex;
        align-items: center;
        justify-content: center;

        @include bp(sp) {
          width: 65px;
          height: 65px;
          position: relative;
          top: unset;
          left: unset;
          margin-left: unset;
          margin-right: 15px;
        }

        img {
          margin: 0 auto;
          width: 120px;
          height: 120px;
          border-radius: 50%;
        }
      }

      &-title {
        @include bp(sp) {
          display: flex;
          flex-direction: column;
        }

        h4 {
          color: #18af94;
          font-size: 20px;
          letter-spacing: 0;
          margin-bottom: 5px;
          font-weight: bold;

          @include bp(sp) {
            font-size: 22px;
            margin-bottom: 5px;
          }
        }

        h5 {
          color: #222f3e;
          font-size: 20px;
          letter-spacing: 0;
          margin-bottom: 15px;

          @include bp(sp) {
            font-size: 17px;
            margin: 0;
          }
        }
      }
    }

    p {
      text-align: left;
      color: #4e5a62;
      font-size: 14px;
      letter-spacing: 0;
      margin: 0;
      line-height: 20px;
    }
  }
}

.info {
  border-collapse: collapse;
  max-width: 860px;
  margin: auto;

  td {
    border: 1px solid #18af94;
    padding: 14px 10px;
    font-size: 14px;

    @include bp(sp) {
      width: 100%;
      display: block;
      padding: 10px 15px;
      font-size: 14px;
    }

    &:first-child {
      font-size: 14px;
      color: #222f3e;
      font-weight: bold;
      text-align: center;
      width: 20%;
      background-color: rgba(24, 175, 148, 0.2);

      @include bp(sp) {
        width: 100%;
        font-size: 14px;
      }
    }
  }
}
</style>

<style lang="scss">
.step-content a,
.campaign-list li a {
  color: #0645ad;
  &:hover {
    color: #0645ad;
  }
}

.campaign-list {
  margin: 0;
  padding-left: 20px;
  li {
    list-style-type: disc;
  }
}
</style>
