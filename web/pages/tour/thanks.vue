<template>
  <div class="wrapper">
    <style>
      body {
        font-family: serif, 'Noto Sans', 'Noto Sans TC', 'Noto Sans SC',
          'Noto Sans JP', sans-serif;
      }
    </style>
    <TheHeader class="sticky" />
    <main class="site-main bg--tour">
      <div class="thanks">
        <div class="container">
          <BreadCrumb :bread-crumbs="breadCrumbs" />
          <div class="content">
            <TitleSection :title="$t(`tour.thanks.title`)" />
            <p>{{ $t(`tour.thanks.description`) }}</p>
            <NuxtLink :to="localePath('/tour')">{{
              $t(`tour.thanks.button`)
            }}</NuxtLink>
          </div>
        </div>
      </div>
    </main>
    <TheFooter :banner-img="false" />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { MetaInfo } from 'vue-meta/types/vue-meta.d'
import TheHeader from '~/components/tour/TheHeader.vue'
import TheFooter from '~/components/tour/TheFooter.vue'
import TitleSection from '~/components/tour/TitleSection.vue'
import BreadCrumb from '~/components/tour/BreadCrumb.vue'

export default Vue.extend({
  name: 'Thanks',
  components: {
    TheHeader,
    TheFooter,
    TitleSection,
    BreadCrumb,
  },
  data() {
    return {
      titleHeading: 'Recommended Properties' as String,
      breadCrumbs: [
        { title: this.$t('tour.breadCrumbs.home'), path: '/' },
        {
          title: this.$t('tour.breadCrumbs.vipTour'),
          path: '/tour',
        },
        {
          title: this.$t('tour.breadCrumbs.tourInquiry'),
          path: '/tour/inquiry',
        },
        { title: this.$t('tour.breadCrumbs.thanks'), path: '/tour/thanks' },
      ],
    }
  },
  head(): MetaInfo {
    return {
      meta: [{ hid: 'robots', name: 'robots', content: 'noindex' }],
    }
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.bg--tour {
  background: #f9f6f1;
}

.content {
  margin-top: calc-rem(50px);
  margin-bottom: calc-rem(80px);
  text-align: center;

  @include bp('sp') {
    margin-bottom: calc-rem(60px);
    margin-top: calc-rem(28px);
  }
}
.content h1 {
  color: #16ab29;
  font-size: calc-rem(32px);
  font-weight: bold;
  line-height: calc-rem(43px);
  margin-bottom: calc-rem(10px);

  @include bp('sp') {
    font-size: calc-rem(22px);
  }
}

.content p {
  color: #333;
  font-size: 24px;
  margin-bottom: 60px;
}

.nuxt-link-active {
  border: none;
  padding: 20px 54px;
  border-radius: 2px;
  background-color: #c29b40;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.2);
  color: #fff;
  font-size: 18px;
  letter-spacing: 1px;
  line-height: 27px;
  font-weight: bold;
}

.site-main {
  height: calc(100vh - 66px);
}
</style>
