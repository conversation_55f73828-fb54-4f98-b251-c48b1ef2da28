<template>
  <div>
    <style>
      body {
        font-family: serif, 'Noto Sans', 'Noto Sans TC', 'Noto Sans SC',
          'Noto Sans JP', sans-serif;
      }
    </style>
    <div class="wrapper">
      <TheHeader dark class="sticky" :with-banner="false" />
      <main class="site-main new-banner bg--tour">
        <HomeBanner />
        <div class="container">
          <TourInformation />
          <TourInclusive />
          <TourFlow />
          <TourSchedule />
        </div>
      </main>
      <TheFooter />
      <div class="buttons__wrap">
        <a href="tel:03-4579-2103" class="btn__footer btn__phone">
          <img
            height="18"
            width="18"
            src="~/assets/images/icons/phone-call-white.svg"
            class="phone call"
          />
          <p class="phone">{{ $t(`tour.callAgent`) }}</p>
        </a>
        <nuxt-link
          :to="localePath('/tour/inquiry')"
          class="btn__footer btn__inquiry"
        >
          <img
            height="18"
            width="18"
            src="~/assets/images/tour/icon_email.png"
            class="phone call"
          />
          <p class="phone">{{ $t(`tour.menuItem.inquiryNow`) }}</p>
        </nuxt-link>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import TheHeader from '~/components/tour/TheHeader.vue'
import HomeBanner from '~/components/tour/HomeBanner.vue'
import TourInformation from '~/components/tour/TourInformation.vue'
import TourInclusive from '~/components/tour/TourInclusive.vue'
import TourFlow from '~/components/tour/TourFlow.vue'
import TourSchedule from '~/components/tour/TourSchedule.vue'
import TheFooter from '~/components/tour/TheFooter.vue'

export default Vue.extend({
  components: {
    TheHeader,
    HomeBanner,
    TourInformation,
    TourInclusive,
    TourFlow,
    TourSchedule,
    TheFooter,
  },
  data() {
    return {
      recentProperties: {} as any,
      topProperties: {} as any,
      more_link: '/rent/tokyo',
    }
  },
  head(): { title: string; meta: Array<any> } {
    return {
      title: this.$t('home.meta.title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('home.meta.description'),
        },
        // Facebook
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.$t('home.meta.title'),
        },
        {
          hid: 'og:type',
          property: 'og:type',
          content: 'website',
        },
        {
          hid: 'og:url',
          property: 'og:url',
          content: this.pageURL,
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: `${this.$config.appUrl}/social-main.png`,
        },
        {
          hid: 'og:site_name',
          property: 'og:site_name',
          content: 'Living Japan',
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.$t('home.meta.description'),
        },
        // Twitter
        {
          hid: 'twitter:card',
          property: 'twitter:card',
          content: 'summary_large_image',
        },
        {
          hid: 'twitter:site',
          property: 'twitter:site',
          content: '@livingjapan_',
        },
        {
          hid: 'twitter:url',
          property: 'twitter:url',
          content: this.pageURL,
        },
        {
          hid: 'twitter:title',
          property: 'twitter:title',
          content: this.$t('home.meta.title'),
        },
        {
          hid: 'twitter:description',
          property: 'twitter:description',
          content: this.$t('home.meta.description'),
        },
        {
          hid: 'twitter:image',
          property: 'twitter:image',
          content: `${this.$config.appUrl}/social-main.png`,
        },
        {
          name: 'facebook-domain-verification',
          content: '73q1ntltqtkta6ojj5vr8vm6sh1v7z',
        },
      ],
    }
  },
  computed: {
    pageURL(): string {
      return this.$route
        ? this.$config.appUrl + this.$route.path
        : this.$config.appUrl
    },
  },
  methods: { get },
})
</script>
<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.bg--tour {
  background: #f9f6f1;
  padding-top: 69px;
  @include bp(sp) {
    padding-top: 50px;
  }

}

.buttons__wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 98;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 6px;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: inset 0 1px 0 0 #ddd;
  width: 100%;

  @include bp(pc) {
    display: none;
  }

  .btn__footer {
    padding: 12px 0;
    width: 100%;
    border-radius: 2px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 5px 0;

    img {
      margin-right: 10px;
    }

    p {
      margin-bottom: 0;
      color: #fff;
      font-size: 18px;
      font-weight: bold;
      line-height: 24px;
    }
  }

  .btn__phone {
    background-color: #068511;
    margin-left: 10px;
  }

  .btn__inquiry {
    background-color: #c29b40;
    margin-right: 10px;
  }
}
</style>
