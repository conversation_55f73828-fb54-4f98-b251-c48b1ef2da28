<template>
  <div class="wrapper">
    <style>
      body {
        font-family: serif, 'Noto Sans', 'Noto Sans TC', 'Noto Sans SC',
          'Noto Sans JP', sans-serif;
      }
    </style>
    <TheHeader class="sticky" />
    <main class="site-main">
      <div class="container">
        <BreadCrumb :bread-crumbs="breadCrumbs" />
        <div class="tour-inquiry">
          <TitleSection :title="$t(`tour.inquiry.title`)" />
          <p class="tour-inquiry__description">
            {{ $t(`tour.inquiry.description`) }}
          </p>
          <form @submit.prevent="handleSubmit">
            <div v-if="errorMessage" class="error-message">
              <the-alert
                class="alert--danger"
                :content="errorMessage"
                :dismiss-able="true"
              />
            </div>
            <identity-input
              v-model="form.identity"
              :errors="errors"
              :phone-required="true"
              :phone-placeholder="false"
              @focus="hideError($event)"
              @blur="validate($event)"
            />
            <template v-for="key in optionOrders">
              <field-row
                :key="`select_${key}`"
                :label="$t(`tour.inquiry.${key}.label`)"
                required
                :error="errors[key]"
              >
                <select
                  v-model="form[key]"
                  required
                  @focus="hideError(key)"
                  @blur="validate(key)"
                >
                  <option value="" disabled selected>
                    {{ $t('tour.inquiry.commonPlaceholders.select') }}
                  </option>
                  <option
                    v-for="item in options[key]"
                    :key="item.key"
                    :value="item.key"
                  >
                    {{ item.label }}
                  </option>
                </select>
              </field-row>
            </template>
            <field-row :label="$t('tour.inquiry.note.label')">
              <textarea
                id="memo"
                v-model="form.memo"
                name="memo"
                :placeholder="$t('tour.inquiry.note.placeholder')"
              />
            </field-row>
            <agreement-check v-model="form.hasAgreed" />
            <div class="inquiry-property-submit">
              <BaseButton
                :class="{ 'button--yellow': !isSubmitting }"
                :disabled="hasError || isSubmitting"
                type="submit"
              >
                {{ $t('tour.inquiry.buttonSubmit') }}
              </BaseButton>
            </div>
          </form>
        </div>
      </div>
    </main>
    <TheFooter :banner-img="false" />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import flatten from 'flat'
import get from 'lodash/get'
import mapValues from 'lodash/mapValues'
import * as Yup from 'yup'
import sendForm from '~/apollo/mutations/sendForm.graphql'
import { EnumFormFormType } from '~/types/types'
import { getCookie } from '~/common/utils'
import TheHeader from '~/components/tour/TheHeader.vue'
import TheFooter from '~/components/tour/TheFooter.vue'
import TitleSection from '~/components/tour/TitleSection.vue'
import BreadCrumb from '~/components/tour/BreadCrumb.vue'

const initialValues = {
  identity: {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    preferredLanguage: [],
  },
  purpose: '',
  propertyType: '',
  budget: '',
  memo: '',
  hasAgreed: false,
}

type ErrorDict = { [key: string]: string | null }
const optionOrders = ['purpose', 'propertyType', 'budget']

export default Vue.extend({
  components: {
    TheHeader,
    TheFooter,
    TitleSection,
    BreadCrumb,
  },
  beforeRouteLeave(_to, _from, next) {
    if (this.isSubmitting) return next()

    this.$showConfirmDialog({
      title: this.$t('inquiry.titleDialog').toString(),
      message: this.$t('inquiry.leaveDialogMessage').toString(),
      hasImgNotice: true,
      businessHour: this.$t('inquiry.businessHourDialog').toString(),
      okButton: this.$t('inquiry.leaveConfirmButton').toString(),
      cancelButton: this.$t('inquiry.leaveCancelButton').toString(),
      okCallback: next,
      cancelCallback: () => next(false),
    })
  },
  data() {
    return {
      breadCrumbs: [
        { title: this.$t('tour.breadCrumbs.home'), path: '/' },
        {
          title: this.$t('tour.breadCrumbs.vipTour'),
          path: '/tour',
        },
        {
          title: this.$t('tour.breadCrumbs.tourInquiry'),
          path: '/tour/inquiry',
        },
      ],
      errorMessage: '',
      isSubmitting: false,
      errors: mapValues(flatten(initialValues), () => '') as ErrorDict,
      hasError: true,
      form: initialValues,
      optionOrders,
    }
  },
  computed: {
    schema() {
      const required = {
        select: Yup.string().required(this.$t('tour.inquiry.required.select')),
        input: Yup.string().required(this.$t('tour.inquiry.required.input')),
      }

      return Yup.object().shape({
        identity: Yup.object().shape({
          firstName: required.input,
          lastName: Yup.string(),
          email: required.input.email(this.$t('tour.inquiry.required.email')),
          phone: required.input,
          preferredLanguage: Yup.array()
            .of(Yup.string())
            .required(this.$t('tour.inquiry.required.select')),
        }),
        purpose: required.select,
        propertyType: required.select,
        budget: required.select,
        hasAgreed: Yup.boolean().oneOf(
          [true],
          'Must Accept Terms and Condition'
        ),
      })
    },
    options(): { [key: string]: any[] } {
      return {
        purpose: this.toOptions('tour.inquiry.purpose.options'),
        propertyType: this.toOptions('tour.inquiry.propertyType.options'),
        budget: [
          ...[50, 100, 300, 500].map((num) => ({
            key: `${num}M`,
            label: this.$t(`tour.inquiry.budget.options.a${num}m`),
          })),
          {
            key: 'over_1B',
            label: this.$t(`tour.inquiry.budget.options.over_1b`),
          },
        ],
        paymentMethod: this.toOptions('tour.inquiry.paymentMethod.options'),
      }
    },
  },
  watch: {
    form: {
      async handler() {
        try {
          await this.schema.validate(this.form)
          this.hasError = false
        } catch (err) {
          this.hasError = true
        }
      },
      deep: true,
    },
  },
  methods: {
    toOptions(translationKey: string): any[] {
      return Object.keys(this.$t(translationKey)).map((key) => ({
        key,
        label: this.$t(`${translationKey}.${key}`),
      }))
    },
    hideError(field: string): void {
      this.errors[field] = null
    },
    async validate(field: string): Promise<void> {
      try {
        await this.schema.validateAt(field, this.form)
        this.errors[field] = null
      } catch (err) {
        this.errors[field] = err.message
      }
    },
    async handleSubmit() {
      this.isSubmitting = true
      try {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { identity, memo, hasAgreed: __, ...formTour } = this.form
        const result = await this.$apollo.mutate({
          mutation: sendForm,
          variables: {
            record: {
              ...identity,
              memo,
              formTour,
              formType: EnumFormFormType.Tour,
              locale: this.$i18n.localeProperties.iso?.replace('-', '_'),
              utm: {
                utm_source: getCookie('utm_source'),
              },
              affiliate: {
                code: getCookie('affiliate'),
              },
            },
          },
        })
        if (result.data?.sendForm?.record?._id) {
          await this.$router.push(this.localePath('/tour/thanks'))
        } else {
          this.errorMessage = this.$t('contact.messages.failed').toString()
        }
        this.$recaptcha.reset()
      } catch (error) {
        this.errorMessage = get(
          error,
          'networkError.result.errors[0].message',
          error.message
        )
      }
      this.isSubmitting = false
    },
  },
})
</script>
<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

form {
  max-width: 600px;
  margin: auto;
}

::v-deep .input {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px !important;

  .label {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
    &.required {
      &::after {
        content: '*';
        color: red;
      }
    }
  }

  input {
    width: 100%;
    border: 1px solid #bbb;
    font-size: 14px;
    padding: 10px 14px;
    border-radius: 2px;

    &:focus {
      border-color: #333;
    }

    &::placeholder {
      font-size: 14px;
      color: #aaa;
    }
  }
}

/** Move out later */
select {
  border: 1px solid #bbb;
  padding: 10px 14px;
  border-radius: 2px;
  font-size: 14px;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: transparent;
  background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
  background-repeat: no-repeat;
  background-position-x: calc(100% - 10px);
  background-position-y: 50%;

  &:focus {
    border-color: #333;
  }

  &:required:invalid {
    color: #aaa;
  }

  option {
    color: #333;
    font-size: 14px;
    &[value=''][disabled] {
      display: none;
    }
  }
}

textarea {
  padding: 10px 14px;
  font-size: 14px;
  border: 1px solid #bbb;
  border-radius: 2px;
  height: 120px;
}

.agreement {
  margin-bottom: 15px;
}

.base-button {
  width: 100%;
  transition: 0.3s;

  &:disabled {
    border-radius: 5px;
    background-color: #aaa !important;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.2);
  }
}

.site-main {
  padding-bottom: 60px;
  background: #f9f6f1;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

  @include bp('sp') {
    background: #f9f6f2;
  }
}

.tour-inquiry {
  padding-top: 40px;
  width: 700px;
  margin: 0 auto;
  border-radius: 5px;

  @include bp('sp') {
    box-shadow: unset;
    width: 100%;
    padding: 40px 0 60px;
    margin: 0 auto;
  }

  form {
    max-width: 700px;
  }

  &__description {
    color: #555;
    font-size: 16px;
    line-height: 1.5em;
    letter-spacing: 0;
  }
}

.inquiry-property-submit {
  margin-top: 10px;
  margin-bottom: 40px;

  @include bp(sp) {
    margin-bottom: unset;
  }

  button {
    width: 100%;
    border-radius: 2px;
    background-color: #aaa;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.2);
    border: 0;
    color: #fff;
    font-size: 18px;
    letter-spacing: 1px;
    line-height: 27px;
    padding: 21px 0 !important;
    text-align: center;
    height: auto;
  }
}

.error-message {
  margin-bottom: 20px;
}

.privacy-link {
  color: #223983;
  border-bottom: 2px solid #223983;
}
</style>
<style lang="scss">
@import '~/assets/scss/tools/bootstraping';

.tour-inquiry {
  .inquiry-property-detail,
  .inquiry-property-options,
  .inquiry-property-input {
    margin-bottom: calc-rem(20px);
  }

  select,
  .input input,
  textarea {
    font-size: 18px;
  }

  select {
    background-color: #fff;
  }

  .inquiry-form-property {
    .rent-item {
      max-width: none;
      margin-bottom: 0;
      border-bottom: 1px solid #ddd;
      padding: 0 0 20px 0;
    }

    .rent-item-inner {
      display: flex;
    }

    .rent-image {
      width: calc-rem(140px);
      margin-right: calc-rem(14px);

      .rent-thumb {
        margin-bottom: 0;
        border-radius: 3px;
      }

      @include bp('sp') {
        width: calc-rem(100px);

        .rent-thumb {
          padding-bottom: 100%;
        }
      }

      img {
        background: $color_gray_light7;
        border-radius: calc-rem(3px);
        object-fit: cover;
      }
    }

    .rent-content {
      flex: 1;
    }
  }
}
</style>
