<template>
  <div class="wrapper" :class="onDetailPage && 'mb-30'">
    <TheHeader
      v-if="!hiddenHeader"
      :display-social="true"
      :with-banner="false"
      class="sticky"
    />
    <component :is="element" :class="!onDetailPage && 'site-main'">
      <div class="inquiry">
        <form class="inquiry-form" @submit.prevent="onFormSubmit">
          <div class="inquiry-form-property">
            <div class="inquiry-property-title">
              <h2>{{ $t('inquiry.propertyInquiry') }}</h2>
            </div>
            <div v-if="property" class="inquiry-property-detail">
              <PropertyHorizontal :property="property" :room-code="roomCode" />
            </div>
            <div v-if="errorMessage" class="error-message">
              <the-alert
                class="alert--danger"
                :content="errorMessage"
                :dismiss-able="true"
              />
            </div>
            <PropertyInput
              v-model="inquiryInfo"
              :available-languages="availableLanguages"
              @on-invalid="hasError = true"
              @on-valid="hasError = false"
              @dataCountry="handleDataCountry"
            />
            <div class="inquiry-property-agreement">
              <BaseCheckbox>
                <template slot="checkField">
                  <input
                    id="agreement"
                    v-model="isAgreement"
                    type="checkbox"
                    name="agreement"
                  />
                </template>
                <template slot="checkLabel">
                  <label for="agreement">
                    <a
                      v-if="$i18n.locale === 'kr'"
                      class="privacy-link"
                      href="https://www.inbound-platform.com/en/privacy/"
                      target="_blank"
                      >{{ $t('common.privacyPolicy') }}</a
                    >
                    {{ $t('inquiry.labelPolicy') }}
                    <a
                      v-if="$i18n.locale !== 'kr'"
                      class="privacy-link"
                      href="https://www.inbound-platform.com/en/privacy/"
                      target="_blank"
                      >{{ $t('common.privacyPolicy') }}</a
                    >.</label
                  >
                </template>
              </BaseCheckbox>
            </div>
            <div class="inquiry-property-submit">
              <BaseButton
                :class="{ 'button--blue': shouldFormValid }"
                :disabled="!shouldFormValid || !property || isSubmitting"
              >
                {{ $t('inquiry.buttonSubmit') }}
              </BaseButton>
            </div>
          </div>
        </form>
      </div>
    </component>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { MetaInfo } from 'vue-meta/types/vue-meta.d'
import get from 'lodash/get'
import { InquiryInfo } from '~/types'
import getProperty from '~/apollo/queries/getProperty.graphql'
import createInquiry from '~/apollo/mutations/createInquiry.graphql'
import { Property } from '~/types/types'
import { isPropertyVisible, getCookie } from '~/common/utils'

export default Vue.extend({
  name: 'PageInquiry',
  beforeRouteLeave(_to, _from, next) {
    if (this.isSubmitted) return next()

    this.$showConfirmDialog({
      title: this.$t('inquiry.titleDialog').toString(),
      message: this.$t('inquiry.leaveDialogMessage').toString(),
      hasImgNotice: true,
      businessHour: this.$t('inquiry.businessHourDialog').toString(),
      okButton: this.$t('inquiry.leaveConfirmButton').toString(),
      cancelButton: this.$t('inquiry.leaveCancelButton').toString(),
      okCallback: next,
      cancelCallback: () => next(false),
    })
  },
  props: {
    propertyDetail: {
      type: Object,
      default: null,
    },
    roomCodeDetail: {
      type: String,
      default: '',
    },
    hiddenHeader: {
      required: false,
      default: false,
      type: Boolean,
    },
    isMetaHidden: {
      required: false,
      default: false,
      type: Boolean,
    },
    onDetailPage: {
      required: false,
      default: false,
      type: Boolean,
    },
  },
  async asyncData(context: any) {
    const { app, route } = context
    const { rent, purposes = [] } = route.query

    const path = context.localePath('/')
    if (!rent) return context.redirect(path)
    if (rent === '') return context.redirect(path)

    // Why this instead of smart query? To handle 404 when item not found
    // Refer: https://github.com/nuxt-community/apollo-module/issues/381
    const result = await app.apolloProvider.defaultClient.query({
      query: getProperty,
      variables: { roomCode: rent },
    })

    const isVisible = isPropertyVisible(result, rent)
    if (!isVisible) {
      return context.redirect(path)
    }

    return {
      roomCode: rent,
      property: result.data.property,
      inquiryInfo: {
        name: '',
        email: '',
        phone: '',
        memo: '',
        purpose: Array.isArray(purposes) ? purposes : [purposes],
        preferredLanguage: [],
        whenToMoveIn: '',
      } as InquiryInfo,
    }
  },
  data() {
    return {
      isSubmitting: false,
      isSubmitted: false,
      inquiryInfo: {
        purpose: ['check_availability'],
        preferredLanguage: [],
        whenToMoveIn: '',
      } as Object,
      property: this.propertyDetail || (null as Property | null),
      roomCode: this.roomCodeDetail || (null as null | string),
      hasError: true as boolean,
      isAgreement: false as boolean,
      errorMessage: null as null | string,
      countryData: {
        areaCodes: null,
        dialCode: '81',
        iso2: 'jp',
        name: 'Japan (日本)',
        priority: 0,
      } as Object,
    }
  },
  head(): MetaInfo {
    if (this.isMetaHidden) {
      return {
        meta: [],
      }
    }
    return {
      meta: [{ hid: 'robots', name: 'robots', content: 'noindex,nofollow' }],
    }
  },
  computed: {
    availableLanguages(): string[] {
      const languages = this.property?.agency?.public?.availableLanguages || []
      const languagesTermp = ['en', 'jp', 'zh', 'kr']
      const languagesSort = [] as string[]

      languagesTermp.forEach((item) => {
        if (languages.includes(item)) {
          languagesSort.push(item)
        }
      })

      return languagesSort
    },
    shouldFormValid(): boolean {
      return !this.hasError && this.isAgreement
    },
    element() {
      if (this.onDetailPage) {
        return 'div'
      }
      return 'main'
    },
  },
  methods: {
    handleDataCountry(countryData: string) {
      this.countryData = countryData
    },
    handleOnInfoUpdated(data: InquiryInfo): void {
      this.inquiryInfo = data
    },
    async onFormSubmit(): Promise<void> {
      this.isSubmitting = true
      if (this.inquiryInfo.phone && this.countryData) {
        const { dialCode, name }: any = this.countryData
        this.inquiryInfo.phone = `+${dialCode}${this.inquiryInfo.phone} ${name}`
      }
      try {
        const result = await this.$apollo.mutate({
          mutation: createInquiry,
          variables: {
            record: {
              ...this.inquiryInfo,
              locale: this.$i18n.localeProperties.iso?.replace('-', '_'),
              propertyId: this.property?._id,
              roomCode: this.roomCode,
              utm: {
                utm_source: getCookie('utm_source'),
              },
              affiliate: {
                code: getCookie('affiliate'),
              },
            },
          },
        })
        const inquiryId = get(result, 'data.createInquiry.recordId', null)
        if (inquiryId) {
          this.isSubmitted = true
          await this.$router.push(this.localePath('/inquiry/thanks'))
        } else {
          this.errorMessage = 'Failed send inquiry! Please try again later'
        }
      } catch (error) {
        this.errorMessage = get(
          error,
          'networkError.result.errors[0].message',
          error.message
        )
      }

      this.isSubmitting = false
    },
  },
})
</script>
<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.site-main {
  padding-bottom: 60px;
  background: $color_white_smoke;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

  @include bp('sp') {
    background: $color_white;
  }
}

.inquiry {
  background: $color_white;
  padding-top: 40px;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  border-radius: 5px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.2);

  @include bp('sp') {
    box-shadow: unset;
    width: 100%;
    padding: 40px 20px 60px 20px;
    margin: 0 auto;
  }
}

.inquiry-form {
  margin: 0 auto;

  @include bp('pc') {
    width: 600px;
  }
}

.inquiry-property-title {
  background-color: $color_gray_light7;
  height: 40px;
  margin-bottom: 15px;

  h2 {
    font-family: $font_title;
    color: #333;
    font-weight: bold;
    letter-spacing: 0;
    padding: 0 14px;
    font-size: 16px;
    line-height: 40px;
  }
}

.inquiry-property-submit {
  padding-top: 10px;
  padding-bottom: 40px;

  @include bp('sp') {
    padding-bottom: unset;
  }

  button {
    width: 100%;
    border-radius: calc-rem(5px);
    background-color: #aaa;
    box-shadow: 0 calc-rem(3px) calc-rem(6px) 0 rgba(0, 0, 0, 0.2);
    border: 0;
    color: #fff;
    padding: calc-rem(15px) 0;
    text-align: center;
  }
}

.error-message {
  margin-bottom: 20px;
}

.privacy-link {
  color: $color_green;
}

.mb-30 {
  margin-bottom: 30px;

  @include bp('sp') {
    margin-bottom: 20px;
  }
}
</style>
<style lang="scss">
@import '~/assets/scss/tools/bootstraping';

.inquiry-property-detail,
.inquiry-property-options,
.inquiry-property-input {
  margin-bottom: calc-rem(20px);
}

.inquiry-form-property {
  .rent-item {
    max-width: none;
    margin-bottom: 0;
    border-bottom: 1px solid #ddd;
    padding: 0 0 20px 0;
  }

  .rent-item-inner {
    display: flex;
  }

  .rent-image {
    width: calc-rem(140px);
    margin-right: calc-rem(14px);

    .rent-thumb {
      margin-bottom: 0;
      border-radius: 3px;
    }

    @include bp('sp') {
      width: calc-rem(100px);

      .rent-thumb {
        padding-bottom: 100%;
      }
    }

    img {
      background: $color_gray_light7;
      border-radius: calc-rem(3px);
      object-fit: cover;
    }
  }

  .rent-content {
    flex: 1;
  }
}
</style>
