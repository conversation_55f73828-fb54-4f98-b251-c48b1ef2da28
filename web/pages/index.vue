<template>
  <NuxtErrorBoundary>
    <div class="wrapper">
      <TheHeader dark class="sticky" />
      <main class="site-main new-banner">
        <HomeBanner />
        <features />
        <div class="container reviews">
          <Reviews />
        </div>
        <div class="container">
          <RentGrid :title-heading="$t('home.featuredSectionTitle')" :properties="featuredProperties"
            :more-link="more_link" :loading="$apollo.queries.featuredProperties.loading" />
          <RentGrid :title-heading="$t('home.newSectionTitle')" :properties="get(recentProperties, 'items', [])"
            :more-link="more_link" :loading="$apollo.queries.recentProperties.loading" />
          <RentGrid :title-heading="$t('home.rankingSectionTitle')" :properties="get(topProperties, 'items', [])"
            :more-link="more_link" :is-ranking="true" :loading="$apollo.queries.topProperties.loading" />
        </div>
        <feat-banner />
        <div class="container">
          <div class="favorite">
            <div class="favorite">
              <div class="favorite-heading">
                <h2 class="favorite-heading-title">{{ $t('home.lblAroundUniversity') }}</h2>
              </div>
              <div class="favorite-grid">
                <nuxt-link :to="localePath(
                  `/rent/tokyo?stations=22790&stations=29636&stations=22545&stations=29332`
                )
                  " class="favorite-grid-item">
                  <div class="favorite-item">
                    <img alt="" src="~/assets/images/home/<USER>/uni_waseda.png" class="lazyLoad isLoaded" />
                    <div class="content">
                      <h4>{{ $t('home.lblWasedaUniversity') }}</h4>
                      <p>{{ $t('home.lblStudentProperties', { count: 72 }) }}</p>
                    </div>
                  </div>
                </nuxt-link>
                <nuxt-link :to="localePath(
                  `/rent/tokyo?stations=22790&stations=29636&stations=22545`
                )
                  " class="favorite-grid-item">
                  <div class="favorite-item">
                    <img alt="" src="~/assets/images/home/<USER>/uni_keio.png" class="lazyLoad isLoaded" />
                    <div class="content">
                      <h4>{{ $t('home.lblKeioUniversity') }}</h4>
                      <p>{{ $t('home.lblStudentProperties', { count: 46 }) }}</p>
                    </div>
                  </div>
                </nuxt-link>
                <nuxt-link :to="localePath(`/rent/tokyo?stations=22595&stations=22973`)" class="favorite-grid-item">
                  <div class="favorite-item">
                    <img alt="" src="~/assets/images/home/<USER>/uni_tokyo.png" class="lazyLoad isLoaded" />
                    <div class="content">
                      <h4>{{ $t('home.lblTokyoUniversity') }}</h4>
                      <p>{{ $t('home.lblStudentProperties', { count: 12 }) }}</p>
                    </div>
                  </div>
                </nuxt-link>
                <nuxt-link :to="localePath(
                  `/rent/tokyo?stations=22507&stations=22520&stations=23041&stations=22710&stations=22673`
                )
                  " class="favorite-grid-item">
                  <div class="favorite-item">
                    <img alt="" src="~/assets/images/home/<USER>/uni_sophia.png" class="lazyLoad isLoaded" />
                    <div class="content">
                      <h4 data-v-08543fa8="">
                        {{ $t('home.lblSophiaUniversity') }}
                      </h4>
                      <p data-v-08543fa8="">
                        {{ $t('home.lblStudentProperties', { count: 34 }) }}
                      </p>
                    </div>
                  </div>
                </nuxt-link>
                <nuxt-link :to="localePath(
                  `/rent/tokyo?stations=22507&stations=22520&stations=23041&stations=22710&stations=22673`
                )
                  " class="favorite-grid-item">
                  <div class="favorite-item">
                    <img alt="" src="~/assets/images/home/<USER>/uni_temple.png" class="lazyLoad isLoaded" />
                    <div class="content">
                      <h4>{{ $t('home.lblTempleUniversity') }}</h4>
                      <p>{{ $t('home.lblStudentProperties', { count: 34 }) }}</p>
                    </div>
                  </div>
                </nuxt-link>
                <nuxt-link :to="localePath(
                  `/rent/tokyo?stations=23006&stations=23008&stations=22933&stations=22986&stations=22637`
                )
                  " class="favorite-grid-item">
                  <div class="favorite-item">
                    <img alt="" src="~/assets/images/home/<USER>/uni_icu.png" class="lazyLoad isLoaded" />
                    <div class="content">
                      <h4>{{ $t('home.lblICUUniversity') }}</h4>
                      <p>{{ $t('home.lblStudentProperties', { count: 18 }) }}</p>
                    </div>
                  </div>
                </nuxt-link>
              </div>
            </div>
          </div>
          <div class="favorite">
            <favorite />
          </div>
          <div class="popular-area">
            <popular-area />
          </div>
        </div>
      </main>
      <TheFooter />
    </div>
  </NuxtErrorBoundary>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import setWith from 'lodash/setWith'
import getHomeProperties from '~/apollo/queries/getHomeProperties.graphql'
import getFeaturedProperties from '~/apollo/queries/getFeaturedProperties.graphql'
import {
  EnumPropertyRoomsVisibility,
  FilterFindManyPropertyInput,
  SortFindManyPropertyInput,
} from '~/types/types'

interface Variable {
  sort: SortFindManyPropertyInput
  filter: FilterFindManyPropertyInput
}

export default Vue.extend({
  data() {
    return {
      recentProperties: {} as any,
      topProperties: {} as any,
      more_link: '/rent/tokyo',
    }
  },
  head(): { title: string; meta: Array<any> } {
    return {
      title: this.$t('home.meta.title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('home.meta.description'),
        },
        // Facebook
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.$t('home.meta.title'),
        },
        {
          hid: 'og:type',
          property: 'og:type',
          content: 'website',
        },
        {
          hid: 'og:url',
          property: 'og:url',
          content: this.pageURL,
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: `${this.$config.appUrl}/social-main.png`,
        },
        {
          hid: 'og:site_name',
          property: 'og:site_name',
          content: 'Living Japan',
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.$t('home.meta.description'),
        },
        // Twitter
        {
          hid: 'twitter:card',
          property: 'twitter:card',
          content: 'summary_large_image',
        },
        {
          hid: 'twitter:site',
          property: 'twitter:site',
          content: '@livingjapan_',
        },
        {
          hid: 'twitter:url',
          property: 'twitter:url',
          content: this.pageURL,
        },
        {
          hid: 'twitter:title',
          property: 'twitter:title',
          content: this.$t('home.meta.title'),
        },
        {
          hid: 'twitter:description',
          property: 'twitter:description',
          content: this.$t('home.meta.description'),
        },
        {
          hid: 'twitter:image',
          property: 'twitter:image',
          content: `${this.$config.appUrl}/social-main.png`,
        },
        {
          name: 'facebook-domain-verification',
          content: '73q1ntltqtkta6ojj5vr8vm6sh1v7z',
        },
      ],
    }
  },

  apollo: {
    featuredProperties: {
      query: getFeaturedProperties,
    },
    recentProperties: {
      query: getHomeProperties,
      variables(): Variable {
        return {
          sort: SortFindManyPropertyInput.CreatedatDesc,
          filter: setWith({}, '_operators.rooms.visibility.in', [
            EnumPropertyRoomsVisibility.Public,
            EnumPropertyRoomsVisibility.Full,
          ]),
        }
      },
      update(data) {
        return data.properties
      },
    },
    topProperties: {
      query: getHomeProperties,
      variables(): Variable {
        return {
          sort: SortFindManyPropertyInput.HighestViewWeekly,
          filter: setWith({}, '_operators.rooms.visibility.in', [
            EnumPropertyRoomsVisibility.Public,
          ]),
        }
      },
      update(data) {
        return data.properties
      },
    },
  },
  computed: {
    pageURL(): string {
      return this.$route
        ? this.$config.appUrl + this.$route.path
        : this.$config.appUrl
    },
  },
  methods: { get },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.reviews {
  padding-top: 30px;
}

.favorite {
  @include bp(pc) {
    padding-top: 30px;
  }
}

.favorite-heading {
  margin-bottom: 10px;
  @include flex(null, center, space-between);
}

.favorite-heading-title {
  margin: 0;
  font-size: 22px;
  line-height: 1.5;
}

.favorite-grid {
  display: flex;
  flex-wrap: wrap;
  margin-left: -10px;
  margin-right: -10px;
}

.favorite-grid-item {
  width: 100%;
  padding-left: 10px;
  margin-bottom: 30px;
  padding-right: 10px;

  @include bp(768px, 900px) {
    width: 50%;
  }

  @include bp(901px) {
    width: 25%;
  }

  .favorite-item {
    display: flex;
    border-radius: 4px;
    padding-right: 16px;
    align-items: center;
    background-color: #fff;
    transition: all ease 0.3s;
    font-family: $font_base;
    box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.1);

    &:hover {
      box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.2);
    }

    img {
      width: 70px;
      height: 70px;
      margin-right: 16px;
      object-fit: cover;
      border-radius: 4px 0 0 4px;
    }

    h4 {
      color: $color_base;
      font-size: 16px;
      margin-bottom: 0;
      font-weight: bold;
      line-height: 22px;
    }

    p {
      font-size: 12px;
      margin-bottom: 0;
      line-height: 17px;
      color: $color_base;
    }
  }
}
</style>
