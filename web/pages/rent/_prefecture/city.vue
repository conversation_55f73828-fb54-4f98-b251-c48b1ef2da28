<template>
  <div class="wrapper">
    <TheHeader class="sticky" />
    <main class="site-main">
      <div class="container">
        <h1 class="page-heading flex aic">
          <img
            src="~/assets/images/icons/ic_map.svg"
            alt=""
            width="24"
            height="24"
          />
          <span>
            {{ prefectureName('city.pageTitle') }}
          </span>
        </h1>
        <div class="search_form">
          <div class="search_form__group flex">
            <div class="search_form__group__name">
              <BaseCheckbox class="search_form__checkbox">
                <template slot="checkField">
                  <input
                    id="tokyo_all"
                    type="checkbox"
                    :checked="isCheckAll"
                    @change="checkAll"
                  />
                </template>
                <template slot="checkLabel">
                  <label for="tokyo_all">
                    {{ prefectureName('city.listTitle') }}
                  </label>
                </template>
              </BaseCheckbox>
            </div>
            <ul class="search_form__group__list flex fw">
              <li
                v-for="city in $accessor.cities[prefecture] || []"
                :key="city._id"
                class="search_form__group__list-item"
              >
                <BaseCheckbox
                  class="search_form__checkbox"
                  :class="{ disabled: city.propertyCounts === 0 }"
                >
                  <template slot="checkField">
                    <input
                      :id="`area_${city._id}`"
                      type="checkbox"
                      :value="city._id"
                      :checked="
                        areaSelected.includes(city._id) &&
                        city.propertyCounts > 0
                      "
                      @change="onSelectArea(city._id)"
                    />
                  </template>
                  <template slot="checkLabel">
                    <label :for="`area_${city._id}`">
                      {{ city[localeField] || city.en }} ({{
                        city.propertyCounts
                      }})
                    </label>
                  </template>
                </BaseCheckbox>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </main>
    <TheFooter class="has-stickybar" />
    <div class="form-button">
      <div class="flex jcc">
        <base-button
          class="button--blue"
          :disabled="!areaSelected.length"
          @click.prevent="onSubmit"
        >
          {{ $t('city.buttonSubmit', { count: propertyCount }) }}
        </base-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import setWith from 'lodash/setWith'
import { Context } from '@nuxt/types'
import { getPropertyCount, toLocaleField } from '~/common/utils'
import { City } from '~/types/types'

const filterPath = '_operators.address.city.in'

interface AsyncDataReturn {
  prefecture: string
}

export default Vue.extend({
  name: 'SearchRentByCity',
  async asyncData({ app, params }: Context): Promise<AsyncDataReturn> {
    const prefecture = (params?.prefecture as string) || ''
    if (get(app.$accessor, `cities.${prefecture}.length`, 0))
      return { prefecture }
    await app.$accessor.fetchPrefecture(prefecture)
    await app.$accessor.fetchCities(prefecture)
    return { prefecture }
  },
  data() {
    return {
      areaSelected: [] as string[],
      propertyCount: '',
      prefecture: 'tokyo',
    }
  },
  computed: {
    isCheckAll(): boolean {
      return (
        this.$accessor.cities[this.prefecture].length ===
        this.areaSelected.length
      )
    },
    localeField(): keyof City {
      return toLocaleField(this?.$i18n?.localeProperties?.iso)
    },
  },
  watch: {
    areaSelected: {
      immediate: false,
      handler() {
        this.countProperties()
      },
    },
  },
  methods: {
    prefectureName(key: string): string {
      const prefecture = this.$accessor.prefectureName(
        this.prefecture,
        this.localeField
      )

      return this.$t(key, { prefecture }).toString()
    },
    onSelectArea(id: string) {
      if (this.areaSelected.includes(id)) {
        this.areaSelected = this.areaSelected.filter((item) => item !== id)
      } else {
        this.areaSelected.push(id)
      }
    },
    onSubmit() {
      if (this.areaSelected?.length) {
        const isMultipleCity = this.areaSelected.length > 1
        const cityArg = isMultipleCity ? '' : this.areaSelected.toString()

        this.$router.push({
          path: this.localePath(`/rent/${this.prefecture}/${cityArg}`),
          query: isMultipleCity ? { cities: this.areaSelected } : {},
        })
      }
    },
    async countProperties() {
      if (!this.areaSelected.length) {
        this.propertyCount = ''
      } else {
        const filter = {}
        setWith(filter, filterPath, this.areaSelected)
        const propertyCount = await getPropertyCount(this.$apollo, filter)
        this.propertyCount = `(${propertyCount})`
      }
    },
    checkAll() {
      if (this.isCheckAll) {
        this.areaSelected = []
      } else {
        this.areaSelected = this.$accessor.cities[this.prefecture].map(
          (item) => item._id
        )
      }
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/_bootstraping';

.page-heading {
  font-size: 18px;

  span {
    padding-left: 10px;
    font-family: Arial, sans-serif;
    color: #4e5a62;
  }

  @include bp(700px, 0) {
    font-size: 20px;
  }

  img {
    width: 20px;
    filter: invert(33%) sepia(10%) saturate(669%) hue-rotate(161deg)
      brightness(97%) contrast(88%);
  }
}

.search_form {
  margin-left: -20px;
  margin-right: -20px;

  @include bp(700px, 0) {
    margin-left: 0;
    margin-right: 0;
  }
}

.search_form__group {
  border-top: 1px solid #cbdae7;
  border-bottom: 1px solid #cbdae7;
  flex-wrap: wrap;

  @include bp(700px, 0) {
    flex-wrap: nowrap;
  }

  + .search_form__group {
    border-top: none;
  }
}

.search_form__group__name {
  width: 100%;
  color: #4e5a62;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 0;
  padding: 14px 20px;
  background: #f0f5fa;
  font-family: Arial, sans-serif;

  @include bp(700px, 0) {
    min-width: 280px;
    width: 280px;
  }

  @media (max-width: 768px) {
    border-bottom: 1px solid #cbdae7;
  }
}
.search_form__group__list {
  margin-left: 0;
  margin-bottom: 0;
  flex: 1 1 auto;
}

.search_form__group__list-item {
  width: 100%;

  @include bp(700px, 0) {
    width: 33.33%;
  }
}

.search_form__checkbox {
  padding: 10px 20px;

  &.disabled {
    pointer-events: none;
  }
}

.form-button {
  bottom: 0;
  width: 100%;
  padding: 10px 20px;
  position: sticky;
  background: #fff;
  border-top: 1px solid #cbdae7;
  border-bottom: 1px solid #cbdae7;
}

.button--blue {
  width: 100%;
  height: 48px;
  font-size: 16px;
  max-width: 400px;
  line-height: 48px;

  &[disabled] {
    opacity: 0.4;
  }

  @media (max-width: 768px) {
    max-width: 100%;
  }
}
</style>

<style lang="scss">
.search_form__checkbox {
  &:hover .checkmark {
    box-shadow: 0 0 0 0.2em rgb(37 173 180 / 50%);
  }

  .inquiry-property-checkbox {
    font-family: Arial, sans-serif;
  }

  .inquiry-property-checkbox .checkmark {
    border: 1.2px solid #cbdae7 !important;
  }

  &.disabled {
    .inquiry-property-checkbox {
      color: #aaa;

      .checkmark {
        background: #eee !important;
        border: #eee !important;
      }
    }
  }

  .inquiry-property-checkbox input:checked ~ .checkmark {
    background-color: #25adb4 !important;
    border-color: #1ba3aa !important;
  }

  .inquiry-property-checkbox .checkmark::after {
    border-color: #fff !important;
  }
}
</style>
