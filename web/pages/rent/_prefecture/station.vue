<template>
  <div class="wrapper">
    <TheHeader class="sticky" />
    <main class="site-main">
      <station-list
        :value="selected"
        :prefecture="prefectureName"
        @input="selected = $event"
      />
    </main>
    <TheFooter class="has-stickybar" />
    <div class="form-button">
      <div class="flex jcc">
        <base-button
          class="button--blue"
          :disabled="!selected.length"
          @click.prevent="onSubmit"
        >
          {{ $t('station.buttonSubmit', { count: propertyCount }) }}
        </base-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import setWith from 'lodash/setWith'
import { getPropertyCount } from '~/common/utils'
type ReturnAsyncData = {
  prefectureName: string
}

export default Vue.extend({
  name: 'Stations',
  asyncData(context: any): ReturnAsyncData {
    const { error, params } = context
    const prefectureName = (params?.prefecture as string) || ''
    const message = context.i18n.t('common.pageNotFound')
    if (prefectureName !== 'tokyo') {
      return error({ statusCode: 404, message })
    }

    return {
      prefectureName,
    }
  },
  data() {
    return {
      selected: [] as string[],
      prefectureName: null,
      propertyCount: '',
    }
  },
  watch: {
    selected: {
      immediate: false,
      handler() {
        this.countProperties()
      },
    },
  },
  methods: {
    onSubmit() {
      if (this.selected?.length) {
        this.$router.push({
          path: this.localePath(`/rent/tokyo/`),
          query: { stations: this.selected },
        })
      }
    },

    async countProperties() {
      if (!this.selected.length) {
        this.propertyCount = ''
      } else {
        const filter = {}
        const path = '_operators.access.stations.stationId.in'
        setWith(filter, path, this.selected)

        const totalProperty = await getPropertyCount(this.$apollo, filter)
        this.propertyCount = `(${totalProperty})`
      }
    },
    getLineStyle(color: string) {
      return color ? { 'background-color': color } : {}
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/_bootstraping';

.form-button {
  bottom: 0;
  width: 100%;
  position: sticky;
  background: #fff;
  padding: 10px 20px;
  border-top: 1px solid #cbdae7;
  border-bottom: 1px solid #cbdae7;
}

.button--blue {
  width: 100%;
  max-width: 400px;
  font-size: 14px;
  height: 48px;
  line-height: 48px;

  &[disabled] {
    opacity: 0.4;
  }

  @media (max-width: 768px) {
    height: 44px;
    max-width: 100%;
  }
}
</style>
