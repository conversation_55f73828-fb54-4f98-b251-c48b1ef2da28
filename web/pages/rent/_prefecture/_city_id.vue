<router>
{
  // match: /rent/tokyo/r3p13ca1c1
  // match: /rent/tokyo
  // match: /rent
  path: '/rent/:prefecture?/:city_id(\\d{5})?'
}
</router>

<template>
  <div class="wrapper">
    <TheHeader class="sticky" />
    <main class="site-main">
      <div class="container">
        <h1 class="page-heading">{{ pageTitle }}</h1>
        <div class="page-wrapper">
          <div class="main-content">
            <div class="page-meta">
              <div class="properties-count">
                {{
                  $t('rentList.hits', {
                    count: get(pagination, 'itemCount', '~'),
                  })
                }}
              </div>
              <PropertySort
                class="properties-order"
                :options="orderOptions"
                :value="sortBy"
                :class="{ show: showSort }"
                @change="onSortChange($event)"
                @close="showSort = false"
              />
            </div>
            <FilterMobile
              v-if="hasItem"
              @show-filter="showFilter = true"
              @show-sort="showSort = true"
            />
            <template v-if="$apollo.queries.properties.loading">
              <PropertyLoadingItem
                v-for="loadingIndex in 3"
                :key="loadingIndex"
              />
            </template>
            <template v-else-if="!hasItem">
              {{ $t('rentList.noResult') }}
            </template>
            <template v-else>
              <div class="property-list">
                <PropertyItem
                  v-for="property in items"
                  :key="property._id"
                  :property="property"
                />
              </div>
              <div class="page-pagination">
                <ThePagination
                  v-model="page"
                  :total="get(pagination, 'itemCount', 0)"
                  :per-page="get(pagination, 'perPage', 0)"
                  @input="onPageChange"
                />
              </div>
              <p class="page-pagination__summary">
                {{
                  $t('rentList.paginationInfo', {
                    num: `${pageSummary.from}-${pageSummary.to}`,
                    total: get(pagination, 'itemCount', '~'),
                  })
                }}
              </p>
            </template>
          </div>
          <div class="page-aside" :class="{ show: showFilter }">
            <SearchWidget
              class="search-right"
              :filters="filters"
              :prefecture="prefectureKeyword"
              @hide-filter="showFilter = false"
              @on-filter="onFilter"
            />
          </div>
        </div>
      </div>
    </main>
    <TheFooter class="has-stickybar" />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import capitalize from 'lodash/capitalize'
import setWith from 'lodash/set'
import omit from 'lodash/omit'
import ObjectID from 'bson-objectid'
import getProperties from '~/apollo/queries/getProperties.graphql'
import agencyById from '~/apollo/queries/getAgencyById.graphql'
import getCityById from '~/apollo/queries/getCityById.graphql'
import {
  Prefecture,
  EnumPropertyRoomsVisibility,
  PaginationInfo,
  Property,
  PropertyPagination,
  SortFindManyPropertyInput,
  Maybe,
} from '~/types/types'
import { toLocaleField } from '~/common/utils'
import { enabledPrefectures } from '~/constants/prefectures'

const sortCode = {
  latest: SortFindManyPropertyInput.CreatedatDesc,
  highest_price: SortFindManyPropertyInput.HighestmonthlypriceDesc,
  lowest_price: SortFindManyPropertyInput.LowestmonthlypriceAsc,
}

type SortCodeKey = keyof typeof sortCode

type filtersParam = {
  agencyId: string
  layouts: string[]
  buildingTypes: string[]
  prefecture: string
  cities?: string[]
  stationDistance: number
  monthlyMin: number
  monthlyMax: number
  sizeMin: number
  sizeMax: number
  visibility: EnumPropertyRoomsVisibility[]
  tags: string[]
  stations: string[]
}

type AsyncDataReturn = Promise<{
  page: number
  prefectureKeyword: string
  prefecture: Prefecture
  agencyName: string
  sortBy: SortCodeKey
  filters: filtersParam
  cityName?: string
}>

const routeQueryMap = {
  buildingTypes: 'building_types',
  monthlyMin: 'monthly_min',
  monthlyMax: 'monthly_max',
  sizeMin: 'size_min',
  sizeMax: 'size_max',
  layouts: 'layouts',
  cities: 'cities',
  stationDistance: 'station_distance',
  tags: 'tags',
  stations: 'stations',
}

export default Vue.extend({
  name: 'SearchProperties',
  async asyncData({ error, query, params, app, i18n }: any): AsyncDataReturn {
    const page = Number.parseInt(query.page) || 1
    const sortBy = (query?.sort as SortCodeKey) || 'latest'
    const monthlyMin = Number.parseInt(get(query, 'monthly_min', 0)) || 0
    const monthlyMax = Number.parseInt(get(query, 'monthly_max', 0)) || 0
    const sizeMin = Number.parseFloat((query?.size_min as string) || '0')
    const sizeMax = Number.parseFloat((query?.size_max as string) || '0')
    const stationDistance = Number.parseFloat(
      (query?.station_distance as string) || '0'
    )
    const layouts = (query?.layouts as string[]) || []
    const stations = (query?.stations as string[]) || []
    const tags = (query?.tags as string[]) || []
    let cities = (query?.cities as string[]) || []
    const buildingTypes = (query?.building_types as string[]) || []
    const message = i18n.t('common.pageNotFound')

    const prefectureKeyword = (params?.prefecture as string) || ''
    const allowedPath = ['/', '', ...enabledPrefectures]
    if (!allowedPath.includes(prefectureKeyword)) {
      return error({ statusCode: 404, message })
    }

    const prefecture = await app.$accessor.fetchPrefecture(prefectureKeyword)

    const cityId = (params?.city_id as string) || ''
    let cityName = ''

    if (cityId) {
      const city = await app.apolloProvider.defaultClient.query({
        query: getCityById,
        variables: { id: cityId },
      })
      const field = toLocaleField(i18n.localeProperties.iso)
      cityName = get(city, `data.cityById.${field}`)
      if (!cityName) {
        return error({ statusCode: 404, message })
      }
      cities = [cityId]
    }

    const agencyId = params?.agency_id || null
    let agencyName = ''
    if (agencyId) {
      if (!ObjectID.isValid(agencyId)) {
        return error({ statusCode: 404, message })
      }
      const agencyData = await app.apolloProvider.defaultClient.query({
        query: agencyById,
        variables: { id: agencyId },
      })
      if (!agencyData?.data?.agencyById) {
        return error({ statusCode: 404, message })
      }

      agencyName = agencyData.data.agencyById.public.name
    }

    return {
      page,
      prefecture,
      prefectureKeyword,
      agencyName,
      cityName,
      sortBy,
      filters: {
        agencyId,
        layouts: Array.isArray(layouts) ? layouts : [layouts],
        stations: Array.isArray(stations) ? stations : [stations],
        buildingTypes: Array.isArray(buildingTypes)
          ? buildingTypes
          : [buildingTypes],
        prefecture: prefecture?._id,
        cities: Array.isArray(cities) ? cities : [cities],
        stationDistance,
        monthlyMin,
        monthlyMax,
        sizeMin,
        sizeMax,
        visibility: [
          EnumPropertyRoomsVisibility.Public,
          EnumPropertyRoomsVisibility.Full,
        ],
        tags: Array.isArray(tags) ? tags : [tags],
      },
    }
  },
  data() {
    return {
      page: 0,
      prefecture: null as Maybe<Prefecture>,
      prefectureKeyword: null as string | null,
      agencyName: null as string | null,
      cityName: null as string | null,
      properties: {} as PropertyPagination,
      orderOptions: [
        {
          value: 'latest',
          label: this.$t('rentList.sort.new'),
        },
        {
          value: 'lowest_price',
          label: this.$t('rentList.sort.lowestPrice'),
        },
        {
          value: 'highest_price',
          label: this.$t('rentList.sort.highestPrice'),
        },
      ],
      sortBy: 'latest' as SortCodeKey,
      showFilter: false,
      showSort: false,

      // Filters placeholder,
      // Please refer asyncData() how to get it
      filters: {
        agencyId: null as string | null,
        layouts: [] as string[],
        buildingTypes: [] as string[],
        prefecture: null as string | null,
        cities: [] as string[],
        stationDistance: 0,
        monthlyMax: 0,
        monthlyMin: 0,
        sizeMax: 0,
        sizeMin: 0,
        visibility: [
          EnumPropertyRoomsVisibility.Public,
          EnumPropertyRoomsVisibility.Full,
        ],
        tags: [] as string[],
        stations: [] as string[],
      },
      agency: {},
    }
  },
  head(): { title: string; meta: Array<any> } {
    const param = { prefectureName: this.prefectureName }
    const title = this.$t('rentList.meta.title', param)
    const description = this.$t('rentList.meta.description')
    return {
      title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: description,
        },
        // Facebook
        {
          hid: 'og:title',
          property: 'og:title',
          content: title,
        },
        {
          hid: 'og:type',
          property: 'og:type',
          content: 'article',
        },
        {
          hid: 'og:url',
          property: 'og:url',
          content: this.pageURL,
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: `${this.$config.appUrl}/social-main.png`,
        },
        {
          hid: 'og:site_name',
          property: 'og:site_name',
          content: 'Living Japan',
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: description,
        },
        // Twitter
        {
          hid: 'twitter:card',
          property: 'twitter:card',
          content: 'summary_large_image',
        },
        {
          hid: 'twitter:site',
          property: 'twitter:site',
          content: '@livingjapan_',
        },
        {
          hid: 'twitter:url',
          property: 'twitter:url',
          content: this.pageURL,
        },
        {
          hid: 'twitter:title',
          property: 'twitter:title',
          content: title,
        },
        {
          hid: 'twitter:description',
          property: 'twitter:description',
          content: description,
        },
        {
          hid: 'twitter:image',
          property: 'twitter:image',
          content: `${this.$config.appUrl}/social-main.png`,
        },
      ],
    }
  },
  computed: {
    pageTitle(): string {
      if (this.cityName) {
        return this.$t('rentList.propertiesIn', {
          prefecture: this.cityName,
        }).toString()
      }

      if (this.prefectureName) {
        return this.$t('rentList.propertiesIn', {
          prefecture: this.prefectureName,
        }).toString()
      }

      if (this.agencyName) {
        return this.$t('rentList.propertiesOf', {
          agency: this.agencyName,
        }).toString()
      }
      return this.$t('rentList.allProperties')
    },

    prefectureName(): string {
      if (this.prefecture) {
        const field = toLocaleField(this.$i18n.localeProperties.iso)

        const name =
          get(this, `prefecture.${field}`, null) ||
          get(this, `prefecture.en`, null)
        if (!name) return '_missing_'
        return capitalize(name.replace(/(-[a-z]+)$/, ''))
      }

      return ''
    },

    pageSummary(): { from: number; to: number } {
      const { currentPage, perPage } = this.pagination
      const from = (currentPage - 1) * perPage + 1
      const to = currentPage * perPage
      return { from, to }
    },
    items(): Array<Property> {
      return this.properties?.items || ([] as Array<Property>)
    },
    pagination(): PaginationInfo {
      return this.properties?.pageInfo || {}
    },
    hasItem(): boolean {
      return (this.pagination?.itemCount || 0) > 0
    },
    pageURL() {
      return this.$route
        ? this.$config.appUrl + this.$route.path
        : this.$config.appUrl
    },
  },
  watchQuery: [
    'page',
    'sort',
    'building_types',
    'layouts',
    'monthly_min',
    'monthly_max',
    'station_distance',
    'cities',
    'tags',
    'stations',
  ],
  methods: {
    get,
    onPageChange(page: string) {
      const query = { ...this.$route.query, page }
      this.$router.push({ query })
    },
    onSortChange(sort: SortCodeKey): void {
      // FIXME: Later investigate why sortBy param always giving event object
      // instead of plain string
      if (typeof sort !== 'string') return
      const query = { ...this.$route.query, sort }
      this.$router.push({ query })
    },
    onFilter(filters: filtersParam): void {
      let searchQuery = {}
      let cloneFilter = { ...filters }

      const cities = get(filters, 'cities', [])
      const isMultipleCity = cities.length > 1
      let path = this.$route.path.replace(this.$route.params.city_id, '')

      if (!isMultipleCity && !this.$route.params.agency_id) {
        cloneFilter = omit(filters, 'cities')
        path = this.localePath(
          `/rent/${this.prefectureKeyword}/${cities.toString()}`
        )
      }

      Object.keys(routeQueryMap).forEach((key: string) => {
        const filterValue = get(cloneFilter, key)
        if (filterValue) {
          const newKey = get(routeQueryMap, key)
          searchQuery = { ...searchQuery, [newKey]: filterValue }
        }
      })

      const { sort, page } = this.$route.query

      const query = { sort, page, ...searchQuery }
      this.$router.push({
        path,
        query,
      })
      this.showFilter = false
    },
  },
  apollo: {
    properties: {
      query: getProperties,
      fetchPolicy: 'network-only',
      variables(): { perPage: number; page: number; sort: string } {
        const input = {
          perPage: 20,
          sort: sortCode[this.sortBy],
          page: this.page,
          filter: {},
        }

        const filtersMap = {
          monthlyMin: 'filter._operators.rooms.monthlyFee.gte',
          monthlyMax: 'filter._operators.rooms.monthlyFee.lte',
          sizeMin: 'filter._operators.rooms.size.gte',
          sizeMax: 'filter._operators.rooms.size.lte',
          layouts: 'filter._operators.rooms.roomLayout.in',
          buildingTypes: 'filter._operators.buildingType.in',
          stationDistance: 'filter._operators.nearestStationDistance.lte',
          prefecture: 'filter.address.prefecture',
          cities: 'filter._operators.address.city.in',
          visibility: 'filter._operators.rooms.visibility.in',
          agencyId: 'filter.agencyId',
          tags: 'filter._include_rooms_tags',
          stations: 'filter._operators.access.stations.stationId.in',
        }

        for (const filter in this.filters) {
          const filterKey = filter as keyof typeof filtersMap
          const value =
            // lowercase when selected layout is `More`
            filterKey === 'layouts'
              ? this.filters.layouts?.map((layout) =>
                  layout === 'More' ? layout.toLowerCase() : layout
                )
              : this.filters[filterKey]

          const isFilterApplied =
            (typeof value === 'number' && value > 0) ||
            (Array.isArray(value) && value.length > 0) ||
            (typeof value === 'string' && value.trim().length > 0)

          if (!isFilterApplied) continue
          setWith(input, filtersMap[filterKey], value)
        }

        return input
      },
    },
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/settings/_variables.scss';
@import '~/assets/scss/tools/_functions.scss';
@import '~/assets/scss/tools/_breakpoint.scss';

.page-heading {
  margin-bottom: 0;
  font-size: 18px;

  @include font-size(22, 30);

  @include bp('pc') {
    margin-bottom: 5px;
    font-size: 24px;
    line-height: 1.2em;
  }
}

@include bp('sp') {
  .site-main {
    padding-bottom: 20px;
    padding-top: 4.375rem;
    background: #f5f5f5;
  }

  .property-list {
    margin-left: -20px;
    margin-right: -20px;
    padding: 0 20px;
  }

  .property-detail {
    margin: 0 -20px;
    padding: 0 20px;
  }

  .property-image {
    margin: 0 20px 15px;
  }

  .property-item {
    background: #fff;
    padding: 20px 20px 0;
    margin: 0 -20px 15px;
  }

  .property-header {
    margin-bottom: 15px;
  }

  .property-room__img {
    width: 80px;
  }

  .search__widget__content {
    padding: 20px 20px 0;

    @include bp(sp) {
      flex: 1;
    }
  }

  .search-filter-close {
    width: 28px;
    height: 28px;
  }
  .search-filter-close::after,
  .search-filter-close::before {
    left: 6px;
  }
}

.page-wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}

.main-content {
  width: 800px;
  max-width: 100%;

  @include bp(769px, 1100px) {
    width: 60%;
  }

  @include bp('pc') {
    margin-right: 40px;
  }
}

.search-right {
  margin-bottom: 20px;
  @include bp(sp) {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    height: 100%;
    margin-bottom: 5px;
  }
}

.page-aside {
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
  opacity: 0;
  z-index: -1;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: fixed;
  background: $color_white;
  visibility: hidden;
  transition: all 0.25s ease;
  transform: translateX(10px) scale(0.89);

  &.show {
    z-index: 99;
    opacity: 1;
    visibility: visible;
    transform: translateX(0) scale(1);
  }

  @include bp('sp') {
    height: 100%;
  }

  @include bp('pc') {
    z-index: 1;
    opacity: 1;
    position: static;
    visibility: visible;
    transform: translateX(0) scale(1);
    padding: 20px 20px 0 20px;
    background: $color_gray_light6;
    min-height: 1015px;
  }
}

.page-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.properties-count {
  line-height: calc-rem(22px);
}

.properties-order {
  @include bp('pc') {
    width: calc-rem(150px);
  }
}

.page-pagination {
  margin-top: calc-rem(30px);
}

.page-pagination__summary {
  margin-top: calc-rem(15px);
  text-align: center;
  @include font-size(16, 22);
}
</style>

<style lang="scss">
@import '~/assets/scss/settings/_variables.scss';

.page-aside .line-box-fixed {
  background-color: $color_white;
  margin: 0 -20px;
  padding-top: 20px;

  .line-pc-only {
    max-width: 100%;
  }
}
</style>
