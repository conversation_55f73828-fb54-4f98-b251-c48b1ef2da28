<router>
 {
   // match: /rent/tokyo/12345678
    path: '/rent/:prefecture/:room_code(\\d{8})'
 }
</router>
<template>
  <div class="wrapper">
    <TheHeader class="sticky" />
    <main class="site-main">
      <TheBreadcrumbs :crumbs="breadcrumbs" />
      <div class="container">
        <div class="page-wrapper">
          <div class="main-content">
            <PropertySlider
              :prod-sliders="propertyImages"
              :is-full="Visibility.Full === selectedRoom.visibility"
            />
            <section class="property-summary">
              <div class="property-tags">
                <tag-label>{{ $t('roomDetail.foreignersWelcome') }}</tag-label>
                <tag-label
                  v-for="tag in get(selectedRoom, 'tags', [])"
                  :key="tag"
                >
                  {{ get(tags, `${tag}`, '') }}
                </tag-label>
              </div>
              <h1 class="property-name">{{ propertyName }}</h1>
              <div
                v-if="Visibility.Full !== selectedRoom.visibility"
                class="property-price"
              >
                {{ propertyPrice }}
                <small>({{ $t('common.maintenanceFee') }})</small>
              </div>
              <div
                v-if="get(selectedRoom, 'rejMetadata.propertyId')"
                class="rej-property-id"
              >
                Property ID: {{ get(selectedRoom, 'rejMetadata.propertyId') }}
              </div>
              <p class="property-info__row">
                {{ propertyAddress }}
              </p>
              <p
                v-for="(
                  stationAccessByWalk, indexStation
                ) in stationsAccessByWalk"
                :key="indexStation"
                class="property-info__row"
              >
                {{ stationAccessByWalk }}
              </p>
              <div v-if="location.lat && location.lng" class="maps-place">
                <GmapMap
                  :center="location"
                  :zoom="14"
                  style="width: 100%; height: 200px"
                >
                  <GmapMarker v-if="addressLine" :position="location" />
                  <GmapCircle
                    v-else
                    :center="location"
                    :radius="500"
                    :visible="true"
                    :options="{
                      fillColor: 'red',
                      fillOpacity: 0.1,
                      strokeOpacity: 0,
                    }"
                  />
                </GmapMap>
              </div>
              <div class="language-comment">
                {{ commentLang }}
              </div>
            </section>
            <section class="detail-section">
              <h3 class="section-title">
                {{ $t('roomDetail.propertyDetails') }}
              </h3>
              <div class="property-detail">
                <div class="property-detail__row">
                  <div class="property-detail__cell property-detail__heading">
                    {{ $t('roomDetail.layout') }}
                  </div>
                  <div class="property-detail__cell">
                    {{ getRoomLayout(selectedRoom) }}
                  </div>
                </div>
                <div class="property-detail__row">
                  <div class="property-detail__cell property-detail__heading">
                    {{ $t('roomDetail.size') }}
                  </div>
                  <div class="property-detail__cell">
                    {{ get(selectedRoom, 'size', 0) }}m<sup>2</sup>
                  </div>
                </div>
                <div class="property-detail__row">
                  <div class="property-detail__cell property-detail__heading">
                    {{ $t('roomDetail.yearBuilt') }}
                  </div>
                  <div class="property-detail__cell">
                    {{ buildDate }}
                  </div>
                </div>
                <div class="property-detail__row">
                  <div class="property-detail__cell property-detail__heading">
                    {{ $t('roomDetail.floor') }}
                  </div>
                  <div class="property-detail__cell">
                    {{ get(selectedRoom, 'floor', '') }}F /
                    {{ get(property, 'maxFloor', '') }}F
                  </div>
                </div>
                <div class="property-detail__row">
                  <div class="property-detail__cell property-detail__heading">
                    {{ $t('roomDetail.windowDirection') }}
                  </div>
                  <div class="property-detail__cell">
                    {{
                      $t(
                        'common.options.room.mainWindow.' +
                          get(selectedRoom, 'mainWindow')
                      )
                    }}
                  </div>
                </div>
                <div class="property-detail__row">
                  <div class="property-detail__cell property-detail__heading">
                    {{ $t('roomDetail.availableFrom') }}
                  </div>
                  <div class="property-detail__cell">
                    {{ availability }}
                  </div>
                </div>
                <div class="property-detail__row">
                  <div class="property-detail__cell property-detail__heading">
                    {{ $t('roomDetail.type') }}
                  </div>
                  <div class="property-detail__cell">
                    {{
                      $t(
                        'common.options.property.buildingType.' +
                          property.buildingType
                      )
                    }}
                  </div>
                </div>
                <div class="property-detail__row">
                  <div class="property-detail__cell property-detail__heading">
                    {{ $t('roomDetail.structure') }}
                  </div>
                  <div class="property-detail__cell">
                    {{
                      $t(
                        'common.options.property.structure.' +
                          property.structure
                      )
                    }}
                  </div>
                </div>
                <div class="property-detail__row">
                  <div class="property-detail__cell property-detail__heading">
                    {{ $t('roomDetail.contractType') }}
                  </div>
                  <div class="property-detail__cell">
                    {{
                      $t(
                        `common.options.room.contractType.${selectedRoom.contractType}`
                      )
                    }}
                    {{
                      selectedRoom.contractLength != 1
                        ? $tc(
                            `common.years`,
                            selectedRoom.contractLength / 12,
                            {
                              count: selectedRoom.contractLength / 12,
                            }
                          )
                        : $t(`common.aMonth`)
                    }}
                  </div>
                </div>
                <div class="property-detail__row">
                  <div class="property-detail__cell property-detail__heading">
                    {{ $t('roomDetail.transactionType') }}
                  </div>
                  <div class="property-detail__cell">
                    {{
                      $t(
                        `common.options.room.transactionType.${selectedRoom.transactionType}`
                      )
                    }}
                  </div>
                </div>
              </div>
            </section>
            <section class="detail-section">
              <h3 class="section-title">
                {{ $t('roomDetail.features') }}
              </h3>
              <PropertyFacilitiesList :room-facilities="roomFacilities" />
            </section>
            <div class="page-aside only_sp">
              <DetailSideWidget
                :is-inhouse-agency="isInhouseAgency"
                :agency="get(property, 'agency', {})"
                :room="selectedRoom"
              />
            </div>
            <section v-if="otherRooms.length > 0" class="detail-section">
              <h3 class="section-title">
                {{ $t('roomDetail.otherAvailableRooms') }}
              </h3>
              <PropertyRoomTable
                :rooms="otherRooms"
                :prefecture="prefectureKeyword"
              />
            </section>
            <PageInquiry
              v-if="Visibility.Full !== selectedRoom.visibility"
              :property-detail="property"
              :room-code-detail="roomCode"
              :hidden-header="true"
              :on-detail-page="true"
              :is-meta-hidden="true"
            />
            <agency-detail-section :agency="get(property, 'agency', {})" />
          </div>
          <div class="page-aside only_pc">
            <DetailSideWidget
              :is-inhouse-agency="isInhouseAgency"
              :agency="get(property, 'agency', {})"
              :room="selectedRoom"
            />
          </div>
          <div class="footprint">
            {{ $t('roomDetail.updatedDate') }}: {{ updatedDate }}
            <small style="display: none">({{ realUpdatedDate }})</small>
            <br />
            {{ $t('roomDetail.nextUpdateDate') }}:
            {{ $t('roomDetail.nextUpdateContent') }}
          </div>
        </div>
      </div>
    </main>
    <section class="related-property" client-only>
      <div class="container">
        <RentGrid
          :title-heading="$t('roomDetail.moreProperties')"
          :properties="get(moreProperties, 'items', [])"
          :loading="$apollo.queries.moreProperties.loading"
          :show-all-button="false"
        />
      </div>
    </section>
    <TheFooter class="has-stickybar" />
    <TheStickyBar
      v-if="Visibility.Full !== selectedRoom.visibility"
      :room-code="roomCode"
      :room-image="propertyImages[0]"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import dayjs from 'dayjs'
import get from 'lodash/get'
import setWith from 'lodash/setWith'
import capitalize from 'lodash/capitalize'
import getProperty from '~/apollo/queries/getProperty.graphql'
import incrementPageView from '~/apollo/mutations/incrementPageView.graphql'
import {
  EnumPropertyBuildingType,
  EnumPropertyRoomsVisibility,
  EnumPropertyStructure,
  FilterFindManyPropertyInput,
  Property,
  PropertyRooms,
  SortFindManyPropertyInput,
} from '~/types/types'
import { formatPrice } from '~/common/formatPrice'
import {
  getStationAccessMultiple,
  getAddressDetail,
  getRoomName,
  getRoomLayout,
  isPropertyVisible,
  getImage,
  toLocaleField,
} from '~/common/utils'
import getProperties from '~/apollo/queries/getProperties.graphql'
import { BreadcrumbsInterface } from '~/types'
import { enabledPrefectures } from '~/constants/prefectures'
import PageInquiry from '~/pages/inquiry/index.vue'

const property: Property = {
  _id: '',
  builtDate: '',
  structure: EnumPropertyStructure.Wooden,
  buildingName: '',
  images: {
    collections: [],
  },
  access: {
    stations: [],
  },
  buildingType: EnumPropertyBuildingType.House,
  maxFloor: 0,
  rooms: [],
  agencyId: '',
  addressDetail: {
    prefecture: '',
    city: '',
    chome: '',
    coord: [],
  },
  languageComment: {},
}

type locationType = {
  lat: number | null
  lng: number | null
}

type ReturnAsyncData = Promise<{
  title: string
  property: PropertyRooms
  roomCode: string
  prefecture: string
  prefectureName: string
  prefectureKeyword: string
  location?: locationType
  sort: string
}>

const SortKeys = [
  SortFindManyPropertyInput.IdAsc,
  SortFindManyPropertyInput.IdDesc,
  SortFindManyPropertyInput.AgencyidAsc,
  SortFindManyPropertyInput.AgencyidDesc,
  SortFindManyPropertyInput.HighestmonthlypriceAsc,
  SortFindManyPropertyInput.HighestmonthlypriceDesc,
  SortFindManyPropertyInput.LowestmonthlypriceAsc,
  SortFindManyPropertyInput.LowestmonthlypriceDesc,
  SortFindManyPropertyInput.PageviewsAsc,
  SortFindManyPropertyInput.PageviewsDesc,
  SortFindManyPropertyInput.CreatedatAsc,
  SortFindManyPropertyInput.CreatedatDesc,
  SortFindManyPropertyInput.UpdatedatAsc,
  SortFindManyPropertyInput.HighestViewWeekly,
]

export default Vue.extend({
  components: {
    PageInquiry,
  },
  async asyncData(context: any): ReturnAsyncData {
    const { error, app, params } = context
    // Why this instead of smart query? To handle 404 when item not found
    // Refer: https://github.com/nuxt-community/apollo-module/issues/381
    const prefectureKeyword = (params?.prefecture as string) || ''
    const message = context.i18n.t('common.pageNotFound')
    const allowedPath = enabledPrefectures
    if (!allowedPath.includes(prefectureKeyword)) {
      return error({ statusCode: 404, message })
    }

    const prefecture = await app.$accessor.fetchPrefecture(prefectureKeyword)

    const result = await app.apolloProvider.defaultClient.query({
      query: getProperty,
      variables: { roomCode: params.room_code },
    })
    const property = result.data.property

    const isVisible = isPropertyVisible(result, params.room_code)
    if (!isVisible) {
      return error({ statusCode: 404, message })
    }

    if (prefectureKeyword !== property?.prefecturePath) {
      return error({ statusCode: 404, message })
    }
    const field = toLocaleField(context.i18n.localeProperties.iso)
    const prefectureName = capitalize(prefecture[field] || prefecture.en)

    const location = {
      lat: get(property, 'addressDetail.coord[1]', null),
      lng: get(property, 'addressDetail.coord[0]', null),
    }

    return {
      title: typeof app.head === 'function' ? app.head().title : app.head.title,
      prefecture: params.prefecture,
      prefectureName,
      prefectureKeyword,
      property,
      location,
      sort: SortKeys[Math.floor(Math.random() * SortKeys.length)],
      roomCode: params.room_code,
    }
  },
  apollo: {
    moreProperties: {
      query: getProperties,
      variables(): {
        sort: SortFindManyPropertyInput
        perPage: number
        filter: FilterFindManyPropertyInput
      } {
        return {
          sort: this.sort,
          perPage: 4,
          filter: setWith({}, '_operators.rooms.visibility.in', [
            EnumPropertyRoomsVisibility.Public,
          ]),
        }
      },
      update(data) {
        return data.properties
      },
    },
  },
  data() {
    return {
      // TODO: Implement API get more properties instead using random sort
      sort: SortFindManyPropertyInput.IdDesc,
      Visibility: EnumPropertyRoomsVisibility,
      tags: this.$t('common.tags'),
      moreProperties: {} as any,
      property,
      roomCode: null,
      title: '' as string,
      prefecture: '' as string,
      prefectureName: '' as string,
      prefectureKeyword: '' as string,
      location: {
        lat: null,
        lng: null,
      } as locationType,
    }
  },
  head(): { title: string; meta: Array<any> } {
    const params = {
      prefectureName: this.prefectureName,
      cityName: this.propertyCityName,
      layout: `${this.selectedRoom.numberOfRoom}${this.selectedRoom.layout}`,
      propertyName: this.propertyName,
    }
    const title = this.$t('roomDetail.meta.title', params)
    const description = this.$t('roomDetail.meta.description', params)
    const image = getImage(
      this.propertyImages[0] as string,
      this.$config.cdnUrl
    )

    return {
      title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: description,
        },
        // Facebook
        {
          hid: 'og:title',
          property: 'og:title',
          content: title,
        },
        {
          hid: 'og:type',
          property: 'og:type',
          content: 'article',
        },
        {
          hid: 'og:url',
          property: 'og:url',
          content: this.pageURL,
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: image,
        },
        {
          hid: 'og:site_name',
          property: 'og:site_name',
          content: 'Living Japan',
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: description,
        },
        // Twitter
        {
          hid: 'twitter:card',
          property: 'twitter:card',
          content: 'summary_large_image',
        },
        {
          hid: 'twitter:site',
          property: 'twitter:site',
          content: '@livingjapan_',
        },
        {
          hid: 'twitter:url',
          property: 'twitter:url',
          content: this.pageURL,
        },
        {
          hid: 'twitter:title',
          property: 'twitter:title',
          content: title,
        },
        {
          hid: 'twitter:description',
          property: 'twitter:description',
          content: description,
        },
        {
          hid: 'twitter:image',
          property: 'twitter:image',
          content: image,
        },
      ],
    }
  },
  computed: {
    commentLang(): string {
      if (!this.property.languageComment) {
        return ''
      }

      switch (this.$i18n.locale) {
        case 'en':
          return this.property.languageComment.en
        case 'jp':
          return this.property.languageComment.jp !== ''
            ? this.property.languageComment.jp
            : this.property.languageComment.en
        case 'kr':
          return this.property.languageComment.kr !== ''
            ? this.property.languageComment.kr
            : this.property.languageComment.en
        case 'zh':
          return this.property.languageComment.zhCn !== ''
            ? this.property.languageComment.zhCn
            : this.property.languageComment.en
        case 'zh-tw':
          return this.property.languageComment.zhTw !== ''
            ? this.property.languageComment.zhTw
            : this.property.languageComment.en
        default:
          return ''
      }
    },
    propertyPrice(): string {
      const rentFee = get(this.selectedRoom, 'fees.monthly.rentFee', 0)
      const maintenanceFee = get(
        this.selectedRoom,
        'fees.monthly.maintenanceFee',
        0
      )
      return `${this.formatPrice(rentFee)} + ${this.formatPrice(
        maintenanceFee
      )}`
    },
    propertyName(): string {
      const translate = (key: string, values: any) => this.$t(key, values)
      return getRoomName(
        this.property,
        this.selectedRoom?.code || '',
        translate
      )
    },
    propertyCityName(): string {
      return get(this.property.addressDetail, 'city', '')
    },
    propertyAddress(): string {
      if (!this.property?.addressDetail) return ''
      const translate = (key: string, values: any) => this.$t(key, values)
      return getAddressDetail(this.property.addressDetail, translate).toString()
    },
    propertyImages(): string[] {
      return [
        get(this.selectedRoom, 'images.featured', ''),
        get(this.selectedRoom, 'images.floorPlan', ''),
        ...get(this.selectedRoom, 'images.collections', []),
        ...get(this.property, 'images.collections', []),
      ]
    },
    buildDate(): string {
      const date = get(this.property, 'builtDate', new Date())
      return dayjs(date).format('YYYY')
    },
    realUpdatedDate(): string {
      return dayjs(this.property.updatedAt).format('YYYY/MM/DD')
    },
    updatedDate(): string {
      const date = dayjs(this.property.updatedAt)
      const today = dayjs()
      const format = 'YYYY/MM/DD'
      if (today.isAfter(date) && this.isInhouseAgency) {
        return today.format(format)
      }
      return date.format(format)
    },
    isInhouseAgency(): boolean {
      // FIXME: Get the inhouse agency Ids from database
      const inhouseAgencyIds = [
        '6094a628951c484ec914ee7c', // Dev
        '60ec0da3baab7275a58281f9', // Prod
      ]
      return inhouseAgencyIds.includes(this.property.agency?._id)
    },
    roomFacilities(): Array<string> {
      const roomFacilities = get(this.selectedRoom, 'facilities', null)
      if (!roomFacilities) {
        return []
      }
      const facilitiesArr: Array<string> = []
      delete roomFacilities.__typename
      Object.keys(roomFacilities).forEach((item: string) => {
        facilitiesArr.push(...get(roomFacilities, item, []))
      })
      return facilitiesArr
    },
    selectedRoom(): PropertyRooms {
      const rooms = this.property?.rooms || ([] as PropertyRooms[])
      return rooms.find((room) => room?.code === this.roomCode) as PropertyRooms
    },
    availability(): string {
      const availability = this.selectedRoom?.availability
      if (availability?.now) {
        return this.$t('roomDetail.availability.now')
      }
      if (availability?.enquire) {
        return this.$t('roomDetail.availability.ask')
      }
      return ''
    },
    otherRooms(): PropertyRooms[] {
      const rooms = this.property?.rooms || ([] as PropertyRooms[])
      return rooms.filter(
        (room) =>
          room?.code !== this.roomCode &&
          room?.visibility !== EnumPropertyRoomsVisibility.Private
      ) as PropertyRooms[]
    },
    stationsAccessByWalk(): Array<string | null> {
      const stations = get(this.property, 'access.stations', [])
      const translate = (key: string, values: any) => this.$t(key, values)
      return getStationAccessMultiple(stations, translate)
    },
    pageURL() {
      return this.$route
        ? this.$config.appUrl + this.$route.path
        : this.$config.appUrl
    },
    breadcrumbs(): Array<BreadcrumbsInterface> {
      const breadcrumbs: Array<BreadcrumbsInterface> = [
        {
          path: '/rent',
          title: capitalize(this.$t('common.rent')),
        },
      ]
      if (this.prefectureName) {
        breadcrumbs.push({
          path: `/rent/${this.prefecture}`,
          title: this.prefectureName,
        })
      }
      if (this.roomCode && this.propertyName) {
        breadcrumbs.push({
          path: null,
          title: this.propertyName,
        })
      }
      return breadcrumbs
    },
    addressLine(): string | null {
      return get(this.property, 'addressDetail.line', null)
    },
  },
  mounted() {
    // Increment page view
    this.$apollo.mutate({
      mutation: incrementPageView,
      variables: {
        propertyId: this.property._id,
        roomCode: this.roomCode,
      },
    })
    this.$gtag?.event('purchase', {
      value: this.selectedRoom?.fees?.monthly?.rentFee || 0,
      items: [
        {
          id: this.selectedRoom?.code,
          google_business_vertical: 'hotel_rental',
        },
      ],
    })
  },
  methods: {
    getRoomLayout,
    formatPrice,
    get,
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.language-comment {
  margin-top: 10px;
  color: #555;
}

.site-main {
  @include bp('sp') {
    padding-top: 50px;
  }
}

.page-wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}

.main-content {
  @include bp('pc') {
    width: 60%;
  }
}

.page-aside {
  margin-bottom: calc-rem(20px);

  @include bp('pc') {
    padding-left: 80px;
    width: 40%;
  }

  @include bp(769px, 1100px) {
    width: 40%;
  }
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  margin-bottom: 10px;
  font-size: 18px;
  line-height: 1.2em;
}

.property-detail {
  display: flex;
  flex-wrap: wrap;
  border: 1px solid $color_gray_light1;
}

.property-detail__row {
  width: 100%;
  display: flex;
  border-bottom: 1px solid $color_gray_light1;

  &:last-child {
    border-bottom: none;
  }

  @include bp('pc') {
    width: 50%;

    &:nth-child(2n + 1) {
      border-right: 1px solid $color_gray_light1;
    }

    &:nth-last-of-type(2) {
      border-bottom: none;
    }
  }
}

.property-detail__cell {
  width: 50%;
  box-sizing: border-box;
  font-size: 14px;
  padding: 7px 14px;

  &:first-child {
    border-right: 1px solid $color_gray_light1;
  }
}

.property-detail__heading {
  background: $color_gray_light2;
  font-weight: 700;
  font-size: 14px;
}

.property-summary {
  margin-bottom: 24px;
  border-bottom: 1px solid $color_gray_light1;
  padding-bottom: calc-rem(30px);
}

.property-tags {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  margin-bottom: -5px;
  text-transform: uppercase;
}

.property-name {
  color: $color_base;
  font-weight: 700;
  font-size: 28px;
  margin-bottom: 8px;
  line-height: 1.2em;
  font-family: $font_title;
}

.property-price {
  font-weight: 700;
  font-size: 18px;
  line-height: 1.2em;
  margin-bottom: 10px;

  small {
    @include font-size(14, 19);
  }
}

.rej-property-id {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 10px;
}

.property-info__row {
  color: $color_gray_light4;
  margin-bottom: 0;

  @include font-size(14, 19);
}

.related-property {
  padding-bottom: 20px;
  padding-top: 10px;
  background: $color_gray_light2;
  border-bottom: 1px solid $color_gray_light1;

  @include bp('sp') {
    padding-bottom: 0;
  }
}

.maps-place {
  height: 200px;
  margin-top: 20px;
}

.footprint {
  font-size: 12px;
  margin-top: 15px;

  small {
    line-height: 0;
  }
}
</style>

<style lang="scss">
@import '~/assets/scss/tools/bootstraping';

.property-feature {
  .base-checkbox__label {
    padding: 2px 10px !important;
    font-size: 14px !important;
    color: #aaa !important;
  }

  input:checked + .base-checkbox__label {
    color: #fff !important;
  }
}

.maps-place {
  .GMap__Wrapper {
    height: 200px;
  }
}
</style>
