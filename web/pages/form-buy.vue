<template>
  <client-only>
    <div class="wrapper">
      <TheHeader class="sticky" :with-banner="false" />
      <main class="site-main">
        <div class="page-header">
          <h1>{{ $t(`formBuy.header`) }}</h1>
        </div>
        <div class="container">
          <form @submit.prevent="handleSubmit">
            <div v-if="errorMessage" class="error-message">
              <the-alert
                class="alert--danger"
                :content="errorMessage"
                :dismiss-able="true"
              />
            </div>
            <h2>{{ $t(`formBuy.title`) }}</h2>
            <p class="note">
              {{ $t(`formBuy.description`) }}
            </p>
            <identity-input
              v-model="form.identity"
              :errors="errors"
              @focus="hideError($event)"
              @blur="validate($event)"
              @dataCountry="handleDataCountry"
            />
            <template v-for="key in optionOrders">
              <field-row
                :key="`select_${key}`"
                :label="$t(`formBuy.${key}.label`)"
                required
                :error="errors[key]"
              >
                <select
                  v-model="form[key]"
                  required
                  @focus="hideError(key)"
                  @blur="validate(key)"
                >
                  <option value="" disabled selected>
                    {{ $t('formRent.commonPlaceholders.select') }}
                  </option>
                  <option
                    v-for="item in options[key]"
                    :key="item.key"
                    :value="item.key"
                  >
                    {{ item.label }}
                  </option>
                </select>
              </field-row>
              <field-row
                v-if="key === 'propertyType'"
                :key="`input_${key}`"
                required
                :error="errors.desiredAreas"
                :label="$t('formBuy.desiredAreas.label')"
              >
                <input
                  v-model="form.desiredAreas"
                  :placeholder="$t('formBuy.desiredAreas.placeholder')"
                  @focus="hideError('desiredAreas')"
                  @blur="validate('desiredAreas')"
                />
              </field-row>
            </template>
            <field-row
              :label="$t('formBuy.timeHorizon.label')"
              :error="errors.timeHorizon"
              required
            >
              <input
                v-model="form.timeHorizon"
                :placeholder="$t('formBuy.timeHorizon.placeholder')"
                @focus="hideError('timeHorizon')"
                @blur="validate('timeHorizon')"
              />
            </field-row>
            <field-row :label="$t('formRent.note.label')">
              <textarea
                id="memo"
                v-model="form.memo"
                name="memo"
                :placeholder="$t('formRent.note.placeholderRenew')"
              />
            </field-row>
            <agreement-check v-model="form.hasAgreed" />
            <BaseButton
              :disabled="hasError || isSubmitting"
              type="submit"
              :class="{ 'button--blue': !isSubmitting }"
            >
              {{ $t('formRent.submit') }}
            </BaseButton>
          </form>
        </div>
      </main>
      <TheFooter />
    </div>
  </client-only>
</template>

<script lang="ts">
import Vue from 'vue'
import flatten from 'flat'
import get from 'lodash/get'
import mapValues from 'lodash/mapValues'
import * as Yup from 'yup'
import sendForm from '~/apollo/mutations/sendForm.graphql'
import { EnumFormFormType } from '~/types/types'
import { getCookie } from '~/common/utils'

const initialValues = {
  identity: {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    preferredLanguage: [],
  },
  purpose: '',
  propertyType: '',
  desiredAreas: '',
  budget: '',
  paymentMethod: '',
  timeHorizon: '',
  memo: '',
  hasAgreed: false,
}

type ErrorDict = { [key: string]: string | null }
const optionOrders = ['purpose', 'propertyType', 'budget', 'paymentMethod']

export default Vue.extend({
  beforeRouteLeave(_to, _from, next) {
    if (this.isSubmitting) return next()

    this.$showConfirmDialog({
      title: this.$t('inquiry.titleDialog').toString(),
      message: this.$t('inquiry.leaveDialogMessage').toString(),
      hasImgNotice: true,
      businessHour: this.$t('inquiry.businessHourDialog').toString(),
      okButton: this.$t('inquiry.leaveConfirmButton').toString(),
      cancelButton: this.$t('inquiry.leaveCancelButton').toString(),
      okCallback: next,
      cancelCallback: () => next(false),
    })
  },
  data() {
    return {
      errorMessage: '',
      isSubmitting: false,
      errors: mapValues(flatten(initialValues), () => '') as ErrorDict,
      hasError: true,
      form: initialValues,
      optionOrders,
      countryData: {
        areaCodes: null,
        dialCode: '81',
        iso2: 'jp',
        name: 'Japan (日本)',
        priority: 0,
      } as Object,
    }
  },
  computed: {
    schema() {
      const required = {
        select: Yup.string().required(this.$t('formRent.required.select')),
        input: Yup.string().required(this.$t('formRent.required.input')),
      }

      return Yup.object().shape({
        identity: Yup.object().shape({
          firstName: required.input,
          lastName: Yup.string(),
          email: required.input.email(this.$t('formRent.required.email')),
          phone: Yup.string(),
          preferredLanguage: Yup.array()
            .of(Yup.string())
            .required(this.$t('formRent.required.select')),
        }),
        purpose: required.select,
        propertyType: required.select,
        desiredAreas: required.input,
        budget: required.select,
        paymentMethod: required.select,
        timeHorizon: required.input,
        hasAgreed: Yup.boolean().oneOf(
          [true],
          'Must Accept Terms and Condition'
        ),
      })
    },
    options(): { [key: string]: any[] } {
      return {
        purpose: this.toOptions('formBuy.purpose.options'),
        propertyType: this.toOptions('formBuy.propertyType.options'),
        budget: [
          ...[50, 100, 300, 500].map((num) => ({
            key: `${num}M`,
            label: this.$t(`formBuy.budget.options.a${num}m`),
          })),
          {
            key: 'over_1B',
            label: this.$t(`formBuy.budget.options.over_1b`),
          },
        ],
        paymentMethod: this.toOptions('formBuy.paymentMethod.options'),
      }
    },
  },
  watch: {
    form: {
      async handler() {
        try {
          await this.schema.validate(this.form)
          this.hasError = false
        } catch (err) {
          this.hasError = true
        }
      },
      deep: true,
    },
  },
  methods: {
    handleDataCountry(countryData: string) {
      this.countryData = countryData
    },
    toOptions(translationKey: string): any[] {
      return Object.keys(this.$t(translationKey)).map((key) => ({
        key,
        label: this.$t(`${translationKey}.${key}`),
      }))
    },
    hideError(field: string): void {
      this.errors[field] = null
    },
    async validate(field: string): Promise<void> {
      try {
        await this.schema.validateAt(field, this.form)
        this.errors[field] = null
      } catch (err) {
        this.errors[field] = err.message
      }
    },
    async handleSubmit() {
      this.isSubmitting = true
      try {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { identity, memo, hasAgreed: __, ...formBuy } = this.form
        if (identity.phone && this.countryData) {
          const { dialCode, name }: any = this.countryData
          identity.phone = `+${dialCode}${identity.phone} ${name}`
        }
        const result = await this.$apollo.mutate({
          mutation: sendForm,
          variables: {
            record: {
              ...identity,
              memo,
              formBuy,
              formType: EnumFormFormType.Buy,
              locale: this.$i18n.localeProperties.iso?.replace('-', '_'),
              utm: {
                utm_source: getCookie('utm_source'),
              },
              affiliate: {
                code: getCookie('affiliate'),
              },
            },
          },
        })
        if (result.data?.sendForm?.record?._id) {
          await this.$router.push(this.localePath('/inquiry/thanks'))
        } else {
          this.errorMessage = this.$t('contact.messages.failed').toString()
        }
        this.$recaptcha.reset()
      } catch (error) {
        this.errorMessage = get(
          error,
          'networkError.result.errors[0].message',
          error.message
        )
      }
      this.isSubmitting = false
    },
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.site-main {
  padding-top: 59.6px;
  padding-bottom: 100px;
  @include bp(sp) {
    padding-top: 60px;
  }
}

.page-header {
  height: 200px;
  background: url('~/assets/images/contact/header.jpg');
  background-size: cover;
  box-shadow: inset 0 0 0 1000px rgba(138, 138, 138, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;

  h1 {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
    margin: 0;
  }
}

h2 {
  font-size: 24px;
  margin-bottom: 15px;
}

.note {
  font-size: 14px;
  margin-bottom: 30px;
  line-height: 1.2em;
}

form {
  max-width: 600px;
  margin: auto;
}

::v-deep .input {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px !important;

  .label {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
    &.required {
      &::after {
        content: '*';
        color: red;
      }
    }
  }

  input {
    width: 100%;
    border: 1px solid #bbb;
    font-size: 14px;
    padding: 10px 14px;
    border-radius: 2px;

    &:focus {
      border-color: #333;
    }

    &::placeholder {
      font-size: 14px;
      color: #aaa;
    }
  }
}

/** Move out later */
select {
  border: 1px solid #bbb;
  padding: 10px 14px;
  border-radius: 2px;
  font-size: 14px;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: transparent;
  background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
  background-repeat: no-repeat;
  background-position-x: calc(100% - 10px);
  background-position-y: 50%;

  &:focus {
    border-color: #333;
  }

  &:required:invalid {
    color: #aaa;
  }

  option {
    color: #333;
    font-size: 14px;
    &[value=''][disabled] {
      display: none;
    }
  }
}

textarea {
  padding: 10px 14px;
  font-size: 14px;
  border: 1px solid #bbb;
  border-radius: 2px;
  height: 120px;
}

.agreement {
  margin-bottom: 15px;
}

.base-button {
  width: 100%;
  transition: 0.3s;

  &:disabled {
    border-radius: 5px;
    background-color: #aaa !important;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.2);
  }
}
</style>
