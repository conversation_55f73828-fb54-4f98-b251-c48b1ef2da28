import get from 'lodash/get'
import {
  getAccessorType,
  getterTree,
  actionTree,
  mutationTree,
} from 'typed-vuex'
import { filters } from '@/data.json'
import getCities from '~/apollo/queries/getCities.graphql'
import getServiceLines from '~/apollo/queries/getServiceLines.graphql'
import getPrefectureByName from '~/apollo/queries/getPrefectureByName.graphql'
import { City, Maybe, Prefecture, ServiceLine, Station } from '~/types/types'
import { CheckableCity } from '~/types'

export const state = () => ({
  filters,
  totalPageCount: 5,
  isContactPopupVisible: false,
  cities: {} as { [prefecture: string]: CheckableCity[] },
  areaMap: {} as { [key: string]: City },
  prefectureMap: {} as { [prefecture: string]: Prefecture },

  // FIXME: move these into submodules
  lines: {} as { [key: string]: ServiceLine },
  stationNames: {} as { [key: string]: string },
})

export type RootState = ReturnType<typeof state>

export const getters = getterTree(state, {
  selectedArea(state) {
    return (prefecture: string) => {
      const areasSelected = [] as any[]
      state.cities[prefecture].forEach((item) => {
        if (item.checked) {
          areasSelected.push({ id: item._id, name: item.en })
        }
      })
      return areasSelected
    }
  },

  prefectureName(state) {
    return (keyword: string, locale: string) => {
      const existing = state.prefectureMap[keyword]
      return get(existing, locale, keyword) || get(existing, 'en', keyword)
    }
  },

  selectedLayout({ filters }) {
    return filters.layoutSearch
      .filter((layout) => layout.checked)
      .map((layout) => layout.label)
      .join(', ')
  },

  haveLineStation(state) {
    return Object.keys(state.lines).length > 0
  },
})

export const mutations = mutationTree(state, {
  toggleLayout(state, payload) {
    const layout = state.filters.layoutSearch.find(
      (item) => item.id === payload.id
    )

    if (layout) {
      layout.checked = !layout.checked
    }
  },

  setCities(state, payload: { prefecture: string; cities: CheckableCity[] }) {
    state.cities = {
      ...state.cities,
      [payload.prefecture]: payload.cities,
    }
    state.areaMap = {
      ...state.areaMap,
      ...payload.cities.reduce(
        (acc, city) => ({
          ...acc,
          [city._id]: city,
        }),
        {}
      ),
    }
  },
  setStationNames(state, stationNames) {
    state.stationNames = stationNames
  },

  setPrefecture(state, payload: { prefecture: Prefecture; keyword: string }) {
    state.prefectureMap[payload.keyword] = payload.prefecture
  },

  setLines(state, lines) {
    state.lines = lines
  },
  setContactPopupVisible(state, value) {
    state.isContactPopupVisible = value
  },
})

export const actions = actionTree(
  { state, getters, mutations },
  {
    async fetchCities({ commit, state }, prefectureKeyword: string) {
      const client = this.app.apolloProvider.defaultClient
      const prefecture = state.prefectureMap[prefectureKeyword]
      const response = await client.query({
        query: getCities,
        variables: { prefId: prefecture?._id },
      })

      const cities = get(response, 'data.cities', []) as City[]
      commit('setCities', { prefecture: prefectureKeyword, cities })
    },

    async fetchPrefecture({ commit, state }, prefectureKeyword: string) {
      const existing = state.prefectureMap[prefectureKeyword]
      if (existing) return existing
      const client = this.app.apolloProvider.defaultClient
      const result =
        prefectureKeyword && prefectureKeyword !== '/'
          ? await client.query({
              query: getPrefectureByName,
              variables: { keyword: prefectureKeyword },
            })
          : null
      const prefecture = get(
        result,
        `data.prefectureByName`,
        null
      ) as Maybe<Prefecture>
      if (!prefecture) return null

      commit('setPrefecture', { prefecture, keyword: prefectureKeyword })
      return prefecture
    },

    async fetchStationLines({ commit, state }) {
      const client = this.app.apolloProvider.defaultClient
      const response = await client.query({
        query: getServiceLines,
      })

      type StationDict = typeof state.stationNames
      const stationNames = get(response, 'data.stations', []).reduce(
        (acc: StationDict, item: Station) => ({
          ...acc,
          [item._id]: item.localeName,
        }),
        {} as StationDict
      )
      commit('setStationNames', stationNames)

      type LineDict = typeof state.lines
      const lines = get(response, 'data.serviceLines', []).reduce(
        (acc: LineDict, item: ServiceLine) => ({
          ...acc,
          [item._id]: item,
        }),
        {} as LineDict
      )
      commit('setLines', lines)
    },

    showContactPopup({ commit }, value: boolean) {
      commit('setContactPopupVisible', value)
      document.body.style.overflow = value ? 'hidden' : 'unset'
    },
  }
)

export const accessorType = getAccessorType({
  state,
  getters,
  mutations,
  actions,
  // Uncomment below lines once we have submodule
  // more reference: https://typed-vuex.roe.dev/getting-started-nuxt
  // modules: {
  //   // The key (submodule) needs to match the Nuxt namespace (e.g. ~/store/submodule.ts)
  //   submodule,
  // },
})
