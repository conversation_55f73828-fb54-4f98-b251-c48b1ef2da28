{"name": "web", "version": "3.14.4", "private": true, "scripts": {"dev": "nuxt", "docker-local": "yarn && yarn dev", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "gql": "graphql-codegen --config config.yml", "lint:ts": "eslint --ext \".ts,.vue\" .", "lint:style": "stylelint \"**/*.{vue,css,scss}\"", "lint": "yarn lint:ts && yarn lint:style"}, "dependencies": {"cookie-universal-nuxt": "^2.2.2", "@aceforth/nuxt-optimized-images": "^1.4.0", "@nuxt/content": "^1.14.0", "@nuxtjs/apollo": "^4.0.1-rc.5", "@nuxtjs/gtm": "~2.4.0", "@nuxtjs/recaptcha": "^1.0.4", "@nuxtjs/sitemap": "^2.4.0", "apollo-fetch": "^0.7.0", "bootstrap-vue": "^2.21.2", "bson-objectid": "^2.0.1", "core-js": "^3.9.1", "dayjs": "^1.10.4", "express": "^4.17.3", "flat": "^5.0.2", "gmap-vue": "^1.5.0", "graphql-tag": "^2.11.0", "intl-tel-input": "^17.0.16", "lodash": "^4.17.21", "mitt": "^2.1.0", "nuxt": "^2.15.3", "nuxt-basic-auth-module": "^1.4.0", "nuxt-feature-toggle": "^1.2.1", "nuxt-i18n": "^6.27.2", "nuxt-lazy-load": "^1.2.6", "nuxt-ssr-cache": "git+https://github.com/putuyoga/nuxt-ssr-cache.git#0336608", "nuxt-typed-vuex": "^0.2.0", "nuxt-winston-log": "^1.2.0", "objects-to-csv": "^1.3.6", "portal-vue": "2.1.7", "redirect-ssl": "^3.0.0", "vue-agile": "^1.1.3", "vue-clickaway2": "^2.3.2", "vue-fb-customer-chat": "^0.2.0", "vue-gtag": "^1.16.1", "vue-lazy-hydration": "^2.0.0-beta.4", "yup": "^0.32.9"}, "devDependencies": {"@graphql-codegen/cli": "^1.21.2", "@graphql-codegen/typescript": "^1.21.1", "@graphql-codegen/typescript-graphql-files-modules": "^1.18.1", "@graphql-codegen/typescript-operations": "^1.17.15", "@nuxt/types": "^2.15.3", "@nuxt/typescript-build": "^2.1.0", "@nuxtjs/device": "^2.0.1", "@nuxtjs/eslint-config-typescript": "^6.0.0", "@nuxtjs/eslint-module": "^3.0.2", "@nuxtjs/google-fonts": "^1.3.0", "@nuxtjs/router-extras": "^1.1.1", "@nuxtjs/stylelint-module": "^4.0.0", "@types/flat": "^5.0.1", "@types/objects-to-csv": "^1.3.1", "babel-eslint": "^10.1.0", "eslint": "^7.22.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-nuxt": "^2.0.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.7.0", "fibers": "^5.0.0", "prettier": "^2.2.1", "sass": "^1.32.8", "sass-loader": "10", "stylelint": "^13.12.0", "stylelint-config-prettier": "^8.0.2", "stylelint-config-standard": "^21.0.0", "stylelint-config-standard-scss": "^1.1.0", "stylelint-scss": "^3.19.0"}, "volta": {"node": "14.16.0"}}