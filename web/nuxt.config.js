import redirectSSL from 'redirect-ssl'
import pkg from './package.json'
require('dotenv').config()

export default {
  rootDir: process.cwd(),
  buildDir: process.cwd() + '/.nuxt/',
  // Uncomment this line to debug network request on SPA mode
  // ssr: false,

  // If you provide a version, it will be stored inside cache.
  // Later when you deploy a new version, old cache will be
  // automatically purged.
  version: pkg.version,

  // Global page headers: https://go.nuxtjs.dev/config-head
  head: {
    title: 'Living Japan',
    htmlAttrs: {
      lang: 'en',
    },
    meta: [
      { charset: 'utf-8' },
      {
        name: 'viewport',
        content:
          'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0',
      },
      { hid: 'description', name: 'description', content: '' },
      {
        property: 'og:image',
        hid: 'og:image',
        content: process.env.APP_URL + '/social-main.png',
      },
    ],
    link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }],
    script: [
      {
        innerHTML: `
          !function (t, e, c, n) {
            var s = e.createElement(c);
            s.async = 1, s.src = 'https://scripts.claspo.io/scripts/' + n + '.js';
            var r = e.scripts[0];
            r.parentNode.insertBefore(s, r);
            var f = function () {
              f.c(arguments);
            };
            f.q = [];
            f.c = function () {
              f.q.push(arguments);
            };
            t['claspo'] = t['claspo'] || f;
          }(window, document, 'script', '6A47ECDDCF764890AA06185FB5B7E93D');
        `,
        type: 'text/javascript',
        body: true,
      },
      {
        innerHTML: `claspo('init');`,
        type: 'text/javascript',
        body: true,
      },
    ],
  },

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: ['@/assets/scss/main.scss'],

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: [
    '~/plugins/clickaway',
    '~/plugins/vue-agile',
    '~/plugins/confirm-dialog.ts',
    { src: '~/plugins/vue2-google-map', ssr: true },
    { src: '~/plugins/ga.client.ts', mode: 'client' },
  ],

  // Auto import components: https://go.nuxtjs.dev/config-components
  components: {
    dirs: [
      '~/components',
      '~/components/common',
      '~/components/property',
      '~/components/home',
    ],
  },

  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  buildModules: [
    // https://go.nuxtjs.dev/typescript
    [
      '@nuxt/typescript-build',
      {
        // FIXME: this only for IPC-deploy-server
        // to speed up the build
        typeCheck: false,
        typescript: {
          memoryLimit: 512,
        },
        ignoreNotFoundWarnings: true,
        loaders: { transpileOnly: true },
      },
    ],
    // https://go.nuxtjs.dev/stylelint
    '@nuxtjs/stylelint-module',
    // https://github.com/nuxt-community/device-module
    '@nuxtjs/device',
    '@nuxtjs/router-extras',
    '@nuxtjs/google-fonts',
    '@aceforth/nuxt-optimized-images',
  ],

  // Modules: https://go.nuxtjs.dev/config-modules
  modules: [
    'nuxt-winston-log',
    'bootstrap-vue/nuxt',
    '@nuxtjs/apollo',
    'nuxt-basic-auth-module',
    '@nuxt/content',
    '@nuxtjs/sitemap',
    'nuxt-i18n',
    '@nuxtjs/gtm',
    'nuxt-ssr-cache',
    'nuxt-typed-vuex',
    '@nuxtjs/recaptcha',
    'nuxt-feature-toggle',
    ['nuxt-lazy-load', { defaultImage: '/images/0.png' }],
    'portal-vue/nuxt',
    ['cookie-universal-nuxt', { alias: 'cookiz' }],
    // temporary remove sentry because it breaks deployment
    // '@nuxtjs/sentry'
  ],
  // temporary remove sentry because it breaks deployment
  // sentry: {
  //   dsn: 'https://<EMAIL>/4506137234178048', // Enter your project's DSN.
  //   // Additional module options go here.
  // },
  winstonLog: {
    logName: 'server.log',
  },

  // https://github.com/arash16/nuxt-ssr-cache#readme
  cache: {
    useHostPrefix: false,
    pages: ['/'],
    store: {
      type: 'memory',
      max: 100,
      ttl: 900, // 15min
    },
  },

  router: {
    trailingSlash: false,
    middleware: 'trailingSlashRedirect',
  },

  serverMiddleware: [
    redirectSSL.create({ enabled: false }),
    '~/server-middleware/redirect.ts',
    { path: '/feed', handler: '~/server-middleware/feed.ts' },
  ],

  i18n: require('./nuxt.config.i18n.js').default,
  gtm: {},
  googleFonts: {
    display: 'swap',
    families: {
      'Noto+Sans': [400, 700],
      'Noto+Sans+TC': [400, 700],
      'Noto+Sans+SC': [400, 700],
      'Noto+Sans+JP': [400, 700],
    },
  },

  recaptcha: {
    size: 'normal',
    version: 2,
  },

  sitemap: require('./nuxt.config.sitemap.js').default,

  bootstrapVue: {
    components: [
      'BPagination',
      'BCardText',
      'BCard',
      'BCardTitle',
      'BIconPatchCheckFill',
      'BFormRating',
      'BIconArrowLeftCircleFill',
      'BIconArrowRightCircleFill',
      'BTooltip',
    ],
    bootstrapCSS: false, // Or `css: false`
    bootstrapVueCSS: false, // Or `bvCSS: false`
  },

  apollo: {
    clientConfigs: {
      default: '~/nuxt.config.apollo.ts',
    },
  },

  // Config for @nuxt/content
  content: {
    liveEdit: false,
  },

  // Basic AUTH
  basic: {
    name: process.env.BASIC_AUTH_USER,
    pass: process.env.BASIC_AUTH_PASS,
    enabled: process.env.BASIC_AUTH_ENABLED === 'true',
  },

  // Build Configuration: https://go.nuxtjs.dev/config-build
  build: {
    transpile: ['vue-agile', '/^gmap-vue($|\\/)/'],

    // TODO: we will use this possibly for advance use cases
    // extend(config, { isClient }) {
    //   console.log(config)
    // },
  },

  publicRuntimeConfig: {
    appUrl: process.env.APP_URL,
    cdnUrl: process.env.CDN_URL,
    gqlUrl: process.env.GQL_URL,
    gqlSSRUrl: process.env.GQL_SSR_URL,
    gMapAPI: process.env.GMAP_APIKEY,
    isAnalyticEnabled: process.env.GA_ENABLED === 'true',
    clientVersion: pkg.version,
    recaptcha: {
      siteKey: process.env.RECAPTCHA_SITE_KEY,
    },
    gtm: {
      id: process.env.GTM_ID,
    },
    livechatEnabled: process.env.LIVECHAT_ENABLED === 'true',
    ipinfoToken: process.env.VUE_APP_IPINFO_TOKEN,
    tabikoShowChatWidget: process.env.NUXT_PUBLIC_TABIKO_CHAT_WIDGET_SHOW,
    tabikoWidgetUrl: process.env.NUXT_PUBLIC_TABIKO_CHAT_WIDGET_URL,
    tabikoWidgetKeySite: process.env.NUXT_PUBLIC_TABIKO_CHAT_WIDGET_KEY_SITE,
    tabikoWidgetCDN: process.env.NUXT_PUBLIC_TABIKO_CHAT_WIDGET_CDN,
  },
}
