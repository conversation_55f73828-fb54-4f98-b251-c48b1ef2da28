/* ==========================================================================
   #Spacing
   ========================================================================== */
@include repeat-unit-classes-map($repeatable-slugs, 0, 10, px, 5);

.m {
    &--auto {
        margin-left: auto;
        margin-right: auto;
    }

    &l {
        &--auto {
            margin-left: auto;
        }
    }

    &r {
        &--auto {
            margin-right: auto;
        }
    }

    &x {

        @include bp('pc') {
            &__pc {
                &--auto {
                    margin-left: auto;
                    margin-right: auto;
                }
            }
        }

        @include bp('sp') {
            &__sp {
                &--auto {
                    margin-left: auto;
                    margin-right: auto;
                }
            }
        }

        &--auto {
            margin-left: auto;
            margin-right: auto;
        }
    }
}

.p {
    &--auto {
        padding-left: auto;
        padding-right: auto;
    }

    &l {
        &--auto {
            padding-left: auto;
        }
    }

    &r {
        &--auto {
            padding-right: auto;
        }
    }

    &x {

        @include bp('pc') {
            &__pc {
                &--auto {
                    padding-left: auto;
                    padding-right: auto;
                }
            }
        }

        @include bp('sp') {
            &__sp {
                &--auto {
                    padding-left: auto;
                    padding-right: auto;
                }
            }
        }

        &--auto {
            padding-left: auto;
            padding-right: auto;
        }
    }
}
