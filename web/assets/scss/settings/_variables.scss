/* ==========================================================================
#Variables
========================================================================== */
// For fonts, see nuxt.config.js at `googleFonts` section.

// Define color
$color_base: #333;
$color_black: #000;
$color_white: #fff;
$color_gray_light1: #ddd;
$color_gray_light2: #f7f7f7;
$color_gray_light3: #ccc;
$color_gray_light4: #555;
$color_gray_light5: #aaa;
$color_gray_light6: #f3f7f7;
$color_gray_light7: #edf7f7;
$color_gray_light8: #888;
$color_blue: #2471cb;
$color_blue2: #1a5fb0;
$color_green: #18AF94;
$color_pink: #FC7A7A;;
$color_velocity_gray: #e8e8e8;
$color_white_smoke: #f5f5f5;
$color_orange: #f97f0d;
$color_japonica: #d66969;
$color_red1: #d7240c;
$color_blue_dark: #002349;

/* Define theme colors */
$theme-colors: () !default;
$colors: (
  'default': $color_base,
  'black': $color_black,
  'white': $color_white,
);
$theme-colors: map-merge($colors, $theme-colors);

// Define theme font-family
$font_base: 'Noto Sans', 'Noto Sans TC', 'Noto Sans SC', sans-serif;
$font_title: Arial, 'Noto Sans TC', 'Noto Sans SC', sans-serif;

// Define theme font-size
$fs_base: 16;
$fs_h1: 50;
$fs_h2: 30;
$fs_h3: 24;
$fs_h4: 20;
$fs_h5: 18;
$fs_h6: 14;

$fs_base_sp: 14;

$lh_base: 24;
$lh_base_sp: 20;
$fw_base: 400;

// Define space base
$space_base: 15;
$space_base_px: 15px;

// Size content
$width_content_pc: 1130;
$width_content_pc_px: 1230px;
$width_content_pc_design: 1420;
$width_content_pc_design_px: 1420px;
$width_content_sp_design: 750;
$width_content_sp_design_px: 750px;
