/* ==========================================================================
#SHARED
========================================================================== */

/**
* Shared declarations for certain elements.
*/

/**
* Always declare margins in the same direction:
* csswizardry.com/2012/06/single-direction-margin-declarations
*/

address,
h1,
h2,
h3,
h4,
h5,
h6,
blockquote,
p,
pre,
dl,
ol,
ul,
figure,
hr,
table,
fieldset {
  margin-bottom: calc-rem($space_base_px);
}

/**
* Consistent indentation for lists.
*/

dd,
ol,
ul {
  margin-left: calc-rem($space_base_px);
}

/**
* Setting default site
*/

html {
  font-size: 1rem;
  height: 100vh;
}
body {
  color: $color_base;
  font-family: $font_base;
  font-weight: $fw_base;
  word-break: break-word;
  @include font-size($fs_base, $lh_base);

  min-height: 100vh;
  min-height: -webkit-fill-available;
}

/**
* Container setting
*/

.container {
  width: 100%;
  margin: 0 auto;

  @include bp('pc') {
    max-width: calc-rem($width_content_pc_px);
    padding-left: calc-rem(15px);
    padding-right: calc-rem(15px);
  }

  @include bp('sp') {
    padding-left: 20px;
    padding-right: 20px;
  }
}

.container--s {
  max-width: calc-rem(800px);
}

/**
* Common <a> setting
*/
a {
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
}

/**
* Common images setting
*/

img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

/**
* Thumbnails image setting
*/

.thumb {
  position: relative;
  display: block;
  height: 0;
  overflow: hidden;
  margin: 0;

  img {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    -webkit-transition: all 0.75s ease;
    transition: all 0.75s ease;
  }

  &-1x1 {
    padding-bottom: 100%;
  }
}

/**
* Default button setting
*/
.btn {
  font-size: calc-rem(16px);
  line-height: calc-rem(18px);
  color: $color_white;
  border: none;
  border-radius: calc-rem(4px);
  width: 100%;
  height: calc-rem(48px);
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &-active {
    background: $color_blue;
  }

  &-white {
    background: $color_white;
    color: $color_gray_light4;
    border: 1px solid $color_gray_light3;

    &:hover {
      background: $color_blue;
      color: $color_white;
      border: none;
    }
  }

  &:hover {
    background: $color_blue;
    box-shadow: 0 0 calc-rem(6px) calc-rem(1px) rgba($color_blue, 0.8);
  }
}

.site-main {
  padding-top: 81px;
  padding-bottom: 60px;

  &.new-banner {
    padding-top: 59.6px;
  }

  @include bp('sp') {
    &.new-banner {
      padding-top: 60px;
    }

    padding-bottom: calc-rem(40px);
    padding-top: calc-rem(70px);
  }
}

/**
* Custom scroll bar
*/
.scrollbar {
  scrollbar-color: $color_green $color_velocity_gray;
  scrollbar-width: thin;

  &::-webkit-scrollbar-track {
    background-color: $color_velocity_gray;
  }

  &::-webkit-scrollbar {
    width: calc-rem(8px);
    height: calc-rem(8px);
  }

  &::-webkit-scrollbar-thumb {
    background-color: $color_green;
  }
}

.flex {
  display: flex;
}

.aic {
  align-items: center;
}

.fw {
  flex-wrap: wrap;
}

.jcc {
  justify-content: center;
}

.sp-only {
  @media (min-width: 767px) {
    display: none;
  }
}
