import get from 'lodash/get'
import minBy from 'lodash/minBy'
import find from 'lodash/find'
import VueI18n from 'vue-i18n/types/index'
import { ApolloClientMethods } from 'vue-apollo/types/vue-apollo'
import dayjs from 'dayjs'
import {
  City,
  EnumPropertyRoomsVisibility,
  Maybe,
  Property,
  PropertyAccessStations,
  PropertyAddress,
  PropertyRooms,
} from '~/types/types'
import countProperties from '~/apollo/queries/countProperties.graphql'
export const getNearestStation = (
  stations: Array<Maybe<PropertyAccessStations>>
) => {
  // Remove access which doesn't have stationId
  const filteredStations = stations.filter(
    (station) => station?.stationId && station?.stationId !== ''
  )
  return minBy(filteredStations, 'distance.walk')
}

export const getStationAccessMultiple = (
  stationsMultiple: Array<PropertyAccessStations>,
  translate: typeof VueI18n.prototype.t
): Array<string | null> => {
  if (!stationsMultiple) return []
  return stationsMultiple.map(function (station: PropertyAccessStations) {
    if (!station.lineId || !station.stationId) return null
    const showWalk = get(station, 'distance.walk', 0) > 0
    return translate(`common.accessBy${showWalk ? 'Walk' : 'Bus'}`, {
      line: station.lineName !== '[missing]' ? station.lineName : '',
      station: station.stationName,
      minute: station.distance![showWalk ? 'walk' : 'bus'],
    }).toString()
  })
}

export const getStationAccessCard = (
  stations: Array<PropertyAccessStations>,
  translate: typeof VueI18n.prototype.t
): string => {
  const nearest = getNearestStation(stations)
  if (!nearest) return ''
  const station = nearest.stationName
  const line = nearest.lineName
  const time = get(nearest, 'distance.walk', '')
  return translate('common.minWalkFrom', {
    time,
    line,
    station,
  }).toString()
}

export const getAddressDetail = (
  { chome, city, prefecture }: PropertyAddress,
  translate: typeof VueI18n.prototype.t
) => {
  return translate('common.addressDetail', { chome, city, prefecture })
}

export const getPropertyName = (
  property: Property,
  translate: typeof VueI18n.prototype.t
) => {
  const stationName = getNearestStation(property?.access?.stations || [])
    ?.stationName
  return `${translate('common.stationName', { stationName }) || ''}`
}

export const getRoomName = (
  property: Property,
  roomCode: string,
  translate: typeof VueI18n.prototype.t
) => {
  const stationName = getNearestStation(property?.access?.stations || [])
    ?.stationName
  const room = find(property.rooms, { code: roomCode })
  const formattedStation = translate('common.stationName', { stationName })
  return `${formattedStation || ''}${getRoomLayout(room)} ${room?.roomName}`
}

export const getImage = (imageUrl: string, cdnUrl: string) => {
  if (imageUrl.indexOf('http') === 0) return imageUrl
  return `${cdnUrl}/${imageUrl}`
}

export const getRoomLayout = (room?: PropertyRooms | null) =>
  `${room?.numberOfRoom}${room?.layout}`

/**
 * Handle cases when the room have a sibling where the visibility is public
 * FIXME: fix it on graphql side instead?
 */
export const isPropertyVisible = (result: any, roomCode: string) => {
  if (!result?.data?.property) return false
  const { rooms } = result.data.property
  const visibleStates = [
    EnumPropertyRoomsVisibility.Public,
    EnumPropertyRoomsVisibility.Full,
  ]
  return rooms.find(
    (item: PropertyRooms) =>
      roomCode === item.code && visibleStates.includes(item.visibility)
  )
}

export const getPropertyCount = async (
  apolloClient: ApolloClientMethods,
  filter: any
): Promise<number> => {
  const result = await apolloClient.query({
    query: countProperties,
    variables: { filter },
  })
  return get(result, 'data.properties.count', 0)
}

export const addOrRemoveItem = <T>(array: Array<T>, item: T): Array<T> => {
  array.includes(item) ? array.splice(array.indexOf(item), 1) : array.push(item)
  return array
}

export const toLocaleField = (localeCode?: string): keyof City => {
  if (!localeCode) return 'en'
  return localeCode
    .toLowerCase()
    .replace(/-us|-jp/, '')
    .replace('-', '_') as keyof City
}

export const getCookie = <String>(cookieName?: String) => {
  const cookie: any = {}
  document.cookie.split(';').forEach(function (el) {
    const [key, value] = el.split('=')
    cookie[key.trim()] = value
  })
  return cookie[cookieName]
}

export const setCookie = (
  cookieName?: string,
  cookieValue?: string,
  expiredDay?: number
) => {
  const expires =
    'expires=' + dayjs().add(expiredDay, 'day').toDate().toUTCString()
  document.cookie = `${cookieName}=${cookieValue};${expires};path=/`
}
