import express from 'express'
import ObjectToCSV from 'objects-to-csv'
import { createApolloFetch } from 'apollo-fetch'
import dayjs from 'dayjs'
import get from 'lodash/get'
import { Property, PropertyAccessStations } from '~/types/types'

const app = express()
const fetchGql = createApolloFetch({ uri: process.env.GQL_SSR_URL })
const MAX_PROPERTY_COUNT = 1400
const query = `
query{
  properties (perPage: ${MAX_PROPERTY_COUNT}) {
    items{
      buildingType
      prefecturePath
      addressDetail {
        prefecture
        city
        chome
      }
    	access {
        stations {
          stationId
          stationName
          distance {
            walk
          }
        }
      }
      rooms {
        code
        roomName
        monthlyFee
        roomLayout
        images {
          featured
        }
        tags
      }
    }
  }
}
`

app.all('/fb.csv', async (_req, res) => {
  const now = dayjs().format('YYYYMMDD')
  const fileName = `livingjapan_fbfeed_${now}.csv`
  const result = await fetchGql({ query })
  const properties = get(result, 'data.properties.items', [])
  const formattedItems = formatToRow(properties)
  const rows = formattedItems.map(toFBRow)
  const csv = new ObjectToCSV(rows)
  res.setHeader('Content-Type', 'text/csv')
  res.setHeader('Content-disposition', `attachment; filename=${fileName}`)
  return res.send(await csv.toString())
})

app.all('/google.csv', async (_req, res) => {
  const now = dayjs().format('YYYYMMDD')
  const fileName = `livingjapan_googlefeed_${now}.csv`
  const result = await fetchGql({ query })
  const properties = get(result, 'data.properties.items', [])
  const formattedItems = formatToRow(properties)
  const rows = formattedItems.map(toGoogleRow)
  const csv = new ObjectToCSV(rows)
  res.setHeader('Content-Type', 'text/csv')
  res.setHeader('Content-disposition', `attachment; filename=${fileName}`)
  return res.send(await csv.toString())
})

interface CSVFriendlyItem {
  id: string
  name: string
  url: string
  image: string
  destination: string
  description: string
  price: string
  category: string
  keywords: string
  fullAddress: string
  city: string
  prefecture: string
  streetAddress: string
  postalCode: string
  coord: string[]
}

const formatToRow = (items: Property[]): CSVFriendlyItem[] =>
  items.reduce((acc, property) => {
    const { prefecture, city, chome, postalCode, line, coord } =
      property.addressDetail || {}
    const nearestStation = getNearestStation(property?.access?.stations || [])
    return [
      ...acc,
      ...property.rooms.reduce((acc2, room) => {
        const { stationName, distance } = nearestStation || {}
        const noStation = stationName?.includes('missing')
        const initialName = noStation
          ? city
          : `${nearestStation?.stationName} Sta.`

        const image =
          room?.images.featured.indexOf('https://') === 0
            ? room?.images.featured
            : `${process.env.CDN_URL}/${room?.images.featured}`

        const row: CSVFriendlyItem = {
          id: room?.code || '<missing>',
          name: `${initialName} ${room?.roomLayout} ${room?.roomName}`,
          url: `${process.env.APP_URL}/rent/${property.prefecturePath}/${room?.code}`,
          image,
          destination: `${prefecture}, ${city}`,
          description: noStation
            ? ''
            : `${distance?.walk} minutes from ${nearestStation?.stationName} Station`,
          price: `${room?.monthlyFee} JPY`,
          category: (property.buildingType || '').replace(/_/g, ' '),
          keywords: room?.tags
            ? room.tags.map((tag) => tag?.replace(/_/g, ' ')).join(';')
            : '',
          fullAddress: `${prefecture}, ${city}, ${chome}`,
          prefecture: `${prefecture}`,
          city: `${city}`,
          streetAddress: line || '',
          postalCode: postalCode || '',
          coord:
            coord && coord.length > 1
              ? coord.map((c) => c?.toString() || '')
              : ['', ''],
        }

        return [...acc2, row]
      }, [] as CSVFriendlyItem[]),
    ]
  }, [] as CSVFriendlyItem[])

const toGoogleRow = (item: CSVFriendlyItem) => ({
  'Property ID': item.id,
  'Property Name': item.name,
  'Final URL': item.url,
  'Image URL': item.image,
  'Destination name': item.destination,
  Description: item.description,
  Price: item.price,
  'Sale Price': item.price,
  'Star rating': '',
  Category: item.category,
  'Contextual keywords': item.keywords,
  Address: item.fullAddress,
  'Tracking template': '',
  'Custom parameter': '',
  'Final mobile URL': '',
  'Android app link': '',
  'iOS app link': '',
  'iOS app store ID': '',
})

const toFBRow = (item: CSVFriendlyItem) => ({
  home_listing_id: item.id,
  name: item.name,
  availability: 'for_rent',
  num_beds: 1,
  num_baths: 1,
  num_units: 1,
  description: item.description,
  'address.addr1': item.streetAddress,
  'address.city': item.city,
  'address.region': item.prefecture,
  'address.country': 'Japan',
  'address.postal_code': item.postalCode,
  longitude: item.coord[1],
  latitude: item.coord[1],
  'neighborhood[0]': item.destination,
  'image[0].url': item.image,
  price: item.price,
  url: item.url,
  'applink.ios_url': '',
  'applink.android_url': '',
  'applink.android_package': '',
  'applink.android_app_name': '',
  'available_dates_price_config[0].start_date': '',
  'available_dates_price_config[0].end_date': '',
  'available_dates_price_config[0].rate': item.price,
  'available_dates_price_config[0].currency': 'JPY',
  'available_dates_price_config[0].interval': '',
  'available_dates_price_config[1].start_date': '',
  'available_dates_price_config[1].end_date': '',
  'available_dates_price_config[1].rate': item.price,
  'available_dates_price_config[1].currency': 'JPY',
  'available_dates_price_config[1].interval': '',
})

const getNearestStation = (stations: PropertyAccessStations[]) =>
  stations
    .filter((station) => !station?.stationName?.includes('missing'))
    .reduce(
      (nearest, station) => {
        const nearestDistance = nearest?.distance?.walk || 0
        const comparisonDistance = station?.distance?.walk || 0
        if (nearestDistance > comparisonDistance) return station
        return nearest
      },
      {
        stationName: 'missing',
        distance: { walk: 100 },
      }
    )

export default app
