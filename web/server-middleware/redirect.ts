import { ServerMiddleware } from '@nuxt/types'

/**
 * Redirect all url where it doesn't match the
 * defined APP_URL env
 */
const RedirectMiddleware: ServerMiddleware = function (_, _res, next) {
  return next()
  // if (process.env.NODE_ENV === 'development') return next()

  // const appUrl = process.env.APP_URL
  // const domain = appUrl?.replace(/(^\w+:|^)\/\//, '')

  // if (req.headers.host === domain) return next()

  // res.writeHead(301, { Location: `${appUrl}${req.url}` })
  // return res.end()
}
export default RedirectMiddleware
