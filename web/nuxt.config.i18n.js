export default {
  locales: [
    {
      code: 'en',
      iso: 'en-US',
      file: 'en/index.ts',
      name: 'English',
    },
    {
      code: 'zh',
      iso: 'zh-CN',
      file: 'zh-cn/index.ts',
      name: '简体中文',
    },
    {
      code: 'zh-tw',
      iso: 'zh-TW',
      file: 'zh-tw/index.ts',
      name: '繁體中文',
    },
    {
      code: 'kr',
      iso: 'ko-KR',
      file: 'kr/index.ts',
      name: '한국',
    },
  {
    code: 'es',
    iso: 'es-ES',
    file: 'es/index.ts',
    name: 'Spanish',
  },
{
  code: 'fr',
  iso: 'fr-FR',
  file: 'fr/index.ts',
  name: 'French',
},
  ],
  baseUrl: (context) => context.$config.appUrl,
  strategy: 'prefix_except_default',
  detectBrowserLanguage: false,
  defaultLocale: 'en',
  langDir: '~/locales/',
  vueI18n: {
    fallbackLocale: 'en',
  },
}
