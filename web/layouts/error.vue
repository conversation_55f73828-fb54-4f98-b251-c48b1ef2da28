<template>
  <div class="wrapper">
    <TheHeader class="sticky" />
    <main class="site-main">
      <div class="thanks">
        <div class="container">
          <div class="content">
            <h1 v-if="error.statusCode === 404">
              {{ $t('common.pageNotFound') }}
            </h1>
            <h1 v-else>An error occurred - {{ error.statusCode }}</h1>

            <button>
              <NuxtLink to="/">Top Page</NuxtLink>
            </button>
          </div>
        </div>
      </div>
    </main>
    <TheFooter />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { MetaInfo } from 'vue-meta/types/vue-meta.d'
interface Page {
  title: string
  body: any
}

const page: Page = {
  title: 'Not Found',
  body: '',
}

export default Vue.extend({
  layout: 'error',
  props: ['error'], // you can set a custom layout for the error page
  asyncData({ app }: any) {
    return {
      title: typeof app.head === 'function' ? app.head().title : app.head.title,
    }
  },
  data() {
    return {
      page,
      title: '',
    }
  },
  head(): MetaInfo {
    let robotsContent = 'noindex'
    try {
      if (this.$nuxt.$route.name?.includes('rent-prefecture-room_code')) {
        robotsContent = robotsContent + ',nofollow'
      }
    } catch (err) {}

    return {
      title: this.page.title + ' - ' + this.title,
      meta: [{ hid: 'robots', name: 'robots', content: robotsContent }],
    }
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.content {
  margin-top: calc-rem(50px);
  margin-bottom: calc-rem(80px);
  text-align: center;

  @include bp('sp') {
    margin-bottom: calc-rem(60px);
    margin-top: calc-rem(28px);
  }
}
.content h1 {
  color: $color_base;
  font-size: calc-rem(32px);
  font-weight: normal;
  line-height: calc-rem(43px);
  margin-bottom: calc-rem(20px);

  @include bp('sp') {
    font-size: calc-rem(22px);
  }
}

.content p {
  color: #333;
  font-size: calc-rem(16px);
  line-height: calc-rem(22px);
  margin-bottom: calc-rem(30px);

  @include bp('sp') {
    margin-bottom: calc-rem(20px);
  }
}

.content button {
  border-radius: calc-rem(4px);
  background-color: #333;
  border: none;
  padding: 7px 20px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 20%);
}
.nuxt-link-active {
  color: #fff;
  font-weight: bold;
  line-height: calc-rem(22px);
}
</style>
