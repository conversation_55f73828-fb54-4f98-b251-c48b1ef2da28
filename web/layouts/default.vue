<template>
  
 
  <div :class="{ [fontClass]: !false }">
 
    <Nuxt />
    <confirm-dialogue />
    <contact-popup v-if="$accessor.isContactPopupVisible" />
    <portal-target name="root" />
  </div>
 
</template>

<script lang="ts">
import { Route } from 'vue-router/types'
import { setCookie } from '../common/utils'

export default {
  computed: {
    fontClass() {
      const locale = this.$i18n.locale
      const map = {
        jp: 'font-jp',
        'zh-tw': 'font-zhtw',
        zh: 'font-zhcn',
      } as any
      return map[locale] || 'font-en'
    },
  },
  // eslint-disable-next-line
  head(): any {
    const i18nHead = this.$nuxtI18nHead({ addSeoAttributes: true });
    
    
    return {
      ...i18nHead,
      link: i18nHead.link.reduce((acc: any, item: any) => {
        const transformed = {
          ...item,
          href: item.href.replace(/\/$/, ''),
          ...(item.hreflang ? { hreflang: item.hreflang?.toLowerCase() } : {}),
        }
        const blacklist = ['en-us', 'zh', 'ko', 'jp-jp']
        if (blacklist.includes(transformed.hreflang)) return acc
        if (transformed.hreflang !== 'zh-tw') return [...acc, transformed]
        return [
          ...acc,
          transformed,
          {
            ...transformed,
            hreflang: 'zh-hk',
          },
        ]
      }, [
        { rel: 'alternate', hreflang: 'x-default', href: 'https://www.livingjapan.com' },
        { rel: 'alternate', hreflang: 'zh-Hant', href: 'https://www.livingjapan.com/zh' },
        { rel: 'alternate', hreflang: 'zh-Hans', href: 'https://www.livingjapan.com/zh-tw' },
        { rel: 'alternate', hreflang: 'ko', href: 'https://www.livingjapan.com/kr' },
        { rel: 'alternate', hreflang: 'es', href: 'https://www.livingjapan.com/es' },
        { rel: 'alternate', hreflang: 'fr', href: 'https://www.livingjapan.com/fr' }
      ]),
      ...(this.$config.livechatEnabled && {
        script: [
          {
            src: 'main.js',
          },
        ],
      }),
    }
  },
  watch: {
    $route(value: Route) {
      const isTopPage = value?.name?.indexOf('index') === 0
      const widgetEl = document.getElementById('chat-widget-container')
      if (!isTopPage && widgetEl) {
        widgetEl.style.display = 'none'
      } else if (widgetEl) {
        widgetEl.style.display = 'block'
      }
    },
  },

  mounted() {
    ;(this.$recaptcha as any).language = this.$i18n.locale
    const utmSource = this.$route.query.utm_source || ''
    const affiliate = this.$route.query.aid || ''

    if (utmSource) {
      setCookie('utm_source', utmSource, 1)
    }

    if (affiliate) {
      setCookie('affiliate', affiliate, 1)
    }
  },
}
</script>

<style>
html {
  font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  word-spacing: 1px;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
}

.button--green {
  display: inline-block;
  border-radius: 4px;
  border: 1px solid #3b8070;
  color: #3b8070;
  text-decoration: none;
  padding: 10px 30px;
}

.button--green:hover {
  color: #fff;
  background-color: #3b8070;
}

.button--grey {
  display: inline-block;
  border-radius: 4px;
  border: 1px solid #35495e;
  color: #35495e;
  text-decoration: none;
  padding: 10px 30px;
  margin-left: 15px;
}

.button--grey:hover {
  color: #fff;
  background-color: #35495e;
}
</style>
