import get from 'lodash/get'
import { enabledPrefectures } from './constants/prefectures'

function pad(number) {
  if (number < 10) {
    return '0' + number
  }
  return number
}

// eslint-disable-next-line no-extend-native
Date.prototype.toISOString = function () {
  return (
    this.getUTCFullYear() +
    '-' +
    pad(this.getUTCMonth() + 1) +
    '-' +
    pad(this.getUTCDate()) +
    'T' +
    pad(this.getUTCHours()) +
    ':' +
    pad(this.getUTCMinutes()) +
    ':' +
    pad(this.getUTCSeconds()) +
    '+09:00'
  )
}

const { createApolloFetch } = require('apollo-fetch')

let query = ``

const uri = process.env.GQL_SSR_URL
const apolloFetch = createApolloFetch({ uri })

const whitelist = [
  { path: '/', priority: 1 },
  ...enabledPrefectures.map((item) => ({
    path: `/rent/${item}`,
    priority: 0.9,
  })),
]

const blacklist = [
  '/terms-partners',
  '/inquiry',
  '/inquiry/thanks',
  '/contact-us/thanks',
  '/rent',
  '/contact-us',
  '/privacy-policy',
  '/term-of-service',
  '/referral-program',
  '/campaign',
  '/form-rent',
  '/form-buy',
  '/form-sell',
  '/tour',
  '/tour/inquiry',
  '/tour/thanks',
  '/access',
  '/',
]

export default {
  path: '/sitemap.xml',
  xslUrl: '/sitemap.xsl',
  hostname: process.env.APP_URL,
  cacheTime: 1000 * 60 * 15, // 15 minutes
  gzip: true,
  generate: false,
  exclude: blacklist,
  async routes() {
    const currentDate = new Date()
    const date =
      currentDate.getFullYear() +
      '-' +
      (currentDate.getMonth() + 1).toString().padStart(2, '0') +
      '-' +
      currentDate.getDate().toString().padStart(2, '0')
    const time =
      currentDate.getHours().toString().padStart(2, '0') +
      ':' +
      currentDate.getMinutes().toString().padStart(2, '0') +
      ':' +
      currentDate.getSeconds().toString().padStart(2, '0')
    const dateTime = date + 'T' + time + '.000Z'

    const lang = {
      kr: 'ko-kr',
      zh: 'zh-cn',
      'zh-tw': 'zh-tw',
      en: 'en',
      es: 'es',
      fr: 'fr',
    }
    const toLocales = (path) => [
      { lang: 'en', url: path },
      ...['kr', 'zh', 'zh-tw', 'es', 'fr'].reduce(
        (acc, code) => [
          ...acc,
          {
            lang: `${lang[code]}`,
            url: `/${code}${path}`,
          },
        ],
        []
      ),
    ]

    const staticRoutes = whitelist.map(({ path, priority = 0.8 }) => ({
      priority,
      url: path,
      links: toLocales(path),
      changefreq: 'daily',
      lastmod: dateTime,
    }))

    try {
      query = `
      query {
        properties (perPage: 2000) {
          items {
            rooms {
              code
              updatedAt
            }
            addressDetail {
              prefecture
            }
          }
        }
      }
      `

      const result = await apolloFetch({ query })
      const propertiesRoute = get(result, 'data.properties.items', []).reduce(
        (acc, item) => [
          ...acc,
          ...item.rooms.reduce(
            (acc2, { code, updatedAt }) => [
              ...acc2,
              {
                url: `/rent/${item.addressDetail.prefecture.toLowerCase()}/${code}`,
                links: toLocales(
                  `/rent/${item.addressDetail.prefecture.toLowerCase()}/${code}`
                ),
                changefreq: 'daily',
                priority: 0.9,
                lastmod: updatedAt,
              },
            ],
            []
          ),
        ],
        []
      )

      query = `
      query cities {
        tokyoCities: cities(filter: { prefId: "13" }) {
          _id
        }
        saitamaCities: cities(filter: { prefId: "11" }) {
          _id
        }
        chibaCities: cities(filter: { prefId: "12" }) {
          _id
        }
        kanagawaCities: cities(filter: { prefId: "14" }) {
          _id
        }
      }
      `

      const result2 = await apolloFetch({ query })
      const reducer = (prefecture) => (acc, { _id }) => [
        ...acc,
        {
          url: `/rent/${prefecture}/${_id}`,
          links: toLocales(`/rent/${prefecture}/${_id}`),
          changefreq: 'daily',
          priority: 0.9,
          lastmod: dateTime,
        },
      ]

      const tokyoCitiesRoute = get(result2, 'data.tokyoCities', []).reduce(
        reducer('tokyo'),
        []
      )

      const saitamaCitiesRoute = get(result2, 'data.saitamaCities', []).reduce(
        reducer('saitama'),
        []
      )

      const chibaCitiesRoute = get(result2, 'data.chibaCities', []).reduce(
        reducer('chiba'),
        []
      )

      const kanagawaCitiesRoute = get(
        result2,
        'data.kanagawaCities',
        []
      ).reduce(reducer('kanagawa'), [])

      return [
        ...staticRoutes,
        ...tokyoCitiesRoute,
        ...saitamaCitiesRoute,
        ...chibaCitiesRoute,
        ...kanagawaCitiesRoute,
        ...propertiesRoute,
      ]
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error)
    }

    return staticRoutes
  },
}
