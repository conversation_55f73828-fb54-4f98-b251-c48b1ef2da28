<template>
  <div class="inquiry-property" :class="{ 'full-label': fullLabel }">
    <label class="inquiry-property-checkbox">
      <slot name="checkField"></slot>
      <span class="checkmark"></span>
      <span class="checkbox"><slot name="checkLabel"></slot></span>
    </label>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'Checkbox',
  props: {
    fullLabel: {
      required: false,
      default: false,
      type: Boolean,
    },
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.inquiry-property {
  display: flex;
  padding-bottom: calc-rem(10px);
}

.inquiry-property-content-item:last-child .inquiry-property {
  padding-bottom: 0;
}

.inquiry-property-checkbox {
  align-items: center;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 0;
  width: 100%;
  display: flex;
  line-height: 1.3;
  color: $color_base;
  font-size: calc-rem(14px);

  input {
    opacity: 0;
    height: 0;
    width: 0;
    position: absolute;
    visibility: hidden;
  }

  .checkmark {
    position: relative;
    height: 24px;
    width: 24px;
    background-color: $color_white;
    margin-right: 10px;
    border: 2px solid $color_gray_light1;
    border-radius: 4px;
  }

  input:checked ~ .checkmark {
    background-color: $color_white;
    border: calc-rem(2px) solid $color_gray_light1;
  }

  .checkmark::after {
    content: '';
    position: absolute;
    display: none;
    top: calc(50% - (2rem / 16));
    left: 50%;
    width: calc-rem(8px);
    height: calc-rem(15px);
    border: solid $color_gray_light4;
    border-width: 0 calc-rem(3px) calc-rem(3px) 0;
    -webkit-transform: translate(-50%, -50%) rotate(45deg);
    -ms-transform: translate(-50%, -50%) rotate(45deg);
    transform: translate(-50%, -50%) rotate(45deg);
  }

  input:checked ~ .checkmark::after {
    display: block;
  }
}

.full-label {
  .checkbox {
    flex: 1;
  }
}
</style>
