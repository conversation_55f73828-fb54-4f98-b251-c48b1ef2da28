<template>
  <input
    v-model="inputValue"
    :type="type"
    class="base-input"
    :placeholder="placeholder"
    @change="$emit('change', inputValue)"
    v-on="$listeners"
  />
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'BaseInput',
  props: {
    placeholder: {
      type: String,
      required: false,
      default: '',
    },
    value: {
      type: String,
      required: false,
      default: '',
    },
    type: {
      type: String,
      required: false,
      default: 'text',
    },
  },
  data() {
    return {
      inputValue: this.value,
    }
  },
  watch: {
    value(value) {
      this.inputValue = value
    },
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.base-input {
  color: $color_gray_light5;
  width: 100%;
  font-size: 14px;
  letter-spacing: 0;
  border-radius: 5px;
  border: 1px solid $color_velocity_gray;
  background-color: $color_white;
  padding: 7px 10px;
}
</style>
