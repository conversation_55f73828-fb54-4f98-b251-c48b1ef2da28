<template>
  <nuxt-link
    v-if="to"
    :to="localePath(to)"
    class="base-button"
    v-on="$listeners"
    ><slot></slot
  ></nuxt-link>
  <button v-else class="base-button" v-on="$listeners">
    <slot></slot>
  </button>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'BaseButton',
  props: {
    to: {
      type: [String, Object],
      required: false,
      default: null,
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.base-button {
  font-family: $font_title;
  border: 0;
  cursor: pointer;
  font-weight: 700;
  height: 48px;
  padding: 0 20px;
  // line-height: 48px;
  font-size: 14px;
  border-radius: 4px;
  text-align: center;
  text-decoration: none;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  transition: all 0.35s ease;

  &[disabled='disabled'] {
    cursor: not-allowed;
    pointer-events: none;
  }

  @include bp('sp') {
    height: 44px;
    line-height: 44px;
  }
}

.base-button--green {
  color: $color_white;
  background: $color_green;

  &:hover {
    background: #13957e;
  }
}

.button--blue {
  color: $color_white;
  background: $color_blue !important;

  &:hover {
    background: $color_blue2 !important;
  }
}

.button--yellow {
  color: $color_white;
  background: #c29b40 !important;

  &:hover {
    opacity: 0.9;
  }
}

.button--full {
  width: 100%;
}

.button--outline {
  background: $color_white;
  color: $color_gray_light4;
  border: 1px solid $color_gray_light3;

  &:hover {
    background-color: $color_white_smoke;
  }
}
.button--orange {
  color: $color_blue;
  background-color: $color_white;
  border: 1px solid $color_blue;

  &:hover {
    background-color: #f8fbff;
  }
}
</style>
