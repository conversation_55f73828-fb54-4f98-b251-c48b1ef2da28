<template>
  <select
    v-model="selected"
    class="base-select"
    @change="$emit('input', selected)"
  >
    <option
      v-for="option in options"
      :key="option.value"
      :value="option.value"
      :selected="option.value === value"
    >
      {{ option.label }}
    </option>
  </select>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'BaseSelect',
  props: {
    options: {
      type: Array,
      required: true,
    },
    placeholder: {
      type: String,
      required: false,
      default: null,
    },
    value: {
      type: [String, Number],
      required: false,
      default: null,
    },
  },
  data() {
    return {
      selected: this.value,
    }
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.base-select {
  width: 100%;
  cursor: pointer;
  appearance: none;
  display: inline-block;
  border: 1px solid $color_velocity_gray;
  background-color: $color_white;
  background-size: 12px 12px;
  background-repeat: no-repeat;
  background-position: calc(100% - 14px) center;
  background-image: url('~/assets/images/icons/ic_arrow_bt.svg');
  font-size: 14px;
  border-radius: 5px;
  padding: 7px 10px;
  color: #333;
}
</style>
