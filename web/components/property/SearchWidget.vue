<template>
  <div style="display: flex; flex-direction: column; flex: 1">
    <div class="search__widget__heading">
      <ModalHeading
        :title="$t('rentList.filterTitle')"
        @close="$emit('hide-filter')"
      />
    </div>

    <div class="search__widget__content" style="flex: 1">
      <div v-if="filters.stations.length > 0" class="filter__section">
        <h4 class="filter__title">Station</h4>
        <div class="filter__row">
          <div class="filter__col">
            <BaseInput
              :placeholder="
                selectedStationName ? '' : $t('common.filter.station')
              "
              readonly
              :value="selectedStationName"
              @click="showStationOverlay = true"
            />
            <station-overlay
              v-if="$device.isDesktop"
              v-model="searchFilter.stations"
              :is-opened="showStationOverlay"
              @close="showStationOverlay = false"
            />
            <station-modal-mobile
              v-else
              v-model="searchFilter.stations"
              :is-opened="showStationOverlay"
              @close="showStationOverlay = false"
            />
          </div>
        </div>
      </div>
      <div v-else class="filter__section">
        <h4 class="filter__title">{{ $t('rentList.area') }}</h4>
        <div class="filter__row">
          <div class="filter__col">
            <BaseInput
              :placeholder="selectedCityNames ? '' : $t('common.filter.area')"
              readonly
              :value="selectedCityNames"
              @click="showAreaPanel = true"
            />
            <area-modal
              v-if="$device.isDesktop"
              v-model="searchFilter.cities"
              :is-opened="showAreaPanel"
              :prefecture="prefecture"
              @close="showAreaPanel = false"
            />
            <area-modal-mobile
              v-else
              v-model="searchFilter.cities"
              :is-opened="showAreaPanel"
              button-style="back"
              align="right"
              :prefecture="prefecture"
              @done="showAreaPanel = false"
              @close="showAreaPanel = false"
            />
          </div>
        </div>
      </div>
      <div class="filter__section">
        <h4 class="filter__title">{{ $t('rentList.monthlyRentFee') }}</h4>
        <div class="filter__row">
          <div class="filter__col filter__col--2">
            <input
              type="text"
              :placeholder="$t('common.filter.minRent')"
              class="input-price-filter"
              readonly
              :value="minPriceLabel"
              @click="
                minRentPanelOpened = true
                maxRentPanelOpened = false
              "
            />
            <PriceRent
              :price-list="[
                { value: 0, label: $t('common.filter.noMin') },
                ...filtersPrice,
              ]"
              :is-opened="minRentPanelOpened"
              @select="onMinPriceSelect"
              @close="minRentPanelOpened = false"
            />
          </div>
          <div class="filter__col filter__divider">〜</div>
          <div class="filter__col filter__col--2">
            <input
              type="text"
              :placeholder="$t('common.filter.maxRent')"
              class="input-price-filter"
              readonly
              :value="maxPriceLabel"
              @click="
                maxRentPanelOpened = true
                minRentPanelOpened = false
              "
            />
            <PriceRent
              :price-list="[
                { value: 0, label: $t('common.filter.noMax') },
                ...filtersPrice,
              ]"
              :is-opened="maxRentPanelOpened"
              @select="onMaxPriceSelect"
              @close="maxRentPanelOpened = false"
            />
          </div>
        </div>
      </div>
      <div class="filter__section">
        <h4 class="filter__title">{{ $t('rentList.layout') }}</h4>
        <div class="filter__row filter__row__layout">
          <SearchCheckbox
            v-for="(layout, index) in filterLayouts"
            :key="index"
            class="filter__layout"
            :value="layout"
            :label="layout"
            :is-checked="shouldLayoutSelected(layout)"
            @change="onChangeLayout"
          />
        </div>
      </div>
      <div class="filter__section">
        <h4 class="filter__title">{{ $t('rentList.distanceFromStation') }}</h4>
        <div class="filter__row">
          <div class="filter__col">
            <BaseSelect
              v-model="searchFilter.stationDistance"
              :options="distances"
            />
          </div>
        </div>
      </div>
      <div class="filter__section">
        <h4 class="filter__title">{{ $t('rentList.buildingType') }}</h4>
        <div class="filter__row filter__row__build-type">
          <SearchCheckbox
            v-for="(buildType, key) in filterBuildingTypes"
            :key="key"
            class="filter__build-type"
            :value="key"
            :is-checked="filters.buildingTypes.includes(key)"
            :label="buildType"
            @change="onChangeOptionMultiple('buildingTypes', key)"
          />
        </div>
      </div>
      <div class="filter__section">
        <h4 class="filter__title">{{ $t('rentList.features') }}</h4>
        <div class="filter__row filter__row__build-type">
          <SearchCheckbox
            v-for="(tag, key) in filterTags"
            :key="key"
            class="filter__build-type"
            :value="key"
            :is-checked="filters.tags.includes(key)"
            :label="tag"
            @change="onChangeOptionMultiple('tags', key)"
          />
        </div>
      </div>
    </div>

    <div class="search__widget__button">
      <BaseButton
        class="button--blue button--full"
        @click.prevent="$emit('on-filter', searchFilter)"
      >
        {{ $t('common.filter.search') }}
      </BaseButton>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import cloneDeep from 'lodash/cloneDeep'
import get from 'lodash/get'
import set from 'lodash/set'
import range from 'lodash/range'
import intersection from 'lodash/intersection'
import { EnumPropertyBuildingType as BuildingType } from '~/types/types'
import { PriceList, TagType } from '~/types'
import { toLocaleField } from '~/common/utils'

type filtersType = {
  layouts: string[]
  buildingTypes: string[]
  cities: string[]
  stations: string[]
  stationDistance: number
  monthlyMin: number
  monthlyMax: number
  sizeMin: number
  sizeMax: number
  tags: string[]
}

const defaultFilter = {
  layouts: [],
  buildingTypes: [],
  cities: [],
  stations: [],
  stationDistance: 0,
  monthlyMin: 0,
  monthlyMax: 0,
  sizeMin: 0,
  sizeMax: 0,
  tags: [],
}
const formatter = new Intl.NumberFormat()
export default Vue.extend({
  name: 'SearchWidget',
  props: {
    filters: {
      type: Object as PropType<filtersType>,
      required: false,
      default: () => ({ ...defaultFilter }),
    },
    prefecture: {
      type: String,
      required: false,
      default: () => 'tokyo',
    },
  },
  data() {
    return {
      showAreaPanel: false,
      showStationOverlay: false,
      filterBuildingTypes: {
        [BuildingType.Apartment]: this.$t(
          'common.options.property.buildingType.' + BuildingType.Apartment
        ),
        [BuildingType.House]: this.$t(
          'common.options.property.buildingType.' + BuildingType.House
        ),
        [BuildingType.ShareHouse]: this.$t(
          'common.options.property.buildingType.' + BuildingType.ShareHouse
        ),
        [BuildingType.MonthlyApartment]: this.$t(
          'common.options.property.buildingType.' +
            BuildingType.MonthlyApartment
        ),
      },
      filterLayouts: [
        '1R, 1K, 1DK',
        '1LDK',
        '2K',
        '2DK',
        '2LDK',
        '3K',
        '3DK',
        '3LDK',
        this.$t('common.filter.more').toString(),
      ],
      searchFilter: cloneDeep(this.filters),
      minRentPanelOpened: false,
      maxRentPanelOpened: false,
      filterTags: Object.entries(this.$t('common.tags')).reduce(
        (acc, [key, value]) => {
          return key === TagType.OfficeUse
            ? acc
            : {
                ...acc,
                [key]: value,
              }
        },
        {}
      ),
    }
  },
  head() {
    return {
      htmlAttrs: {
        class: (this as any).showAreaPanel ? 'menu-opened' : '',
      },
    }
  },
  computed: {
    selectedCityNames(): string {
      const { cities: selectedCity } = this.searchFilter
      const field = toLocaleField(this.$i18n.localeProperties.iso)
      const hasCityData = get(
        this.$accessor,
        `cities.${this.prefecture}.length`,
        0
      )
      if (!selectedCity.length || !hasCityData) return ''
      return selectedCity
        .map((id) => this.$accessor.areaMap[id][field])
        .join(', ')
    },
    selectedStationName(): string {
      const { stations } = this.searchFilter
      if (!stations.length || !this.$accessor.haveLineStation) return ''
      return stations.map((id) => this.$accessor.stationNames[id]).join(', ')
    },
    filtersPrice() {
      return [
        ...range(70000, 150000, 5000),
        ...range(150000, 300001, 10000),
      ].map((value) => ({
        value,
        label: `¥${formatter.format(value)}`,
      }))
    },
    distances() {
      let distance = 5
      const minutes = [
        {
          value: 0,
          label: this.$t('rentList.distancePlaceholder'),
        },
        {
          value: 3,
          label: this.$t('rentList.minWalk', { distance: 3 }),
        },
      ]
      while (distance <= 30) {
        minutes.push({
          value: distance,
          label: this.$t('rentList.minWalk', { distance }),
        })
        distance += 5
      }
      return minutes
    },
    minPriceLabel(): string {
      return this.searchFilter.monthlyMin
        ? '¥' + formatter.format(this.searchFilter.monthlyMin)
        : this.$t('common.filter.noMin').toString()
    },
    maxPriceLabel(): string {
      return this.searchFilter.monthlyMax
        ? '¥' + formatter.format(this.searchFilter.monthlyMax)
        : this.$t('common.filter.noMax').toString()
    },
  },
  watch: {
    filters(value) {
      this.searchFilter = { ...value }
    },
  },
  methods: {
    onChangeOptionMultiple(optionName: string, optionValue: string): void {
      const options = get(this.searchFilter, optionName, [])
      if (!options.includes(optionValue)) {
        set(this.searchFilter, optionName, [...options, optionValue])
      } else {
        const optionClean = options.filter(
          (item: string) => item !== optionValue
        )
        set(this.searchFilter, optionName, optionClean)
      }
    },
    onChangeLayout({ value }: { value: string }): void {
      const layouts = value.split(',').map((item: string) => item.trim())
      const typeIntersect = intersection(this.searchFilter.layouts, layouts)
      if (typeIntersect.length) {
        this.searchFilter.layouts = this.searchFilter.layouts.filter(
          (layout) => !layouts.includes(layout)
        )
      } else {
        this.searchFilter.layouts = [...this.searchFilter.layouts, ...layouts]
      }
    },
    shouldLayoutSelected(layout: string): boolean {
      const layouts = layout.split(',').map((item: string) => item.trim())
      const intersect = intersection(this.filters.layouts, layouts)
      return intersect.length > 0
    },
    onMinPriceSelect({ value }: PriceList) {
      this.searchFilter.monthlyMin = value
      this.minRentPanelOpened = false
    },
    onMaxPriceSelect({ value }: PriceList) {
      this.searchFilter.monthlyMax = value
      this.maxRentPanelOpened = false
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

//Filter
.filter__section {
  position: relative;
  padding-bottom: calc-rem(30px);

  &:not(:last-of-type) {
    margin-bottom: 30px;
    border-bottom: 1px solid $color_velocity_gray;
  }
}

.filter__title {
  font-weight: 700;
  font-size: 14px;
  margin-bottom: 10px;
  line-height: 1.2em;
}

.filter__row {
  display: flex;
  flex-wrap: wrap;
}

.filter__col {
  width: 100%;
  position: relative;

  .base-input {
    color: $color_base;
  }
}

.filter__col--2 {
  width: calc(50% - 20px);
}

.filter__divider {
  width: 40px;
  color: $color_gray_light5;
  text-align: center;
  align-items: center;
  display: inline-flex;
  justify-content: center;
  font-family: 'Hiragino Sans', sans-serif;

  @include font-size(14, 21);
}

.filter__layout {
  width: calc(33.33% - 8px);
}

.filter__row__layout,
.filter__row__build-type {
  margin: -4px;

  * {
    margin: 4px;
  }
}

.filter__build-type {
  width: calc(50% - 8px);
}

.search__widget__content {
  @include bp('sp') {
    padding: 20px 20px 0;
    overflow-y: auto;
    flex: 1;
    -webkit-overflow-scrolling: touch;
  }
}

.filter__layout,
.filter__build-type {
  text-align: center;
}

.input-price-filter {
  width: 100%;
  cursor: pointer;
  appearance: none;
  display: inline-block;
  border: 1px solid $color_velocity_gray;
  background-color: $color_white;
  background-size: 12px 12px;
  background-repeat: no-repeat;
  background-position: calc(100% - 14px) center;
  background-image: url('~/assets/images/icons/ic_arrow_bt.svg');
  font-size: 14px;
  border-radius: 5px;
  padding: 7px 10px;
  color: #333;
}

.search__widget__button {
  padding: 0 27px;
  @include bp(sp) {
    z-index: 90;
    padding: 14px 27px;
    height: 68px;
    background: #fff;
    box-shadow: 0 -1px 2px 0 #ddd;
  }
}
</style>
