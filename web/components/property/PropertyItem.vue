<template>
  <article class="property-item">
    <header class="property-header">
      <div class="property-heading">
        <PropertyType class="property-heading__type">
          {{
            $t('common.options.property.buildingType.' + property.buildingType)
          }}
        </PropertyType>
        <h2 class="property-title property-title__pc">
          {{ propertyName }}
        </h2>
      </div>
      <div class="property-detail">
        <div class="property-image">
          <ThumbSlider :images="propertyImages" />
        </div>
        <h2 class="property-title property-title__mobile">
          {{ propertyName }}
        </h2>
        <div class="property-info">
          <div class="property-info__row">
            <div class="property-info__cell property-info__heading">
              {{ $t('rentList.location') }}
            </div>
            <div class="property-info__cell">
              {{ addressDetail }}
            </div>
          </div>
          <div class="property-info__row">
            <div class="property-info__cell property-info__heading">
              {{ $t('rentList.access') }}
            </div>
            <div class="property-info__cell cell_station_only_sp">
              <div v-for="(station, accessIndex) in access" :key="accessIndex">
                {{ station }}
              </div>
            </div>
          </div>
          <div class="property-info__row">
            <div class="property-info__cell property-info__heading">
              {{ $t('rentList.yearBuilt') }} /
              {{ $t('rentList.story') }}
            </div>
            <div class="property-info__cell">
              {{ buildDateAndStory }}
            </div>
          </div>
        </div>
      </div>
    </header>
    <div class="property-room">
      <div class="property-room__count">
        {{ $t('rentList.roomsAvailable', { room: totalRoom }) }}
      </div>
      <PropertyRoomTable
        :rooms="property.rooms"
        :prefecture="get(property, 'prefecturePath')"
      />
    </div>
  </article>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import dayjs from 'dayjs'
import get from 'lodash/get'
import {
  getAddressDetail,
  getImage,
  getPropertyName,
  getStationAccessMultiple,
} from '~/common/utils'
import { Property } from '~/types/types'

export default Vue.extend({
  name: 'PropertyItem',
  props: {
    property: {
      type: Object as PropType<Property>,
      required: true,
    },
  },
  computed: {
    propertyName(): string {
      const translate = (key: string, values: any) => this.$t(key, values)
      return getPropertyName(this.property, translate)
    },
    propertyImages(): Array<string> {
      const mostExpensiveRoom = this.property?.rooms?.reduce((acc, room) => {
        const currentHighest = acc?.fees?.monthly?.rentFee || 0
        const nextRoom = room?.fees?.monthly?.rentFee || 0
        return currentHighest < nextRoom ? room : acc
      }, null)

      return [
        get(mostExpensiveRoom, 'images.featured', ''),
        ...get(mostExpensiveRoom, 'images.collections', []),
        ...get(this.property, 'images.collections', []),
      ].map((image) => getImage(image, this.$config.cdnUrl))
    },
    totalRoom(): number {
      return this.property.rooms.length
    },
    access(): Array<string | null> {
      const stations = get(this.property, 'access.stations', [])
      const translate = (key: string, values: any) => this.$t(key, values)
      return getStationAccessMultiple(stations, translate)
    },
    buildDateAndStory(): string {
      return this.$t('rentList.storyValue', {
        builtDate: dayjs(this.property.builtDate).format('YYYY'),
        floor: this.property.maxFloor,
      }).toString()
    },
    addressDetail(): string {
      const detail = this.property?.addressDetail
      if (!detail) return ''
      const translate = (key: string, values: any) => this.$t(key, values)
      return getAddressDetail(detail, translate).toString()
    },
  },
  methods: {
    get,
    formatPrice(price: number, displaySign: boolean = true) {
      if (!displaySign) {
        return new Intl.NumberFormat().format(price)
      }

      return new Intl.NumberFormat('ja-JP', {
        style: 'currency',
        currency: 'JPY',
      }).format(price)
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/_bootstraping.scss';

.property-item {
  background: #fff;
  padding: 0 20px;
  margin: 0 -20px 15px;

  @include bp('pc') {
    border: 1px solid $color_gray_light3;
    padding: 20px 20px 10px;
  }

  @include bp(sp) {
    margin: 0 -20px 20px;
  }
}

.property-header {
  position: relative;
  margin-bottom: 15px;
}

.property-heading {
  display: flex;
  align-items: center;

  @include bp('pc') {
    margin-bottom: calc-rem(10px);
  }
}

.property-heading__type {
  top: 15px;
  left: 5px;
  position: absolute;
  margin-right: 10px;
  display: inline-block;
  z-index: 1;

  @include bp('pc') {
    position: static;
  }
}

.property-title {
  font-weight: 700;
  margin-bottom: 0;

  @include font-size(18, 24);
}

.property-title__pc {
  display: none;

  @include bp('pc') {
    font-family: $font_title;
    display: block;
  }
}

.property-title__mobile {
  margin-bottom: calc-rem(5px);
  font-family: $font_title;
  padding: 0 20px;

  @include bp('pc') {
    display: none;
  }
}

.property-detail {
  display: flex;
  flex-wrap: wrap;

  @include bp('sp') {
    margin: 0 -20px;
    padding: 0;
  }

  @media (min-width: 1110px) {
    flex-wrap: nowrap;
  }
}

.property-image {
  width: 100%;
  height: 250px;
  margin-bottom: 15px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  @include bp('pc') {
    margin-left: 0;
    margin-right: 0;
  }

  @include bp(1100px) {
    width: 300px;
    margin-bottom: 0;
    height: 180px;
    margin-right: 20px;
  }
}

.property-info {
  width: 100%;

  @include bp('pc') {
    max-width: 100%;
    margin-bottom: 0;
    display: table;
    border-collapse: collapse;
  }

  @include bp(1100px) {
    width: calc(100% - 320px);
  }

  @include bp(sp) {
    padding: 0 20px;
  }
}

.property-info__row {
  display: table-row;
}

.property-info__cell {
  padding-bottom: 3px;
  line-height: 1.5em;
  font-size: 13px;

  @include bp('pc') {
    display: table-cell;
    vertical-align: middle;
    border: 1px solid $color_gray_light1;
    padding: 10px;
  }
}

.property-info__heading {
  display: none;

  @include bp('pc') {
    display: table-cell;
    font-weight: 400;
    width: calc-rem(130px);
    background-color: $color_gray_light2;
  }
}

.property-room__count {
  font-weight: 700;
  margin-bottom: 5px;
  @include font-size(14, 19);
}

.cell_station_only_sp div:not(:first-child) {
  @include bp('sp') {
    display: none;
  }
}
</style>

<style lang="scss">
@media (hover) {
  .property-item:hover {
    .thumb-slider .agile__actions {
      visibility: visible;
      opacity: 1;
    }
  }
}
</style>
