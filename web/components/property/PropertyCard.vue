<template>
  <div class="rent-item item-hv">
    <div class="rent-item-inner">
      <span v-if="ranking" class="rent-ranking">{{ ranking }}</span>
      <div class="rent-image">
        <figure class="thumb rent-thumb">
          <img :src="propertyImage" :alt="propertyName" />
          <property-type v-if="propertyTag" class="rent-thumb-tag">
            {{ propertyTag }}
          </property-type>
        </figure>
        <full-badge v-if="isFull" />
      </div>
      <div class="rent-content">
        <h3 class="rent-title">{{ propertyName }}</h3>
        <div class="rent-price">
          <span>
            {{ formatPrice(propertyFee.rentFee) }} {{ maintenanceFeeCheck }}
          </span>
          <small>({{ maintenanceText }})</small>
        </div>
        <p class="rent-location">{{ propertyAddress }}m<sup>2</sup></p>
        <p class="rent-access">
          {{ stationAccessByWalk }}
        </p>
      </div>
      <nuxt-link :to="localePath(propertyAnchor)" class="rent-link">
        {{ propertyName }}
      </nuxt-link>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import get from 'lodash/get'
import head from 'lodash/head'
import { EnumPropertyRoomsVisibility, Property } from '~/types/types'
import { formatPrice } from '~/common/formatPrice'
import { TagType } from '~/types'
import {
  getImage,
  getRoomLayout,
  getRoomName,
  getStationAccessCard,
} from '~/common/utils'

interface propertyFeeInterface {
  rentFee: number
  maintenanceFee: number
}

export default Vue.extend({
  name: 'PropertyCard',
  props: {
    property: {
      type: Object as PropType<Property>,
      required: true,
    },
    ranking: {
      type: Number,
      required: false,
      default: null,
    },
  },
  computed: {
    propertyFee(): propertyFeeInterface {
      const room = head(this.property.rooms)
      if (room) {
        return {
          rentFee: get(room, 'fees.monthly.rentFee', 0),
          maintenanceFee: get(room, 'fees.monthly.maintenanceFee', 0),
        }
      }
      return {
        rentFee: 0,
        maintenanceFee: 0,
      }
    },

    propertyAddress() {
      const room = head(this.property.rooms)
      const detail = get(this.property, 'addressDetail', '')
      const layout = getRoomLayout(room)
      const size = get(room, 'size', 0)
      if (!detail || !room) return ''

      const { city, prefecture } = detail
      return this.$t('home.propertyCard.subtitle', {
        city,
        prefecture,
        layout,
        size,
      })
    },
    isFull(): boolean {
      const room = head(this.property.rooms)
      return EnumPropertyRoomsVisibility.Full === room?.visibility
    },
    propertyImage(): string {
      const image = get(this.property, 'rooms[0].images.featured', '')
      return getImage(image, this.$config.cdnUrl)
    },
    propertyAnchor() {
      const roomCode = get(this.property, 'rooms[0].code', '__roomCode__')
      const prefecture = get(this.property, 'prefecturePath', '__pref__')
      return `/rent/${prefecture}/${roomCode}`
    },
    stationAccessByWalk(): string {
      const stations = get(this.property, 'access.stations', [])
      const translate = (key: string, values: any) => this.$t(key, values)
      return getStationAccessCard(stations, translate)
    },
    propertyName() {
      const translate = (key: string, values: any) => this.$t(key, values)
      const room = head(this.property.rooms)
      return getRoomName(this.property, room?.code || '', translate)
    },
    propertyTag() {
      const tag = get(this, 'property.rooms[0].tags[0]')
      if (!tag) return null
      if (!Object.values(TagType).includes(tag)) return null
      const whiteList = [TagType.Furnished, TagType.FreeRent]
      return whiteList.includes(tag) ? this.$t('common.tags.' + tag) : null
    },
    maintenanceFeeCheck(): string {
      if (this.propertyFee.maintenanceFee) {
        return `+ ${formatPrice(get(this.propertyFee, 'maintenanceFee', 0))}`
      }
      return ''
    },

    maintenanceText(): string {
      if (this.propertyFee.maintenanceFee) {
        return this.$t('common.maintenanceFee').toString()
      }
      return this.$t('common.noMaintenanceFee').toString()
    },
  },
  methods: {
    formatPrice,
  },
})
</script>

<style lang="scss">
@import '~/assets/scss/settings/variables';
@import '~/assets/scss/tools/bootstraping';

.rent {
  &-image {
    position: relative;
    .full-overlay__badge {
      width: 100px;
      height: 32px;
    }
  }

  &-item {
    width: 100%;
    margin-bottom: 30px;
    max-width: 25%;
    padding: 0 7px;

    &-inner {
      position: relative;

      &:hover {
        .thumb {
          opacity: 0.8;
        }
      }
    }

    @include bp('sp') {
      min-width: 85%;
      max-width: 85%;
      padding: 0 7px;
    }

    // iphone 5s & SE
    @include bp(0, '321px') {
      min-width: 285px;
      max-width: 285px;
    }

    // tablet
    @include bp('768px', '1025px') {
      min-width: 33.3%;
      max-width: 33.3%;
    }
  }

  &-ranking {
    background: #e12858;
    line-height: 1;
    font-weight: 600;
    color: $color_white;
    position: absolute;
    top: 7px;
    left: 7px;
    border-radius: 50%;
    z-index: 1;
    width: 28px;
    height: 28px;
    font-size: 18px;
    opacity: 0.95;

    @include flex(null, center, center);
  }

  &-thumb {
    padding-bottom: 65.72%;
    margin-bottom: 12px;
    overflow: hidden;
    border-radius: 6px;
    border: 1px solid #e8e8e8;

    &-tag {
      position: absolute;
      right: 0;
      margin: 7px;
      background-color: $color_pink;
    }
  }

  &-title {
    font-family: $font_title;
    color: $color_green;
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 0;
    line-height: 1.2em;
  }

  &-price {
    font-weight: bold;

    span {
      font-size: 16px;
      line-height: calc-rem(20px);
    }

    small {
      font-size: 11px;
    }
  }

  &-location,
  &-access {
    color: $color_gray_light4;
    margin-bottom: 0;
    font-size: 13px;
    line-height: 1.5;
  }

  &-link {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 2;
    top: 0;
    left: 0;
    text-indent: -9999px;
  }
}

/**
* Keyframes setting
*/
@-webkit-keyframes circle {
  0% {
    opacity: 1;
  }
  40% {
    opacity: 1;
  }
  100% {
    width: 200%;
    height: 200%;
    opacity: 0;
  }
}

@keyframes circle {
  0% {
    opacity: 1;
  }
  40% {
    opacity: 1;
  }
  100% {
    width: 200%;
    height: 200%;
    opacity: 0;
  }
}
</style>
