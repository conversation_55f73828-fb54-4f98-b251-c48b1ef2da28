<template>
  <div>
    <div class="sort__backdrop"></div>
    <div class="property-sort-wrap">
      <div class="property-sort__heading">
        <ModalHeading
          :title="$t('rentList.sortTitle')"
          @close="$emit('close')"
        />
      </div>
      <select
        v-model="selected"
        class="property-sort__select"
        @change="$emit('change', selected)"
        v-on="$listeners"
      >
        <option v-if="!value" selected value="">{{ placeholder }}</option>
        <option
          v-for="option in options"
          :key="option.value"
          :value="option.value"
          :selected="option.value === value"
        >
          {{ option.label }}
        </option>
      </select>
      <div class="property-sort__list">
        <div
          v-for="option in options"
          :key="option.value"
          class="property-sort__item"
          :class="{ selected: option.value === value }"
          @click="onClick(option.value)"
        >
          {{ option.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'PropertySort',
  props: {
    options: {
      type: Array,
      required: true,
    },
    placeholder: {
      type: String,
      required: false,
      default: null,
    },
    value: {
      type: [String, Number],
      required: false,
      default: null,
    },
  },
  data() {
    return {
      selected: this.value,
    }
  },
  methods: {
    onClick(item: string) {
      this.selected = item
      this.$emit('change', this.selected)
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.property-sort-wrap {
  @include bp('sp') {
    top: auto;
    bottom: 0;
    left: 0;
    opacity: 0;
    z-index: -1;
    width: 100vw;
    height: 100%;
    position: fixed;
    background: $color_white;
    visibility: hidden;
    transition: all 0.25s ease;
    transform: translateX(10px) scale(0.89);

    .show & {
      opacity: 1;
      z-index: 99;
      visibility: visible;
      transform: translateX(0) scale(1);
    }
  }
}

.property-sort__heading {
  @include bp('pc') {
    display: none;
  }
}

.property-sort__select {
  width: 100%;
  cursor: pointer;
  appearance: none;
  display: inline-block;
  border: 1px solid $color_velocity_gray;
  background-color: $color_white_smoke;
  background-size: 12px 12px;
  background-repeat: no-repeat;
  background-position: calc(100% - 14px) center;
  background-image: url('~/assets/images/icons/ic_arrow_bt.svg');
  font-size: 14px;
  padding: 5px 10px;

  @include bp('sp') {
    display: none;
  }
}

.property-sort__list {
  @include bp('pc') {
    display: none;
  }
}

.property-sort__item {
  border-bottom: 1px solid $color_gray_light1;
  padding: calc-rem(15px) calc-rem(20px);

  @include font-size(14, 19);

  &.selected {
    background: $color_white url('~/assets/images/icons/ic-check-02.svg')
      calc(100% - 20px) center no-repeat;
    background-size: 16px 16px;
  }
}

.sort__backdrop {
  display: none;

  @include bp(sp) {
    .show & {
      top: 0;
      left: 0;
      z-index: 99;
      width: 100vw;
      height: 100vh;
      display: block;
      position: fixed;
      background: rgba(0, 0, 0, 0.8);
    }
  }
}
</style>
