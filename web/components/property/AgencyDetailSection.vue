<template>
  <div class="agency__section">
    <h3 class="agency__section__title">{{ $t('roomDetail.agentInfo') }}</h3>
    <div class="agency__widget">
      <div class="agency__info">
        <div v-if="agencyAvatar" class="agency__avatar">
          <img :src="agencyAvatar" alt="agency avatar" />
        </div>
        <div class="agency__content">
          <span class="agency__listed">{{ $t('roomDetail.listedBy') }}</span>
          <strong class="agency__name">
            <nuxt-link
              :to="localePath(`/agencies/${agency._id}`)"
              class="agency__name-link"
            >
              {{ get(agency, 'public.name') }}
            </nuxt-link>
          </strong>
          <a :href="`tel:${get(agency, 'public.phone')}`">
            <div class="phone-number">
              <img src="~/assets/images/contact/call.png" />
              <strong>{{ get(agency, 'public.phone') }}</strong>
            </div>
          </a>
        </div>
      </div>
      <div class="agency__info__row">
        <strong>{{ $t('roomDetail.language') }}: </strong
        >{{ availableLanguages }}
      </div>
      <div class="agency__info__row">
        <strong>{{ $t('roomDetail.businessHours') }}: </strong>
        {{ get(agency, 'public.businessHour.start', '00:00') }} -
        {{
          get(agency, 'public.businessHour.end', '00:00').concat(
            offDay ? `, ${offDay}` : ''
          )
        }}
      </div>
      <div class="agency__info__row">
        <strong>{{ $t('roomDetail.company') }}: </strong>
        {{ agency.public.name }}
      </div>
      <div class="agency__info__row">
        <strong>{{ $t('roomDetail.address') }}: </strong>
        {{ fullAddress }}
      </div>
      <div class="agency__info__row license">{{ licenseText }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import get from 'lodash/get'
import Vue, { PropType } from 'vue'
import { Agency } from '~/types/types'

export default Vue.extend({
  name: 'AgencyDetailSection',
  props: {
    agency: {
      required: true,
      type: Object as PropType<Agency>,
    },
  },
  computed: {
    availableLanguages(): string {
      const languages = get(this.agency, 'public.availableLanguages', [])
      return languages
        .map((lang: string) => this.$t('common.language.' + lang))
        .join(', ')
    },
    offDay(): string {
      const closeDays = get(this.agency, 'public.closeDays', [])
      if (!closeDays.length) {
        return ''
      }

      const closeMap = closeDays.map((day: string) =>
        this.$t('common.dateTime.dayOfWeek.' + day)
      )
      return this.$t('common.agency.closeOn') + ' ' + closeMap.join(', ')
    },
    agencyAvatar(): string | null {
      const avatar = get(this.agency, 'public.imageUrl', '')
      if (avatar) {
        return this.$config.cdnUrl + avatar
      }

      return null
    },
    fullAddress(): string {
      const empty = {
        prefecture: '',
        city: '',
        chome: '',
        buildingName: '',
        streetAddress: '',
      }
      const translationKey = this.agency?.addressDetail?.buildingName
        ? 'roomDetail.addressValue'
        : 'roomDetail.shortAddressValue'
      return this.$t(
        translationKey,
        this.agency?.addressDetail || empty
      ).toString()
    },
    licenseText(): string {
      if (!this.agency?.basic?.license?.haveLicense) return ''
      const issueNumber = get(this.agency, 'basic.license.issueNumber', '')
      const licenseNumber = get(this.agency, 'basic.license.number', '')
      const licenseType = get(this.agency, 'basic.license.licenseType')
      return this.$t('common.agency.licenseType.' + licenseType, {
        licenseNumber,
        issueNumber,
      }).toString()
    },
  },
  methods: { get },
})
</script>

<style scoped lang="scss">
@import 'assets/scss/tools/bootstraping';
.agency__section {
  background-color: $color_gray_light2;
  padding-top: 30px;
  padding-bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  @include bp(sp) {
    margin-top: 30px;
  }

  &__title {
    color: $color_base;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 18px;
    text-align: center;
    padding-bottom: 10px;
    border-bottom: 2px solid $color_base;
  }
}

.agency__widget {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: 20px 30px;
  width: 100%;
  max-width: 600px;

  @include bp(sp) {
    background-color: #f7f7f7;
    padding: 0 30px;
  }
}

.agency__info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.agency__avatar {
  overflow: hidden;
  position: relative;
  margin-right: 12px;
  min-width: 80px;
  min-height: 80px;
  width: 80px;
  height: 80px;

  @include bp(sp) {
    display: none;
  }

  img {
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    transform: translate(-50%, -50%);
  }
}

.agency__listed {
  color: $color_gray_light8;
  font-size: 12px;
  font-weight: bold;
  line-height: 17px;
}

.agency__name {
  font-weight: 700;
  margin-bottom: 0;
  line-height: 16px;
  font-size: 14px;

  &-link {
    display: block;
    font-size: 16px;
    color: $color_base;

    &:hover {
      text-decoration: underline;
    }
  }
}

.agency__info__row {
  color: $color_gray_light4;
  margin-bottom: 2px;
  font-size: 12px;
  line-height: 17px;

  &.license {
    margin-top: 8px;
  }
}

.phone-number {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
  padding: 4px 15px;
  border: 0;
  background-color: $color_gray_light2;
  border-radius: 4px;
  transition: 0.3s;

  @include bp(sp) {
    padding: 4px 0;
  }

  // iphone 5s & SE
  @include bp(0, '321px') {
    padding: 2px 4px;
  }

  &:hover {
    opacity: 0.8;
  }

  img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    // equal to color: #1f3a4f;
    filter: brightness(0) saturate(100%) invert(18%) sepia(19%) saturate(1319%)
      hue-rotate(164deg) brightness(99%) contrast(93%);
  }

  strong {
    color: #1f3a4f;
    font-size: 20px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 27px;
  }
}
</style>
