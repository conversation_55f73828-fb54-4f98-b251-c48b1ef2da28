<template>
  <filter-modal
    button-style="back"
    :is-open="isOpened"
    :heading-text="$t('common.filter.selectStationTitle')"
    @done="handleOnDone()"
    @reset="clearAll()"
    @close="$emit('close')"
  >
    <station-list v-model="selected" :show-title="false" />
  </filter-modal>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'

export default Vue.extend({
  name: 'StationModalMobile',
  props: {
    isOpened: {
      type: Boolean as PropType<boolean>,
      required: false,
      default: false,
    },
    value: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      selected: this.value,
    }
  },
  methods: {
    handleOnDone(): void {
      this.$emit('input', this.selected)
      this.$emit('close')
    },

    clearAll() {
      this.selected = []
      this.$emit('input', [])
      this.$emit('close')
    },
  },
})
</script>
