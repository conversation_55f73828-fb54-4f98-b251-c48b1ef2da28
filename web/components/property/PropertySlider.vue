<template>
  <div class="property-slider">
    <div class="image-slider" style="position: relative">
      <div style="position: relative">
        <agile
          ref="main"
          class="image-slider-agile-main"
          :options="options"
          :as-nav-for="asNavFor1"
        >
          <div
            v-for="(slider, index) in prodSliders"
            :key="index"
            :class="`slider-full slide--${index}`"
          >
            <img
              class="image-slider-base"
              :src="getImage(slider, $config.cdnUrl)"
              data-not-lazy
              :alt="index"
            />
          </div>
          <template slot="prevButton">
            <img
              class="ic-left-slide"
              :src="require(`~/assets/images/icons/ic-left-slide.svg`)"
            />
          </template>
          <template slot="nextButton">
            <img
              class="ic-right-slide"
              :src="require(`~/assets/images/icons/ic-right-slide.svg`)"
            />
          </template>
        </agile>
        <full-badge v-if="isFull" />
      </div>
      <agile
        v-show="$device.isDesktop"
        ref="thumbnails"
        class="image-slider-agile-sub"
        :options="optionsThumb"
        :as-nav-for="asNavFor2"
      >
        <div>
          <ul class="thumb-slider">
            <li
              v-for="(slider, index) in prodSliders"
              :key="index"
              @click="$refs.thumbnails.goTo(index)"
            >
              <figure class="thumb thumb-1x1">
                <img
                  :src="getImage(slider, $config.cdnUrl)"
                  :alt="index"
                  data-not-lazy
                />
              </figure>
            </li>
          </ul>
        </div>
      </agile>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { getImage } from '~/common/utils'
export default Vue.extend({
  name: 'PropertySlider',
  props: {
    prodSliders: {
      type: Array,
      required: true,
    },
    isFull: {
      type: Boolean,
    },
  },
  data() {
    return {
      asNavFor1: [] as any,
      asNavFor2: [] as any,
      options: {
        dots: false,
        fade: true,
        navButtons: true,
      },
      optionsThumb: {
        dots: false,
        fade: true,
        navButtons: false,
      },
    }
  },
  mounted() {
    this.asNavFor1.push(this.$refs.thumbnails)
    this.asNavFor2.push(this.$refs.main)
  },
  methods: { getImage },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.property-slider {
  width: 100%;

  @include bp('sp') {
    width: 100%;
    max-height: calc-rem(276px);
    margin-bottom: calc-rem(30px);
  }
}

.image-slider {
  @include bp('sp') {
    margin: 0 -20px;
  }
  .full-overlay__badge {
    width: 100px;
    height: 32px;
  }
}

.slider-full {
  width: 100% !important;
  max-width: 100%;
  text-align: center;
}

.image-slider-base {
  width: 100%;
  height: 100%;
  max-height: calc-rem(450px);
  object-fit: contain;
}

.image-slider-agile-main {
  @include bp('pc') {
    padding-bottom: calc-rem(3px);
  }
}

.image-slider-agile-sub {
  @include bp('pc') {
    padding-bottom: calc-rem(30px);
  }

  @include bp('sp') {
    display: none;
  }
}

.ic-left-slide,
.ic-right-slide {
  height: calc-rem(18px);
  width: calc-rem(18px);
  @include bp('sp') {
    height: calc-rem(18px);
    width: calc-rem(18px);
  }
}

.thumb-slider {
  margin: 0 calc-rem(-3.5px);

  @include flex;

  li {
    width: calc(100% / 7);
    padding: calc-rem(3.5px);
    cursor: pointer;
    transition: all 0.3s ease;

    @include bp(0, 992px) {
      width: 20%;
    }
  }
}

.thumb-slider li:hover {
  opacity: 0.75;
}
</style>
<style lang="scss">
@import '~/assets/scss/tools/bootstraping';

.image-slider-base {
  @include bp('sp') {
    height: calc-rem(270px) !important;
  }
}
.image-slider {
  .agile__nav-button {
    background: rgba($color_black, 0.2);
    border-radius: 50%;
    border: none !important;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    padding: 0;
    height: calc-rem(48px);
    width: calc-rem(48px);
    display: flex;
    align-items: center;
    justify-content: center;

    @include bp('sp') {
      height: calc-rem(35px);
      width: calc-rem(35px);
    }
  }

  .agile__nav-button--prev {
    left: calc-rem(20px);
  }

  .agile__nav-button--next {
    right: calc-rem(20px);
  }

  .agile__list {
    position: relative;
  }
}
</style>
