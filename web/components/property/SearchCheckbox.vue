<template>
  <div class="base-checkbox">
    <label>
      <input
        v-model="checked"
        type="checkbox"
        :value="value"
        hidden
        @change="$emit('change', { checked, value })"
      />
      <span class="base-checkbox__label">{{ label }}</span>
    </label>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'SearchCheckbox',
  props: {
    value: {
      type: [String, Number],
      required: false,
      default: null,
    },
    label: {
      type: String,
      required: true,
    },
    isChecked: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      checked: this.isChecked,
    }
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.base-checkbox__label {
  color: $color_base;
  display: block;
  cursor: pointer;
  border-radius: 5px;
  border: 1px solid $color_velocity_gray;
  background-color: $color_white;
  font-size: 14px;
  padding: 7px 10px;
}

.base-checkbox {
  input:checked + .base-checkbox__label {
    background: $color_green;
    border: 1px solid $color_green;
    color: $color_white;
  }

  &[readonly] {
    pointer-events: none;
  }
}

.checkbox--orange input:checked + .base-checkbox__label {
  background: $color_pink;
  border: 1px solid $color_pink;
}
</style>
