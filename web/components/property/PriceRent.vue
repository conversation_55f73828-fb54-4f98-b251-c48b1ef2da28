<template>
  <div
    v-if="isOpened"
    v-on-clickaway="() => $emit('close')"
    class="price-panel"
  >
    <ul class="price-list">
      <li
        v-for="(price, index) in priceList"
        :key="index"
        :class="{ active: price.value === value }"
        @click="onClick(price)"
      >
        {{ price.label }}
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import { PriceList } from '~/types'
export default Vue.extend({
  name: 'PriceRent',
  props: {
    isOpened: {
      type: Boolean,
      required: false,
      default: false,
    },
    priceList: {
      required: true,
      type: Array as PropType<Array<PriceList>>,
    },
  },
  data() {
    return {
      value: '' as string,
    }
  },
  methods: {
    onClick(value: string) {
      this.value = value
      this.$emit('select', value)
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.price {
  &-panel {
    top: 110%;
    z-index: 99;
    position: absolute;
    min-width: 100%;
    width: 150px;
    border-radius: 8px;
    font-size: 14px;
    background-color: $color_white;
    max-width: calc(100vw - (80rem / 16));
    box-shadow: 0 calc-rem(2px) calc-rem(6px) rgba(0, 0, 0, 0.3);

    @include bp('pc') {
      overflow: hidden;
    }
  }

  &-list {
    margin: 0;
    max-height: calc-rem(300px);
    overflow: auto;

    li {
      padding: 7px 14px;
      cursor: pointer;

      &:hover,
      &.active {
        background-color: $color_gray_light1;
      }
    }
  }
}
</style>
