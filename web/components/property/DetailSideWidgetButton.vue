<template>
  <div class="detail-aside__footer">
    <BaseButton
      class="button--full button--blue detail-aside__button"
      :to="`${inquiryUrl}&purposes=check_availability`"
    >
      {{ $t('roomDetail.button.askAvailability') }}
    </BaseButton>
    <BaseButton
      class="button--full button--orange detail-aside__button"
      :to="`${inquiryUrl}&purposes=request_tour`"
    >
      {{ $t('roomDetail.button.requestTour') }}
    </BaseButton>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    inquiryUrl: {
      type: String,
      required: true,
    },
  },
})
</script>

<style lang="scss" scoped>
@import 'assets/scss/tools/bootstraping';

.detail-aside__button {
  margin-bottom: calc-rem(10px);
}
</style>
