<template>
  <div class="property-room__table">
    <div class="property-room__header">
      <div class="property-room__cell">{{ $t('rentList.floorPlan') }}</div>
      <div class="property-room__cell">{{ $t('rentList.floor') }}</div>
      <div class="property-room__cell">
        {{ $t('rentList.rent') }}<br />{{ $t('rentList.maintenanceFee') }}
      </div>
      <div class="property-room__cell">
        {{ $t('rentList.layout') }}<br />{{ $t('rentList.size') }}
      </div>
      <div class="property-room__cell">{{ $t('rentList.details') }}</div>
    </div>
    <div v-for="(room, index) in rooms" :key="index" class="property-room__row">
      <div class="property-room__cell property-room__img">
        <figure>
          <nuxt-link
            :to="localePath(`/rent/${prefecture}/${get(room, 'code', '0')}`)"
            :target="$device.isDesktop ? '_blank' : '_self'"
          >
            <img
              :src="getImage(get(room, 'images.floorPlan', ''), $config.cdnUrl)"
              :alt="get(room, 'roomName', '')"
            />
          </nuxt-link>
        </figure>
        <full-badge v-if="room.visibility === Visibility.Full" />
      </div>
      <div class="property-room__cell property-room__floor">
        <p class="property-room__data__pc">
          <span class="d--block">
            <strong>{{ get(room, 'floor', '') }}F</strong>
          </span>
          <small>{{ get(room, 'roomName', '') }}</small>
        </p>
        <p class="property-room__data__mobile">
          <span class="d--block">
            <strong>
              {{ toPrice(room, 'fees.monthly.rentFee') }}
            </strong>
          </span>
          <span>
            {{ get(room, 'floor', '') }}F {{ get(room, 'roomName', '') }} /
            {{ getRoomLayout(room) }} / {{ get(room, 'size', '') }}m<sup>2</sup>
          </span>
        </p>
        <property-room-table-button
          class-name="only_sp"
          :room-code="get(room, 'code', '0')"
          :prefecture="prefecture"
          :show-contact="room.visibility !== Visibility.Full"
        />
      </div>
      <div
        v-if="room.visibility !== Visibility.Full"
        class="property-room__cell property-room__price"
      >
        <p>
          <strong>{{ toPrice(room, 'fees.monthly.rentFee') }}</strong>
        </p>
        <small v-if="!!get(room, 'fees.monthly.maintenanceFee', null)">
          {{ toPrice(room, 'fees.monthly.maintenanceFee') }}
        </small>
      </div>
      <div v-else class="property-room__cell property-room__price">
        <p>
          <strong>￥-</strong>
        </p>
        <small v-if="!!get(room, 'fees.monthly.maintenanceFee', null)">
          ￥-
        </small>
      </div>
      <div class="property-room__cell property-room__layout">
        <p>
          <strong>
            {{ getRoomLayout(room) }}
          </strong>
        </p>
        <small>{{ get(room, 'size', '') }}m<sup>2</sup></small>
      </div>
      <property-room-table-button
        class-name="only_pc"
        :room-code="get(room, 'code', '0')"
        :prefecture="prefecture"
        :show-contact="room.visibility !== Visibility.Full"
      />
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import get from 'lodash/get'
import { EnumPropertyRoomsVisibility, PropertyRooms } from '~/types/types'
import { formatPrice } from '~/common/formatPrice'
import { getImage, getRoomLayout } from '~/common/utils'

export default Vue.extend({
  name: 'PropertyRoomTable',
  props: {
    rooms: {
      type: Array as PropType<Array<PropertyRooms>>,
      required: true,
    },
    prefecture: {
      type: String,
      required: false,
      default: 'tokyo',
    },
  },
  data: () => ({ Visibility: EnumPropertyRoomsVisibility }),
  methods: {
    getRoomLayout,
    getMaintenanceFee(room: PropertyRooms) {
      const fee = get(room, 'fees.monthly.maintenanceFee', 0)
      if (fee) {
        return ` + ${this.toPrice(room, 'fees.monthly.maintenanceFee')}`
      }
      return ''
    },
    getMaintenanceText(room: PropertyRooms) {
      const fee = get(room, 'fees.monthly.maintenanceFee', 0)
      if (fee) {
        return this.$t('common.maintenanceFee')
      }
      return this.$t('common.noMaintenanceFee')
    },
    toPrice(room: PropertyRooms, path: string) {
      return formatPrice(get(room, path))
    },
    get,
    getImage,
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.property-room__table {
  width: 100%;
  display: table;
}

.property-room__cell {
  padding-top: 10px;
  text-align: center;
  display: table-cell;
  padding-bottom: 10px;
  vertical-align: middle;
  font-size: 13px;

  @include bp(1100px) {
    width: 20%;
  }

  @include bp(sp) {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
  }

  figure,
  p {
    margin-bottom: 0;
  }

  p {
    font-size: 16px;

    @include bp(sp) {
      font-size: 14px;
      padding: 5px 0 0;
    }
  }

  small {
    font-size: 14px;
  }
}

.property-room__img {
  width: 100px;
  margin-right: 10px;
  position: relative;

  @include bp(1100px) {
    margin-right: 0;
    width: 80px;
  }

  @include bp(sp) {
    width: 100px;
    margin: 0 10px 0 0;
  }

  @include bp(0, 320px) {
    width: 80px;
  }

  img {
    border: 1px solid $color_velocity_gray;
    height: 80px;
    width: 100px;
    object-fit: contain;

    @include bp(sp) {
      height: 100px;
    }
  }
}

.property-room__floor {
  vertical-align: top;
  text-align: left;
  flex: 1 1 auto;
  padding-bottom: calc-rem(54px);

  @include bp(1100px) {
    padding-bottom: 10px;
    vertical-align: middle;
    text-align: center;
  }
}

.property-room__price,
.property-room__layout {
  display: none;

  @include bp(1100px) {
    display: table-cell;
  }
}

.property-room__header {
  display: none;

  @include bp(1100px) {
    display: table-row;
    background: $color_white_smoke;
    font-size: 13px;
    line-height: 1.2em;

    .property-room__cell {
      border-width: 0 !important;
      line-height: 1.5em;
    }
  }
}

.property-room__row {
  display: flex;
  position: relative;
  align-items: flex-start;
  border-bottom: 1px solid $color_gray_light1;

  @include bp(sp) {
    border-bottom: 1px dashed $color_gray_light1;

    &:last-child {
      border-bottom: none;
    }
  }

  @include bp(1100px) {
    display: table-row;

    &:not(:last-child) {
      .property-room__cell {
        border-bottom: 1px dashed $color_gray_light1;
      }
    }
  }

  .base-button {
    font-size: 14px;
    height: 40px;
    padding: 0 20px;
    line-height: 40px;
  }
}

.property-room__data__mobile {
  @include font-size(12, 18);

  strong {
    @include font-size(14);
    @include bp(sp) {
      font-size: 16px;
    }
  }

  small {
    @include font-size(10);
  }

  @include bp(1100px) {
    display: none;
  }

  @include bp(sp) {
    span:last-child {
      color: #555;
      margin-top: 2px;
      display: block;
    }
  }
}

.property-room__data__pc {
  display: none;

  @include bp(1100px) {
    display: block;
  }
}

.full-overlay__badge {
  width: 100%;
  height: 100%;
  max-width: 100px;
  max-height: 80px;
  font-size: 14px;
  border-radius: 0;
}
</style>
