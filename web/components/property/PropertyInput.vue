<template>
  <div>
    <div class="inquiry-property-options">
      <div class="inquiry-property-options-title">
        <h3>
          <span class="property-required">*</span>
          {{ $t('inquiry.purposeTitle') }}
        </h3>
      </div>
      <div class="inquiry-property-options-list">
        <div v-for="(option, key) in inquiryPurposes" :key="key" class="inquiry-property-content-item">
          <BaseCheckbox>
            <template slot="checkField">
              <input :id="`purpose${key}`" v-model="values.purpose" type="checkbox" :value="key" @change="onChange" />
            </template>
            <template slot="checkLabel">
              <label :for="`purpose${key}`">{{ option }}</label>
            </template>
          </BaseCheckbox>
        </div>
      </div>
      <div class="inquiry-property-options-title language-top">
        <h3>
          <span class="property-required">*</span>
          {{ $t('inquiry.languageTitle') }}
        </h3>
      </div>
      <div class="inquiry-property-languages-list">
        <div v-for="code in availableLanguages" :key="code" class="inquiry-property-content-item">
          <BaseCheckbox>
            <template slot="checkField">
              <input :id="`lang${code}`" v-model="values.preferredLanguage" type="checkbox" :value="code"
                @change="onChange" />
            </template>
            <template slot="checkLabel">
              <label :for="`lang${code}`">
                {{ $t(`common.language.${code}`) }}
              </label>
            </template>
          </BaseCheckbox>
        </div>
      </div>
    </div>
    <div class="inquiry-property-options-title">
      <h3>
        <span class="property-required">*</span>
        {{ $t(`formRent.whenToMoveIn.label`) }}
      </h3>
    </div>
    <div class="inquiry-when-move-in">
      <field-row :key="`select_whenToMoveIn`">
        <select v-model="values.whenToMoveIn" @change="onChange" required>
          <option value="" disabled selected>
            {{ $t('formRent.commonPlaceholders.select') }}
          </option>
          <option v-for="item in whenToMoveIn" :key="item.key" :value="item.key">
            {{ item.label }}
          </option>
        </select>
      </field-row>
    </div>
    <div class="inquiry-property-input">
      <label for="name"><span class="property-required">*</span>
        {{ $t('inquiry.labelName') }}</label>
      <input id="name" v-model="values.name" name="name" :class="{ 'valid-error': errors.name }" placeholder="Name"
        @change="onChange" @blur="validate('name')" @focus="hideError('name')" />
    </div>
    <div class="inquiry-property-input">
      <label for="email"><span class="property-required">*</span>
        {{ $t('inquiry.labelEmail') }}</label>
      <input id="email" v-model="values.email" name="email" type="email" autocorrect="off" autocapitalize="none"
        :class="{ 'valid-error': errors.email }" placeholder="<EMAIL>" @change="onChange"
        @blur="validate('email')" @focus="hideError('email')" />
    </div>
    <div class="inquiry-property-input">
      <label for="phone">{{ $t('inquiry.labelPhoneNumber') }}</label>
      <input id="phone" ref="inputPhone" v-model="values.phone" class="input-phone" name="phone" type="tel"
        @change="onChange" />
    </div>
    <div class="inquiry-property-input">
      <label for="memo">{{ $t('inquiry.labelMessage') }}</label>
      <textarea id="memo" v-model="values.memo" name="memo" :placeholder="$t('inquiry.placeholderMessageRenew')"
        @change="onChange" />
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import * as Yup from 'yup'
import flatten from 'flat'
import intlTelInput from 'intl-tel-input'
import errors from '~/components/inquiry/inquiry.errors.json'
import { InquiryInfo, InquiryError } from '~/types'
import 'intl-tel-input/build/css/intlTelInput.css'

type errorKey = 'email' | 'name' | 'purpose'
const schema = Yup.object().shape({
  name: Yup.string().required(errors.required),
  email: Yup.string().email(errors.email.invalid).required(errors.required),
  purpose: Yup.array().of(Yup.string()).required(errors.required).min(1),
  preferredLanguage: Yup.array()
    .of(Yup.string())
    .required(errors.required)
    .min(1),
  whenToMoveIn: Yup.string().required(errors.required).min(1),
})

export default Vue.extend({
  name: 'PropertyInput',
  props: {
    value: { type: Object as PropType<InquiryInfo>, required: true },
    availableLanguages: { type: Array as PropType<string[]>, required: true },
  },
  data() {
    return {
      values: this.value,
      errors: flatten(schema.fields) as InquiryError,
      inquiryPurposes: this.$t('inquiry.purposes'),
      inquiryWhenToMoveIn: this.$t('inquiry.whenToMoveIn'),
      dataCountry: {} as Object,
    }
  },
  computed: {
    whenToMoveIn(): any[] {
      return this.toOptions('formRent.whenToMoveIn.options')
    },
  },
  watch: {
    dataCountry(val) {
      this.$emit('dataCountry', val)
    },
  },
  mounted() {
    // Fix me when remove settimeout ref text not work
    setTimeout(() => {
      this.$nextTick(() => {
        const input = this.$refs.inputPhone
        if (!input) {
          return
        }
        const global = this
        const iti = intlTelInput(input, {
          initialCountry: 'auto',
          geoIpLookup: function getIp(callback: any) {
            fetch(`https://ipinfo.io?token=${global.$config.ipinfoToken}`, {
              headers: { Accept: 'application/json' },
            })
              .then((resp) => resp.json())
              .catch(() => {
                return {
                  country: 'jp',
                }
              })
              .then((resp) => callback(resp.country))
          },
          utilsScript:
            'https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.12/js/utils.min.js',
          preferredCountries: ['jp', 'us'],
        })
        input.addEventListener('countrychange', function () {
          const dataCountry = iti.getSelectedCountryData()
          global.dataCountry = dataCountry
        })
      })
    })
  },
  methods: {
    toOptions(translationKey: string): any[] {
      return Object.keys(this.$t(translationKey)).map((key) => ({
        key,
        label: this.$t(`${translationKey}.${key}`),
      }))
    },
    hideError(field: errorKey): void {
      this.errors[field] = null
    },
    async validate(field: errorKey): Promise<void> {
      try {
        await schema.validateAt(field, this.values)
        this.errors[field] = null
      } catch (err) {
        this.errors[field] = err.message
      }
    },
    async onChange() {
      try {
        await schema.validate(this.values)
        this.$emit('on-valid')
      } catch (err) {
        this.$emit('on-invalid')
      }
      this.$emit('input', this.values)
    },
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/settings/variables';
@import '~/assets/scss/tools/bootstraping';

.inquiry-property-options-title h3 {
  font-size: 14px;
  font-weight: bold;
  line-height: calc-rem(17px);
}

.inquiry-property-options-list {
  margin-top: -5px;

  label {
    font-size: 15px;
  }
}

.inquiry-property-input {

  input,
  textarea {
    width: 100%;
    font-size: 15px;
    border: 2px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    margin-top: 3px;
    padding: 1em;

    &::placeholder {
      color: #aaa;
    }
  }

  input {
    height: 44px;
  }

  textarea {
    height: 200px;
    min-width: 100%;
    max-width: 100%;
    padding-top: 0.5em;
  }

  input:focus,
  textarea:focus {
    border: calc-rem(2px) solid $color_green;
  }

  label {
    font-size: 14px;
    font-weight: bold;
  }
}

.property-required {
  color: #ff485f;
}

.valid-error {
  border: calc-rem(2px) solid red !important;
}

.language-top {
  margin-top: 1.25rem;
}

.inquiry-when-move-in {
  .input {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px !important;

    .label {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 5px;

      &.required {
        &::after {
          content: '*';
          color: red;
        }
      }
    }

    input {
      width: 100%;
      border: 1px solid #bbb;
      font-size: 14px;
      padding: 10px 14px;
      border-radius: 2px;

      &:focus {
        border-color: #333;
      }

      &::placeholder {
        font-size: 14px;
        color: #aaa;
      }
    }
  }

  select {
    border: 1px solid #bbb;
    padding: 10px 14px;
    border-radius: 2px;
    font-size: 14px;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: transparent;
    background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
    background-repeat: no-repeat;
    background-position-x: calc(100% - 10px);
    background-position-y: 50%;

    &:focus {
      border-color: #333;
    }

    &:required:invalid {
      color: #aaa;
    }

    option {
      color: #333;
      font-size: 14px;

      &[value=''][disabled] {
        display: none;
      }
    }
  }
}
</style>

<style lang="scss">
@import '~/assets/scss/tools/bootstraping';

.iti__flag {
  background-image: url('https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.12/img/flags.png');
}

@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
  .iti__flag {
    background-image: url('https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.12/img/<EMAIL>');
  }
}

.inquiry-property-input {
  .iti {
    width: 100%;
  }

  .input-phone {
    padding-left: 50px !important;
  }

  @include bp(sp) {
    .iti__country-list {
      width: calc(100vw - 40px) !important;
    }
  }
}
</style>
