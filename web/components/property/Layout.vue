<template>
  <div v-if="isOpened" v-on-clickaway="() => $emit('close')">
    <div class="search-filter__backdrop"></div>
    <div class="layout-panel">
      <div class="layout-widget-heading">
        <ModalHeading
          :title="$t('common.filter.selectLayouts')"
          @close="$emit('close')"
        />
      </div>
      <div class="layout-panel-inner">
        <ul class="layout-list scrollbar">
          <li v-for="(layout, index) in layoutSearch" :key="layout.id">
            <BaseCheckbox>
              <template slot="checkField">
                <input
                  :id="`layout-${index}`"
                  type="checkbox"
                  :value="layout.label"
                  :checked="shouldLayoutSelected(layout.value)"
                  @change="$accessor.toggleLayout(layout.value)"
                />
              </template>
              <template slot="checkLabel">
                <label :for="`layout-${index}`">{{ layout.label }}</label>
              </template>
            </BaseCheckbox>
          </li>
        </ul>
      </div>
      <div class="layout-footer">
        <BaseButton
          type="submit"
          class="btn-layout button--blue button--full"
          @click.prevent="onDone"
        >
          {{ $t('common.filter.buttonDone') }}
        </BaseButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import intersection from 'lodash/intersection'
import { LayoutSearch } from '~/types'
export default Vue.extend({
  name: 'Layout',
  props: {
    isOpened: {
      type: Boolean,
      required: false,
      default: false,
    },
    layoutSearch: {
      required: true,
      type: Array as PropType<Array<LayoutSearch>>,
    },
  },
  data() {
    return {
      selectedLayout: [] as string[],
    }
  },
  methods: {
    toggleLayoutAction(layoutItem: string) {
      const layouts = layoutItem.split(',').map((item) => item.trim())

      const layoutIntersect = intersection(this.selectedLayout, layouts)

      if (layoutIntersect.length) {
        this.selectedLayout = this.selectedLayout.filter(
          (layout: string) => !layouts.includes(layout)
        )
      } else {
        this.selectedLayout = [...this.selectedLayout, ...layouts]
      }
    },
    onDone() {
      this.$emit('layout-done', this.selectedLayout)
      this.$emit('close')
    },
    shouldLayoutSelected(layout: string): boolean {
      const layouts = layout.split(',').map((item: string) => item.trim())
      const intersect = intersection(this.selectedLayout, layouts)
      return intersect.length > 0
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.layout {
  &-panel {
    background-color: $color_white;
    position: absolute;
    top: 110%;
    z-index: 97;
    max-width: 100%;
    left: 0;
    box-shadow: 0 calc-rem(2px) calc-rem(6px) rgba(0, 0, 0, 0.3);

    @include bp('pc') {
      border-radius: 8px;
      padding-bottom: 15px;
      width: 200px;
    }

    @include bp('sp') {
      position: fixed;
      min-height: 90vh;
      top: auto;
      bottom: 0;
      width: 100%;
      z-index: 99;
      border-radius: 0;
    }

    &-inner {
      overflow: auto;
      max-height: calc-rem(300px);

      @include bp('sp') {
        width: 100%;
        height: calc(90vh - (120rem / 16));
        max-height: none;
        border-radius: 0;
      }
    }
  }

  &-list {
    margin: 0;
    height: 100%;

    @include bp('pc') {
      padding: 20px 15px 0 15px;
    }

    li {
      @include bp('pc') {
        &:not(:last-child) {
          margin-bottom: calc-rem(15px);
        }
      }

      @include bp('sp') {
        padding: 16px 20px;
        border-bottom: 1px solid $color_gray_light1;
      }
    }

    .inquiry-property {
      padding-bottom: 0;
    }
  }

  &-footer {
    display: flex;
    justify-content: center;
    padding-top: 15px;
    .btn-layout {
      max-width: calc-rem(150px);
      height: calc-rem(40px);
      font-size: calc-rem(14px);
      line-height: calc-rem(19px);
      font-weight: normal;
    }

    @include bp('sp') {
      background: $color_white;
      padding: 15px 20px;
      border-top: 1px solid $color_gray_light1;

      .btn-layout {
        max-width: 50%;
      }
    }
  }
}
</style>

<style lang="scss">
@import '~/assets/scss/tools/bootstraping';
.layout-panel {
  .layout-list {
    .inquiry-property {
      padding-bottom: 0;
    }

    .inquiry-property-checkbox .checkmark {
      width: calc-rem(24px);
      height: calc-rem(24px);

      &::after {
        border-color: $color_green;
      }
    }
  }
}
</style>
