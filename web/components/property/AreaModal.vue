<template>
  <client-only>
    <portal to="root">
      <div v-if="isOpened" class="modal">
        <div class="modal-heading">
          <div class="modal-heading__inner container">
            <TheCloseButton @click="$emit('close')" />
          </div>
        </div>
        <div class="container">
          <div class="search_form">
            <h1 class="page-heading flex aic">
              <img
                src="~/assets/images/icons/ic_map.svg"
                alt=""
                width="24"
                height="24"
              />
              <span>{{ prefectureName('city.pageTitle') }}</span>
            </h1>
            <div class="search_form__group flex">
              <div class="search_form__group__name">
                <BaseCheckbox class="search_form__checkbox">
                  <template slot="checkField">
                    <input
                      id="tokyo_all"
                      type="checkbox"
                      :checked="isAllChecked"
                      @change="toggleAll"
                    />
                  </template>
                  <template slot="checkLabel">
                    <label for="all_label">
                      {{ prefectureName('city.listTitle') }}
                    </label>
                  </template>
                </BaseCheckbox>
              </div>
              <ul class="search_form__group__list flex fw">
                <li
                  v-for="city in $accessor.cities[prefecture]"
                  :key="city._id"
                  class="search_form__group__list-item"
                >
                  <BaseCheckbox
                    class="search_form__checkbox"
                    :class="{ disabled: city.propertyCounts === 0 }"
                  >
                    <template slot="checkField">
                      <input
                        :id="`area_${city._id}`"
                        type="checkbox"
                        :value="city._id"
                        :checked="isChecked(city._id)"
                        @change="handleOnToggle(city._id)"
                      />
                    </template>
                    <template slot="checkLabel">
                      <label :for="`area_${city._id}`">
                        {{ city[localeField] || city.en }} ({{
                          city.propertyCounts
                        }})
                      </label>
                    </template>
                  </BaseCheckbox>
                </li>
              </ul>
              <div class="search_form__footer">
                <BaseButton
                  type="submit"
                  class="btn-filter button--blue button--full"
                  @click.prevent="$emit('close')"
                >
                  {{ $t('common.filter.buttonDone') }}
                </BaseButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </portal>
  </client-only>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import get from 'lodash/get'
import xor from 'lodash/xor'
import { City } from '~/types/types'
import { toLocaleField } from '~/common/utils'

export default Vue.extend({
  name: 'AreaModal',
  props: {
    isOpened: {
      type: Boolean as PropType<boolean>,
      required: false,
      default: false,
    },
    value: {
      type: Array,
      required: true,
    },
    prefecture: {
      type: String,
      required: false,
      default: () => 'tokyo',
    },
  },

  async fetch() {
    if (get(this.$accessor, `cities.${this.prefecture}.length`, 0)) return
    await this.$accessor.fetchCities(this.prefecture)
  },
  computed: {
    isAllChecked(): boolean {
      return this.availableCities.length === this.value.length
    },
    availableCities() {
      const cities = get(
        this.$accessor,
        `cities.${this.prefecture}`,
        []
      ) as City[]
      return cities.filter((city) => city.propertyCounts)
    },
    localeField(): keyof City {
      return toLocaleField(this?.$i18n?.localeProperties?.iso)
    },
  },
  watch: {
    isOpened(value) {
      document.body.style.overflow = value ? 'hidden' : 'unset'
    },
  },
  methods: {
    prefectureName(key: string): string {
      const prefecture = this.$accessor.prefectureName(
        this.prefecture,
        this.localeField
      )

      return this.$t(key, { prefecture }).toString()
    },
    isChecked(id: string) {
      return !!this.value.find((city) => city === id)
    },
    handleOnToggle(id: string) {
      this.$emit('input', xor(this.value, [id]))
    },
    toggleAll() {
      if (this.isAllChecked) return this.$emit('input', [])
      const value = this.availableCities.map((area) => area._id)
      this.$emit('input', value)
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/_bootstraping';

.modal {
  z-index: 999;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: stretch;
  background: #fff;
  overflow-y: scroll;

  &-heading {
    background: #fff;
    display: flex;
    z-index: 998;
    width: 100%;
    height: 70px;
    position: fixed;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.15);

    &__inner {
      position: relative;
    }
  }
}

.page-heading {
  margin-top: 100px;
  font-size: 18px;

  span {
    padding-left: 10px;
    font-family: Arial, sans-serif;
    color: #4e5a62;
  }

  @include bp(700px, 0) {
    font-size: 20px;
  }

  img {
    width: 20px;
    filter: invert(33%) sepia(10%) saturate(669%) hue-rotate(161deg)
      brightness(97%) contrast(88%);
  }
}

.search_form {
  display: flex;
  flex-direction: column;
  padding-bottom: 100px;
  @include bp(sp) {
    flex: 1;
    padding: 0;
    max-height: 100%;
  }
}

.search_form__group {
  border-top: 1px solid #cbdae7;
  border-bottom: 1px solid #cbdae7;
  flex-wrap: wrap;
  padding: 0;

  @include bp(700px, 0) {
    flex-wrap: nowrap;
  }

  + .search_form__group {
    border-top: none;
  }
}

.search_form__group__name {
  width: 100%;
  color: #4e5a62;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 0;
  padding: 14px 20px;
  background: #f0f5fa;
  font-family: Arial, sans-serif;

  @include bp(700px, 0) {
    min-width: 280px;
    width: 280px;
  }

  @media (max-width: 768px) {
    border-bottom: 1px solid #cbdae7;
  }
}
.search_form__group__list {
  margin-left: 0;
  margin-bottom: 0;
  flex: 1 1 auto;

  @include bp(sp) {
    padding: 14px 0;
    padding-bottom: 64px;
  }
}

.search_form__group__list-item {
  width: 100%;

  @include bp(700px, 0) {
    width: 33.33%;
  }
}

.search_form__checkbox {
  padding: 10px 20px;

  &.disabled {
    pointer-events: none;
  }
}

.search_form__footer {
  flex-wrap: nowrap;
  justify-content: center;
  padding: 10px;
  border-top: 1px solid #ddd;
  background: #fff;
  display: flex;
  flex: 1;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;

  .btn-filter {
    max-width: calc-rem(150px);
    height: calc-rem(40px);
    font-size: calc-rem(14px);
    line-height: calc-rem(19px);
    font-weight: normal;

    @include bp(pc) {
      width: 100%;
      height: 48px;
      font-size: 16px;
      max-width: 400px;
      line-height: 48px;
      font-weight: bold;
    }
  }
}

.form-button {
  bottom: 0;
  width: 100%;
  padding: 10px 20px;
  position: sticky;
  background: #fff;
  border-top: 1px solid #cbdae7;
  border-bottom: 1px solid #cbdae7;
}

.button--blue {
  width: 100%;
  height: 48px;
  font-size: 16px;
  max-width: 400px;
  line-height: 48px;

  &[disabled] {
    opacity: 0.4;
  }

  @media (max-width: 768px) {
    max-width: 100%;
  }
}
</style>

<style lang="scss">
.search_form__checkbox {
  &:hover .checkmark {
    box-shadow: 0 0 0 0.2em rgb(37 173 180 / 50%);
  }

  .inquiry-property-checkbox {
    font-family: Arial, sans-serif;
  }

  .inquiry-property-checkbox .checkmark {
    border: 1.2px solid #cbdae7 !important;
  }

  &.disabled {
    .inquiry-property-checkbox {
      color: #aaa;

      .checkmark {
        background: #eee !important;
        border: #eee !important;
      }
    }
  }

  .inquiry-property-checkbox input:checked ~ .checkmark {
    background-color: #25adb4 !important;
    border-color: #1ba3aa !important;
  }

  .inquiry-property-checkbox .checkmark::after {
    border-color: #fff !important;
  }
}
</style>
