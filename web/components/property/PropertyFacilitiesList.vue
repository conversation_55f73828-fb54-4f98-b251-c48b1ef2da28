<template>
  <div>
    <SearchCheckbox
      v-for="(facility, code) in facilities"
      :key="code"
      :label="facility"
      readonly
      :value="facility"
      :is-checked="roomFacilities.includes(code)"
      class="checkbox--orange property-feature"
    />
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'

export default Vue.extend({
  name: 'PropertyFacilitiesList',
  props: {
    roomFacilities: {
      type: Array as PropType<Array<string>>,
      required: true,
    },
  },
  data() {
    return {
      facilities: Object.entries(this.$t('common.facilities')).reduce(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        (acc, [key, value]) => {
          return { ...acc, ...value.items }
        },
        {}
      ),
    }
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.property-feature {
  width: auto;
  display: inline-flex;
  margin-right: calc-rem(7px);
  margin-bottom: calc-rem(7px);
}
</style>
