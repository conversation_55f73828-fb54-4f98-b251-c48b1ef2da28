<template>
  <div class="filter__mobile">
    <div class="filter__mobile__item" @click="$emit('show-filter')">
      <div class="filter__icon">
        <img src="~/assets/images/icons/filters_sp.svg" alt="Filter icon" />
      </div>
      <div class="filter__text">
        {{ $t('rentList.controlSPButton.filters') }}
      </div>
    </div>
    <div class="filter__mobile__item" @click="$emit('show-sort')">
      <div class="filter__icon">
        <img src="~/assets/images/icons/sort_sp.svg" alt="Order icon" />
      </div>
      <div class="filter__text">{{ $t('rentList.controlSPButton.sort') }}</div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'

export default Vue.extend({
  name: 'FilterMobile',
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.filter__mobile {
  background: rgba($color_black, 0.75);
  position: fixed;
  width: 280px;
  bottom: 15px;
  left: 50%;
  border-radius: 25px;
  overflow: hidden;
  display: flex;
  justify-content: space-around;
  transform: translateX(-50%);
  z-index: 90;

  @include bp('pc') {
    border-bottom: 10px solid $color_gray_light1;
    display: none;
  }
}

.filter__mobile__item {
  width: 50%;
  display: flex;
  align-items: center;
  padding: 12px 5px 12px 24px;

  &:first-child {
    border-right: 1px solid $color_gray_light5;
  }
}

.filter__icon {
  width: 16px;
  margin-right: 10px;

  img {
    display: block;
  }
}

.filter__text {
  color: $color_white;
  font-size: 14px;
  line-height: calc-rem(17px);
}
</style>
