<template>
  <div class="property-room__cell property-room__cta" :class="className">
    <BaseButton
      class="base-button--green"
      :target="$device.isDesktop ? '_blank' : '_self'"
      :to="`/rent/${prefecture}/${roomCode}`"
    >
      {{ $t('rentList.details') }}
    </BaseButton>
    <BaseButton
      v-if="showContact"
      class="base-button--green"
      :target="$device.isDesktop ? '_blank' : '_self'"
      :to="`/inquiry?rent=${roomCode}&purposes=check_availability`"
    >
      {{ $t('rentList.contact') }}
    </BaseButton>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    className: {
      type: String,
      default: () => '',
    },
    roomCode: {
      type: String,
      required: true,
    },
    prefecture: {
      type: String,
      required: true,
    },
    showContact: {
      type: Boolean,
      default: () => true,
    },
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.property-room__cta {
  display: flex;
  flex-direction: row;

  @include bp(1100px) {
    display: table-cell;
  }

  a {
    width: 100px;
    height: 38px;
    padding: 0;

    @include bp(0, 320px) {
      width: 93px;
      font-size: 13px;
    }

    &:first-child {
      margin-bottom: 5px;
      @include bp(sp) {
        margin-bottom: 0;
      }
    }
  }

  .base-button--green:last-child {
    background: $color_white;
    color: $color_green;
    border: 2px solid $color_green;

    &:hover {
      opacity: 0.7;
    }

    @include bp(sp) {
      margin-left: 10px;
    }
  }
}
</style>
