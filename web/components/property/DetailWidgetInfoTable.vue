<template>
  <table class="info-table">
    <tbody>
      <tr v-for="(item, index) in info" :key="index">
        <th>{{ get(item, 'label', '__MISSING__') }}</th>
        <td v-if="isHidden">-</td>
        <td v-else>{{ formatPrice(get(item, 'value', 0)) }}</td>
      </tr>
    </tbody>
  </table>
</template>

<script lang="ts">
import Vue from 'vue'
import get from 'lodash/get'
import { formatPrice } from '~/common/formatPrice'

export default Vue.extend({
  name: 'DetailWidgetInfoTable',
  props: {
    isHidden: {
      type: Boolean,
    },
    info: {
      type: Array,
      required: true,
    },
  },
  methods: {
    get,
    formatPrice,
  },
})
</script>

<style scoped lang="scss">
@import 'assets/scss/tools/bootstraping';

.info-table {
  border: none;
  width: 100%;

  th,
  td {
    width: 50%;
    padding: 5px 0;
    @include font-size(14, 19);
  }

  th {
    text-align: left;
  }

  td {
    text-align: right;
  }
}
</style>
