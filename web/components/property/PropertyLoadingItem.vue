<template>
  <article class="property-item">
    <header class="property-header">
      <div class="property-heading">
        <div class="shine heading" />
      </div>
      <div class="property-detail">
        <div class="property-image">
          <div class="shine cover" />
        </div>
        <h2 class="property-title property-title__mobile">
          <div class="shine cover" />
        </h2>
        <div class="property-info">
          <div class="shine info" />
        </div>
      </div>
    </header>
    <div class="property-room">
      <div class="property-room__count"><div class="shine count" /></div>
      <div class="shine room-heading" />
      <div class="shine other-room" />
    </div>
  </article>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'PropertyLoadingItem',
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/_bootstraping.scss';

.property-item {
  background: #fff;
  padding: 0 20px;
  margin: 0 -20px 15px;

  @include bp('pc') {
    border: 1px solid $color_gray_light3;
    padding: 20px 20px 10px;
  }
}

.property-header {
  position: relative;
  margin-bottom: 15px;
}

.property-heading {
  display: none;

  @include bp('pc') {
    display: flex;
    align-items: center;
    margin-bottom: calc-rem(10px);
  }
}

.property-title {
  font-weight: 700;
  margin-bottom: 0;

  @include font-size(18, 24);
}

.property-title__mobile {
  margin-bottom: calc-rem(5px);

  @include bp('pc') {
    display: none;
  }
}

.property-detail {
  display: flex;
  flex-wrap: wrap;
  @include bp('sp') {
    margin: 0 -20px;
    padding: 0 20px;
  }
}

.property-image {
  width: 100vw;
  height: 250px;
  margin: 0 20px 15px;

  @include bp('pc') {
    margin-left: 0;
    margin-right: 0;
  }

  @include bp(1100px) {
    flex: 0 1 178px;
    margin-bottom: 0;
    height: 140px;
    margin-right: 20px;
  }
}

.property-info {
  width: 100%;

  @include bp('pc') {
    flex: 1 1 auto;
  }

  @include bp(1100px) {
    width: 550px;
  }
}

.property-room__count {
  font-weight: 700;
  margin-bottom: 5px;
  @include font-size(14, 19);
}

.shine {
  background: #f6f7f8
    linear-gradient(
      to right,
      #f6f7f8 0%,
      #edeef1 20%,
      #f6f7f8 40%,
      #f6f7f8 100%
    )
    no-repeat;
  background-size: 800px 230px;
  display: inline-block;
  position: relative;
  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: forwards;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-name: placeholderShimmer;
  -webkit-animation-timing-function: linear;
  animation-duration: 1s;
  animation-fill-mode: forward;
  animation-iteration-count: infinite;
  animation-name: placeholderShimmer;
  animation-timing-function: linear;

  &.heading {
    width: 50%;
    height: 24px;
  }

  &.cover,
  &.info {
    width: 100%;
    height: 100%;
  }

  &.count {
    width: 178px;
    height: 24px;
  }

  &.room-heading {
    display: none;

    @include bp('pc') {
      display: block;
      height: 50px;
      width: 100%;
      margin-bottom: 5px;
    }
  }

  &.other-room {
    height: 110px;
    width: 100%;
  }
}

.rent-content .shine {
  width: 100%;
  height: 24px;
}

@keyframes placeholderShimmer {
  0% {
    background-position: -468px 0;
  }

  100% {
    background-position: 468px 0;
  }
}
</style>
