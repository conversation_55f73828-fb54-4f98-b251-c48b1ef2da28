<template>
  <portal to="root">
    <div v-show="isOpen" v-on-clickaway="() => $emit('close')">
      <div class="filter-modal__backdrop"></div>
      <div class="filter-modal" :class="alignClass">
        <div class="filter-modal-heading only_sp">
          <h4 class="filter-modal-heading__title">{{ headingText }}</h4>
          <button
            class="filter-modal-heading__button"
            :class="buttonClass"
            @click.prevent="$emit('close')"
          ></button>
        </div>
        <div class="filter-modal-inner">
          <slot />
        </div>
        <div class="filter-modal-footer">
          <BaseButton
            type="button"
            class="btn-filter button--outline button--full"
            @click.prevent="$emit('reset')"
          >
            {{ $t('common.filter.buttonReset') }}
          </BaseButton>
          <BaseButton
            type="submit"
            class="btn-filter button--blue button--full"
            @click.prevent="$emit('done')"
          >
            {{ $t('common.filter.buttonDone') }}
          </BaseButton>
        </div>
      </div>
    </div>
  </portal>
</template>

<script lang="ts">
/**
 * TODO: refactor `AreaModalMobile` to reuse this component later
 */
import Vue, { PropType } from 'vue'

export default Vue.extend({
  name: 'FilterModal',
  props: {
    headingText: {
      type: String,
      default: () => '',
    },
    isOpen: {
      type: Boolean as PropType<boolean>,
      required: false,
      default: false,
    },
    buttonStyle: {
      type: String,
      required: false,
      default: 'close',
      validator: (value) => ['close', 'back'].includes(value),
    },
    align: {
      type: String,
      required: false,
      default: 'left',
      validator: (value) => ['left', 'right'].includes(value),
    },
  },
  data: () => ({
    selected: [] as string[],
  }),
  computed: {
    buttonClass(): string {
      return `button-${this.buttonStyle}`
    },
    alignClass(): string {
      return `filter-modal-heading__align-${this.align}`
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.filter {
  &-modal {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
    position: fixed;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: $color_white;

    &-heading {
      height: calc-rem(50px);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      box-shadow: 0 calc-rem(2px) calc-rem(6px) rgba(0, 0, 0, 0.15);

      &__align-left {
        left: 0;
      }

      &__align-right {
        right: 0;
      }

      &__title {
        font-size: calc-rem(16px);
        line-height: calc-rem(22px);
        margin: 0;
      }

      &__button {
        top: 50%;
        border: none;
        display: block;
        cursor: pointer;
        border-radius: 50%;
        position: absolute;
        width: calc-rem(30px);
        height: calc-rem(30px);
        transform: translateY(-50%);
      }
    }

    &-inner {
      overflow: auto;
      padding: 10px 0;
      flex: 1;
    }

    &-footer {
      display: flex;
      flex-wrap: nowrap;
      justify-content: center;
      padding: 10px;
      border-top: 1px solid #ddd;

      .btn-filter {
        max-width: calc-rem(150px);
        height: calc-rem(40px);
        font-size: calc-rem(14px);
        line-height: calc-rem(19px);
        font-weight: normal;

        &:not(:last-child) {
          margin-right: calc-rem(20px);
        }
      }

      @include bp('sp') {
        padding: 15px 20px;

        .btn-filter {
          max-width: none;

          &:not(:last-child) {
            margin-right: 15px;
            line-height: 1.1875rem;
          }
        }
      }
    }
  }
}

.button {
  &-close {
    background: $color_gray_light5;
    right: 15px;

    &::before,
    &::after {
      background: $color_white;
      position: absolute;
      content: '';
      top: 50%;
      left: calc-rem(7px);
      width: calc-rem(16px);
      height: calc-rem(1px);
    }

    &::before {
      -webkit-transform: rotate(45deg);
      transform: rotate(45deg);
    }

    &::after {
      -webkit-transform: rotate(-45deg);
      transform: rotate(-45deg);
    }
  }

  &-back {
    left: calc-rem(20px);
    background: transparent url('~/assets/images/icons/ic-left.svg') center
      center no-repeat;
    background-size: calc-rem(16px) calc-rem(16px);
  }
}
</style>
