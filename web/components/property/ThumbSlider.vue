<template>
  <div class="thumb-slider">
    <agile class="thumb-slider-agile" :options="options">
      <div v-for="(slider, index) in images" :key="index">
        <img class="thumb-image" :src="slider" :alt="index" />
      </div>
      <template slot="prevButton">
        <img
          class="ic-left-slide"
          :src="require(`~/assets/images/icons/ic-left-slide.svg`)"
        />
      </template>
      <template slot="nextButton">
        <img
          class="ic-right-slide"
          :src="require(`~/assets/images/icons/ic-right-slide.svg`)"
        />
      </template>
    </agile>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'

export default Vue.extend({
  name: 'ThumbSlider',
  props: {
    images: {
      type: Array as PropType<Array<string>>,
      required: true,
    },
  },
  data() {
    return {
      options: {
        dots: false,
        navButtons: true,
      },
    }
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.thumb-slider {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .thumb-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .thumb-slider-agile {
    height: 100%;
  }
}
</style>

<style lang="scss">
@import '~/assets/scss/tools/bootstraping';

.thumb-slider {
  .agile__list,
  .agile__track {
    height: 100%;
  }

  .agile__actions {
    top: 50%;
    width: 100%;
    padding-left: 5px;
    padding-right: 5px;
    position: absolute;
    transform: translateY(-50%);
    transition: all ease 0.25s;

    @media (hover) {
      opacity: 0;
      visibility: hidden;
    }
  }

  .agile__nav-button {
    padding: 4px;
    border-radius: 50%;
    width: 30px;
    border: none;
    height: 30px;
    background: rgba($color_black, 0.3);
    align-items: center;
    display: inline-flex;
    justify-content: center;

    img {
      width: 12px;
      height: 13px;
    }

    @include bp('pc') {
      width: 25px;
      height: 25px;
    }
  }
}
</style>
