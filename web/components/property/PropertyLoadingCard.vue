<template>
  <div class="rent-item">
    <div class="rent-item-inner">
      <div class="shine cover" />
      <div class="rent-content">
        <div class="shine" />
        <div class="shine" />
        <div class="shine" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'PropertyLoadingCard',
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';
.rent {
  &-item {
    width: 100%;
    max-width: 25%;
    margin-bottom: calc-rem(40px);
    padding: 0 calc-rem(12.5px);

    @include bp('sp') {
      width: calc-rem(350px);
      min-width: calc-rem(350px);
      max-width: 50%;
      padding: 0 calc-rem(7.5px);
    }
  }
}
.cover {
  width: 100%;
  height: 230px;
  overflow: hidden;
  border-radius: 6px;
  margin-bottom: 5px;
}

.shine {
  background: #f6f7f8
    linear-gradient(
      to right,
      #f6f7f8 0%,
      #edeef1 20%,
      #f6f7f8 40%,
      #f6f7f8 100%
    )
    no-repeat;
  background-size: 800px 230px;
  display: inline-block;
  position: relative;
  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: forwards;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-name: placeholderShimmer;
  -webkit-animation-timing-function: linear;
  animation-duration: 1s;
  animation-fill-mode: forward;
  animation-iteration-count: infinite;
  animation-name: placeholderShimmer;
  animation-timing-function: linear;
}

.rent-content .shine {
  width: 100%;
  height: 24px;
}

@keyframes placeholderShimmer {
  0% {
    background-position: -468px 0;
  }

  100% {
    background-position: 468px 0;
  }
}
</style>
