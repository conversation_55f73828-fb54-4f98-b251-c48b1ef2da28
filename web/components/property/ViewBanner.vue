<template>
  <div v-if="count > 5" class="view-banner" :class="{ hidden }">
    <img :src="getImage(roomImage, $config.cdnUrl)" />
    <div class="view-banner__content">
      <span class="view-banner__content__label">{{
        $t('roomDetail.pvPopupLabel')
      }}</span>
      <p
        class="view-banner__content__desc"
        v-html="$t('roomDetail.pvPopupContent', { count })"
      />
    </div>
    <span class="close-btn" @click.prevent="hidden = true"></span>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import getPropertyPageView from '~/apollo/queries/getPropertyPageView.graphql'
import { getImage } from '~/common/utils'

export default Vue.extend({
  props: {
    roomImage: {
      type: String,
      required: true,
    },
    roomCode: {
      type: String,
      required: true,
    },
  },
  data: () => ({
    count: 0,
    hidden: false,
  }),
  apollo: {
    propertyWithPageView: {
      query: getPropertyPageView,
      variables() {
        return { roomCode: this.roomCode }
      },
      result({ data }) {
        const { rooms } = data.propertyWithPageView
        const selected = rooms.find((room: any) => room.code === this.roomCode)
        this.count = selected?.todayPageView || 0

        const delay = 8500 // 8.5s
        setTimeout(() => {
          this.hidden = true
        }, delay)
      },
    },
  },
  methods: { getImage },
})
</script>

<style lang="scss" scoped>
@import 'assets/scss/tools/bootstraping';

.view-banner {
  position: relative;
  display: flex;
  flex-direction: row;
  background-color: #35415d;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  transition: 0.5s ease-in;

  &.hidden {
    transform: translateX(100vw);
    opacity: 0;
  }

  img {
    object-fit: cover;
    min-width: 70px;
    width: 70px;
    height: 70px;
    margin-right: 10px;
  }

  &__content {
    display: flex;
    flex-direction: column;

    &__label {
      align-self: flex-start;
      background-color: $color_white;
      color: $color_pink;
      font-weight: bold;
      font-size: 10px;
      padding: 0 15px;
      border-radius: 15px;
      margin-bottom: 5px;
      margin-top: 2px;
    }

    &__desc {
      color: $color_white;
      font-size: 13px;
      line-height: 18px;
      margin: 0;
    }
  }

  .close-btn {
    background: $color_white;
    border-radius: 50%;
    position: absolute;
    top: 0;
    right: 0;
    width: calc-rem(20px);
    height: calc-rem(20px);
    display: block;
    transform: translate(-50%, 50%);
    cursor: pointer;

    &::before,
    &::after {
      background: $color_base;
      position: absolute;
      content: '';
      top: 50%;
      left: calc-rem(4px);
      width: calc-rem(12px);
      height: calc-rem(1px);
    }

    &::before {
      -webkit-transform: rotate(45deg);
      transform: rotate(45deg);
    }

    &::after {
      -webkit-transform: rotate(-45deg);
      transform: rotate(-45deg);
    }
  }
}
</style>

<style lang="scss">
@import 'assets/scss/tools/bootstraping';

.view-banner__content__desc {
  strong {
    color: $color_pink;
  }
}
</style>
