<template>
  <div class="inquiry-property-detail">
    <div class="rent-item item-hv">
      <div class="rent-item-inner">
        <div class="rent-image">
          <figure class="thumb rent-thumb">
            <img :src="propertyImage" :alt="propertyName" />
          </figure>
        </div>
        <div class="rent-content">
          <h3 class="rent-title">{{ propertyName }}</h3>
          <div class="rent-price">
            <span>
              {{ formatPrice(propertyFee.rentFee) }} +
              {{ formatPrice(propertyFee.maintenanceFee) }}
            </span>
            <small>({{ $t('common.maintenanceFee') }})</small>
          </div>
          <p class="rent-location">{{ propertyAddress }}</p>
          <p class="rent-access">
            {{ stationAccessByWalk }}
          </p>
        </div>
        <nuxt-link
          v-if="showAnchor"
          :to="localePath(propertyAnchor)"
          class="rent-link"
        >
          {{ propertyName }}
        </nuxt-link>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import get from 'lodash/get'
import { formatPrice } from '~/common/formatPrice'
import {
  getAddressDetail,
  getImage,
  getRoomName,
  getStationAccessCard,
} from '~/common/utils'
import { Maybe, Property, PropertyRooms } from '~/types/types'

interface propertyFeeInterface {
  rentFee: number
  maintenanceFee: number
}

export default Vue.extend({
  name: 'PropertyHorizontal',
  props: {
    property: {
      type: Object as PropType<Property>,
      required: true,
    },
    roomCode: {
      type: String,
      required: true,
    },
    showAnchor: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  computed: {
    selectedRoom(): Maybe<PropertyRooms> | undefined {
      return this.property.rooms.find((room) => room?.code === this.roomCode)
    },
    propertyFee(): propertyFeeInterface {
      if (this.selectedRoom) {
        return {
          rentFee: get(this.selectedRoom, 'fees.monthly.rentFee', 0),
          maintenanceFee: get(
            this.selectedRoom,
            'fees.monthly.maintenanceFee',
            0
          ),
        }
      }
      return {
        rentFee: 0,
        maintenanceFee: 0,
      }
    },
    propertyAddress() {
      if (!this.property?.addressDetail) return ''
      const translate = (key: string, values: any) => this.$t(key, values)
      return getAddressDetail(this.property.addressDetail, translate)
    },
    propertyImage(): string {
      const image = get(this.selectedRoom, 'images.featured', '')
      return getImage(image, this.$config.cdnUrl)
    },
    propertyAnchor() {
      return '/properties/' + get(this.property, '_id', '')
    },
    stationAccessByWalk(): string {
      const stations = get(this.property, 'access.stations', [])
      const translate = (key: string, values: any) => this.$t(key, values)
      return getStationAccessCard(stations, translate)
    },
    propertyName() {
      const translate = (key: string, values: any) => this.$t(key, values)
      return getRoomName(this.property, this.roomCode, translate)
    },
  },
  methods: {
    get,
    formatPrice,
  },
})
</script>
