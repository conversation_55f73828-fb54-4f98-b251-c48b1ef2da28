<template>
  <div class="property-type"><slot></slot></div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'PropertyType',
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.property-type {
  color: $color_white;
  font-weight: 700;
  padding: 0 8px;
  text-align: center;
  background-color: #e12858;
  text-transform: uppercase;
  font-family: $font_title;
  font-size: 12px;
}
</style>
