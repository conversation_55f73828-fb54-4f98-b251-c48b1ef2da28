<template>
  <div class="widget-info">
    <h3 v-if="title" class="widget-heading">{{ title }}</h3>
    <slot></slot>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'DetailInfoWidget',
  props: {
    title: {
      type: String,
      required: false,
      default: null,
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.widget-heading {
  color: $color_green;
  font-weight: 700;
  text-transform: uppercase;
  font-family: $font_title;
  font-size: 16px;
  line-height: 1.2em;
  margin-bottom: 10px;
}

.widget-info + .widget-info {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid $color_gray_light1;
}
</style>
