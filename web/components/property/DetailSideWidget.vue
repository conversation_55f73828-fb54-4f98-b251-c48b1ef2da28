<template>
  <div class="page-aside-container">
    <div
      v-if="room.visibility !== Visibility.Full"
      class="page-aside-common page-aside-top"
    >
      <div class="page-aside-inner">
        <DetailInfoWidget>
          <AgencyWidget :is-inhouse-agency="isInhouseAgency" :agency="agency" />
        </DetailInfoWidget>
        <detail-side-widget-button :inquiry-url="inquiryUrl" />
      </div>
    </div>
    <div class="page-aside-common">
      <div class="page-aside-inner">
        <DetailInfoWidget :title="$t('roomDetail.monthlyCost')">
          <DetailWidgetInfoTable
            :info="allMonthlyCosts"
            :is-hidden="room.visibility === Visibility.Full"
          />
        </DetailInfoWidget>
        <DetailInfoWidget :title="$t('roomDetail.estimatedFee')">
          <DetailWidgetInfoTable
            :info="estimateFee"
            :is-hidden="room.visibility === Visibility.Full"
          />
          <TheAlert class="alert--danger" :content="contentNotice" />
          <div
            v-if="room.visibility !== Visibility.Full"
            class="only_sp page-aside-price"
          >
            <detail-side-widget-button :inquiry-url="inquiryUrl" />
          </div>
        </DetailInfoWidget>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropOptions, PropType } from 'vue'
import get from 'lodash/get'
import sumBy from 'lodash/sumBy'
import DetailSideWidgetButton from './DetailSideWidgetButton.vue'
import {
  Agency,
  EnumPropertyRoomsFeesMoveInDepositFeeType as MoveInFeeType,
  EnumPropertyRoomsFeesOthersChargeTiming as ChargeTiming,
  EnumPropertyRoomsFeesOthersFeeType,
  EnumPropertyRoomsVisibility,
  Maybe,
  PropertyRooms,
  PropertyRoomsFeesMoveInDeposit as FeesMoveIn,
  PropertyRoomsFeesOthers,
} from '~/types/types'

const feeMap = ['rentFee', 'maintenanceFee']

const moveInFeesMap = ['deposit', 'keyMoney', 'agencyFee', 'guarantorFee']

interface FeeInterface {
  label: string
  value: number
}

export default Vue.extend({
  name: 'DetailSideWidget',
  components: { DetailSideWidgetButton },
  props: {
    isInhouseAgency: {
      required: true,
      type: Boolean,
    },
    agency: {
      required: true,
      type: Object,
    } as PropOptions<Agency>,
    room: {
      required: true,
      type: Object as PropType<PropertyRooms>,
    },
  },
  data: () => ({ Visibility: EnumPropertyRoomsVisibility }),
  computed: {
    contentNotice(): string {
      const defaultNotice = this.$t('roomDetail.feeNotice').toString()
      if (!this.isLeopalace) return defaultNotice
      const leopalaceNotice = this.$t('roomDetail.leopalaceNotice').toString()
      return defaultNotice.concat(` ${leopalaceNotice}`)
    },
    inquiryUrl(): string {
      return `/inquiry?rent=${this.room.code}`
    },
    basicMonthlyCost(): Array<FeeInterface> {
      return feeMap.map((item) => {
        return {
          label: this.$t('roomDetail.' + item),
          value: get(this.room, `fees.monthly.${item}`, 0),
        } as FeeInterface
      })
    },
    otherMonthlyCost(): FeeInterface[] {
      const byTiming = (fee: Maybe<PropertyRoomsFeesOthers>) =>
        fee?.chargeTiming === ChargeTiming.Monthly
      const fees = this.room?.fees?.others?.filter(byTiming) || []

      const types = this.$t('common.options.room.otherFeeType')
      return fees.map((fee) => ({
        label: fee?.feeType ? get(types, fee?.feeType, '') : '[missing_type_2]',
        value: fee?.value || 0,
      }))
    },
    totalMonthlyCost(): FeeInterface {
      const all = [...this.basicMonthlyCost, ...this.otherMonthlyCost]
      return {
        label: this.$t('roomDetail.totalMonthlyCost').toString(),
        value: sumBy(all, 'value'),
      }
    },
    allMonthlyCosts(): Array<FeeInterface> {
      return [
        ...this.basicMonthlyCost,
        ...this.otherMonthlyCost,
        this.totalMonthlyCost,
      ]
    },
    otherMoveInFee(): FeeInterface[] {
      const byTiming = (fee: Maybe<PropertyRoomsFeesOthers>) =>
        fee?.chargeTiming === ChargeTiming.MoveIn
      const fees = this.room?.fees?.others?.filter(byTiming) || []

      const types = this.$t('common.options.room.otherFeeType')
      return fees.map((fee) => ({
        label: fee?.feeType ? get(types, fee?.feeType, '') : '[missing_type_2]',
        value: fee?.value || 0,
      }))
    },
    // FIXME: consider fetch it from database later
    isLeopalace() {
      return [
        '6059c502ff4efe47ef7c08a9', // local
        '61ee6edce756c700121f9f76', // dev
        '61f0d030d9daa90012471a26', // prod
      ].includes(this.agency?._id)
    },
    estimateFee(): Array<FeeInterface> {
      const moveInFees = moveInFeesMap.map(this.toFormattedMoveInFees)
      moveInFees.push(...this.otherMoveInFee)

      moveInFees.push({
        ...this.totalMonthlyCost,
        value: this.isLeopalace
          ? this.totalMonthlyCost.value * 2
          : this.totalMonthlyCost.value,
      })
      moveInFees.push({
        label: this.$t('roomDetail.totalMoveInFees').toString(),
        value: sumBy(moveInFees, 'value'),
      })
      return moveInFees
    },
    communityFee(): number {
      const community = EnumPropertyRoomsFeesOthersFeeType.Community
      const otherFee = this.room?.fees?.others?.find(
        (fee) => fee?.feeType === community
      )
      return otherFee?.value || 0
    },
  },
  methods: {
    get,
    toFormattedMoveInFees(key: string): FeeInterface {
      if (!this.room?.fees) {
        // eslint-disable-next-line no-console
        console.error('this.room.fees does not exist. Pls fix on the database')
        return {
          label: '__CORRUPT__',
          value: 0,
        }
      }
      const baseFee = get(this.room, `fees.moveIn.${key}`, {
        value: 0,
      }) as FeesMoveIn
      const type = baseFee?.feeType || MoveInFeeType.FixedPrice
      const taxRatio = 1.1
      const baseValue = // agency fee will get additional 10% tax charge
        key === 'agencyFee' ? baseFee.value * taxRatio : baseFee.value

      // If the type of the fee is a multiplier,
      // - in normal condition, the guarantor fee will be = rent + maintenance
      // - for leopalace the guarantor fee will be = rent + maintenance + community
      // - for other than guarantor fee, we can use only the monthly rent fee alone
      const rentAndMaintenance = this.room?.monthlyFee || 0
      const monthlyFee =
        key === 'guarantorFee'
          ? this.isLeopalace
            ? rentAndMaintenance + this.communityFee
            : rentAndMaintenance
          : this.room?.fees?.monthly?.rentFee

      const value: number =
        type === MoveInFeeType.MonthlyMultiplier
          ? baseValue * (monthlyFee || 0)
          : baseValue
      return {
        value,
        label: this.$t(`roomDetail.${key}`).toString(),
      }
    },
  },
})
</script>

<style scoped lang="scss">
@import 'assets/scss/tools/bootstraping';

.page-aside-container {
  display: flex;
  flex-direction: column;

  // Reorder the position on mobile
  @include bp(sp) {
    .page-aside-common:first-child {
      order: 2;
      margin-bottom: 0;
    }

    .page-aside-common:last-child {
      order: 1;
      margin-bottom: 20px;
    }
  }
}

.page-aside-price {
  margin-top: 20px;
}

.page-aside-top {
  margin-bottom: 20px;
}

.page-aside-common {
  border-radius: 5px;
  background-color: $color_white;
  border: 1px solid $color_gray_light1;
  box-shadow: 0 2px 10px 0 rgba(198, 198, 198, 0.5);
  padding: 20px 30px;

  @include bp(0, 992px) {
    padding: calc-rem(20px);
  }
}
</style>
