<template>
  <div class="agency__widget">
    <div class="agency__info">
      <div v-if="agencyAvatar" class="agency__avatar">
        <img :src="agencyAvatar" alt="agency avatar" />
      </div>
      <!-- FIXME: Put no picture here -->
      <!-- <div class="agency__avatar">
        <img src="nopicture.png" alt="agency avatar" />
      </div> -->
      <div class="agency__content">
        <span class="agency__listed">{{ $t('roomDetail.listedBy') }}</span>
        <h3 class="agency__name">
          <nuxt-link
            :to="localePath(`/agencies/${agency._id}`)"
            class="agency__name-link"
            >{{ get(agency, 'public.name') }}
          </nuxt-link>
        </h3>
      </div>
    </div>
    <div class="agency__info__row">
      <strong>{{ $t('roomDetail.language') }}</strong
      ><br />
      {{ availableLanguages }}
    </div>
    <div class="agency__info__row">
      <strong>{{ $t('roomDetail.businessHours') }}</strong
      ><br />
      <p>
        {{ get(agency, 'public.businessHour.start', '00:00') }} -
        {{ get(agency, 'public.businessHour.end', '00:00') }}<br />
        {{ offDay }}
      </p>
    </div>
    <div v-if="isInhouseAgency" class="agency__info__row">
      <div class="agency__service__content">
        <img :src="imgServiceComp" alt="img-service" />
      </div>
    </div>
    <div class="agency__info__row">{{ licenseText }}</div>
  </div>
</template>

<script lang="ts">
import get from 'lodash/get'
import Vue, { PropType } from 'vue'
import { Agency } from '~/types/types'

export default Vue.extend({
  name: 'AgencyWidget',
  props: {
    isInhouseAgency: {
      required: true,
      type: Boolean,
    },
    agency: {
      required: true,
      type: Object as PropType<Agency>,
    },
  },
  data() {
    return {
      serviceItems: [
        {
          text: this.$t('roomDetail.serviceElectricity'),
          image: require('~/assets/images/details/electricity.png'),
        },
        {
          text: this.$t('roomDetail.serviceGas'),
          image: require('~/assets/images/details/gas.png'),
        },
        {
          text: this.$t('roomDetail.serviceWater'),
          image: require('~/assets/images/details/water.png'),
        },
        {
          text: this.$t('roomDetail.serviceWifi'),
          image: require('~/assets/images/details/internet.png'),
        },
      ],
      imgService: '',
    }
  },
  computed: {
    imgServiceComp(): any {
      const locale = this.$i18n.locale
      let imageUrl = ''
      if (locale) {
        switch (locale) {
          case 'en':
            imageUrl = require('~/assets/images/details/EN.png')
            break
          case 'jp':
            imageUrl = require('~/assets/images/details/JP.png')
            break

          case 'kr':
            imageUrl = require('~/assets/images/details/KR.png')
            break

          case 'zh-tw':
            imageUrl = require('~/assets/images/details/ZH-TW.png')
            break
          case 'zh':
            imageUrl = require('~/assets/images/details/ZH.png')
            break
        }
      }
      return imageUrl
    },
    availableLanguages(): string {
      const languages = get(this.agency, 'public.availableLanguages', [])
      const languagesTermp = ['en', 'jp', 'zh', 'kr']
      const languagesSort = [] as string[]

      languagesTermp.forEach((item) => {
        if (languages.includes(item)) {
          languagesSort.push(item)
        }
      })

      return languagesSort
        .map((lang: string) => this.$t('common.language.' + lang))
        .join(', ')
    },
    offDay(): string {
      const closeDays = get(this.agency, 'public.closeDays', [])
      if (!closeDays.length) {
        return ''
      }

      const closeMap = closeDays.map((day: string) =>
        this.$t('common.dateTime.dayOfWeek.' + day)
      )
      return this.$t('common.agency.closeOn') + ' ' + closeMap.join(', ')
    },
    agencyAvatar(): string | null {
      const avatar = get(this.agency, 'public.imageUrl', '')
      if (avatar) {
        return this.$config.cdnUrl + avatar
      }

      return null
    },
    licenseText(): string {
      if (!this.agency?.basic?.license?.haveLicense) return ''
      const issueNumber = get(this.agency, 'basic.license.issueNumber', '')
      const licenseNumber = get(this.agency, 'basic.license.number', '')
      const licenseType = get(this.agency, 'basic.license.licenseType')
      return this.$t('common.agency.licenseType.' + licenseType, {
        licenseNumber,
        issueNumber,
      }).toString()
    },
  },
  methods: { get },
})
</script>

<style scoped lang="scss">
@import 'assets/scss/tools/bootstraping';

.agency__widget {
  margin-bottom: 15px;
}

.agency__info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.agency__avatar {
  overflow: hidden;
  position: relative;
  margin-right: 12px;
  min-width: 60px;
  min-height: 60px;
  width: 60px;
  height: 60px;

  img {
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    transform: translate(-50%, -50%);
  }
}

.agency__listed {
  color: $color_gray_light8;
  font-size: 12px;
  font-weight: bold;
  line-height: 17px;
}

.agency__name {
  font-weight: 700;
  margin-bottom: 0;
  line-height: 1.2em;
  font-size: 16px;

  &-link {
    color: $color_base;

    &:hover {
      text-decoration: underline;
    }
  }
}

.agency__info__row {
  color: $color_gray_light4;
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 1.2em;

  p {
    margin-bottom: 0;
  }
}

.agency__service__content {
  margin: 0;
}

.agency__service__ttl {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  line-height: 20px;
  padding-bottom: 30px;
  position: relative;

  @include bp(0, '350px') {
    font-size: 12px;
    line-height: 16px;
  }

  @include bp('350px', '391px') {
    line-height: 17px;
    font-size: 13px;
  }

  @include bp('769px', '1140px') {
    font-size: 13px;
  }

  &::after {
    background-color: currentColor;
    bottom: 20px;
    content: '';
    display: inline-block;
    width: 50px;
    height: 1px;
    left: 50%;
    transform: translate(-50%, -50%);
    position: absolute;
  }

  img {
    min-width: 16px;
    min-height: 16px;
    max-height: auto;
    margin-right: 8px;
    transition: margin 300ms;

    @include bp(sp) {
      min-width: 16px;
      min-height: 16px;
      margin-right: 4px;
    }
  }

  span {
    padding-left: 2px;
    white-space: nowrap;
  }
}

.agency__service__item {
  display: flex;
  flex-wrap: wrap;
  margin: -4px 0;

  li {
    display: flex;
    align-items: center;
    min-width: 50%;
    margin: 4px 0;

    @include bp(0, '335px') {
      width: 100%;
    }

    @include bp('769px', '1050px') {
      width: 100%;
    }

    span {
      font-size: 14px;
      line-height: 14px;

      @include bp('0', '391px') {
        font-size: 12px;
        line-height: 12px;
      }

      @include bp('769px', '1140px') {
        font-size: 12px;
        line-height: 12px;
      }
    }

    img {
      width: 15px;
      margin-right: 10px;
      transition: margin 300ms;

      @include bp(sp) {
        width: 12px;
      }

      @include bp('769px', '1140px') {
        width: 12px;
      }
    }
  }
}
</style>
