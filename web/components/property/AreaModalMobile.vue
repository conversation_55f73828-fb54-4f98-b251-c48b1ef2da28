<template>
  <filter-modal
    button-style="back"
    :is-open="isOpened"
    :heading-text="$t('common.filter.selectAreaTitle')"
    @done="handleOnDone()"
    @reset="clearAll()"
    @close="$emit('close')"
  >
    <div class="d--flex flex--column">
      <div
        v-for="(city, index) in $accessor.cities[prefecture]"
        :key="index"
        class="search-filter-list"
      >
        <div class="search-filter-all">
          <BaseCheckbox
            class="search_form__checkbox"
            :class="{ disabled: city.propertyCounts === 0 }"
          >
            <template slot="checkField">
              <input
                :id="city._id"
                type="checkbox"
                :value="city._id"
                :checked="isChecked(city._id)"
                @change="handleOnToggle(city._id)"
              />
            </template>
            <template slot="checkLabel">
              <label :for="city._id">
                {{ city[localeField] || city.en }} ({{ city.propertyCounts }})
              </label>
            </template>
          </BaseCheckbox>
        </div>
      </div>
    </div>
  </filter-modal>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import get from 'lodash/get'
import xor from 'lodash/xor'
import { toLocaleField } from '~/common/utils'
import { City } from '~/types/types'

export default Vue.extend({
  name: 'FilterArea',
  props: {
    isOpened: {
      type: Boolean as PropType<boolean>,
      required: false,
      default: false,
    },
    buttonStyle: {
      type: String,
      required: false,
      default: 'close',
      validator: (value) => ['close', 'back'].includes(value),
    },
    align: {
      type: String,
      required: false,
      default: 'left',
      validator: (value) => ['left', 'right'].includes(value),
    },
    value: {
      type: Array,
      required: true,
    },
    prefecture: {
      type: String,
      required: false,
      default: () => 'tokyo',
    },
  },
  data: () => ({
    selected: [],
  }),
  async fetch() {
    await this.$accessor.fetchPrefecture(this.prefecture)
    if (get(this.$accessor, `cities.${this.prefecture}.length`, 0)) return
    await this.$accessor.fetchCities(this.prefecture)
  },
  computed: {
    localeField(): keyof City {
      return toLocaleField(this?.$i18n?.localeProperties?.iso)
    },
  },
  methods: {
    prefectureName(key: string): string {
      const prefecture = this.$accessor.prefectureName(
        this.prefecture,
        this.localeField
      )

      return this.$t(key, { prefecture }).toString()
    },
    handleOnDone(): void {
      this.$emit('done', this.$accessor.selectedArea(this.prefecture))
      this.$emit('close')
    },
    isChecked(id: string) {
      return !!this.value.find((city) => city === id)
    },
    handleOnToggle(id: string) {
      this.$emit('input', xor(this.value, [id]))
    },
    clearAll() {
      this.$emit('input', [])
    },
  },
})
</script>

<style scoped lang="scss">
/**
 * TODO: Remove unused CSS
 */
@import '~/assets/scss/tools/bootstraping';

.search {
  &-filter {
    top: 110%;
    z-index: 97;
    position: absolute;
    width: calc-rem(820px);
    border-radius: 8px;
    background-color: $color_white;
    max-width: calc(100vw - (80rem / 16));
    box-shadow: 0 calc-rem(2px) calc-rem(6px) rgba(0, 0, 0, 0.3);

    @include bp('pc') {
      overflow: hidden;
    }

    &-inner {
      overflow: auto;
      @include bp('pc') {
        height: calc-rem(320px);
      }
      @include bp('sp') {
        padding: 10px 0;
        height: calc(100vh - (120rem / 16));
      }
    }

    &-left {
      left: 0;
    }

    &-right {
      right: 0;
    }

    &-list {
      padding: 20px 15px;
      border-bottom: 1px solid $color_gray_light1;

      @include bp('sp') {
        padding: 0;
        border: none;
      }
    }

    &-all {
      margin-bottom: 10px;
      font-size: calc-rem(14px);
      line-height: calc-rem(19px);
      position: relative;

      .inquiry-property {
        padding-bottom: 0;
      }

      @include bp('sp') {
        margin-bottom: 0;
        padding: 10px 20px;
      }
    }

    &-child {
      font-size: calc-rem(12px);
      line-height: calc-rem(17px);

      @include bp('pc') {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding-left: calc-rem(15px);
      }

      @include bp('sp') {
        visibility: hidden;
        opacity: 0;
        max-height: 0;
        overflow: hidden;
        transition: all 0.3s ease;

        &.active {
          visibility: visible;
          opacity: 1;
          overflow: visible;
          max-height: calc(100vh - (120rem / 16));
        }
      }
    }

    &-item {
      @include bp('pc') {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%;
        margin-top: calc-rem(10px);
      }

      @include bp('sp') {
        padding: calc-rem(14px) 0 calc-rem(14px) calc-rem(35px);
        border-bottom: calc-rem(1px) solid $color_gray_light1;
      }
    }

    &-footer {
      display: flex;
      flex-wrap: nowrap;
      justify-content: center;
      padding: 10px;
      border-top: 1px solid #ddd;

      .btn-filter {
        max-width: calc-rem(150px);
        height: calc-rem(40px);
        font-size: calc-rem(14px);
        line-height: calc-rem(19px);
        font-weight: normal;

        &:not(:last-child) {
          margin-right: calc-rem(20px);
        }
      }

      @include bp('sp') {
        padding: 15px 20px;

        .btn-filter {
          max-width: none;

          &:not(:last-child) {
            margin-right: 15px;
            line-height: 1.1875rem;
          }
        }
      }
    }

    @include bp('sp') {
      position: fixed;
      min-height: 100vh;
      top: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      max-width: 100%;
      z-index: 99;
      border-radius: 0;

      &-heading {
        height: calc-rem(50px);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        box-shadow: 0 calc-rem(2px) calc-rem(6px) rgba(0, 0, 0, 0.15);
      }

      &-title {
        font-size: calc-rem(16px);
        line-height: calc-rem(22px);
        margin: 0;
      }

      &-button {
        top: 50%;
        border: none;
        display: block;
        cursor: pointer;
        border-radius: 50%;
        position: absolute;
        width: calc-rem(30px);
        height: calc-rem(30px);
        transform: translateY(-50%);
      }

      &-close {
        background: $color_gray_light5;
        right: 15px;

        &::before,
        &::after {
          background: $color_white;
          position: absolute;
          content: '';
          top: 50%;
          left: calc-rem(7px);
          width: calc-rem(16px);
          height: calc-rem(1px);
        }

        &::before {
          -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
        }

        &::after {
          -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
        }
      }

      &-back {
        left: calc-rem(20px);
        background: transparent url('~/assets/images/icons/ic-left.svg') center
          center no-repeat;
        background-size: calc-rem(16px) calc-rem(16px);
      }
    }

    &-toggle {
      position: absolute;
      width: calc-rem(50px);
      height: 100%;
      right: calc-rem(3px);
      top: 50%;
      z-index: 3;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);

      &::after {
        position: absolute;
        content: '';
        top: 50%;
        right: 50%;
        z-index: 1;
        border: solid $color_gray_light1;
        border-width: 0 0 calc-rem(2px) calc-rem(2px);
        display: inline-block;
        padding: calc-rem(4px);
        transition: all 0.15s;
        transform: translate(50%, -50%) rotate(316deg);
      }

      &.ic_toggle {
        &::after {
          transform: translate(50%, -50%) rotate(135deg);
        }
      }
    }
  }
}
</style>

<style lang="scss">
@import '~/assets/scss/tools/bootstraping';
.search-filter-list {
  .inquiry-property-checkbox .checkmark {
    width: calc-rem(24px);
    height: calc-rem(24px);

    &::after {
      border-color: $color_white !important;
    }
  }

  .inquiry-property-checkbox .checkbox label {
    line-height: normal;
  }

  .search-filter-item,
  .search-filter-all {
    .inquiry-property {
      padding-bottom: 0;
      &.disabled {
        pointer-events: none;
        .checkmark {
          background: #eee !important;
          border: #eee !important;
        }
        .checkbox label {
          color: #ccc;
        }
      }
    }
  }
}

.search-filter__backdrop {
  display: none;

  @include bp(sp) {
    top: 0;
    left: 0;
    z-index: 99;
    width: 100vw;
    height: 100vh;
    display: block;
    position: fixed;
    background: rgba(0, 0, 0, 0.8);
  }
}
</style>
