<template>
  <screen-overlay :is-open="isOpened" @close="$emit('close')">
    <station-list :value="value" @input="$emit('input', $event)" />
  </screen-overlay>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'

export default Vue.extend({
  name: 'StationOverlay',
  props: {
    isOpened: {
      type: Boolean as PropType<boolean>,
      required: false,
      default: false,
    },
    value: {
      type: Array,
      required: true,
    },
  },
})
</script>
