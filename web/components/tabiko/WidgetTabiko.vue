<template>
  <div v-if="tabikoShowChatWidget">
    <!-- Start of Tabiko for Business (my.tabiko.com) code -->
    <script>
      window.__lc = window.__lc || {};
      window.__lc.license = "{{ tabikoWidgetKeySite }}"
      window.__lc.widget_url = "{{ tabikoWidgetUrl }}"
      var today = new Date();
      var date = today.getFullYear()+ '' +(today.getMonth()+1)+''+today.getDate()+''+today.getHours()+''+today.getMinutes()
      ;(function(n,t,c){function i(n){return e._h?e._h.apply(null,n):e._q.push(n)}var e={_q:[],_h:null,_v:"2.0",on:function(){i(["on",c.call(arguments)])},once:function(){i(["once",c.call(arguments)])},off:function(){i(["off",c.call(arguments)])},get:function(){if(!e._h)throw new Error("[TabikoForBusinessWidget] You can't use getters before load.");return i(["get",c.call(arguments)])},call:function(){i(["call",c.call(arguments)])},init:function(){var n=t.createElement("script");n.async=!0,n.type="text/javascript",n.src=`{{ tabikoWidgetCDN }}?${date}`,t.head.appendChild(n)}};!n.__lc.asyncInit&&e.init(),n.LiveChatWidget=n.LiveChatWidget||e}(window,document,[].slice))
    </script>
    <noscript><a :href=tabikoWidgetUrl rel="nofollow">Chat with us</a>, powered by <a :href=tabikoWidgetUrl rel="noopener nofollow" target="_blank">Tabiko for Business</a></noscript>
    <!-- End of Tabiko for Business code -->
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  data() {
    return {
      tabikoShowChatWidget: '',
      tabikoWidgetUrl: '',
      tabikoWidgetKeySite: '',
      tabikoWidgetCDN: '',
    }
  },
  mounted() {
    this.tabikoShowChatWidget = this.$config.tabikoShowChatWidget
    this.tabikoWidgetUrl = this.$config.tabikoWidgetUrl
    this.tabikoWidgetKeySite = this.$config.tabikoWidgetKeySite
    this.tabikoWidgetCDN = this.$config.tabikoWidgetCDN
  },
})

</script>
