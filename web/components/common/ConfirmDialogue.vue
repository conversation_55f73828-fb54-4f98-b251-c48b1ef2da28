<template>
  <popup-modal ref="popup">
    <div class="close" @click="cancel">
      <img width="12" height="12" src="~/assets/images/contact/close.png" />
    </div>
    <div v-if="hasImgNotice" class="notice-img">
      <img src="~/assets/images/notice.png" />
    </div>
    <div class="content">
      <h2 class="title">{{ title }}</h2>
      <p>{{ message }}</p>
      <p class="bussiness-hour">{{ businessHour }}</p>
      <div class="btns">
        <a href="tel:03-4579-2103" class="cancel-btn" @click="cancel">
          <img
            width="20"
            height="20"
            src="~/assets/images/contact/call-white.png"
          />
          {{ cancelButton }}
        </a>
        <button :class="type" @click="confirm">{{ okButton }}</button>
      </div>
    </div>
  </popup-modal>
</template>

<script lang="ts">
import Vue from 'vue'
import noop from 'lodash/noop'
import {
  ConfirmOptions,
  ConfirmOptionsType,
  eventBus,
} from '~/plugins/confirm-dialog'

const defaultValue: ConfirmOptions = {
  title: '',
  message: '',
  hasImgNotice: false,
  type: ConfirmOptionsType.Danger,
  businessHour: '',
  okButton: '削除する',
  cancelButton: 'キャンセル',
  okCallback: noop,
  cancelCallback: noop,
}

export default Vue.extend({
  name: 'ConfirmDialogue',
  data: () => ({ ...defaultValue }),
  mounted() {
    eventBus.on<ConfirmOptions>('showConfirmDialog', this.handleEvent)
  },
  beforeDestroy() {
    eventBus.off<ConfirmOptions>('showConfirmDialog', this.handleEvent)
  },
  methods: {
    handleEvent(options?: ConfirmOptions) {
      if (!options) throw new Error('Option is required')
      this.setOptions(options)
      ;(this.$refs.popup as any).open()
    },
    cancel() {
      ;(this.$refs.popup as any).close()
      if (this.cancelCallback) this.cancelCallback()
      this.setOptions(defaultValue)
    },
    confirm() {
      ;(this.$refs.popup as any).close()
      if (this.okCallback) this.okCallback()
      this.setOptions(defaultValue)
    },
    setOptions(options: ConfirmOptions) {
      Object.entries(options).forEach(([field, value]) => {
        if (!value) return
        this.$set(this, field, value)
      })
    },
  },
})
</script>

<style scoped>
.close {
  height: 28px;
  width: 28px;
  float: right;
  position: absolute;
  top: -10px;
  right: -10px;
  cursor: pointer;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

p {
  color: #333;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
  text-align: center;
  margin-top: -5px;
}
.btns {
  text-align: center;
  margin: 0 auto;
  left: 25%;
}

.danger {
  box-sizing: border-box;
  height: 48px;
  width: 100%;
  border-radius: 4px;
  background-color: #fff6f6;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
  padding: -0.5em 1em;
  text-transform: uppercase;
  cursor: pointer;
  border: 1px solid #b60505;
  color: #b60505;
  font-weight: 600;
}

.primary {
  box-sizing: border-box;
  height: 40px;
  width: 120px;
  border: none;
  border-radius: 4px;
  background-color: #2470c7;
  color: #fff;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
  padding: -0.5em 1em;
  text-transform: uppercase;
  cursor: pointer;
}

.cancel-btn {
  box-sizing: border-box;
  height: 60px;
  width: 100%;
  border: none;
  border-radius: 4px;
  background-color: #bf9834;
  color: #fff;
  font-size: 20px;
  letter-spacing: 0;
  line-height: 21px;
  padding: -0.5em 1em;
  text-transform: uppercase;
  cursor: pointer;
  margin-bottom: 14px;
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
}
.cancel-btn img {
  margin-top: 2px;
  margin-right: 5px;
}
.title {
  text-align: center;
  font-size: 20px;
  line-height: 27px;
}
.notice-img img {
  width: 100%;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 180px;
  object-fit: cover;
}
.content {
  padding: 0 20px;
  margin-top: 20px;
}
.bussiness-hour {
  margin-top: 20px;
}
</style>
