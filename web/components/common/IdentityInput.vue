<template>
  <div class="identity-inputs">
    <div class="name-row">
      <field-row
        required
        :error="errors['identity.firstName']"
        :label="$t('formRent.name.label')"
      >
        <input
          v-model="firstName"
          :placeholder="$t('formRent.name.placeholder0')"
          type="text"
          @blur="$emit('blur', 'identity.firstName')"
          @focus="$emit('focus', 'identity.firstName')"
        />
      </field-row>
      <field-row style="display: flex; flex-direction: column">
        <input
          v-model="lastName"
          :placeholder="$t('formRent.name.placeholder1')"
          type="text"
        />
        <div
          v-if="errors['identity.firstName']"
          class="hidden-block"
          style="height: 24px"
        />
      </field-row>
    </div>
    <field-row
      required
      :error="errors['identity.email']"
      :label="$t('formRent.email.label')"
    >
      <input
        v-model="email"
        type="email"
        :placeholder="$t('formRent.email.placeholder')"
        @blur="$emit('blur', 'identity.email')"
        @focus="$emit('focus', 'identity.email')"
      />
    </field-row>
    <field-row
      :required="phoneRequired"
      :error="phoneRequired ? errors['identity.phone'] : ''"
      :label="$t('formRent.phone.label')"
    >
      <input
        ref="inputPhone"
        v-model="phone"
        class="input-phone"
        type="tel"
        @blur="$emit('blur', 'identity.phone')"
        @focus="$emit('focus', 'identity.phone')"
      />
    </field-row>
    <field-row
      required
      :error="errors['identity.preferredLanguage']"
      :label="$t('formRent.preferredLanguage.label')"
    >
      <checkbox-list
        v-model="preferredLanguage"
        :items="languages"
        translation-key="formRent.preferredLanguage.options"
      />
    </field-row>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import intlTelInput from 'intl-tel-input'
import { LanguageCode } from '~/types'
import 'intl-tel-input/build/css/intlTelInput.css'

export default Vue.extend({
  name: 'IdentityInput',
  props: {
    value: {
      type: Object,
      required: true,
    },
    errors: {
      type: Object,
      required: true,
    },
    phoneRequired: {
      type: Boolean,
      required: false,
      default: false,
    },
    phonePlaceholder: {
      type: Boolean,
      required: false,
      default: true,
    },
  },
  data: () => ({
    languages: Object.values(LanguageCode),
    dataCountry: {} as Object,
  }),
  computed: {
    firstName: {
      get(): string {
        return this.value.firstName
      },
      set(newValue: string) {
        this.$emit('input', { ...this.value, firstName: newValue })
      },
    },
    lastName: {
      get(): string {
        return this.value.lastName
      },
      set(newValue: string) {
        this.$emit('input', { ...this.value, lastName: newValue })
      },
    },
    email: {
      get(): string {
        return this.value.email
      },
      set(newValue: string) {
        this.$emit('input', { ...this.value, email: newValue })
      },
    },
    phone: {
      get(): string {
        return this.value.phone
      },
      set(newValue: string) {
        this.$emit('input', { ...this.value, phone: newValue })
      },
    },
    preferredLanguage: {
      get(): string {
        return this.value.preferredLanguage
      },
      set(newValue: string) {
        this.$emit('input', { ...this.value, preferredLanguage: newValue })
      },
    },
  },
  watch: {
    dataCountry(val) {
      this.$emit('dataCountry', val)
    },
  },
  mounted() {
    // Fix me when remove settimeout ref text not work
    setTimeout(() => {
      this.$nextTick(() => {
        const input = this.$refs.inputPhone
        if (!input) {
          return
        }
        const global = this
        const iti = intlTelInput(input, {
          initialCountry: 'auto',
          geoIpLookup: function getIp(callback: any) {
            fetch(`https://ipinfo.io?token=${global.$config.ipinfoToken}`, {
              headers: { Accept: 'application/json' },
            })
              .then((resp) => resp.json())
              .catch(() => {
                return {
                  country: 'us',
                }
              })
              .then((resp) => callback(resp.country))
          },
          utilsScript:
            'https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.12/js/utils.min.js',
          preferredCountries: ['jp', 'us'],
        })
        input.addEventListener('countrychange', function () {
          const dataCountry = iti.getSelectedCountryData()
          global.dataCountry = dataCountry
        })
      })
    })
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.identity-inputs {
  display: flex;
  flex-direction: column;
}

.name-row {
  display: flex;
  flex-direction: row;
  align-items: flex-end;

  @include bp(sp) {
    flex-direction: column;

    .hidden-block {
      display: none;
    }
  }

  * {
    width: 50%;

    @include bp(sp) {
      width: 100%;
    }
  }

  *:not(:last-child) {
    padding-right: 24px;
    @include bp(sp) {
      padding-right: unset;
    }
  }
}
</style>

<style lang="scss">
@import '~/assets/scss/tools/bootstraping';

.iti__flag {
  background-image: url('https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.12/img/flags.png');
}

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .iti__flag {
    background-image: url('https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.12/img/<EMAIL>');
  }
}

.identity-inputs {
  .input-phone {
    padding-left: 50px !important;
  }

  @include bp(sp) {
    .iti__country-list {
      width: calc(100vw - 40px) !important;
    }
  }
}
</style>
