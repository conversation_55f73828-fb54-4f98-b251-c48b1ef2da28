<template>
  <ul class="social">
    <li class="social-item social-item-fb">
      <a href="https://www.facebook.com/livingjapancom/">FaceBook</a>
    </li>
    <li class="social-item social-item-tw">
      <a href="https://twitter.com/livingjapan_">Twitter</a>
    </li>
    <li class="social-item social-item-ig">
      <a href="https://www.instagram.com/livingjapan_official/">Instagram</a>
    </li>
  </ul>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'Social',
})
</script>

<style lang="scss">
@import '~/assets/scss/tools/bootstraping';

//Social
.social {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-wrap: nowrap;

  &-item {
    position: relative;

    &:not(:first-child) {
      margin-left: calc-rem(20px);
    }

    &-fb {
      a {
        @include bg-image(
          '~/assets/images/icons/social_fb.svg',
          no-repeat,
          center,
          center,
          100%
        );
      }
    }

    &-tw {
      a {
        @include bg-image(
          '~/assets/images/icons/social_tw.svg',
          no-repeat,
          center,
          center,
          100%
        );
      }
    }

    &-ig {
      a {
        @include bg-image(
          '~/assets/images/icons/social_ig.svg',
          no-repeat,
          center,
          center,
          100%
        );
      }
    }

    a {
      display: block;
      width: calc-rem(20px);
      height: calc-rem(20px);
      text-indent: -9999px;
      -webket-transition: all 0.2s ease;
      transition: all 0.2s ease;
    }
  }
}
</style>
