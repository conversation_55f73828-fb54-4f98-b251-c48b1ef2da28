<template>
  <div class="locale-switch" :class="{ 'new-style': newStyle }">
    <a
      v-if="iconWhite"
      class="locale-switch__control control--white"
      @click="isOpened = !isOpened"
    >
      <img
        src="~/assets/images/icons/ic_lang_white.svg"
        class="locale-icon"
        alt="locale"
      />
      <div class="locale-switch__control__label">
        {{ selectedLocaleName }}
      </div>
      <img src="~/assets/images/icons/ic_arrow_bt_white.svg" class="arrow-bt" />
    </a>
    <a v-else class="locale-switch__control" @click="isOpened = !isOpened">
      <img
        src="~/assets/images/icons/ic_lang.svg"
        class="locale-icon"
        alt="locale"
      />
      <div class="locale-switch__control__label">{{ selectedLocaleName }}</div>
      <img src="~/assets/images/icons/ic_arrow_bt.svg" class="arrow-bt" />
    </a>
    <div
      v-if="isOpened"
      v-on-clickaway="() => (isOpened = false)"
      class="locale-switch__dropdown"
      :class="{ bottom: position === 'bottom' }"
    >
      <ul class="locale-switch__dropdown__list">
        <li
          v-for="locale in $i18n.locales"
          :key="locale.iso"
          :class="{ active: locale.code === $i18n.locale }"
        >
          <a :href="switchLocalePath(locale.code)">
            {{ locale.name }}
          </a>
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts">
import { LocaleObject } from 'nuxt-i18n/types'
import Vue, { PropType } from 'vue'
export default Vue.extend({
  props: {
    position: {
      type: String as PropType<'bottom' | 'top'>,
      default: 'top',
    },
    newStyle: {
      type: Boolean,
      default: false,
    },
    iconWhite: {
      type: Boolean,
      default: false,
    },
  },
  data: () => ({
    isOpened: false,
  }),
  computed: {
    selectedLocaleName(): string {
      const locale = (this.$i18n.locales as LocaleObject[]).find(
        (item: any) => item.code === this.$i18n.locale
      )
      return locale?.name || ''
    },
  },
})
</script>
<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.locale-switch {
  position: relative;

  &__control {
    margin-right: 15px;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 5px 10px;
    border: 1px solid #465562;
    border-radius: 3px;
    cursor: pointer;
    transition: 0.2s ease-in;

    &:hover {
      opacity: 0.7;
    }

    img.locale-icon {
      width: 18px;
      aspect-ratio: 1;
      margin-right: 10px;
    }

    &__label {
      color: #465562;
      font-size: 14px;
      font-weight: bold;
      margin-right: 16px;
    }

    img.arrow-bt {
      width: 10px;
      height: 10px;
    }
  }

  &__dropdown {
    top: 110%;
    z-index: 99;
    position: absolute;
    min-width: 100%;
    width: 170px;
    border-radius: 5px;
    font-size: 14px;
    background-color: $color_white;
    max-width: calc(100vw - (80rem / 16));
    box-shadow: 0 calc-rem(2px) calc-rem(6px) rgba(0, 0, 0, 0.3);
    overflow: hidden;

    &.bottom {
      top: unset;
      bottom: 110%;
    }

    &__list {
      margin: 7px;
      max-height: calc-rem(300px);
      overflow: auto;

      li {
        border-radius: 5px;
        display: flex;
        align-items: center;
        padding: 7px 14px;
        cursor: pointer;
        transition: 0.2s ease-in;

        &:not(:last-child) {
          margin-bottom: 5px;
        }

        &:hover {
          background-color: $color_white_smoke;
        }

        a {
          display: flex;
          flex-direction: row;
          width: 100%;
          color: #465562;
        }
      }
    }
  }

  .control--white {
    border: 1px solid $color_white;

    .locale-switch__control__label {
      color: $color_white;
    }
  }
}

// override the new style
.new-style {
  .locale-switch__control {
    border: 0;
  }
}
</style>
