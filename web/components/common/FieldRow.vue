<template>
  <div class="input" :class="{ error: error }">
    <label class="label" :class="{ required }">{{ label }}</label>
    <slot></slot>
    <p v-if="error" class="error">
      {{ error }}
    </p>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    required: {
      type: Boolean,
    },
    label: {
      type: String,
      default: () => '',
    },
    error: {
      type: String,
      default: () => null,
    },
  },
})
</script>

<style lang="scss" scoped>
.error {
  margin: 0;
  color: red;
  font-size: 12px;
}
</style>
