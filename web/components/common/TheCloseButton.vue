<template>
  <span class="search-filter-close" @click.prevent="$emit('click')"></span>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'TheCloseButton',
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.search-filter-close {
  background: $color_gray_light5;
  border-radius: 50%;
  position: absolute;
  right: calc-rem(15px);
  top: 50%;
  width: calc-rem(30px);
  height: calc-rem(30px);
  display: block;
  transform: translateY(-50%);
  cursor: pointer;

  &::before,
  &::after {
    background: $color_white;
    position: absolute;
    content: '';
    top: 50%;
    left: calc-rem(7px);
    width: calc-rem(16px);
    height: calc-rem(1px);
  }

  &::before {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
  }

  &::after {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
  }
}
</style>
