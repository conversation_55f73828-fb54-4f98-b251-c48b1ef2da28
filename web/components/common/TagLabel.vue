<template>
  <div class="tag-label">
    <slot></slot>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.tag-label {
  font-family: $font_title;
  font-weight: bold;
  color: white;
  font-size: 13px;
  padding: 0 8px;
  border-radius: 4px;
  background: #e12858;
  width: max-content;
  margin-bottom: 10px;

  &:not(:last-child) {
    margin-right: 10px;
  }
}
</style>
