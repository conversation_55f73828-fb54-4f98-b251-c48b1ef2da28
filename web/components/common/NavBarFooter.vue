<template>
  <ul class="navbar">
    <li>
      <nuxt-link class="nav-menu" :to="localePath('/term-of-service')">
        {{ $t('common.menu.item1') }}
      </nuxt-link>
    </li>
    <li>
      <a
        href="https://www.inbound-platform.com/en/privacy/"
        rel="noreferrer"
        target="_blank"
        class="nav-menu"
      >
        {{ $t('common.menu.item2') }}
      </a>
    </li>
    <li>
      <a
        href="https://www.inbound-platform.com/en/"
        rel="noreferrer"
        target="_blank"
        class="nav-menu"
      >
        {{ $t('common.menu.item3') }}
      </a>
    </li>
    <li>
      <a href="#" class="nav-menu" @click="$accessor.showContactPopup(true)">
        {{ $t('common.menu.item4') }}
      </a>
    </li>
  </ul>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'Navbar',
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.navbar {
  &.nav-footer {
    margin: 0;
    padding: calc-rem(30px) 0;
    border-bottom: 1px solid $color_gray_light1;

    @include bp('pc') {
      @include flex();
    }

    li {
      &:not(:last-child) {
        margin-right: calc-rem(30px);
      }

      a {
        color: $color_base;
        @include font-size(14, 19);

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
