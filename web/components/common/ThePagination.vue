<template>
  <div class="common-pagination">
    <b-pagination
      v-model="currentPageData"
      :total-rows="total"
      :per-page="perPage"
      :hide-goto-end-buttons="true"
      :prev-class="'first__btn'"
      :next-class="'last__btn'"
      first-number
      last-number
      @input="onInput"
    ></b-pagination>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'ThePagination',
  props: {
    value: {
      required: false,
      type: Number,
      default: 1,
    },
    total: {
      required: true,
      type: Number,
    },
    perPage: {
      required: true,
      type: Number,
    },
  },
  data() {
    return {
      currentPageData: this.value,
    }
  },
  methods: {
    onInput(page: Number) {
      this.$emit('input', page)
    },
  },
})
</script>

<style scoped lang="scss">
.pagination {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
</style>

<style lang="scss">
@import '~/assets/scss/tools/bootstraping';

.common-pagination {
  .page-item {
    user-select: none;
    margin-right: 10px;

    @include bp('sp') {
      margin-right: 5px;
    }

    .page-link {
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      color: black;
      background-color: #efefef;
      width: calc-rem(34px);
      height: calc-rem(34px);
      padding: 0;
      text-align: center;
      border-radius: 2px;
      position: relative;
      transition: all 0.2s ease;
      font-weight: bold;

      @media only screen and (max-width: 410px) {
        width: calc-rem(28px);
        height: calc-rem(28px);
      }

      @include font-size(14, 22);

      &:hover {
        background: $color_base;
        color: $color_white;
      }
    }

    &.active .page-link {
      background: $color_base;
      color: $color_white;
    }

    &.disabled {
      &[role='separator'] {
        span.page-link {
          &:hover {
            background: none;
            color: $color_base;
          }
        }
      }
      &.first__btn,
      &.last__btn {
        display: none;
      }
    }

    &:first-child {
      .page-link {
        &::before {
          border-width: calc-rem(1px) calc-rem(1px) 0 0;
        }
      }
    }

    &:last-child {
      .page-link {
        &::before {
          border-width: 0 0 calc-rem(1px) calc-rem(1px);
        }
      }
    }

    &:first-child,
    &:last-child {
      .page-link {
        display: block;
        text-indent: -9999px;

        &::before {
          position: absolute;
          content: '';
          top: 50%;
          left: 50%;
          z-index: 1;
          border-style: solid;
          border-color: $color_base;
          display: inline-block;
          padding: 0.25rem;
          transform: translate(-50%, -50%) rotate(225deg);
        }

        &:hover {
          &::before {
            border-color: $color_white;
          }
        }
      }

      &.disabled {
        cursor: not-allowed;

        .page-link {
          background: none;

          &::before {
            border-color: $color_base;
          }
        }
      }
    }
  }
}
</style>
