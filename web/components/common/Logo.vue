<template>
  <div class="header-logo">
    <nuxt-link :to="localePath('/')" class="logo">
      <img
        :src="require(`~/assets/images/logo_${logoImage}.png`)"
        data-not-lazy
        alt="Living Japan"
        class="only_pc"
      />
      <img
        src="~/assets/images/logo_green.png?inline"
        data-not-lazy
        alt="Living Japan"
        class="only_sp"
      />
    </nuxt-link>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'Logo',
  props: {
    dark: {
      required: false,
      default: false,
      type: Boolean,
    },
  },
  computed: {
    logoImage(): String {
      return 'green'
    },
  },
})
</script>

<style scoped lang="scss">
.logo {
  margin: 0;
}
</style>
