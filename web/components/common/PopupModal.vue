<template>
  <transition name="fade">
    <div v-if="isVisible" class="popup-modal" @click.self="close">
      <div class="window">
        <slot></slot>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'PopupModal',
  data: () => ({
    isVisible: false,
  }),
  methods: {
    open() {
      this.isVisible = true
      this.setBodyStyle('hidden')
    },
    close() {
      this.isVisible = false
      this.setBodyStyle('auto')
    },
    setBodyStyle(value) {
      const body = document.querySelector('body')
      body.style.overflow = value
    },
  },
}
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.popup-modal {
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  z-index: 99;
  height: 120%;
}

.window {
  min-height: 155px;
  width: 354px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 2px 4px 8px rgba(0, 0, 0, 0.2);
  max-width: 480px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  padding-bottom: 30px;
  margin-top: -20%;
}
</style>
