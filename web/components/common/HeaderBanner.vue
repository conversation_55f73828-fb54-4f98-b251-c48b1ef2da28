<template>
  <div class="header-banner__container ">
    <div class="header-banner__container__item only_pc_header">
      {{ $t(`common.headerBanner.message`) }}
    </div>
    <div class="header-banner__container__item sp-header">
      <a href="https://calendly.com/livingjapan/web" target="_blank" title="contact form">
        <div>
          <img height="14" width="14" src="~/assets/images/calendar.png" class="phone call" />
          <span class="only_pc_header">
            {{ $t(`common.headerBanner.scheduleMeeting`) }}
          </span>
          <span class="only_sp_header">
            {{ $t(`common.headerBanner.messageSP`) }}
          </span>
        </div>
      </a>
      <a href="tel:03-4579-2103" target="_blank" title="contact form">
        <div class="header-banner__container__item-highlighted">
          <img height="14" width="14" src="~/assets/images/icons/phone-call-white.svg" />
          <span class="only_pc_header">
            {{ $t(`common.headerBanner.contact`) }}
          </span>
          <span class="only_sp_header">
            {{ $t(`common.headerBanner.contactSP`) }}
          </span>
        </div>
      </a>

    </div>
  </div>
</template>

<script lang="ts">
//
import Vue from 'vue'
export default Vue.extend({
  name: 'HeaderBanner',
})
</script>
<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

//Header
.header-banner {
  &__container {
    height: 44px;
    background-color: #242b51;
    font-size: 13px;
    display: flex;
    justify-content: space-evenly;
    flex-direction: row;
    line-height: 44px;

    &__item {
      display: flex;
      flex-direction: row;
      color: #FFF;
      letter-spacing: 0;
      font-size: 13px;
      height: 44px;

      a {
        color: #fff;
      }

      img {
        margin-right: .4rem;
        margin-top: -.1rem;
      }
    }

    &__item-highlighted {
      margin-left: 0;
      background: #384264;
      height: 40px;
      padding-left: 0.5rem;
      padding-right: 0.5rem;
      color: #FFF
    }
  }

  &__image {
    margin: 0 auto;
    width: 95%;
    position: relative;
    transition: all 0.2s ease;
    background: url('~/assets/images/banners/contact_pc.png');
    background-repeat: no-repeat;
    background-position: center;
    height: 64px;

    @include bp(sp) {
      padding: 0;
      background: url('~/assets/images/banners/contact_sp.png');
      height: 58px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
    }

    // tablet
    @include bp('768px', '1025px') {
      padding: 0;
      background: url('~/assets/images/banners/contact_pc.png');
      height: 58px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
    }
  }
}

.sp-header {
  @include bp(sp) {
    justify-content: space-between;
    font-size: 13px;
  }

  div {
    padding: 0 1rem;
    height: 44px;
  }

  div:hover {
    text-decoration: underline;
  }
}
</style>
