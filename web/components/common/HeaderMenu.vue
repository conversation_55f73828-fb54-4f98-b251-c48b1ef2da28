<template>
  <client-only>
    <ul class="header-menu">
      <li v-for="item in menuItems" :key="item.code" class="header-menu__item">
        <nuxt-link :to="localePath(item.path)" class="header-menu__item__link">
          {{ $t(`common.headerMenu.${item.code}`) }}
          <img v-if="item.subMenuItems" src="~/assets/images/icons/ic_arrow_bt.svg" class="arrow-bt" />
        </nuxt-link>
        <div v-if="item.subMenuItems" class="header-sub-menu">
          <ul>
            <li v-for="subItem in item.subMenuItems" :key="subItem.code">
              <nuxt-link :to="localePath(subItem.path)">
                {{ $t(`common.subHeaderMenu.${subItem.code}`) }}
                <small v-if="subItem.new" class="new">NEW</small>
              </nuxt-link>
            </li>
          </ul>
        </div>
      </li>
      <li class="header-menu__item">
        <div style="margin-top: -0.4rem;">
        <locale-switch new-style />
      </div>
      </li>
      <li class="header-menu__item cta-props">
        <a
          href="javascript:void(0)"
          class="header-menu__item__link"
          @click="$accessor.showContactPopup(true)"
          style="color:white"
        >
          {{ $t(`common.headerMenu.contact`) }}
        </a>
      </li>
    </ul>
  </client-only>
</template>

<script lang="ts">
import Vue from 'vue'
import noop from 'lodash/noop'
import { enabledPrefectures } from '~/constants/prefectures'
export default Vue.extend({
  data() {
    return {
      showContactPopup: false,
      menuItems: [
        {
          code: 'rent',
          path: '/rent/tokyo',
          subMenuItems: enabledPrefectures.map((item) => ({
            code: `rent-${item}`,
            path: `/rent/${item}`,
          })),
        },
        {
          code: 'buy',
          path: '/form-buy',
          subMenuItems: [
            {
              code: 'inquiry-for-buy',
              path: '/form-buy',
            },
            {
              code: 'vip-tour',
              path: '/tour',
              new: true,
            },
          ],
        },
        // { code: 'sell', path: '/form-sell' },
        { code: 'ourOffice', path: '/access' },
      ],
    }
  },
  methods: { noop },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.header-sub-menu {
  top: 80%;
  z-index: 99;
  position: absolute;
  min-width: 100%;
  width: 170px;
  border-radius: 5px;
  font-size: 14px;
  background-color: $color_white;
  max-width: calc(100vw - (80rem / 16));
  box-shadow: 0 calc-rem(2px) calc-rem(6px) rgba(0, 0, 0, 0.3);
  overflow: hidden;

  ul {
    margin: 7px;
    max-height: calc-rem(180px);
    display: none;
    overflow: auto;

    li {
      border-radius: 5px;
      display: flex;
      align-items: center;
      padding: 7px 14px;
      cursor: pointer;
      transition: 0.2s ease-in;

      a {
        display: flex;
        flex-direction: row;
        width: 100%;
        color: #465562;
      }

      &:not(:last-child) {
        margin-bottom: 5px;
      }

      &:hover {
        background-color: $color_white_smoke;
      }
    }
  }
}

.header-menu {
  display: flex;
  flex-direction: row;
  margin: auto 0;
  margin-right: 20px;

  &__item {
    position: relative;
    padding: 1rem;
    margin-right: -1rem;

    &:hover {
      .header-sub-menu ul {
        display: block;
      }
    }

    &__link {
      color: #465562;
      font-weight: bold;
      font-size: 14px;
      padding: 10px 20px;
      transition: 0.3s;

      &:hover {
        opacity: 0.8;
      }
      
    }
    
  }
  .cta-props {
    background-color: #B18E2F;
   }
}

img.arrow-bt {
  margin-left: 2px;
  width: 10px;
  height: 10px;
}

.new {
  color: #fff;
  background-color: #a00;
  padding: 0 4px;
  font-weight: 600;
  border-radius: 2px;
  margin-left: 10px;
}
</style>
