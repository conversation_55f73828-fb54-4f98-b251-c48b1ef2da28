<template>
  <div class="modal-heading">
    <h3 class="modal__title">{{ title }}</h3>
    <TheCloseButton @click="$emit('close')" />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'ModalHeading',
  props: {
    title: {
      type: String,
      required: true,
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.modal-heading {
  display: flex;
  padding: 14px;
  background: $color_white;
  position: relative;
  align-items: center;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.15);

  @include bp('pc') {
    display: none;
  }
}

.modal__title {
  font-weight: 700;
  text-align: center;
  margin-bottom: 0;
  width: 100%;

  @include font-size(16, 22);
}
</style>
