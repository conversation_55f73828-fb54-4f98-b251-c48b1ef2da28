<template>
  <div class="checkbox-list">
    <div v-for="code in items" :key="code" class="checkbox-list__item">
      <BaseCheckbox>
        <template slot="checkField">
          <input
            :id="`check_${code}`"
            v-model="checkedBoxes"
            :value="code"
            type="checkbox"
          />
        </template>
        <template slot="checkLabel">
          <label :for="`check_${code}`">
            {{
              translationKey
                ? $t(translationKey.concat('.').concat(code))
                : code
            }}
          </label>
        </template>
      </BaseCheckbox>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
export default Vue.extend({
  name: 'CheckboxList',
  props: {
    items: {
      type: Array as PropType<string[]>,
      required: true,
    },
    value: {
      type: Array as PropType<string[]>,
      required: true,
    },
    translationKey: {
      type: String,
      default: null,
    },
  },
  computed: {
    checkedBoxes: {
      get(): string[] {
        return this.value
      },
      set(newValue: string[]) {
        this.$emit('input', newValue)
      },
    },
  },
})
</script>

<style lang="scss" scoped>
/* This style has distinction with the other, proceed carefully */
::v-deep .checkmark {
  border: 1px solid #bbb !important;
  border-radius: 2px !important;
}
</style>
