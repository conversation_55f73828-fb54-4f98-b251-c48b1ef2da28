<template>
  <div class="agreement">
    <BaseCheckbox>
      <template slot="checkField">
        <input
          id="agreement"
          v-model="checked"
          type="checkbox"
          name="agreement"
        />
      </template>
      <template slot="checkLabel">
        <label for="agreement">
          <a
            v-if="$i18n.locale === 'kr'"
            class="privacy-link"
            href="https://www.inbound-platform.com/en/privacy/"
            target="_blank"
            >{{ $t('formRent.agreement.privacyPolicy') }}</a
          >
          {{ $t('formRent.agreement.labelPolicy') }}
          <a
            v-if="$i18n.locale !== 'kr'"
            class="privacy-link"
            href="https://www.inbound-platform.com/en/privacy/"
            target="_blank"
            >{{ $t('formRent.agreement.privacyPolicy') }}</a
          >.</label
        >
      </template>
    </BaseCheckbox>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    value: {
      type: <PERSON><PERSON><PERSON>,
      required: true,
    },
  },
  computed: {
    checked: {
      get(): boolean {
        return this.value
      },
      set(newValue: boolean) {
        this.$emit('input', newValue)
      },
    },
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.privacy-link {
  text-decoration: underline;
  color: #223983;
}
</style>
