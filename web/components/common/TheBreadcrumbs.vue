<template>
  <div class="breadcrumbs">
    <ol vocab="http://schema.org/" typeof="BreadcrumbList">
      <li property="itemListElement" typeof="ListItem">
        <nuxt-link property="item" typeof="WebPage" :to="localePath('/')">
          <span property="name">{{ $t('common.top') }}</span>
        </nuxt-link>
        <meta property="position" content="1" />
      </li>
      <li
        v-for="(crumb, index) in crumbs"
        :key="index"
        property="itemListElement"
        typeof="ListItem"
      >
        <nuxt-link
          v-if="crumb.path"
          property="item"
          typeof="WebPage"
          :to="localePath(crumb.path)"
        >
          <span property="name">{{ crumb.title }}</span>
        </nuxt-link>
        <span v-else property="name" class="alone">{{ crumb.title }}</span>
        <meta property="position" :content="index + 2" />
      </li>
    </ol>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import { BreadcrumbsInterface } from '~/types'

export default Vue.extend({
  name: 'TheBreadcrumbs',
  props: {
    title: {
      type: String,
      default: null,
    },
    crumbs: {
      type: Array as PropType<Array<BreadcrumbsInterface>>,
      required: true,
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.breadcrumbs {
  width: 100%;
  margin: 0 auto;
  padding-left: calc-rem(15px);

  @include bp('pc') {
    max-width: calc-rem($width_content_pc_px);
    margin-top: -20px;
  }

  @include bp('sp') {
    padding-left: 20px;
    display: none;
  }
}
ol {
  list-style: none;
  margin: 0;
  padding-bottom: 10px;
  white-space: nowrap;
  max-width: 100%;
  overflow-x: auto;
}
li {
  display: inline;
  font-size: 12px;
  line-height: 18px;

  &::after {
    content: ' > ';
    display: inline;
    font-size: 0.9em;
    color: #aaa;
    padding: 0 0.0725em 0 0.15em;
  }

  &:last-child::after {
    content: '';
  }
}

li a {
  color: #555;
}
li a.nuxt-link-exact-active.nuxt-link-active,
.alone {
  color: #888;
}
</style>
