<template>
  <client-only>
    <portal to="root">
      <div v-show="isOpen" class="overlay">
        <div class="overlay-heading">
          <div class="overlay-heading__inner container">
            <TheCloseButton @click="$emit('close')" />
          </div>
        </div>
        <div class="overlay-content">
          <slot />
        </div>
        <div class="overlay-footer">
          <BaseButton
            type="submit"
            class="btn-filter button--blue button--full"
            @click.prevent="$emit('close')"
          >
            {{ $t('common.filter.buttonDone') }}
          </BaseButton>
        </div>
      </div>
    </portal>
  </client-only>
</template>

<script lang="ts">
/**
 * TODO: Refactor AreaModal to reuse this component
 */
import Vue, { PropType } from 'vue'

export default Vue.extend({
  name: 'ScreenOverlay',
  props: {
    isOpen: {
      type: Boolean as PropType<boolean>,
      required: false,
      default: false,
    },
  },
  watch: {
    isOpen(value) {
      document.body.style.overflow = value ? 'hidden' : 'unset'
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/_bootstraping';

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 998;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;

  &-heading {
    background: #fff;
    // position: sticky;
    z-index: 998;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.15);
    &__inner {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      min-height: 70px;
      .search-filter-close {
        position: relative;
        right: unset;
        top: unset;
        transform: unset;
      }
    }
  }

  &-content {
    flex: 1;
    display: flex;
    background-color: #fff;
    overflow-y: scroll;
    padding: 30px 0;
  }

  &-footer {
    flex-wrap: nowrap;
    justify-content: center;
    padding: 10px;
    max-height: 68px;
    border-top: 1px solid #ddd;
    background: #fff;
    display: flex;
    flex: 1;

    .btn-filter {
      width: 100%;
      height: 48px;
      font-size: 16px;
      max-width: 400px;
      line-height: 48px;
      font-weight: bold;
    }
  }
}
</style>
