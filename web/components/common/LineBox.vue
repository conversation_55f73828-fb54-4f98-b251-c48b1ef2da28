<template>
  <div class="line-box-fixed" :class="{ line_hidden: closeLineBox }">
    <div class="line-box" :class="{ fixed_box: isFixed }">
      <label
        v-if="isButtonClose"
        class="line-modal__close"
        @click="closeLineBox = true"
      ></label>
      <a target="_blank" href="https://lin.ee/Jn8tYbW">
        <img
          src="~/assets/images/line_en.png"
          alt="LINE"
          class="line-pc-only"
        />
      </a>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'LineBox',
  props: {
    isButtonClose: { type: Boolean, default: true },
    isFixed: { type: Boolean, default: true },
  },
  data() {
    return {
      closeLineBox: false,
    }
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/_breakpoint.scss';

.line-box-fixed {
  @include bp('sp') {
    display: none;
  }
}

.fixed_box {
  position: fixed;
  z-index: 9999;
  right: 15px;
  bottom: 15px;
}
.line-modal__close {
  top: -6px;
  z-index: 9;
  right: -6px;
  width: 26px;
  height: 26px;
  border: none;
  opacity: 0.9;
  outline: none;
  display: block;
  cursor: pointer;
  position: absolute;
  border-radius: 50px;
  background-color: #505050;
}
.line-modal__close::after,
.line-modal__close::before {
  top: 5px;
  width: 1px;
  left: 13px;
  content: '';
  height: 16px;
  display: block;
  position: absolute;
  background-color: #fff;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
.line-modal__close::after {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.line-pc-only {
  max-width: 300px;
  max-height: 250px;
}
.line_hidden {
  display: none;
}
</style>
