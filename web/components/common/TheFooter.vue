<template>
  <footer class="footer">
    <div class="container">
      <NavBarFooter class="nav-footer" />
      <div class="footer-bottom">
        <div class="footer-right">
          <Social />
          <locale-switch position="bottom" class="only_pc" />
        </div>
        <div class="copyright">
          <p>
            © 2023 Inbound Platform, Corp. <br />
            R2 事業再構築
            <LazyHydrate never>
              <small class="build" style="display: none">
                v{{ $config.clientVersion }}_{{ $i18n.locale }}_{{ timestamp }}
              </small>
            </LazyHydrate>
          </p>
        </div>
      </div>
    </div>
    <!-- <widget-tabiko /> -->
  </footer>
</template>

<script lang="ts">
import Vue from 'vue'
import LazyHydrate from 'vue-lazy-hydration'
import dayjs from 'dayjs'
import WidgetTabiko from '../tabiko/WidgetTabiko.vue'

export default Vue.extend({
  name: 'TheFooter',
  components: { LazyHydrate, WidgetTabiko },
  computed: {
    timestamp() {
      return dayjs().unix()
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.footer {
  background: $color_gray_light2;

  &-bottom {
    padding: calc-rem(30px) 0 calc-rem(45px);

    @include bp('pc') {
      flex-direction: row-reverse;
      @include flex(null, center, space-between);
    }

    @include bp('sp') {
      padding-bottom: 0;
    }
  }

  &-right {
    @include bp('pc') {
      flex-direction: row-reverse;
      flex: 1;
      @include flex(null, center, flex-start);
    }
  }
}

.copyright {
  p {
    margin-bottom: 0;
    font-size: 12px;
    color: #aaa;
  }

  @include bp('sp') {
    text-align: center;
    padding: calc-rem(40px) 0;
  }
}

.has-stickybar {
  //margin-bottom: calc-rem(110px);

  @include bp('sp') {
    padding-bottom: 64px;
  }
}
</style>

<style lang="scss">
@import '~/assets/scss/tools/bootstraping';

.footer {
  @include bp(sp) {
    padding-bottom: 100px;
  }

  .social {
    justify-content: center;
  }
}

#chat-widget-container {
  display: block !important;
}
</style>
