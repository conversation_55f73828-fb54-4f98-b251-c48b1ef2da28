<template>
  <header class="header" :class="{ withBanner }">
    <header-banner v-if="withBanner" />
    <div class="container">
      <div class="header-wrap">
        <div class="header-logo-wrap">
          <Logo :dark="dark" />
          <img
            class="header-jpx"
            :src="require(`~/assets/images/jpx_logo.png`)"
            data-not-lazy
            alt="JPX"
          />
          <span class="header-jpx-txt">
            Inbound <br class="only-sp" />
            Platform Corp.
            <br />
            Code: 5587
          </span>
        </div>
        <div v-if="displaySocial">
          <div class="only_pc_header d--flex flex--row">
            <header-menu />
          </div>
          <div class="header-sp">
            <input
              v-model="isOpenMenu"
              type="checkbox"
              hidden
              class="nav-header-control"
            />
            <call-agent-button class="header-call-button only_sp_header" />
            <button class="menu-button only_sp_header">
              <span></span>
            </button>
            <NavBar
              class="nav-header only_sp_header"
              @link-clicked="isOpenMenu = false"
            />
            <div :class="{ nav_background: isOpenMenu }"></div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'Header',
  props: {
    dark: {
      required: false,
      default: false,
      type: Boolean,
    },
    withBanner: {
      required: false,
      default: false,
      type: Boolean,
    },
    displaySocial: {
      required: false,
      default: true,
      type: Boolean,
    },
  },
  data() {
    return {
      isOpenMenu: false,
    }
  },
  head() {
    return {
      bodyAttrs: {
        class: (this as any).isOpenMenu ? 'menu-opened' : '',
      },
    }
  },
})
</script>
<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.cta-contact-us {
  position: absolute;
  right: 5rem;
  height: 70px;
  padding: 1.5rem;
  background: #bf9834;
  color: white;
  margin-top: -1.3rem;
}
//Header
.header {
  width: 100%;
  position: relative;
  transition: all 0.2s ease;

  &-call-button {
    margin-right: 30px;
  }

  &:not(.dark) {
    background-color: $color_white;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.15);
  }

  .container {
    background-color: $color_white;

    @include bp(sp) {
      padding: 0.625rem 12px;
    }
  }

  &-wrap {
    @include flex(null, center, space-between);
  }

  &-logo-wrap {
    @include flex(null, center);
  }

  &-jpx-txt {
    padding-left: 4px;
    font-size: 10px;
    line-height: 1;

    @include bp(sp) {
      font-size: 8px;
    }
  }
  &-jpx {
    margin-left: 8px;
    width: 44px;
    height: 44px;

    @include bp(sp) {
      width: 40px;
      height: 40px;
    }
  }

  &-right {
    flex: 1;
  }

  &-logo {
    width: 100%;
    max-width: 160px;
    margin: 0;
  }

  &.dark {
    position: absolute;
    top: 0;
    left: 0;
    .locale-switch__control {
      background-color: $color_white;
      border-color: $color_white;

      &__label {
        color: $color_base;
      }
    }
    .social {
      &-item {
        &-fb {
          a {
            background-image: url('~/assets/images/icons/social_fb_w.svg');
          }
        }

        &-tw {
          a {
            background-image: url('~/assets/images/icons/social_tw_w.svg');
          }
        }

        &-ig {
          a {
            background-image: url('~/assets/images/icons/social_ig_w.svg');
          }
        }
      }
    }
  }

  &.sticky {
    background-color: $color_white;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 98;
    -webkit-box-shadow: 0 calc-rem(1px) calc-rem(4px) rgba($color_base, 0.15);
    box-shadow: 0 calc-rem(1px) calc-rem(4px) rgba($color_base, 0.15);
  }

  @include bp('sp') {
    background-color: $color_white;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 98;
    // height: calc-rem(50px);
    // padding: calc-rem(10px) 0;
    -webkit-box-shadow: 0 calc-rem(1px) calc-rem(4px) rgba($color_base, 0.15);
    box-shadow: 0 calc-rem(1px) calc-rem(4px) rgba($color_base, 0.15);

    &-logo {
      max-width: 100px;
    }
  }

  .menu-button {
    border: none;
    background: none;
    outline: none;
    position: absolute;
    right: calc-rem(1px);
    padding: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    z-index: 8;
    @include size(20px, 16px);

    span {
      background: #c8c9ca;
      //position: relative;
      display: block;
      width: 100%;
      height: calc-rem(2px);
      transition: 0.25s all;

      &::before,
      &::after {
        background: #c8c9ca;
        position: absolute;
        content: '';
        width: 100%;
        height: calc-rem(2px);
        left: 0;
        z-index: 1;
        transition: 0.25s all;
      }

      &::before {
        top: 0;
      }

      &::after {
        bottom: 0;
      }
    }
  }
  .nav-header-control {
    background: transparent;
    border: none;
    position: absolute;
    right: calc-rem(1px);
    top: 50%;
    display: block;
    opacity: 0;
    z-index: 9;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    @include size(20px, 20px);

    &:checked {
      transform: translateY(-52px);

      ~ .menu-button {
        transform: translateY(-52px);

        span {
          background: transparent;

          &::before,
          &::after {
            top: 50%;
            -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
          }

          &::before {
            transform: rotate(45deg);
            @include bp('769px', '1024px') {
              top: 0;
            }
          }

          &::after {
            transform: rotate(-45deg);
            @include bp('769px', '1024px') {
              top: 0;
            }
          }
        }
      }

      ~ .nav-header {
        width: 90%;
        transform: translateX(0);
      }
    }
  }
}
.nav_background {
  background: rgba(0, 0, 0, 0.8);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.withBanner + .site-main {
  padding-top: 158px;

  @include bp(sp) {
    padding-top: calc-rem(128px) !important;
  }
}

.withBanner + .site-main.new-banner {
  padding-top: 70px !important;
  @media (max-width: 768px) {
    padding-top: 50px !important;
  }
}
@include bp(sp) {
  .menu-opened {
    overflow: hidden;
  }
}

.header-sp {
  position: relative;
}
</style>
