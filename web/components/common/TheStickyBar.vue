<template>
  <div class="stickybar">
    <client-only>
      <view-banner :room-code="roomCode" :room-image="roomImage" />
    </client-only>
    <div class="stickybar-inner only_sp">
      <nuxt-link
        class="base-button ripple"
        :to="localePath(`${inquiryUrl}&purposes=check_availability`)"
      >
        <div class="inner">{{ $t('roomDetail.button.askAvailability') }}</div>
      </nuxt-link>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'TheStickyBar',
  props: {
    roomImage: {
      required: true,
      type: String,
    },
    roomCode: {
      required: true,
      type: String,
    },
  },
  computed: {
    inquiryUrl(): string {
      return `/inquiry?rent=${this.roomCode}`
    },
  },
})
</script>

<style scoped lang="scss">
@import 'assets/scss/tools/bootstraping';

.stickybar {
  left: 0;
  bottom: 0;
  width: 100%;
  position: fixed;
  padding: 20px 20px;
  background: transparent;

  @include bp('pc') {
    left: unset;
    max-width: 420px;
    right: 0;
  }

  @include bp('sp') {
    padding: 10px 20px;
  }

  @include bp(0, 320px) {
    padding: 10px 5px;
  }

  &-inner {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ripple {
    box-sizing: border-box;
    position: relative;

    &::before {
      z-index: -1;
      animation: ripple 1.5s ease-out infinite;
      background-color: #acc2ea;
      border-radius: 1em;
      bottom: 0;
      box-sizing: border-box;
      content: '';
      left: 0;
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  @keyframes ripple {
    0% {
      opacity: 1;
    }
    100% {
      border-radius: 2em;
      opacity: 0;
      transform: scale(2);
    }
  }

  .base-button {
    width: 100%;
    background: transparent;
    border: 0;
    border-radius: 0;
    color: inherit;
    display: inline-block;
    font: inherit;
    height: auto;
    line-height: 1;
    margin: 0;
    padding: 0;
    position: relative;
    text-align: center;
    text-decoration: none;
    user-select: none;

    .inner {
      font-family: $font_title;
      font-size: 14px;
      font-weight: bold;
      align-items: center;
      background: #2471cb;
      border: 2px solid transparent;
      border-radius: 99em;
      color: #fff;
      height: 48px;
      line-height: 48px;
      min-width: 2.5em;
      position: relative;
      transition: 0.2s ease;
      width: 100%;
    }
  }
}
</style>
