<template>
  <div class="rent-property">
    <div class="rent-heading">
      <h2 class="rent-heading-title">{{ titleHeading }}</h2>
    </div>
    <div class="rent-wrap">
      <div class="rent-list">
        <template v-if="loading">
          <PropertyLoadingCard v-for="loadingIndex in 4" :key="loadingIndex" />
        </template>
        <PropertyCard
          v-for="(property, index) in properties"
          v-else
          :key="index"
          :property="property"
          :ranking="isRanking ? index + 1 : 0"
        />
      </div>
    </div>
    <div v-if="showAllButton" class="rent-button">
      <BaseButton to="/rent/tokyo" class="button--blue">
        {{ $t('home.allPropertyText') }}
      </BaseButton>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import get from 'lodash/get'
import { Property } from '~/types/types'

export default Vue.extend({
  name: 'RentGrid',
  props: {
    titleHeading: {
      type: String,
      required: false,
      default: null,
    },
    moreLink: {
      type: String,
      required: false,
      default: null,
    },
    properties: {
      type: Array as PropType<Array<Property>>,
      required: true,
    },
    isRanking: {
      type: Boolean,
      required: false,
      default: false,
    },
    loading: {
      type: Boolean,
      required: false,
      default: false,
    },
    showAllButton: {
      type: Boolean,
      required: false,
      default: true,
    },
  },
  methods: {
    get,
  },
})
</script>

<style lang="scss">
@import '~/assets/scss/tools/bootstraping';
.rent {
  &-property {
    margin-top: 60px;

    @include bp('pc') {
      &:first-child {
        margin-top: calc-rem(40px);
      }
    }

    @include bp('sp') {
      margin-top: calc-rem(40px);

      &:not(:last-child) {
        margin-bottom: calc-rem(60px);
      }
    }
  }

  &-heading {
    margin-bottom: 10px;
    @include flex(null, center, space-between);

    &-title {
      margin: 0;
      font-size: 22px;
      line-height: 1.5;
    }
  }

  &-all {
    color: $color_green;
    font-weight: 600;
    font-size: 16px;
    line-height: calc-rem(24px);
  }

  &-wrap {
    @include bp('sp') {
      overflow: auto;
      margin: 0 -20px;
      padding-left: 20px;
    }
  }

  &-list {
    margin: 0 calc-rem(-7px);
    @include flex();

    @include bp('sp') {
      margin: 0 calc-rem(-7px);
      flex-wrap: nowrap;
      justify-content: flex-start;
    }
  }

  &-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

    a {
      width: 300px;
      margin: auto;
      box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.2);

      &:hover {
        box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.4);
      }

      @include bp(sp) {
        width: 100%;
        box-shadow: none;

        &:hover {
          box-shadow: none;
        }
      }
    }
  }
}
</style>
