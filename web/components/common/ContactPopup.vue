<template>
  <client-only>
    <div class="contact-popup__overlay" :class="{ showOverlay: true }">
      <div class="contact-popup__content">
        <a
          href="javascript:void(0)"
          class="close"
          title="close"
          @click="$accessor.showContactPopup(false)"
        >
          <img src="~/assets/images/contact/close.png" alt="close" />
        </a>
        <h2 style="text-transform: uppercase">
          {{ $t('contactPopup.title') }}
        </h2>
        <p>
          {{ $t('contactPopup.description') }}
        </p>
        <h2>{{ $t('contactPopup.mailTitle') }}</h2>
        <div class="contact-popup__content__buttons">
          <a
            v-for="button in mailButtons"
            :key="button.text"
            :href="localePath(button.path)"
            class="contact-button"
          >
            <span>{{ button.text }} </span>
            <div>
              <img src="~/assets/images/contact/next.png" />
            </div>
          </a>
        </div>
        <h2>{{ $t('contactPopup.phoneTitle') }}</h2>
        <a href="tel:03-4579-2103">
          <div class="phone-number">
            <img src="~/assets/images/contact/call.png" />
            <strong>03-4579-2103</strong>
          </div>
        </a>
        <p>{{ $t('contactPopup.businessHours') }}</p>
      </div>
    </div>
  </client-only>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  data() {
    return {
      visible: true,
      mailButtons: [
        { text: this.$t('contactPopup.mailButtons.rent'), path: '/form-rent' },
        { text: this.$t('contactPopup.mailButtons.buy'), path: '/form-buy' },
        // { text: this.$t('contactPopup.mailButtons.sell'), path: '/form-sell' },
      ],
    }
  },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.contact-popup {
  &__overlay {
    display: none;
    z-index: 99;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    transition: 0.3s;
  }

  &__content {
    position: relative;
    max-height: 540px;
    border-radius: 4px;
    background-color: #fff;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: stretch;
    padding: 80px;
    color: #333;

    @include bp(sp) {
      padding: 20px;
    }

    h2 {
      font-size: 20px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 1.5em;
      margin-bottom: 10px;

      &:not(:first-of-type) {
        margin-top: 40px;
        margin-bottom: 10px;
      }
    }

    p {
      font-size: 14px;
      text-align: center;
      letter-spacing: 0;
      line-height: 19px;
      color: #333;
      margin: 0;
    }

    @include bp(sp) {
      margin: 0 20px;
    }
  }
}

.showOverlay {
  display: flex;
  align-items: center;
  justify-content: center;
}

.close {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 15px;

  @include bp(sp) {
    top: 0;
    right: 0;
  }

  img {
    width: 16px;
    height: 16px;
  }
}

.phone-number {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
  padding: 8px 24px;
  border: 2px solid #bf9834;
  border-radius: 4px;
  transition: 0.3s;

  // iphone 5s & SE
  @include bp(0, '321px') {
    padding: 2px 4px;
  }

  &:hover {
    opacity: 0.8;
  }

  img {
    width: 30px;
    height: 30px;
    margin-right: 14px;
  }

  strong {
    color: #bf9834;
    font-size: 28px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 38px;
    text-align: center;
  }
}

.contact-popup__content__buttons {
  display: flex;
  flex-direction: row;

  @include bp(sp) {
    flex-direction: column;
    align-items: center;
    justify-content: stretch;
    width: 100%;
  }

  .contact-button {
    width: 200px;
    border-radius: 4px;
    background: linear-gradient(176.19deg, #d4ac45, #bf9834);
    position: relative;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;

    @include bp(sp) {
      width: 100%;
      height: 48px;
    }

    &:not(:last-child) {
      margin-right: 20px;

      @include bp(sp) {
        margin-right: unset;
        margin-bottom: 10px;
      }
    }

    span {
      color: #fff;
      font-size: 18px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 24px;
      text-align: center;
    }

    div {
      position: absolute;
      right: 15px;
      height: 24px;
      width: 24px;
      background-color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 6px;
        height: 10px;
      }
    }
  }
}
</style>
