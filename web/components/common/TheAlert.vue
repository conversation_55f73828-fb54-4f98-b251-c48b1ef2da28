<template>
  <div v-if="!dismissed" class="alert" :class="{ 'dismiss-able': dismissAble }">
    <button
      v-if="dismissAble"
      class="dismiss-button"
      @click.prevent="onDismiss"
    >
      x
    </button>
    {{ content }}
    <slot></slot>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'TheAlert',
  props: {
    content: {
      type: String,
      required: false,
      default: null,
    },
    dismissAble: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      dismissed: false as boolean,
    }
  },
  methods: {
    onDismiss(): void {
      this.dismissed = true
      this.$emit('dismiss')
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.alert {
  font-size: 12px;
  word-break: break-word;
  padding: 1em;
  line-height: 1.5em;
}

.alert--danger {
  background: $color_white_smoke;
  color: $color_red1;
}

.dismiss-able {
  position: relative;
  padding-top: calc-rem(25px);
  padding-right: calc-rem(25px);
}

.dismiss-button {
  top: 0;
  right: 0;
  cursor: pointer;
  position: absolute;
  line-height: 1;
  padding: calc-rem(10px);
  border: none;
  font-size: calc-rem(16px);
  background: none;
}
</style>
