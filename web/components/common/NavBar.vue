<template>
  <ul class="navbar">
    <li class="navbar-menu-header">
      <img
        src="~/assets/images/logo_green.png"
        data-not-lazy
        alt="Living Japan"
        class="navbar-menu-header-img"
      />
      <a class="close-btn" @click="linkClicked">
        <img width="16" height="16" src="~/assets/images/contact/close.png" />
      </a>
    </li>
    <li>
      <span class="nav-menu nav-action">Property</span>
    </li>
    <li>
      <a
        class="nav-menu nav-child"
        to="#"
        @click="rentSubmenuOpen = !rentSubmenuOpen"
      >
        For Rent
        <img src="~/assets/images/icons/ic_arrow_bt.svg" class="arrow-bt" />
      </a>
      <ul v-if="rentSubmenuOpen">
        <li v-for="prefecture in enabledPrefectures" :key="prefecture">
          <nuxt-link
            :to="localePath(`/rent/${prefecture}`)"
            class="submenu nav-menu nav-child"
          >
            {{ $t(`common.subHeaderMenu.rent-${prefecture}`) }}
          </nuxt-link>
        </li>
      </ul>
    </li>
    <li>
      <a
        class="nav-menu nav-child"
        to="#"
        @click="buySubmenuOpen = !buySubmenuOpen"
      >
        For Buy
        <img src="~/assets/images/icons/ic_arrow_bt.svg" class="arrow-bt" />
      </a>
      <ul v-if="buySubmenuOpen">
        <li>
          <nuxt-link class="nav-menu nav-child" :to="localePath('/form-buy')">
            Inquiry for Buy
          </nuxt-link>
        </li>
        <li>
          <nuxt-link class="nav-menu nav-child" :to="localePath('/tour')">
            VIP Tour
            <small class="new">NEW</small>
          </nuxt-link>
        </li>
      </ul>
    </li>
    <li>
      <!-- <nuxt-link class="nav-menu nav-child" :to="localePath('/form-sell')">
        For Sell
      </nuxt-link> -->
    </li>
    <li>
      <span class="nav-menu nav-action"> {{ $t('common.nav.section3') }} </span>
    </li>
    <li>
      <nuxt-link class="nav-menu nav-child" :to="localePath('/access')">
        Our office
      </nuxt-link>
    </li>
    <li>
      <a class="nav-menu nav-child" @click="contactClicked">
        {{ $t('common.nav.item5') }}
      </a>
    </li>
    <li>
      <span class="nav-menu nav-action"> {{ $t('roomDetail.language') }} </span>
    </li>
    <li
      v-for="locale in $i18n.locales"
      :key="locale.iso"
      :class="{ active: locale.code === $i18n.locale }"
    >
      <a
        :href="switchLocalePath(locale.code)"
        class="nav-menu nav-child lang"
        @click.native="linkClicked"
      >
        {{ locale.name }}
      </a>
    </li>
  </ul>
</template>

<script lang="ts">
import Vue from 'vue'
import { enabledPrefectures } from '~/constants/prefectures'

export default Vue.extend({
  name: 'Navbar',
  data: () => ({
    rentSubmenuOpen: false,
    enabledPrefectures,
    buySubmenuOpen: false,
  }),
  methods: {
    contactClicked() {
      this.linkClicked()
      this.$accessor.showContactPopup(true)
    },
    linkClicked() {
      this.$emit('link-clicked')
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.navbar {
  &.nav-header {
    background-color: $color_white;
    right: 0;
    margin: 0;
    top: 0;
    padding: 0;
    width: 100%;
    z-index: 1;
    min-height: 100vh;
    min-height: -webkit-fill-available;
    height: 100%;
    overflow: hidden;
    overflow-y: scroll;
    position: fixed;
    transform: translateX(100%);
    transition: all 0.15s ease;

    li {
      width: 100%;

      .nav-menu {
        color: $color_base;
        font-size: calc-rem(14px);
        line-height: calc-rem(22px);
        padding: 15px 20px;
        display: block;

        &.lang {
          position: relative;
          padding-left: 50px !important;
        }

        .flag-image {
          width: 20px;
          aspect-ratio: 1;
          margin-right: 10px;
        }
      }

      .nav-action {
        font-weight: bold;
        background-color: #f3f7f7;
      }
    }
  }
}

.img_line_only {
  width: 100% !important;
}

.navbar-menu-header {
  display: flex;
  align-items: center;
  width: 100% !important;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-bottom: 1px solid #f3f7f7;
}

.navbar-menu-header-img {
  display: block;
  height: 35px;
  width: 130px;
  margin: 8px auto;
}

.active {
  a::before {
    color: #18af94;
    font-weight: bold;
    margin-left: -25px;
    margin-right: 10px;
    display: inline-block;
    content: 'L';
    width: 12px;
    height: 12px;
    transform: scaleX(-1) rotate(-45deg);
  }
}

img.arrow-bt {
  margin-left: 4px;
  width: 10px;
  height: 10px;
}

.new {
  color: #fff;
  background-color: #a00;
  padding: 2px 4px;
  font-weight: 600;
  border-radius: 2px;
  margin-left: 8px;
}

.close-btn {
  position: absolute;
  right: 10px;
}
</style>
