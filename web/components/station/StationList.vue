<template>
  <div class="container d--flex flex--column">
    <h1 v-if="showTitle" class="page-heading flex aic">
      <img
        src="~/assets/images/icons/ic_subway.svg"
        alt=""
        width="24"
        height="24"
      />
      <span>{{ $t('station.pageTitle') }}</span>
    </h1>
    <spinner v-if="loading" />
    <div v-else class="search_form d--flex flex--column" style="flex: 1">
      <div
        v-for="company in linesData"
        :key="company['en-us']"
        class="search_form__group flex"
      >
        <h2 class="search_form__group__name">
          {{ getCompanyLocaleName(company) }}
        </h2>
        <ul class="search_form__group__list flex fw">
          <li
            v-for="line in company.lines"
            :key="line._id"
            class="search_form__group__list-item"
          >
            <div
              class="search-item__heading"
              :class="{ opened: lineOpened.includes(line._id) }"
            >
              <BaseCheckbox class="search_form__checkbox" :full-label="true">
                <template slot="checkField">
                  <input
                    type="checkbox"
                    :checked="groupChecked.includes(line._id)"
                    @change="onSelectGroup(line._id)"
                  />
                </template>
                <template slot="checkLabel">
                  <label
                    class="heading-label"
                    @click.stop="toggleSubContent(line._id)"
                  >
                    <i class="line-color" :style="getLineStyle(line.color)"></i
                    >{{ line.localeName }}</label
                  >
                </template>
              </BaseCheckbox>
            </div>
            <ul
              class="search-item__content fw"
              :class="{ opened: lineOpened.includes(line._id) }"
            >
              <li
                v-for="stationId in line.stationIds"
                :key="stationId"
                class="station-item"
              >
                <BaseCheckbox class="search_form__checkbox">
                  <template slot="checkField">
                    <input
                      :id="`station_${stationId}`"
                      type="checkbox"
                      :checked="value.includes(stationId)"
                      @change="onSelect(stationId)"
                    />
                  </template>
                  <template slot="checkLabel">
                    <label :for="`station_${stationId}`">
                      {{ get($accessor, `stationNames.${stationId}`, '_') }}
                    </label>
                  </template>
                </BaseCheckbox>
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import get from 'lodash/get'
import uniq from 'lodash/uniq'
import difference from 'lodash/difference'
import { ServiceLine } from '~/types/types'
import { addOrRemoveItem } from '~/common/utils'
// FIXME: move these data into database?
import serviceCompanies from '~/common/stations.json'

interface LineDetail extends ServiceLine {
  color: string
  code: string | number
}

type ServiceCompany = Omit<typeof serviceCompanies[0], 'lines'> & {
  lines: LineDetail[]
}

export default Vue.extend({
  name: 'StationList',
  props: {
    showTitle: {
      type: Boolean,
      default: () => true,
    },
    value: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
  },
  data: () => ({
    lineOpened: [] as string[],
    loading: false,
  }),

  async fetch() {
    if (this.$accessor.haveLineStation) return
    this.loading = true
    await this.$accessor.fetchStationLines()
    this.loading = false
  },
  computed: {
    groupChecked(): string[] {
      const checkGroup = [] as string[]
      if (this.value.length > 0) {
        Object.keys(this.$accessor.lines).forEach((id) => {
          const item = this.$accessor.lines[id]
          const groupItems = this.getGroupItems(item._id)
          const allCheck = groupItems.every((item) => this.value.includes(item))
          allCheck && checkGroup.push(item._id)
        })
      }
      return checkGroup
    },
    linesData(): ServiceCompany[] {
      if (!this.$accessor.haveLineStation) return []
      const lines = this.$accessor.lines
      return serviceCompanies.map((company) => ({
        ...company,
        lines: company.lines.map((line) => ({
          ...line,
          ...lines[line.code],
        })),
      }))
    },
  },
  methods: {
    get,
    getCompanyLocaleName(company: ServiceCompany): string {
      const locale = this.$i18n.localeProperties.iso?.toLowerCase() as
        | 'en-us'
        | 'zh-tw'
        | 'zh-cn'
      return company[locale] || '▢'
    },
    onSelectGroup(id: string) {
      const items = this.getGroupItems(id)
      const isIncluded = this.groupChecked.includes(id)
      if (isIncluded) {
        this.lineOpened = this.lineOpened.filter((item) => item !== id)
      }
      const newValue = isIncluded
        ? difference(this.value, items)
        : uniq([...this.value, ...items])
      this.$emit('input', newValue)
    },
    onSelect(id: string) {
      const newValue = addOrRemoveItem<string>([...this.value], id)

      this.$emit('input', newValue)
    },
    toggleSubContent(id: string) {
      this.lineOpened = addOrRemoveItem<string>([...this.lineOpened], id)
    },
    getGroupItems(id: string): string[] {
      const items = this.$accessor.lines[id]
      return items?.stationIds?.map((stationId) => stationId || '') || []
    },
    getLineStyle(color: string) {
      return color ? { 'background-color': color } : {}
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/_bootstraping';

.page-heading {
  font-size: 18px;

  span {
    padding-left: 10px;
    font-family: Arial, sans-serif;
    color: #4e5a62;
  }

  img {
    width: 20px;
    filter: invert(33%) sepia(10%) saturate(669%) hue-rotate(161deg)
      brightness(97%) contrast(88%);
  }
}

.search_form {
  margin-left: -20px;
  margin-right: -20px;

  @include bp(700px, 0) {
    margin-left: 0;
    margin-right: 0;
  }
}

.search_form__group {
  border-top: 1px solid #cbdae7;
  border-bottom: 1px solid #cbdae7;
  flex-wrap: wrap;

  @include bp(700px, 0) {
    flex-wrap: nowrap;
  }

  + .search_form__group {
    border-top: none;
  }
}

.search_form__group__name {
  width: 100%;
  padding: 14px 20px;
  font-weight: 700;
  font-size: 18px;
  background: #f0f5fa;
  margin-bottom: 0;
  font-family: Arial, sans-serif;

  @include bp(700px, 0) {
    min-width: 200px;
    width: 280px;
  }

  @include bp(960px, 0) {
    min-width: 280px;
    width: 280px;
  }

  @media (max-width: 768px) {
    border-bottom: 1px solid #cbdae7;
  }
}
.search_form__group__list {
  margin-left: 0;
  margin-bottom: 0;
  flex: 1 1 auto;
}

.search_form__checkbox {
  padding: 10px 20px;
}

.btn-clear {
  margin-top: 20px;
  margin-bottom: 30px;
}

.form-button {
  bottom: 0;
  width: 100%;
  position: sticky;
  background: #fff;
  padding: 10px 20px;
  border-top: 1px solid #cbdae7;
  border-bottom: 1px solid #cbdae7;
}

.button--blue {
  width: 100%;
  max-width: 400px;
  font-size: 16px;
  height: 48px;
  line-height: 48px;

  &[disabled] {
    opacity: 0.4;
  }

  @media (max-width: 768px) {
    max-width: 100%;
  }
}

.search-item__content {
  display: none;

  &.opened {
    display: flex;
  }
}

.station-item {
  width: 100%;

  @include bp(700px, 0) {
    width: 33.33%;
  }
}

.search-item__heading {
  position: relative;
  font-weight: 700;

  &::after {
    content: '';
    width: 10px;
    height: 16px;
    position: absolute;
    right: 10px;
    top: 50%;
    background-color: #ccc;
    background-repeat: no-repeat;
    transition: all ease 0.25s;
    transform: translateY(-50%) rotate(-90deg);
    mask-image: url('~/assets/images/icons/ic-left.svg');
  }

  &.opened {
    &::after {
      transform: translateY(-50%) rotate(90deg);
    }
  }

  &:hover,
  &.opened {
    background: rgba(37, 173, 180, 0.1);
  }
}

.search_form__group__list-item {
  width: 100%;

  &:not(:first-child) {
    .search-item__heading {
      border-top: 1px solid #cbdae7;
    }
  }
}

.heading-label {
  width: 100%;
  cursor: pointer;
  padding-right: 15px;
  display: inline-flex;
  align-items: center;
}

.heading-label,
.inquiry-property-checkbox {
  font-family: Arial, sans-serif;
}

.line-color {
  display: inline-block;
  width: 6px;
  height: 32px;
  margin-right: 10px;
  border-radius: 6px;
}
</style>

<style lang="scss">
.search_form__checkbox {
  &:hover .checkmark {
    box-shadow: 0 0 0 0.2em rgb(37 173 180 / 50%);
  }

  .inquiry-property-checkbox .checkmark {
    border: 1.2px solid #cbdae7 !important;
  }

  .inquiry-property-checkbox input:checked ~ .checkmark {
    background-color: #25adb4 !important;
    border-color: #1ba3aa !important;
  }

  .inquiry-property-checkbox .checkmark::after {
    border-color: #fff !important;
  }
}

.search-item__heading {
  .search_form__checkbox {
    &:hover .inquiry-property-checkbox .checkmark {
      box-shadow: none;
    }
  }
}
</style>
