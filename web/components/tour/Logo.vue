<template>
  <div class="header-logo">
    <nuxt-link :to="localePath('/tour')" class="logo">
      <img
        :src="require(`~/assets/images/logo_tour.png`)"
        data-not-lazy
        alt="Living Japan"
        class="only_pc"
      />
      <img
        src="~/assets/images/logo_tour.png"
        data-not-lazy
        alt="Living Japan"
        class="only_sp"
      />
    </nuxt-link>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'Logo',
  props: {
    dark: {
      required: false,
      default: false,
      type: <PERSON>olean,
    },
  },
})
</script>

<style scoped lang="scss">
.logo {
  margin: 0;
}
</style>
