<template>
  <div class="section-tour">
    <TitleSection :title="$t(`tour.tourInformation.title`)" />
    <div class="tour-info">
      <div class="tour-info__content">
        <div class="content__subtitle">
          <p>{{ $t(`tour.tourInformation.subTitle`) }}</p>
        </div>
        <div class="content__desc">
          <p>
            {{ $t(`tour.tourInformation.description`) }}
          </p>
        </div>
        <div class="content__contact">
          <nuxt-link :to="localePath('/tour/inquiry')">{{
            $t(`tour.tourInformation.button`)
          }}</nuxt-link>
        </div>
      </div>
      <div class="tour-info__img">
        <img
          src="~/assets/images/tour/tour-info.png"
          width="500"
          height="300"
          alt="tour information"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import TitleSection from '~/components/tour/TitleSection.vue'

export default Vue.extend({
  name: 'TourInformation',
  components: { TitleSection },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/_bootstraping';

.section-tour {
  margin-top: 60px;
}

.tour-info {
  display: flex;
  justify-content: space-between;
  color: #3d455e;

  @include bp(sp) {
    flex-direction: column-reverse;
  }

  &__content {
    width: 50%;

    @include bp(sp) {
      width: 100%;
    }

    .content__subtitle {
      p {
        letter-spacing: 0;
        font-size: 32px;
        font-weight: bold;
        line-height: 1.2em;

        @include bp(sp) {
          font-size: 28px;
          margin-top: 20px;
        }
      }
    }

    .content__desc {
      margin-top: 20px;
      margin-bottom: 40px;

      @include bp(sp) {
        margin-bottom: 20px;
      }

      p {
        color: #555;
        font-size: 16px;
        line-height: 1.5em;
        letter-spacing: 0;
      }
    }

    .content__contact {
      @include bp(sp) {
        display: flex;
        align-items: center;
      }

      a {
        padding: 22px 58px;
        color: #3d455e;
        font-size: 16px;
        letter-spacing: 0;
        line-height: 24px;
        border: 1px solid #3d455e;
        font-weight: bold;

        @include bp(sp) {
          width: 100%;
          text-align: center;
        }

        &:hover {
          background: #3d455e;
          color: #fff;
          transition: all 0.2s ease-in;
        }
      }
    }
  }

  &__img {
    @include bp(sp) {
      margin: 0 auto;
    }
  }
}
</style>
