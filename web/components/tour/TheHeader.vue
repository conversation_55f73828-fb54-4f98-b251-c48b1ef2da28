<template>
  <header class="header" :class="{ withBanner }">
    <header-banner v-if="withBanner" />
    <div class="container">
      <div class="header-wrap">
        <Logo :dark="dark" />
        <div v-if="displaySocial">
          <div class="pc_tour d--flex flex--row align__items--center">
            <HeaderMenu />
            <LocaleSwitch :icon-white="true" />
          </div>
          <div class="header-sp">
            <input
              v-model="isOpenMenu"
              type="checkbox"
              hidden
              class="nav-header-control"
            />
            <button class="menu-button sp_tour" @click="handleMenu">
              <span></span>
            </button>
            <NavBar
              class="nav-header sp_tour"
              @link-clicked="isOpenMenu = false"
            />
            <div :class="{ nav_background: isOpenMenu }"></div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script lang="ts">
import Vue from 'vue'
import Logo from '~/components/tour/Logo.vue'
import HeaderMenu from '~/components/tour/HeaderMenu.vue'
import NavBar from '~/components/tour/NavBar.vue'
import LocaleSwitch from '~/components/tour/LocaleSwitch.vue'

export default Vue.extend({
  name: 'Header',
  components: { Logo, HeaderMenu, NavBar, LocaleSwitch },
  props: {
    dark: {
      required: false,
      default: false,
      type: Boolean,
    },
    withBanner: {
      required: false,
      default: false,
      type: Boolean,
    },
    displaySocial: {
      required: false,
      default: true,
      type: Boolean,
    },
  },
  data() {
    return {
      isOpenMenu: false,
    }
  },
  head() {
    return {
      bodyAttrs: {
        class: (this as any).isOpenMenu ? 'menu-opened' : '',
      },
    }
  },
  methods: {
    handleMenu() {
      this.isOpenMenu = !this.isOpenMenu
    },
  },
})
</script>
<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

//Header
.header {
  width: 100%;
  position: relative;
  transition: all 0.2s ease;

  &-call-button {
    margin-right: 36px;
  }

  &:not(.dark) {
    background-color: $color_blue_dark;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.15);
  }

  .container {
    padding-top: 15px;
    padding-bottom: 15px;
    background-color: $color_blue_dark;

    @include bp(sp) {
      padding: 0.625rem 20px;
    }
  }

  &-wrap {
    @include flex(null, center, space-between);
  }

  &-right {
    flex: 1;
  }

  &-logo {
    width: 100%;
    max-width: 160px;
    margin: 0;
  }

  &.dark {
    position: absolute;
    top: 0;
    left: 0;

    .social {
      &-item {
        &-fb {
          a {
            background-image: url('~/assets/images/icons/social_fb_w.svg');
          }
        }

        &-tw {
          a {
            background-image: url('~/assets/images/icons/social_tw_w.svg');
          }
        }

        &-ig {
          a {
            background-image: url('~/assets/images/icons/social_ig_w.svg');
          }
        }
      }
    }
  }

  &.sticky {
    background-color: $color_blue_dark;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 98;
    -webkit-box-shadow: 0 calc-rem(1px) calc-rem(4px) rgba($color_base, 0.15);
    box-shadow: 0 calc-rem(1px) calc-rem(4px) rgba($color_base, 0.15);
  }

  @include bp('sp') {
    background-color: $color_blue_dark;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 98;
    -webkit-box-shadow: 0 calc-rem(1px) calc-rem(4px) rgba($color_base, 0.15);
    box-shadow: 0 calc-rem(1px) calc-rem(4px) rgba($color_base, 0.15);

    &-logo {
      max-width: 130px;
    }
  }

  .menu-button {
    border: none;
    background: none;
    outline: none;
    position: absolute;
    right: calc-rem(1px);
    padding: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    z-index: 8;
    @include size(20px, 16px);

    span {
      background: #c8c9ca;
      //position: relative;
      display: block;
      width: 100%;
      height: calc-rem(2px);
      transition: 0.25s all;

      &::before,
      &::after {
        background: #c8c9ca;
        position: absolute;
        content: '';
        width: 100%;
        height: calc-rem(2px);
        left: 0;
        z-index: 1;
        transition: 0.25s all;
      }

      &::before {
        top: 0;
      }

      &::after {
        bottom: 0;
      }
    }
  }
  .nav-header-control {
    background: transparent;
    border: none;
    position: absolute;
    right: calc-rem(1px);
    top: 50%;
    display: block;
    opacity: 0;
    z-index: 9;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    @include size(20px, 20px);

    &:checked {
      transform: translateY(-65px);
      ~ .menu-button {
        span {
          background: transparent;

          &::before,
          &::after {
            top: 50%;
            -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
          }

          &::before {
            transform: rotate(45deg);
          }

          &::after {
            transform: rotate(-45deg);
          }
        }
      }

      ~ .nav-header {
        width: 90%;
        transform: translateX(0);
      }
    }
  }
}
.nav_background {
  background: rgba(0, 0, 0, 0.8);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>
<style lang="scss">
@import '~/assets/scss/tools/bootstraping';

.header {
  &.dark {
    .locale-switch__control {
      background-color: $color_white;
      border-color: $color_white;

      &__label {
        color: $color_base;
      }
    }
    .social {
      &-item {
        &-fb {
          a {
            background-image: url('~/assets/images/icons/social_fb_w.svg');
          }
        }
        &-tw {
          a {
            background-image: url('~/assets/images/icons/social_tw_w.svg');
          }
        }
        &-ig {
          a {
            background-image: url('~/assets/images/icons/social_ig_w.svg');
          }
        }
      }
    }
  }
}

@include bp(sp) {
  .menu-opened {
    overflow: hidden;
  }
}

.header-sp {
  position: relative;
}
</style>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.withBanner + .site-main {
  padding-top: 158px;

  @include bp(sp) {
    padding-top: calc-rem(128px) !important;
  }
}
</style>
