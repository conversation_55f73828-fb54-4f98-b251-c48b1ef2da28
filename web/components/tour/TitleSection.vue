<template>
  <div class="title">
    <h2>{{ title }}</h2>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'TitleSection',
  props: {
    title: {
      required: false,
      default: '',
      type: String,
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/_bootstraping';

.title {
  margin-bottom: 40px;

  h2 {
    color: #3d455e;
    font-size: 32px;
    letter-spacing: 0;
    line-height: 36px;
    text-align: center;
    position: relative;
    padding-bottom: 15px;

    @include bp(sp) {
      font-size: 24px;
    }

    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 3px;
      background: #c29b40;
    }
  }
}
</style>
