<template>
  <div class="banner__container">
    <div class="banner__image">
      <picture>
        <source media="(max-width: 768px)" :srcset="bannerSp" />
        <img :src="bannerPc" alt="banner tour" />
      </picture>
      <div class="banner__image__btn">
        <nuxt-link to="/tour/inquiry" class="header-menu__item__inquire">
          {{ $t(`tour.menuItem.inquiryNow`) }}
        </nuxt-link>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'HomeBannerV2',
  data() {
    return {
      bannerPc: require('~/assets/images/tour/banner_pc_en.png'),
      bannerSp: require('~/assets/images/tour/banner_sp_en.png'),
    }
  },
  created() {
    switch (this.$route?.name) {
      case 'tour___jp':
        this.bannerPc = require('~/assets/images/tour/banner_pc_jp.png')
        this.bannerSp = require('~/assets/images/tour/banner_sp_jp.png')
        break
      case 'tour___kr':
        this.bannerPc = require('~/assets/images/tour/banner_pc_kr.png')
        this.bannerSp = require('~/assets/images/tour/banner_sp_kr.png')
        break
      case 'tour___zh-tw':
        this.bannerPc = require('~/assets/images/tour/banner_pc_zhtw.png')
        this.bannerSp = require('~/assets/images/tour/banner_sp_zhtw.png')
        break
      case 'tour___zh':
        this.bannerPc = require('~/assets/images/tour/banner_pc_zh.png')
        this.bannerSp = require('~/assets/images/tour/banner_sp_zh.png')
        break
      default:
        this.bannerPc = require('~/assets/images/tour/banner_pc_en.png')
        this.bannerSp = require('~/assets/images/tour/banner_sp_en.png')
    }
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.banner {
  &__image {
    position: relative;

    img {
      width: 100%;
      background-repeat: no-repeat;
      background-position: center;
    }

    &__btn {
      position: absolute;
      left: 50%;
      top: 70%;
      transform: translateX(-50%);

      a {
        font-size: 20px;
        letter-spacing: 0;
        line-height: 24px;
        color: #fff;
        padding: 14px 50px;
        border: 1px solid #c29b40;
        border-radius: 3px;
        background-color: #c29b40;
        font-weight: bold;
        box-shadow: 0 4px 12px 0 rgb(0 0 0 / 40%);
        white-space: nowrap;

        &:hover {
          opacity: 0.8;
        }
      }

      @include bp(pc) {
        display: none;
      }
    }
  }
}
</style>
