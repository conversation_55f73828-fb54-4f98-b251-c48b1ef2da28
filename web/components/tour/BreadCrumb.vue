<template>
  <div class="breadcrumb__list">
    <div v-for="item in breadCrumbs" :key="item.title" class="breadcrumb__item">
      <NuxtLink :to="localePath(item.path)">
        {{ item.title }}
      </NuxtLink>
      <div
        v-if="item !== breadCrumbs[breadCrumbs.length - 1]"
        class="breadcrumb__separator"
      >
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import { BreadcrumbsInterface } from '~/types'

export default Vue.extend({
  name: 'BreadCrumb',
  props: {
    breadCrumbs: {
      required: true,
      type: Array as PropType<Array<BreadcrumbsInterface>>,
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.breadcrumb {
  &__item,
  &__list {
    align-items: center;
    display: flex;
    flex-wrap: wrap;

    .nuxt-link-active,
    a {
      color: #555;
      font-size: 14px;
      letter-spacing: 0;
      line-height: 14px;

      @include bp('sp') {
        font-size: 12px;
      }
    }
  }
}

.breadcrumb__separator {
  margin: 0 8px;
}
</style>
