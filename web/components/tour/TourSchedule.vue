<template>
  <div class="section-tour">
    <TitleSection :title="$t(`tour.tourSchedule.title`)" />
    <div class="tour-schedule__list">
      <div
        v-for="message in scheduleList"
        :key="message.index"
        class="tour-schedule__item"
      >
        <div class="tour-schedule__item__day">{{ message.index }}</div>
        <div class="tour-schedule__item__content">
          <div v-for="item in message.items" :key="item.schedule" class="item">
            <div>{{ item.time }}</div>
            <div>{{ item.schedule }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="tour-schedule__note">
      <p>
        {{ $t(`tour.tourSchedule.description`) }}
      </p>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import TitleSection from '~/components/tour/TitleSection.vue'

export default Vue.extend({
  name: 'TourSchedule',
  components: { TitleSection },
  data() {
    return {
      scheduleList: [
        {
          index: this.$t(`tour.tourSchedule.dayOne`),
          items: [
            {
              time: this.$t(`tour.tourSchedule.dayTimeOne_1`),
              schedule: this.$t(`tour.tourSchedule.dayDescriptionOne_1`),
            },
            {
              time: this.$t(`tour.tourSchedule.dayTimeOne_2`),
              schedule: this.$t(`tour.tourSchedule.dayDescriptionOne_2`),
            },
          ],
        },
        {
          index: this.$t(`tour.tourSchedule.dayTwo`),
          items: [
            {
              time: this.$t(`tour.tourSchedule.dayTimeTwo_1`),
              schedule: this.$t(`tour.tourSchedule.dayDescriptionTwo_1`),
            },
            {
              time: this.$t(`tour.tourSchedule.dayTimeTwo_2`),
              schedule: this.$t(`tour.tourSchedule.dayDescriptionTwo_2`),
            },
          ],
        },
        {
          index: this.$t(`tour.tourSchedule.dayThree`),
          items: [
            {
              time: this.$t(`tour.tourSchedule.dayTimeThree_1`),
              schedule: this.$t(`tour.tourSchedule.dayDescriptionThree_1`),
            },
          ],
        },
      ],
    }
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.section-tour {
  margin-top: 60px;
}

.tour-schedule {
  &__list {
    display: flex;
    flex-direction: column;
    max-width: 800px;
    margin: 0 auto;
  }

  &__item {
    display: flex;
    width: 100%;
    background-color: $color_white;

    @include bp(sp) {
      flex-direction: column;
    }

    + &__item {
      margin-top: 0;
    }

    &__day {
      display: flex;
      align-items: center;
      padding: 0 28px;
      margin-right: 0;
      border: 1px solid #ccc;
      font-size: 18px;
      font-weight: bold;
      color: #fff;
      background-color: #002349;

      @include bp(sp) {
        justify-content: center;
        padding: 10px 0;
      }
    }

    &__content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 80%;
      flex: 1;

      @include bp(sp) {
        width: 100%;
      }

      .item {
        display: flex;
        align-items: stretch;

        + .item {
          margin-top: 0;
        }

        div {
          border: 1px solid #ccc;
          width: 25%;
          padding: 1em 0;
          text-align: center;
          color: #555;
          font-size: 16px;
          letter-spacing: 0;
          line-height: 21px;

          @include bp(sp) {
            display: flex;
            align-items: center;
            justify-content: center;
          }

          + div {
            width: 75%;
            margin-left: 0;
            color: #555;
            font-size: 16px;
            letter-spacing: 0;
            line-height: 21px;
            text-align: left;
            padding-left: 13px;
            padding-right: 13px;

            @include bp(sp) {
              display: flex;
              align-items: center;
              justify-content: flex-start;
            }
          }
        }
      }
    }
  }

  &__note {
    p {
      color: #555;
      font-size: 16px;
      letter-spacing: 0;
      line-height: 21px;
      text-align: center;
      margin-top: 15px;
    }
  }
}
</style>
