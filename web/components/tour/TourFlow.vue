<template>
  <div class="section-tour">
    <TitleSection :title="$t(`tour.tourFlow.title`)" />
    <div class="tour-flow__list">
      <div v-for="item in flowList" :key="item.index" class="tour-flow__item">
        <div class="tour-flow__step">
          <span>{{ item.index }}</span>
        </div>
        <div class="tour-flow__content">
          <p class="tour-flow__content__title">{{ item.title }}</p>
          <p class="tour-flow__content__description">{{ item.description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import TitleSection from '~/components/tour/TitleSection.vue'

export default Vue.extend({
  name: 'TourFlow',
  components: { TitleSection },
  data() {
    return {
      flowList: [
        {
          index: this.$t(`tour.tourFlow.stepOne`),
          title: this.$t(`tour.tourFlow.titleOne`),
          description: this.$t(`tour.tourFlow.DescriptionOne`),
        },
        {
          index: this.$t(`tour.tourFlow.stepTwo`),
          title: this.$t(`tour.tourFlow.titleTwo`),
          description: this.$t(`tour.tourFlow.DescriptionTwo`),
        },
        {
          index: this.$t(`tour.tourFlow.stepThree`),
          title: this.$t(`tour.tourFlow.titleThree`),
          description: this.$t(`tour.tourFlow.DescriptionThree`),
        },
        {
          index: this.$t(`tour.tourFlow.stepFour`),
          title: this.$t(`tour.tourFlow.titleFour`),
          description: this.$t(`tour.tourFlow.DescriptionFour`),
        },
        {
          index: this.$t(`tour.tourFlow.stepFive`),
          title: this.$t(`tour.tourFlow.titleFive`),
          description: this.$t(`tour.tourFlow.DescriptionFive`),
        },
      ],
    }
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.section-tour {
  margin-top: 60px;
}

.tour-flow {
  &__list {
    display: flex;
    flex-direction: column;
    max-width: 800px;
    margin: 0 auto;
  }

  &__item {
    display: flex;
    background-color: $color_white;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);

    &:not(:last-child) {
      margin-bottom: 15px;
    }
  }

  &__step {
    font-size: 18px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 1.5em;
    text-align: center;
    padding: 25px 33px;
    color: $color_white;
    background-color: #002349;
    white-space: nowrap;
    display: flex;
    align-items: center;

    @include bp(sp) {
      padding: 20px 14px;
    }
  }

  &__content {
    padding: 20px 1em;
    background-color: $color_white;

    p {
      margin-bottom: 0.5em;
    }

    &__title {
      color: #002349;
      font-size: 18px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 21px;
    }

    &__description {
      color: #555;
      font-size: 16px;
      line-height: 1.5em;
      letter-spacing: 0;
    }
  }
}
</style>
