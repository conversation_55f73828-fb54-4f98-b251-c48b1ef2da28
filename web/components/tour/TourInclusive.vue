<template>
  <div class="section-tour">
    <TitleSection :title="$t(`tour.allInclusive.title`)" />
    <div class="tour__subtitle">
      <p>
        {{ $t(`tour.allInclusive.description`) }}
      </p>
    </div>
    <div class="tour-inclusive__list">
      <div
        v-for="item in inclusiveList"
        :key="item.name"
        class="tour-inclusive__item"
      >
        <img :src="item.image" width="183" height="120" :alt="item.name" />
        <div class="tour-inclusive__item__name">
          <p>{{ item.name }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import TitleSection from '~/components/tour/TitleSection.vue'

export default Vue.extend({
  name: 'TourInclusive',
  components: { TitleSection },
  data() {
    return {
      inclusiveList: [
        {
          name: this.$t(`tour.allInclusive.flightTicket`),
          image: require(`~/assets/images/tour/tour-flight.png`),
        },
        {
          name: this.$t(`tour.allInclusive.hotel`),
          image: require('~/assets/images/tour/tour-hotel.png'),
        },
        {
          name: this.$t(`tour.allInclusive.limoTaxi`),
          image: require('~/assets/images/tour/tour-lemo.png'),
        },
        {
          name: this.$t(`tour.allInclusive.pcrTest`),
          image: require('~/assets/images/tour/tour-pcr.png'),
        },
        {
          name: this.$t(`tour.allInclusive.multilingual`),
          image: require('~/assets/images/tour/tour-support.png'),
        },
        {
          name: this.$t(`tour.allInclusive.dinnerInvitation`),
          image: require('~/assets/images/tour/tour-dinner.png'),
        },
      ],
    }
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/_bootstraping';

.section-tour {
  margin-top: 60px;
}

.tour__subtitle {
  p {
    font-size: 16px;
    line-height: 1.5em;
    letter-spacing: 0;
    text-align: center;
    margin-bottom: 40px;
  }
}

.tour-inclusive {
  &__list {
    display: flex;
    flex-wrap: wrap;
    gap: 19px;

    @include bp(sp) {
      gap: 14px;
    }
  }

  &__item {
    background-color: $color_white;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);

    @include bp(sp) {
      width: calc(50% - 14px);
    }

    img {
      width: 183px;
      height: 120px;

      @include bp(sp) {
        width: 100%;
        object-fit: cover;
      }
    }

    &__name {
      font-size: 16px;
      letter-spacing: 0;
      line-height: 1.2em;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: pre;
      min-height: 70px;
      font-weight: bold;
      color: #3d455e;

      @include bp(sp) {
        font-size: 14px;
        line-height: 20px;
      }

      p {
        margin-bottom: 0;
      }
    }
  }
}
</style>
