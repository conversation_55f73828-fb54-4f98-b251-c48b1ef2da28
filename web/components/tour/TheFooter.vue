<template>
  <div class="section-tour">
    <div v-if="bannerImg" class="footer__img">
      <picture>
        <source
          media="(max-width: 768px)"
          srcset="~/assets/images/tour/tour-footer_sp.png"
        />
        <img src="~/assets/images/tour/tour-footer_pc.png" alt="footer tour" />
      </picture>
    </div>
    <div class="footer__content">
      <p>© 2022 Inbound Platform, Corp.</p>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'TheFooter',
  props: {
    bannerImg: {
      required: false,
      default: true,
      type: <PERSON><PERSON><PERSON>,
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/_bootstraping';

.footer {
  &__img {
    img {
      width: 100%;
      background-repeat: no-repeat;
      background-position: center;
    }
  }

  &__content {
    @include bp(sp) {
      padding-bottom: 44px;
    }

    p {
      padding: 24px 0;
      width: 100%;
      background-color: #002349;
      color: #fff;
      font-size: 12px;
      letter-spacing: 0;
      line-height: 18px;
      text-align: center;
    }
  }
}
</style>
