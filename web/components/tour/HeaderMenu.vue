<template>
  <client-only>
    <ul class="header-menu">
      <li v-for="item in menuItems" :key="item.code" class="header-menu__item">
        <nuxt-link :to="localePath(item.path)" class="header-menu__item__link">
          {{ $t(`tour.menuItem.${item.code}`) }}
          <img
            v-if="item.subMenuItems"
            src="~/assets/images/icons/ic_arrow_bt_white.svg"
            class="arrow-bt"
          />
        </nuxt-link>
        <div v-if="item.subMenuItems" class="header-sub-menu">
          <ul>
            <li v-for="subItem in item.subMenuItems" :key="subItem.code">
              <nuxt-link :to="localePath(subItem.path)">
                {{ $t(`common.subHeaderMenu.${subItem.code}`) }}
              </nuxt-link>
            </li>
          </ul>
        </div>
      </li>
      <li class="header-menu__item header-menu__item--contact">
        <a href="tel:03-4579-2103" class="header-menu__item__contact">
          <div class="header-menu__item__contact__phone">
            <img
              height="18"
              width="18"
              src="~/assets/images/icons/phone-call-white.svg"
              class="phone call"
            />
            <p class="phone">+81-3-4579-2103</p>
          </div>
          <p class="time">10AM - 7PM Japan Time</p>
        </a>
      </li>
      <li class="header-menu__item">
        <nuxt-link
          :to="localePath('/tour/inquiry')"
          class="header-menu__item__inquire"
        >
          {{ $t(`tour.menuItem.inquiryNow`) }}
        </nuxt-link>
      </li>
    </ul>
  </client-only>
</template>

<script lang="ts">
import Vue from 'vue'
import noop from 'lodash/noop'
import { enabledPrefectures } from '~/constants/prefectures'
export default Vue.extend({
  data() {
    return {
      showContactPopup: false,
      menuItems: [
        { code: 'home', path: '/' },
        {
          code: 'rent',
          path: '/rent/tokyo',
          subMenuItems: enabledPrefectures.map((item) => ({
            code: `rent-${item}`,
            path: `/rent/${item}`,
          })),
        },
        { code: 'buy', path: '/form-buy' },
        { code: 'vipTour', path: '/tour' },
      ],
    }
  },
  methods: { noop },
})
</script>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';

.header-sub-menu {
  top: 100%;
  z-index: 99;
  position: absolute;
  min-width: 100%;
  width: 170px;
  border-radius: 5px;
  font-size: 14px;
  background-color: $color_white;
  max-width: calc(100vw - (80rem / 16));
  box-shadow: 0 calc-rem(2px) calc-rem(6px) rgba(0, 0, 0, 0.3);
  overflow: hidden;

  ul {
    margin: 7px;
    max-height: calc-rem(180px);
    display: none;
    overflow: auto;

    li {
      border-radius: 5px;
      display: flex;
      align-items: center;
      padding: 7px 14px;
      cursor: pointer;
      transition: 0.2s ease-in;

      a {
        display: flex;
        flex-direction: row;
        width: 100%;
        color: #465562;
      }

      &:not(:last-child) {
        margin-bottom: 5px;
      }

      &:hover {
        background-color: $color_white_smoke;
      }
    }
  }
}

.header-menu {
  display: flex;
  align-items: center;
  flex-direction: row;
  margin: auto 0;
  margin-right: 20px;

  &__item {
    position: relative;

    &:hover {
      .header-sub-menu ul {
        display: block;
      }
    }

    &__link {
      color: $color_white;
      font-weight: bold;
      font-size: 16px;
      padding: 10px 20px;
      transition: 0.3s;
      text-transform: uppercase;
      display: flex;
      align-items: center;

      &:hover {
        opacity: 0.9;

        &::before {
          content: '';
          position: absolute;
          bottom: 10%;
          left: 50%;
          transform: translateX(-50%);
          width: 20px;
          height: 3px;
          background: #c29b40;
        }
      }
    }

    &--contact {
      display: flex;
      margin-left: 50px;
      margin-right: 30px;
    }

    &__contact {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      color: $color_white;

      &__phone {
        display: flex;
      }

      .phone {
        font-size: 20px;
        font-weight: bold;
        font-family: sans-serif;
        letter-spacing: 0;
        line-height: 27px;
        margin-bottom: 0;
        margin-left: 10px;
      }

      .time {
        font-size: 13px;
        font-weight: bold;
        font-family: sans-serif;
        letter-spacing: 0;
        line-height: 18px;
        margin-bottom: 0;
        text-align: end;
      }
    }

    &__inquire {
      font-size: 16px;
      font-weight: bold;
      padding: 12px 20px;
      letter-spacing: 0;
      line-height: 21px;
      color: $color_white;
      border: 1px solid #c29b40;
      border-radius: 3px;
      background-color: #c29b40;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

img.arrow-bt {
  margin-left: 3px;
  width: 10px;
  height: 10px;
}
</style>
