<template>
  <div class="favorite-loading-item">
    <div class="shine cover" />
    <div class="loading-content">
      <div class="shine" />
      <div class="shine" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'FavoriteLoading',
}
</script>

<style scoped lang="scss">
.favorite-loading-item {
  display: flex;
  align-items: center;
  background: #fff;
  box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.1);
}

.loading-content {
  flex: 1;
  padding-right: 16px;
}

.shine {
  width: 100%;
  background: #f6f7f8
    linear-gradient(
      to right,
      #f6f7f8 0%,
      #edeef1 20%,
      #f6f7f8 40%,
      #f6f7f8 100%
    )
    no-repeat;
  background-size: 800px 230px;
  display: inline-block;
  position: relative;
  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: forwards;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-name: placeholderShimmer;
  -webkit-animation-timing-function: linear;
  animation-duration: 1s;
  animation-fill-mode: forward;
  animation-iteration-count: infinite;
  animation-name: placeholderShimmer;
  animation-timing-function: linear;

  &.cover {
    width: 70px;
    height: 70px;
    margin-right: 16px;
  }

  .loading-content & {
    height: 20px;
  }
}

@keyframes placeholderShimmer {
  0% {
    background-position: -468px 0;
  }

  100% {
    background-position: 468px 0;
  }
}
</style>
