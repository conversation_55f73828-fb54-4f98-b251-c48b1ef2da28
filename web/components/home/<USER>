<template>
  <div class="feat-banner">
    <div class="feat-banner__inner container">
      <div class="feat-banner__inner__content">
        <h2 v-html="$t('home.featBannerTitle')"></h2>
        <p>{{ $t('home.featBannerContent') }}</p>
      </div>
      <div class="image-container">
        <img src="~/assets/images/home/<USER>" alt="agent" data-not-lazy />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';
@import '~/assets/scss/settings/_variables';

.feat-banner {
  overflow: hidden;
  background-color: #f3f7f7;
  padding: 30px 0;
  margin: 60px 0 30px;

  &__inner {
    display: flex;
    flex-direction: row;

    @include bp('768px', '1024px') {
      flex-direction: column;
    }
    @include bp(sp) {
      flex-direction: column;
    }

    .image-container {
      @include bp(1024px) {
        margin-right: -48%;
      }
      @include bp(1368px) {
        margin-right: -32%;
      }
      @include bp(sp) {
        width: 501px;
        height: 120px;
      }
      img {
        @include bp(pc) {
          display: block;
          width: auto;
          max-width: 1007px;
          height: fit-content;
        }
        @include bp('768px', '1024px') {
          width: 100%;
        }
        @include bp(sp) {
          width: 100%;
          height: 120px;
        }
      }
    }

    &__content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-right: 35px;
      @include bp('768px', '1024px') {
        margin-right: 0;
      }
      @include bp(sp) {
        margin-right: 0;
      }

      h2 {
        font-weight: bold;
        font-size: 34px;
        line-height: 42px;
        margin-bottom: 15px;
        @include bp('768px', '1024px') {
          font-size: 28px;
          line-height: 34px;
          margin-bottom: 15px;
        }
        @include bp(sp) {
          font-size: 28px;
          line-height: 34px;
          margin-bottom: 15px;
        }
      }

      p {
        font-size: 14px;
        letter-spacing: 0;
        line-height: 19px;
      }
    }
  }
}
</style>
