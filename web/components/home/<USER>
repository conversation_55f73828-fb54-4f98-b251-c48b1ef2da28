<template>
  <div class="carousel-container">
    <agile :options="slideOptions">
      <div
        v-for="(slide, index) in imagesSlide"
        :key="index"
        class="slide"
        style="width: 300px"
      >
        <ReviewItem
          :name="slide.name"
          :review="slide.review"
          :rating="slide.rating"
          :start-date="slide.startDate"
        />
      </div>
      <template slot="prevButton">
        <img
          class="ic-left-slide"
          :src="require(`~/assets/images/icons/ic-left-slide.svg`)"
      /></template>
      <template slot="nextButton">
        <img
          class="ic-right-slide"
          :src="require(`~/assets/images/icons/ic-right-slide.svg`)"
      /></template>
    </agile>
    <a
      class="more-reviews"
      target="_blank"
      href="https://www.google.com/maps/place/Living+Japan/@35.6618298,139.7531327,16z/data=!3m1!5s0x60188bc0100d853f:0xcab81b245e9ec1c6!4m8!3m7!1s0x60188b0325908df5:0xe777dcc9f05a6b42!8m2!3d35.6618298!4d139.7531327!9m1!1b1!16s%2Fg%2F11rxvks8k9?entry=ttu&g_ep=EgoyMDI0MDkxNS4wIKXMDSoASAFQAw%3D%3D"
      >More reviews</a
    >
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import reviewsData from '../../assets/reviews.json'
import ReviewItem from './ReviewItem.vue'
export default Vue.extend({
  name: 'Reviews',
  components: {
    ReviewItem,
  },
  computed: {
    imagesSlide(): {
      name: string
      review: string
      rating: number
      startDate: string
    }[] {
      return reviewsData
    },
    slideOptions() {
      return {
        autoplay: false,
        infinite: true,
        dots: false,
        navButtons: true,
        autoplaySpeed: 4000,
        centerMode: true,
        slidesToShow: 1,
        responsive: [
          {
            breakpoint: 767,
            settings: {
              slidesToShow: 3,
              centerMode: false,
            },
          },
        ],
      }
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/settings/_variables';
@import '~/assets/scss/tools/bootstraping';

.carousel-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  max-width: 100%;
}

.more-reviews {
  padding-top: calc-rem(20px);
  cursor: pointer;
  align-self: center;
  text-decoration: underline;
  color: $color_green;
  font-weight: bold;
}

.more-reviews:hover {
  color: $color_green;
}

.ic-left-slide,
.ic-right-slide {
  height: calc-rem(18px);
  width: calc-rem(18px);
  @include bp('sp') {
    height: calc-rem(18px);
    width: calc-rem(18px);
  }
}

::v-deep {
  .agile__slide {
    display: flex;
    justify-content: center;
    @media (min-width: calc-rem(767px)) {
      max-width: calc-rem(260px) !important;
    }
  }
  .agile__actions {
    position: absolute;
    top: calc-rem(110px);
    bottom: 0;
    width: 100%;
    height: calc-rem(20px);
  }

  .agile__nav-button {
    background: rgba($color_black, 0.2);
    border-radius: 50%;
    border: none !important;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    padding: 0;
    height: calc-rem(28px) !important;
    width: calc-rem(28px) !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .agile__nav-button--prev {
    left: calc-rem(1px);
    @media (min-width: calc-rem(767px)) {
      left: calc-rem(15px);
    }
  }

  .agile__nav-button--next {
    right: calc-rem(1px);
    @media (min-width: calc-rem(767px)) {
      right: calc-rem(15px);
    }
  }
}
</style>
