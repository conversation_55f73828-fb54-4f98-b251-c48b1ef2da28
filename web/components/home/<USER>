<template>
  <div class="banner-real-estate">
    <div class="banner-real-estate__img">
      <a
        href="https://www.eventbrite.com/e/tokyo-real-estate-webinar-for-foreigners-6-tickets-410344139367"
        target="_blank"
      >
        <img
          class="isPc"
          src="~/assets/images/webinar6-pc.png"
          alt="real estate"
          data-not-lazy
        />
        <img
          class="isSp"
          src="~/assets/images/webinar6-sp.png"
          alt="real estate"
          data-not-lazy
        />
      </a>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {}
  },
  methods: {
    close() {
      this.isShow = false
    },
  },
}
</script>
<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';
@import '~/assets/scss/settings/_variables';

.banner-real-estate {
  display: flex;
  justify-content: center;
  margin-top: 3.75rem;

  .banner-real-estate__img {
    img:hover {
      opacity: 0.9;
      transition: 0.3s all ease-in-out;
    }

    .isPc {
      display: block;
      @include bp(sp) {
        display: none;
      }
    }

    .isSp {
      display: none;
      @include bp(sp) {
        display: block;
      }
    }
  }
}
</style>
