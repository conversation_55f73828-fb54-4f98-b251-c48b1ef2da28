<template>
  <div class="banner banner-v2">
    <div class="container">
      <div class="flex aic">
        <div class="banner-content banner-part">
          <div class="slider-mobile">
            <img
              class="image-sp"
              data-not-lazy
              src="~/assets/images/home/<USER>"
              alt=""
            />
          </div>
          <h1
            class="page-title"
            :class="{ 'en-title': $i18n.locale === 'en' }"
            v-html="$t('home.pagTitle')"
          ></h1>
          <p class="tag-line">{{ $t('home.tagLine') }}</p>
          <div class="flex fw banner-buttons">
            <nuxt-link
              class="banner-button flex aic jcc"
              :to="localePath('/rent/tokyo/city')"
            >
              <span class="icon-area" aria-label="icon area" />
              {{ $t('home.buttonSearchByArea') }}
            </nuxt-link>
            <nuxt-link
              class="banner-button flex aic jcc"
              :to="localePath('/rent/tokyo/station')"
            >
              <span class="icon-station" aria-label="icon station" />
              {{ $t('home.buttonSearchByStation') }}
            </nuxt-link>
          </div>
        </div>
        <div class="banner-slider banner-part">
          <agile :options="slideOptions">
            <div
              v-for="(slide, index) in imagesSlide"
              :key="index"
              class="slide"
            >
              <img :src="slide" alt="" data-not-lazy />
            </div>
          </agile>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'HomeBannerV2',
  computed: {
    imagesSlide(): string[] {
      return [
        require('~/assets/images/home/<USER>'),
        require('~/assets/images/home/<USER>'),
      ]
    },
    slideOptions() {
      return {
        autoplay: true,
        dots: false,
        navButtons: false,
        autoplaySpeed: 4000,
      }
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.banner {
  color: #24576e;
  padding: 40px 0;
  position: relative;

  &::after {
    content: '';
    background: #e9f2f6;
    width: 100%;
    height: 190px;
    position: absolute;
    left: 0;
    top: 0;
    z-index: -1;
    pointer-events: none;
  }

  @media (max-width: 768px) {
    padding: 30px 0 40px;

    &::after {
      height: 165px;
    }
  }

  @media (min-width: 480px) {
    &::after {
      height: 220px;
    }
  }

  @media (min-width: 768px) {
    &::after {
      height: 320px;
    }
  }

  @media (min-width: 1024px) {
    background: #e9f2f6;

    &::after {
      display: none;
    }
  }

  @include bp('481px', '820px') {
    padding: 60px 0;

    &::after {
      height: 100%;
    }
  }
}

.banner-part {
  @media (min-width: 1024px) {
    width: 50%;
  }
}

.banner-slider {
  display: none;
  margin-left: auto;

  @media (min-width: 1024px) {
    display: block;
  }

  img {
    width: 480px;
    height: auto;
    margin-left: auto;
  }
}

.slider-mobile {
  margin-bottom: 15px;

  @include bp('481px', '820px') {
    display: none;
  }

  @media (min-width: 1024px) {
    display: none;
  }
}

.page-title {
  font-size: 28px;
  line-height: 1.1em;
  margin-bottom: 10px;
  text-shadow: 2px 6px 0 rgba(0, 0, 0, 0);

  @media (min-width: 1024px) {
    font-size: 32px;
    line-height: 1.2em;
  }
}
.en-title {
  font-family: 'Arial Black', sans-serif;
}

.tag-line {
  font-size: 13px;
  line-height: 1.5em;
  margin-bottom: 20px;

  @include bp('481px', '820px') {
    margin-bottom: 40px;
  }

  @media (min-width: 1024px) {
    margin-bottom: 40px;
    font-weight: bold;
  }
}

.banner-buttons {
  @include bp('520px', '820px') {
    gap: 10px;
    flex-wrap: nowrap;
  }

  *:not(:last-child) {
    margin-right: 20px;

    @include bp(sp) {
      margin-right: 0;
      margin-bottom: 15px;
    }

    @include bp('520px', '820px') {
      margin-bottom: 0;
    }

    // ipad
    @include bp('768px', '1024px') {
      margin-right: 20px;
      margin-bottom: 0;
    }

    // ipad pro
    @include bp('1023px', '1025px') {
      margin-bottom: 15px;
      margin-right: 0;
    }
  }
}

.banner-button {
  color: white;
  width: 100%;
  font-size: 16px;
  font-weight: bold;
  padding: 16px 10px;
  text-align: center;
  border-radius: 4px;
  background-color: #a32d7c;
  will-change: background-color;
  transition: ease background-color 0.25s;
  box-shadow: 0 3px 6px 0 rgb(0 0 0 / 20%);

  @include bp('520px', '820px') {
    width: 50%;
  }

  @media (min-width: 768px) {
    width: calc(50% - 10px);
  }

  @media (min-width: 1024px) {
    width: 40%;
  }

  @media (min-width: 1200px) {
    width: 280px;
    max-width: 50%;
  }

  span {
    width: 22px;
    height: 22px;
    background-color: #fff;
    margin-right: 15px;
  }

  &:hover {
    background-color: #8a336d;
  }
}

.icon-station {
  -webkit-mask: url(~/assets/images/icons/ic_station.svg) no-repeat center;
  mask: url(~/assets/images/icons/ic_station.svg) no-repeat center;
}

.icon-area {
  -webkit-mask: url(~/assets/images/icons/ic_map.svg) no-repeat center;
  mask: url(~/assets/images/icons/ic_map.svg) no-repeat center;
}
</style>

<style lang="scss">
@import '~/assets/scss/tools/bootstraping';

.banner-slider {
  .slide.agile__slide {
    display: flex;
    justify-content: flex-end;
  }
}

.banner-v2 {
  .container {
    @include bp('481px', '820px') {
      padding-left: 40px;
      padding-right: 40px;
    }
  }
}
</style>
