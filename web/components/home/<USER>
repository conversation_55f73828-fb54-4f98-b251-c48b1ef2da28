<template>
  <div v-if="isShow" class="qr-wechat">
    <div class="qr-wechat__img">
      <a :href="bannerInfo.link">
        <div class="qr__img">
          <img class="isPc" :src="require(`~/assets/images/${bannerInfo.pc}`)" alt="QR code" data-not-lazy />
          <img class="isSp" :src="require(`~/assets/images/${bannerInfo.sp}`)" alt="QR code" data-not-lazy />
        </div>
      </a>
      <div class="close" @click="close">
        <img width="12" height="12" src="~/assets/images/contact/close-white.png" />
      </div>
    </div>
  </div>
</template>
<script>
const banners = {
  en: {
    pc: 'referralProgram-banner.png',
    sp: 'referralProgram-banner-sp.png',
    link: 'https://www.livingjapan.com/referral-program',
  },
  zh: {
    pc: 'wechat-QRcode-pc.png',
    sp: 'wechat-QRcode-sp.png',
    link: '#',
  },
}
export default {
  data() {
    return {
      isShow: true,
    }
  },
  computed: {
    bannerInfo() {
      return banners[this.$i18n.locale] || banners.en
    },
  },
  methods: {
    close() {
      this.isShow = false
    },
  },
}
</script>
<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';
@import '~/assets/scss/settings/_variables';

.qr-wechat {
  bottom: 0;
  position: fixed;
  z-index: 3;
  left: 0;
  display: none;

  .qr-wechat__img {
    position: relative;

    @include bp(sp) {
      width: 95%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
    }

    .qr__img {
      .isPc {
        display: block;

        @include bp(sp) {
          display: none;
        }
      }

      .isSp {
        display: none;

        @include bp(sp) {
          display: block;
          margin-bottom: 10px;
        }
      }
    }

    .close {
      height: 28px;
      width: 28px;
      float: right;
      position: absolute;
      top: -10px;
      right: -10px;
      cursor: pointer;
      background-color: #aaa;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;

      @include bp(sp) {
        right: -5px;
      }
    }
  }
}
</style>
<style lang="scss">
@import '~/assets/scss/tools/bootstraping';

.font-zhcn,
.font-en {
  .qr-wechat {
    display: block;
  }
}
</style>
