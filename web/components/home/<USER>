<template>
  <div class="intro-section">
    <div class="intro-section__inner">
      <div class="image-container">
        <img
          src="~/assets/images/home/<USER>"
          width="400"
          height="228"
          alt="agent"
          data-not-lazy
        />
        <p class="title">
          {{ $t('home.introSectionTitle') }}
          <span>{{ $t('home.introSectionSubTitle') }}</span>
        </p>
      </div>
      <div class="content-container">
        <p class="title">
          {{ $t('home.introSectionTitle') }}
          <span>{{ $t('home.introSectionSubTitle') }}</span>
        </p>
        <p class="text">
          {{ $t('home.introSectionTxt') }}
        </p>
        <a
          class="btn-intro btn"
          href="https://calendly.com/livingjapan/web"
          target="_blank"
        >
          {{ $t('home.introSectionBtn') }}
          <span class="icon"></span>
        </a>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  methods: {
    openPopup() {
      this.$accessor.showContactPopup(true)
    },
  },
}
</script>
<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';
@import '~/assets/scss/settings/_variables';

.intro-section {
  background-color: #222d51;
  margin: 60px auto;
  border-radius: 4px;
  box-shadow: 1px 0 20px 0 rgba(0, 0, 0, 0.5);
  max-width: 75rem;
  @include bp(sp) {
    margin: 0 auto;
    border-radius: 0;
  }
  &__inner {
    display: flex;
    height: 200px;

    @include bp(sp) {
      flex-direction: column;
      height: auto;
    }

    &__content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-right: 35px;

      @include bp(sp) {
        margin-right: 0;
      }

      h2 {
        font-weight: bold;
        font-size: 34px;
        line-height: 42px;
        margin-bottom: 15px;

        @include bp(sp) {
          font-size: 28px;
          line-height: 34px;
          margin-bottom: 15px;
        }
      }

      p {
        font-size: 14px;
        letter-spacing: 0;
        line-height: 19px;
      }
    }

    .title {
      font-size: 28px;
      line-height: 30px;
      font-weight: 600;
      @include bp(sp) {
        font-size: 18px;
      }
      span {
        color: #f1c24b;
      }
    }
  }
  .image-container {
    object-fit: cover;
    color: #fff;

    @include bp(sp) {
      display: flex;
      gap: 10px;
      padding: 15px 20px 0;
    }

    img {
      min-width: 300px;
      height: 200px;
      @include bp(sp) {
        min-width: unset;
        max-width: 80px;
        height: 60px;
      }
    }

    .title {
      display: none;

      @include bp(sp) {
        display: block;
      }
    }
  }

  .content-container {
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 20px 30px;
    @include bp(sp) {
      padding: 0 20px 20px;
    }

    p {
      margin: 0;
    }

    .title {
      display: block;
      @include bp(sp) {
        display: none;
      }
    }
    .text {
      font-size: 14px;
      line-height: 18px;
      margin-top: 10px;
      margin-bottom: 15px;
      @include bp(sp) {
        margin-top: 0;
      }
      @include bp('418px', '768px') {
        margin-top: 10px;
      }
    }
  }

  .btn-intro {
    background: #bf9834;
    width: 280px;
    min-height: 44px;
    border-radius: 2px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    line-height: 21px;
    font-weight: 600;
    font-size: 16px;
    padding: 0 10px 0 20px;
    cursor: pointer;

    &:hover {
      box-shadow: none;
      opacity: 0.8;
    }

    @include bp(sp) {
      width: 100%;
    }
  }

  .icon {
    background: #fff;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    position: relative;
    &::after {
      border: solid #bf9834;
      border-width: 2px 2px 0 0;
      content: '';
      display: inline-block;
      padding: 3px;
      position: absolute;
      top: 6px;
      right: 7px;
      transform: rotate(45deg);
      -webkit-transform: rotate(45deg);
    }
  }
}
</style>
