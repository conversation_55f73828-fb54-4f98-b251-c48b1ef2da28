<template>
  <div class="popular">
    <div class="popular-heading">
      <h2 class="popular-heading-title">
        {{ $t('home.popularAreaSectionTitle') }}
      </h2>
    </div>
    <div class="popular-grid">
      <nuxt-link
        v-for="area in areas"
        :key="area.id"
        :to="localePath(`/rent/tokyo/${area.id}`)"
        class="popular-grid-item"
      >
        <area-card
          :id="area.id"
          :title="$t(`common.areas.${area.name}`)"
          :image="area.image"
        />
      </nuxt-link>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

import { areas } from '~/constants/popularAreas'

export default Vue.extend({
  name: 'Popular',
  data: () => ({
    areas,
  }),
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.popular {
  @include bp(pc) {
    padding-top: 30px;
  }
}

.popular-heading {
  margin-bottom: 10px;
  @include flex(null, center, space-between);
}

.popular-heading-title {
  margin: 0;
  font-size: 22px;
  line-height: 1.5;
}

.popular-grid {
  display: flex;
  flex-wrap: wrap;
  margin-left: -10px;
  margin-right: -10px;
}

.popular-grid-item {
  width: 100%;
  padding-left: 10px;
  margin-bottom: 30px;
  padding-right: 10px;

  @include bp(768px, 900px) {
    width: 50%;
  }

  @include bp(901px) {
    width: 20%;
  }
}
</style>
