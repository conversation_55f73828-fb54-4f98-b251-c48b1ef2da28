<template>
  <div class="favorite-item">
    <img :src="tag.image" alt="" />
    <div class="content">
      <h4>#{{ tag.name }}</h4>
      <p>{{ $t('common.propertyCount', { count: tag.count }) }}</p>
    </div>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import { FavoriteInterface } from '~/components/home/<USER>'

export default Vue.extend({
  name: 'FavoriteItem',
  props: {
    tag: {
      type: Object as PropType<FavoriteInterface>,
      required: true,
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.favorite-item {
  display: flex;
  border-radius: 4px;
  padding-right: 16px;
  align-items: center;
  background-color: #fff;
  transition: all ease 0.3s;
  font-family: $font_base;
  box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.1);

  &:hover {
    box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.2);
  }

  img {
    width: 70px;
    height: 70px;
    margin-right: 16px;
    object-fit: cover;
    border-radius: 4px 0 0 4px;
  }
}

h4 {
  color: $color_base;
  font-size: 16px;
  margin-bottom: 0;
  font-weight: bold;
  line-height: 22px;
}

p {
  font-size: 12px;
  margin-bottom: 0;
  line-height: 17px;
  color: $color_base;
}
</style>
