<template>
  <div class="review-wrapper">
    <b-card bg-variant="light">
      <b-card-title class="card-title">
        <div class="header">
          <span
            ><span>{{ name }}</span
            ><b-icon-patch-check-fill
              variant="success"
            ></b-icon-patch-check-fill
          ></span>
          <small
            >{{ timeSinceStart }}
            <a
              target="_blank"
              href="https://www.google.com/maps/place/Living+Japan/@35.6618298,139.7531327,16z/data=!3m1!5s0x60188bc0100d853f:0xcab81b245e9ec1c6!4m8!3m7!1s0x60188b0325908df5:0xe777dcc9f05a6b42!8m2!3d35.6618298!4d139.7531327!9m1!1b1!16s%2Fg%2F11rxvks8k9?entry=ttu&g_ep=EgoyMDI0MDkxNS4wIKXMDSoASAFQAw%3D%3D"
              class="google"
              >Google</a
            ></small
          >
        </div>
      </b-card-title>
      <b-form-rating
        class="p-0"
        variant="warning"
        readonly
        no-border
        inline
        size="lg"
        :value="rating"
      />
      <b-card-text>{{ review }}</b-card-text>
    </b-card>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { CardPlugin, FormRatingPlugin } from 'bootstrap-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

Vue.use(CardPlugin)
Vue.use(FormRatingPlugin)

dayjs.extend(relativeTime)
dayjs.extend(utc)
dayjs.extend(timezone)

export default Vue.extend({
  name: 'ReviewItem',
  props: {
    name: {
      required: true,
      type: String,
    },
    review: {
      required: true,
      type: String,
    },
    rating: {
      required: true,
      type: Number,
      default: 0,
    },
    startDate: {
      required: true,
      type: String,
    },
  },
  computed: {
    timeSinceStart(): string {
      // Get the current time in Japan's timezone
      const japanTimeNow = dayjs().tz('Asia/Tokyo')
      const startDateJapan = dayjs(this.startDate).tz('Asia/Tokyo')

      // Calculate the difference in days
      const weeksDifference = japanTimeNow.diff(startDateJapan, 'week')

      // If it's been at least 1 week and less than 4 weeks, display weeks ago
      if (weeksDifference >= 1 && weeksDifference < 4) {
        return `${weeksDifference} week${weeksDifference > 1 ? 's' : ''} ago`
      }

      // Otherwise, fall back to the default relative time behavior
      return dayjs(this.startDate).fromNow()
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/settings/_variables';
@import 'bootstrap/scss/bootstrap.scss';
@import 'bootstrap-vue/src/index.scss';

$card_background: #f8f9fa;

.card {
  border: none;
  height: 15rem;
  width: 15rem;
}

.avatar-container {
  position: relative;
  width: 3rem;
  height: 3rem;
  border-radius: 9999px;
  overflow: hidden;
}

.avatar-container > img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  object-fit: cover;
}

.avatar-container > div {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 9999px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

.card-title {
  display: flex;
  margin-bottom: 0;
  gap: 10px;
  background-color: $card_background;
  border: none;
}

.google {
  color: $color_green;
  cursor: pointer;
}

.google:hover {
  color: $color_green;
}

.card-text {
  display: -webkit-box !important;
  font-size: 0.938rem !important;
  -webkit-line-clamp: 5 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  height: 7.5rem;
  line-clamp: 4 !important;
}

.header > span {
  display: flex;
  gap: 3px;
  align-items: center;
  padding-right: 5px;
  font-weight: 700;
  font-size: 0.875rem;
}

.header > small {
  height: 1rem;
  text-wrap: nowrap;
  font-weight: 400;
  font-size: 0.688rem;
  color: #6c757d;
}

.header {
  display: flex;
  justify-content: center;
  flex-direction: column;
  gap: 5px;
}

.b-rating {
  height: 2.488rem !important;
  background-color: $card_background;
  color: #f9c10c;
}

.b-rating-star {
  padding: 1px !important;
}
</style>
