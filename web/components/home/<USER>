<template>
  <div class="favorite">
    <div class="favorite-heading">
      <h2 class="favorite-heading-title">
        {{ $t('home.favoriteSectionTitle') }}
      </h2>
    </div>
    <div class="favorite-grid">
      <template v-if="$apollo.queries.tags.loading">
        <div v-for="i in 8" :key="i" class="favorite-grid-item">
          <favorite-loading />
        </div>
      </template>
      <nuxt-link
        v-for="tag in tagMapped"
        v-else
        :key="tag.id"
        :to="localePath(`/rent/tokyo?tags=${tag.id}`)"
        class="favorite-grid-item"
      >
        <favorite-item :tag="tag" />
      </nuxt-link>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

import { TagCount } from '~/types/types'
import getTagCount from '~/apollo/queries/getAvailableTags.graphql'
import { TagType } from '~/types'

export interface FavoriteInterface extends TagCount {
  image: string
  name: string
}

export default Vue.extend({
  name: 'Favorite',
  data() {
    return {
      tags: [] as TagCount[],
    }
  },
  apollo: {
    tags: {
      query: getTagCount,
    },
  },
  computed: {
    tagMapped(): FavoriteInterface[] {
      return (
        this.tags
          .filter((tag) => tag.id !== TagType.OfficeUse)
          .map((tag: TagCount) => ({
            ...tag,
            name: this.$t('common.tags.' + tag.id) as string,
            image: require(`assets/images/tags/${tag.id}.jpg`),
          })) || []
      )
    },
  },
})
</script>

<style scoped lang="scss">
@import '~/assets/scss/tools/bootstraping';

.favorite {
  @include bp(pc) {
    padding-top: 30px;
  }
}

.favorite-heading {
  margin-bottom: 10px;
  @include flex(null, center, space-between);
}

.favorite-heading-title {
  margin: 0;
  font-size: 22px;
  line-height: 1.5;
}

.favorite-grid {
  display: flex;
  flex-wrap: wrap;
  margin-left: -10px;
  margin-right: -10px;
}

.favorite-grid-item {
  width: 100%;
  padding-left: 10px;
  margin-bottom: 30px;
  padding-right: 10px;

  @include bp(768px, 900px) {
    width: 50%;
  }

  @include bp(901px) {
    width: 25%;
  }
}
</style>
