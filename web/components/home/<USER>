<template>
  <div class="feature-section">
    <div class="feature-section__content is_PC">
      <div
        v-for="item in featuresItems"
        :key="item.title"
        class="feature-section__item"
      >
        <img
          :src="item.image"
          width="180"
          height="180"
          alt="feature"
          data-not-lazy
        />
        <div class="feature-section__text">
          <p class="title">{{ item.title }}</p>
          <p class="desc">
            {{ item.description }}
          </p>
        </div>
      </div>
    </div>
    <div class="is_SP">
      <agile :options="slideOptions">
        <div
          v-for="(slide, index) in featuresItems"
          :key="index"
          class="slide feature-section__content"
        >
          <img :src="slide.image" alt="" data-not-lazy />
          <div class="feature-section__text">
            <p class="title">{{ slide.title }}</p>
            <p class="desc">
              {{ slide.description }}
            </p>
          </div>
        </div>
      </agile>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      featuresItems: [
        {
          image: require('~/assets/images/features/features-01.png'),
          title: this.$t('home.featureTitlePropertie'),
          description: this.$t('home.featureDescriptionPropertie'),
        },
        {
          image: require('~/assets/images/features/features-02.png'),
          title: this.$t('home.featureTitleEverything'),
          description: this.$t('home.featureDescriptionEverything'),
        },
        {
          image: require('~/assets/images/features/features-03.png'),
          title: this.$t('home.featureTitleService'),
          description: this.$t('home.featureDescriptionService'),
        },
      ],
    }
  },
  computed: {
    slideOptions() {
      return {
        autoplay: true,
        dots: true,
        navButtons: false,
        autoplaySpeed: 5000,
      }
    },
  },
  methods: {
    openPopup() {
      this.$accessor.showContactPopup(true)
    },
  },
}
</script>
<style lang="scss" scoped>
@import '~/assets/scss/tools/bootstraping';
@import '~/assets/scss/settings/_variables';

.is_PC {
  @include bp(pc) {
    display: block;
  }
  @include bp(sp) {
    display: none !important;
  }
}

.is_SP {
  @include bp(pc) {
    display: none;
  }
  @include bp(sp) {
    display: block;
  }
}

.feature-section {
  padding: 40px 20px;
  background-color: #fffbee;

  @include bp(sp) {
    padding: 30px 20px 30px;
  }
}

.feature-section__content {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  @include bp(pc) {
    gap: 50px;
  }
  @include bp(sp) {
    margin-bottom: 20px;
  }
}

.feature-section__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  max-width: 305px;

  @include bp('768px', '1024px') {
    max-width: 205px;
  }
}

.feature-section__text {
  font-family: Arial, sans-serif;

  @include bp(sp) {
    text-align: center;
  }

  p {
    margin-bottom: 0;
  }

  .title {
    text-align: center;
    color: #bf9834;
    font-size: 18px;
    letter-spacing: 0;
    line-height: 18px;
    font-weight: bold;
    margin-bottom: 10px;

    @include bp(sp) {
      font-size: 22px;
      letter-spacing: 0;
      line-height: 28px;
      padding: 0 30px;
      margin: 15px 0;
      word-break: break-word;
    }
  }

  .desc {
    color: #555;
    font-size: 13px;
    letter-spacing: 0;
    line-height: 20px;

    @include bp(sp) {
      font-size: 14px;
      padding: 0;
    }
  }
}

.btn-feature {
  background: #bf9834;
  width: 300px;
  min-height: 60px;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 24px;
  padding: 0 10px 0 20px;
  margin: 0 auto;
  box-shadow: 0 3px 6px 0 rgb(0 0 0 / 20%);

  &:hover {
    box-shadow: none;
    opacity: 0.8;
  }

  @include bp(sp) {
    width: 100%;
  }
}

.icon {
  background: #fff;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  position: relative;
  left: 55px;
  @include bp(sp) {
    left: 0;
    margin-left: 20px;
  }
  &::after {
    border: solid #bf9834;
    border-width: 2px 2px 0 0;
    content: '';
    display: inline-block;
    padding: 3px;
    position: absolute;
    top: 9px;
    right: 10px;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
  }
}
</style>
<style lang="scss">
@import '~/assets/scss/tools/bootstraping';

.feature-section {
  .agile__dots {
    grid-gap: 10px;
    gap: 10px;
  }

  .agile__dot button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
  }

  .agile__dot--current button {
    background: #ffd65b;
  }

  .slide {
    img {
      width: 150px;
    }
  }
}

.font-jp {
  .feature-section {
    .icon {
      left: 30px;

      @include bp(sp) {
        margin-left: 0;
      }
    }
  }
}
</style>
