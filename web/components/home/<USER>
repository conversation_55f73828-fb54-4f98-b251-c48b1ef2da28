<template>
  <div class="area-card" :lazy-background="image">
    <div class="area-card__content">
      <h3>{{ title }}</h3>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    title: { type: String, required: true },
    image: { type: String, required: true },
    id: { type: String, required: true },
  },
})
</script>

<style lang="scss" scoped>
.area-card {
  display: flex;
  transition: all ease 0.3s;
  background-repeat: none;
  background-size: cover;
  box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  height: 180px;

  &:hover {
    box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.4);
  }

  &__content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.2);

    h3 {
      color: #fff;
      font-size: 20px;
      font-weight: bold;
      letter-spacing: 0;
      text-shadow: 2px 4px 10px rgb(0 0 0 / 50%);
      margin: 0;
    }
  }
}
</style>
