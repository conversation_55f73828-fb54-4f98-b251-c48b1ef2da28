{
  "compilerOptions": {
    "target": "ES2018",
    "module": "ESNext",
    "moduleResolution": "Node",
    "lib": [
      "ESNext",
      "ESNext.AsyncIterable",
      "DOM",
      "dom.iterable"
    ],
    "esModuleInterop": true,
    "allowJs": true,
    "sourceMap": true,
    "strict": true,
    "noEmit": true,
    "experimentalDecorators": true,
    "baseUrl": ".",
    "paths": {
      "~/*": [
        "./*"
      ],
      "@/*": [
        "./*"
      ]
    },
    "types": [
      "@nuxt/types",
      "@nuxtjs/apollo",
      "@types/node",
      "vue-apollo/types",
      "@nuxt/content",
      "nuxt-i18n",
      "@nuxtjs/recaptcha",
    ],
    "resolveJsonModule": true
  },
  "exclude": [
    "node_modules",
    ".nuxt",
    "dist"
  ]
}
