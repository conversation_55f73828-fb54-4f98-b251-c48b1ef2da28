import { City, CreateOneInquiryInput } from './types'

export interface Agency {
  avatar: string
  name: string
  language: string
  businessHours: string
  bio: string
}

export interface PropertyDetail {
  label: string
  value: string
}

export interface PropertyFeature {
  name: string
  active: Boolean
}

export interface PropertySubImage {
  id: number
  image: string
  title: string
}

export interface PropertyRoomOptionsList {
  title: string
  value: number
}

export interface PropertyRoom {
  floor: string
  floorPlan: string
  layout: string
  size: string
  price: {
    rent: string
    maintain: string
  }
  optionsList: Array<PropertyRoomOptionsList>
}

export interface Property {
  id: number
  title: string
  image: string
  location: string
  access: string
  yearBuilt: string
  type: string
  priceRent: number
  priceMaintain: number
  details: Array<PropertyDetail>
  rooms: Array<PropertyRoom>
  features: Array<PropertyFeature>
  agency: Agency
  subImage: Array<PropertySubImage>
}

export type InquiryInfo = Omit<
  CreateOneInquiryInput,
  'propertyId' | 'roomCode' | 'locale' | 'whenToMoveIn'
>

export interface InquiryError {
  name: null | string
  email: null | string
  purpose: null | string
  whenToMoveIn: null | string
}

export interface TourInquiryError {
  name: null | string
  email: null | string
  phone: null | string
}

export interface PriceList {
  value: number
  label: string
}

export interface LayoutSearch {
  label: string
  value: string
}

export type ArrayElement<
  ArrayType extends readonly unknown[]
> = ArrayType extends readonly (infer ElementType)[] ? ElementType : never

export interface BreadcrumbsInterface {
  path: null | string
  title: string
}

export enum TagType {
  Furnished = 'furnished',
  FreeRent = 'free_rent',
  Renovation = 'renovation',
  OfficeUse = 'office_use',
  Penthouse = 'penthouse',
  NoKeyMoney = 'no_key_money',
  NoDeposit = 'no_deposit',
  PetFriendly = 'pet_friendly',
}

export enum DayCode {
  mon = 'mon',
  tue = 'tue',
  wed = 'wed',
  thu = 'thu',
  fri = 'fri',
  sat = 'sat',
  sun = 'sun',
}

export enum LanguageCode {
  EN = 'en',
  JP = 'jp',
  ZH = 'zh',
  KR = 'kr',
}

export interface CheckableCity extends City {
  checked?: boolean
}
