
declare module '*/baseAccess.fragment.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const BaseAccess: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/baseAddressDetail.fragment.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const BaseAddressDetail: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/baseAgency.fragment.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const BaseAgency: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/baseRoom.fragment.gql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const BaseRoom: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/createInquiry.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const createInquiry: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/incrementPageView.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const incrementPageView: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/sendForm.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const SendForm: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/sendToSupport.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const SendToSupport: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/countProperties.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const countProperties: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getAgencyById.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const agencyById: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getAvailableTags.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getAvailableTags: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getCities.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const cities: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getCityById.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getCityById: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getFeaturedProperties.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const featuredProperties: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getHomeProperties.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getHomeProperties: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getPrefectureByName.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const prefectureByName: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getProperties.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getProperties: DocumentNode;
export const SimplifiedRoom: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getProperty.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getPropertyByRoomCode: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getPropertyPageView.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getPropertyPageView: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getServiceLines.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getServiceLines: DocumentNode;

  export default defaultDocument;
}
    

declare module '*/getStation.graphql' {
  import { DocumentNode } from 'graphql';
  const defaultDocument: DocumentNode;
  export const getStation: DocumentNode;

  export default defaultDocument;
}
    