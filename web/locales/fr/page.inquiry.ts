export default {
  purposes: {
    check_availability: 'Je veux connaître la disponibilité',
    request_tour: 'Je veux faire une visite',
    visit_office: 'Je veux visiter votre bureau',
    check_other_property:
      "Je veux aussi savoir d'autres informations de propriété",
    other: 'Autre',
  },
  whenToMoveIn: {
    asap: 'Dès que possible',
    in_1_or_2_weeks: 'Dans 1 semaine - 2 semaines',
    in_a_month: 'Dans un mois',
    in_2_months: 'Dans 2 mois',
    once_find_one: 'Quand je trouverais un apartment qui me conviens',
    other: 'Autre',
  },
  propertyInquiry: "Recherche d'apartment",
  purposeTitle: 'Objectif de la recherche',
  languageTitle: 'langue préférée',
  labelName: 'Nom',
  labelEmail: 'E-mail',
  labelPhoneNumber: 'Numéro de téléphone',
  labelMessage: 'Message',
  placeholderPhoneNumber: 'Numéro de téléphone',
  placeholderMessage:
    "Ce que vous souhaitez demander à l'agent tel que le budget, l'emplacement et la date préférés pour une visite d'apartment",
  placeholderMessageRenew:
    "Ce que vous souhaitez demander à l'agent tel que le budget, l'emplacement et la date préférés pour une visite d'apartment. S'il y a une propriété particulière qui vous intéresse, vous pouvez partager son URL à partir de sites Web tels que Suumo, Home et autres.",
  labelPolicy: "je suis d'accord avec le",
  buttonSubmit: 'Soumettre',
  thankTitle: 'Merci!',
  thankText:
    'Votre soumission a été reçue avec succès et notre agent vous contactera bientôt.',
  thankText2:
    'Si vous avez une question urgente, veuillez nous appeler au 03-4579-2103',
  leaveDialogMessage:
    "Vous n'avez pas encore soumis la demande. Vous pouvez également nous joindre en appelant.",
  leaveConfirmButton: 'Partir',
  leaveCancelButton: '03-4579-2103',
  titleDialog: "Attendez! Vous n'avez pas encore soumis la demande",
  businessHourDialog: "Heures d'ouverture: 10h à 19h",
}
