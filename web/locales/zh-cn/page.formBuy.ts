import {
  EnumFormFormBuyPurpose as Purpose,
  EnumFormFormBuyPaymentMethod as PaymentMethod,
  EnumFormFormBuyPropertyType as PropertyType,
} from '~/types/types'

export default {
  purpose: {
    label: '购房目的',
    options: {
      [Purpose.OwnUse]: '自住',
      [Purpose.Investment]: '投资',
      [Purpose.OwnUseInvestment]: '自住兼投资',
    },
  },
  header: '快速买房委托申请',
  title: '快速买房委托申请',
  description:
    '请根据您的需求填写表格，我们专业的房屋管家会及时与您联系提供服务。',
  propertyType: {
    label: '签证类型',
    options: {
      [PropertyType.MansionApartment]: '公寓',
      [PropertyType.House]: '独栋住宅',
      [PropertyType.ResidentialBuilding]: '整栋住宅楼',
      [PropertyType.OfficeBuilding]: '办公楼',
      [PropertyType.Land]: '土地',
      [PropertyType.Other]: '其他',
    },
  },
  desiredAreas: {
    label: '购房区域',
    placeholder: '东京',
  },
  budget: {
    label: '预算',
    options: {
      a50m: '五千万日元',
      a100m: '一亿日元',
      a300m: '三亿日元',
      a500m: '五亿日元',
      over_1b: '十亿日元及以上',
    },
  },
  paymentMethod: {
    label: '付款方式',
    options: {
      [PaymentMethod.Cash]: '现金',
      [PaymentMethod.Loan]: '贷款',
      [PaymentMethod.Other]: '其他',
    },
  },
  timeHorizon: {
    label: '购房时间范围',
    placeholder: '几个月内',
  },
  note: {
    label: '备注',
    placeholder: '关于您的要求请尽可能详细描述更多',
  },
  submit: '提交',
}
