import {
  EnumFormFormRentOccupation as Occupation,
  EnumFormFormRentVisaStatus as VisaStatus,
  EnumFormFormRentWhenToMoveIn as WhenToMoveIn,
  EnumFormFormRentOtherPreferences as OtherPreferences,
} from '~/types/types'

export default {
  commonPlaceholders: {
    select: '请选择',
  },
  required: {
    input: '请填写必填项',
    select: '请选择其中一个选项',
    email: 'Please fill the the correct email',
  },
  header: '快速租房委托申请',
  title: '快速租房委托申请',
  description:
    '请根据您的需求填写表格，我们专业的房屋管家会及时与您联系提供服务。',
  name: {
    label: '姓名',
    placeholder0: '姓',
    placeholder1: '名',
  },
  email: {
    label: '邮箱地址',
    placeholder: '<EMAIL>',
  },
  phone: {
    label: '电话号码',
    placeholder: '电话号码（非必须）',
  },
  preferredLanguage: {
    label: '首选沟通语言',
    options: {
      en: 'English',
      jp: '日本語',
      zh: '中文',
      kr: '한국',
    },
  },
  occupation: {
    label: '职业类型',
    options: {
      [Occupation.CompanyEmployee]: '公司职员',
      [Occupation.PartTimer]: '兼职临时工',
      [Occupation.Student]: '学生',
      [Occupation.Unemployed]: '无业',
      [Occupation.Other]: '其他',
    },
  },
  visaStatus: {
    label: '签证类型',
    options: {
      [VisaStatus.WorkVisa]: '工作签证',
      [VisaStatus.Student]: '留学签证',
      [VisaStatus.LongTermResident]: '长期居住签证',
      [VisaStatus.PermanentResident]: '永住者',
      [VisaStatus.JapaneseNationality]: '日本籍',
      [VisaStatus.Other]: '其他',
    },
  },
  rentBudget: {
    label: '租金预算',
    options: {
      num_in_k: '¥{num},000',
      over_num_in_k: '¥{num},000 及以上',
    },
  },
  floorPlan: {
    label: '房型',
    options: {
      or_more: '{plan} 及以上',
    },
  },
  size: {
    label: '最小面积',
    options: {
      num: '{num}㎡',
      or_more: '{num}㎡ 及以上',
    },
  },
  desiredLocation: {
    label: '理想位置',
    placeholder: '请输入车站名称、或区域',
  },
  stationDistance: {
    label: '到最近车站的距离',
    options: {
      minute_walk: '步行 {num} 分钟',
      dont_care: '无所谓',
    },
  },
  whenToMoveIn: {
    label: '入住时间',
    options: {
      [WhenToMoveIn.Asap]: '尽快',
      [WhenToMoveIn.In_1Or_2Weeks]: '一到两周以内',
      [WhenToMoveIn.InAMonth]: '一个月内',
      [WhenToMoveIn.In_2Months]: '两个月内',
      [WhenToMoveIn.OnceFindOne]: '找到合适房源再考虑入住',
      [WhenToMoveIn.Other]: '其他',
    },
  },
  otherPreferences: {
    label: '其他租房偏好要求',
    options: {
      [OtherPreferences.Not_1stFloor]: '一楼以上',
      [OtherPreferences.SeparateBathToilet]: '卫浴厕所干湿分离',
      [OtherPreferences.AutoLockEntrance]: '楼房大门带安全自动锁',
      [OtherPreferences.WashStand]: '独立洗面台',
      [OtherPreferences.BicyclePark]: '自行车停车位',
      [OtherPreferences.PetFriendly]: '可养宠物',
      [OtherPreferences.PackageDeliveryBox]: '快递寄存箱',
      [OtherPreferences.Elevator]: '有电梯',
      [OtherPreferences.Balcony]: '有阳台',
      [OtherPreferences.NoKeyMoney]: '无钥匙更新费',
      [OtherPreferences.NoDeposit]: '无押金',
      [OtherPreferences.Other]: '其他',
    },
  },
  note: {
    label: '备注',
    placeholder: '请尽可能详细描述更多关于您的要求',
    placeholderRenew: '请尽可能分享更多信息。如果您对特定的物件感兴趣，可以直接分享来自SUUMO、HOME\'s等网站的链接给我们，我们同样能够为您提供帮助。',
  },
  agreement: {
    labelPolicy: '我已阅读并同意',
    privacyPolicy: '隐私政策及个人信息处理',
  },
  submit: '提交',
}
