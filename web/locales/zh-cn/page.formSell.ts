import { EnumFormFormSellPropertyType as PropertyType } from '~/types/types'

export default {
  header: '快速售房委托申请',
  title: '快速售房委托申请',
  description:
    '请根据您的需求填写表格，我们专业的房屋管家会及时与您联系提供服务。',
  propertyType: {
    label: '签证类型',
    options: {
      [PropertyType.MansionApartment]: '公寓',
      [PropertyType.House]: '独栋住宅',
      [PropertyType.ResidentialBuilding]: '整栋住宅楼',
      [PropertyType.OfficeBuilding]: '办公楼',
      [PropertyType.Land]: '土地',
      [PropertyType.Other]: '其他',
    },
  },
  nearestStation: {
    label: '最近的车站',
    placeholder: '涩谷站',
  },
  desiredPrice: {
    label: '预期售价',
    placeholder: '一亿日元',
  },
  submit: 'Submit',
}
