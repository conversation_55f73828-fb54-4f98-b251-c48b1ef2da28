import {
  EnumPropertyBuildingType as BuildingType,
  EnumPropertyRoomsAvailabilityStatus as AvailabilityStatus,
  EnumPropertyRoomsContractType as ContractType,
  EnumPropertyRoomsFeesMoveInDepositFeeType as MoveInFeeType,
  EnumPropertyRoomsFeesOthersChargeTiming as OthersChargeTiming,
  EnumPropertyRoomsFeesOthersFeeType as OthersFeeType,
  EnumPropertyRoomsMainWindow as MainWindow,
  EnumPropertyRoomsTransactionType as TransactionType,
  EnumPropertyRoomsVisibility as Visibility,
  EnumPropertyStructure as Structure,
} from '~/types/types'

export default {
  property: {
    buildingType: {
      [BuildingType.Apartment]: '公寓',
      [BuildingType.House]: '独栋公寓',
      [BuildingType.ShareHouse]: '合租房',
      [BuildingType.MonthlyApartment]: '月租型公寓',
    },
    structure: {
      [Structure.Wooden]: '木造',
      [Structure.LightSteel]: '轻钢构屋',
      [Structure.HeavySteel]: '重型钢构',
      [Structure.Steel]: '钢架',
      [Structure.ReinforceConcrete]: '钢筋混拟土',
      [Structure.SteelFramedReinforcedConcreteBlock]: '钢骨钢筋混凝土构造',
      [Structure.Other]: '其他',
    },
  },
  room: {
    visibility: {
      [Visibility.Public]: '公开',
      [Visibility.Private]: '非公开',
    },
    mainWindow: {
      [MainWindow.Unknown]: '不明',
      [MainWindow.East]: '东方',
      [MainWindow.Southeast]: '东南方',
      [MainWindow.South]: '南方',
      [MainWindow.Southwest]: '南西方',
      [MainWindow.West]: '西方',
      [MainWindow.Northwest]: '西北方',
      [MainWindow.North]: '北方',
      [MainWindow.Northeast]: '東北方',
    },
    availabilityStatus: {
      [AvailabilityStatus.Available]: '可出租',
      [AvailabilityStatus.AvailableSoon]: '近期可出租',
      [AvailabilityStatus.Renovation]: '裝修',
      [AvailabilityStatus.UnderConstruction]: '改建中',
    },
    transactionType: {
      [TransactionType.Agency]: '房屋仲介',
      [TransactionType.Owner]: '屋主自租',
      [TransactionType.Agent]: '房产经纪人',
    },
    contractType: {
      [ContractType.Standard]: '一般租赁合约',
      [ContractType.FixedTerm]: '定期租赁合约',
    },
    feeType: {
      [MoveInFeeType.MonthlyMultiplier]: 'Monthly Multiplier',
      [MoveInFeeType.FixedPrice]: 'Fixed Price',
    },
    otherFeeType: {
      [OthersFeeType.KeyReplacement]: '交换钥匙',
      [OthersFeeType.Support_24Hours]: '24小时提供客户服务',
      [OthersFeeType.Cleaning]: '清洁服务',
      [OthersFeeType.Administrative]: '服务费',
      [OthersFeeType.Disinfection]: '除臭剂',
      [OthersFeeType.FireInsurance]: '火灾保险',
      [OthersFeeType.Community]: '环境保持费',
      [OthersFeeType.Other]: '其他',
      [OthersFeeType.Na]: '[missing_type]',
    },
    otherFeeChargeTiming: {
      [OthersChargeTiming.MoveIn]: '迁出',
      [OthersChargeTiming.MoveOut]: '迁入',
      [OthersChargeTiming.Monthly]: '每月',
      [OthersChargeTiming.Yearly]: '每年',
    },
  },
}
