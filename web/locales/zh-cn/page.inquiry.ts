/* eslint-disable prettier/prettier */
import { EnumInquiryPurpose } from '~/types/types'

export default {
  purposes: {
    [EnumInquiryPurpose.CheckAvailability]: '我想知道是否还有空房',
    [EnumInquiryPurpose.RequestTour]: '我想要参观房间',
    [EnumInquiryPurpose.VisitOffice]: '我想访问您的店面',
    [EnumInquiryPurpose.CheckOtherProperty]: '我还想知道其他物件的资讯',
    [EnumInquiryPurpose.Other]: '其他',
  },
  propertyInquiry: '询问的物件',
  purposeTitle: '询问的目的',
  languageTitle: '选择偏好语言',
  labelName: '姓名',
  labelEmail: '电子信箱',
  labelPhoneNumber: '电话号码',
  labelMessage: '讯息',
  placeholderPhoneNumber: '电话号码（非必须）',
  placeholderMessage: '任何您所想要询问或想确认的事情，例如预算、位置以及预约看房的时间。',
  placeholderMessageRenew: '如果您有任何问题，例如物件预算、所在地点以及参观房源的日期，您都可以咨询我们专业的房屋管家。如果您对特定的物件感兴趣，可以直接分享来自SUUMO、HOME\'s等网站的链接给我们，我们同样能够为您提供帮助。',
  labelPolicy: '我已阅读并同意',
  buttonSubmit: '提交',
  thankTitle: '诚挚感谢您',
  thankText: '我们已经收到您的需求，专员会尽快与您联繫。',
  thankText2: '',
  leaveDialogMessage: '您确定要离开本页吗？您尚未提交您的需求！',
  leaveConfirmButton: '确定离开',
  leaveCancelButton: '继续填写',
}
