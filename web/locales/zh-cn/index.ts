// Common
import tags from './common.tags'
import filter from './common.filter'
import options from './common.options'
import dateTime from './common.dateTime'
import {
  nav,
  menu,
  headerMenu,
  subHeaderMenu,
  headerBanner,
} from './common.menu'
import agency from './common.agency'
import language from './common.language'
import facilities from './common.facilities'
import areas from './common.areas'

// Page
import home from './page.home'
import inquiry from './page.inquiry'
import rentList from './page.rentList'
import roomDetail from './page.roomDetail'
import contact from './page.contact'
import city from './page.city'
import station from './page.station'
import formRent from './page.formRent'
import formBuy from './page.formBuy'
import formSell from './page.formSell'

import contactPopup from './popup.contact'
import tour from './page.tour'
import access from './page.access'

export default {
  common: {
    nav,
    tags,
    menu,
    headerMenu,
    headerBanner,
    subHeaderMenu,
    filter,
    agency,
    options,
    dateTime,
    language,
    facilities,
    areas,
    fullBadge: '已签约',
    property: '物件',
    properties: '多项物件',
    propertyCount: '{count}物件总数',
    privacyPolicy: '隐私政策及个人信息处理',
    topPage: '主页',
    top: '主页',
    stationName: '{stationName}车站 ',
    pageNotFound: '用户欲访问的网页内容已被删除、移动或从未存在',
    accessByWalk: '{line}{station}车站 徒歩{minute}分钟',
    accessByBus: '{line}{station}车站 坐巴士{minute}分钟',
    minWalkFrom: '{station}车站 徒歩{time}分钟（{line}）',
    maintenanceFee: '管理费',
    noMaintenanceFee: '免费管理费',
    addressDetail: '{prefecture}{city}{chome}',
    rent: '租',
    prefecture: {
      tokyo: '东京都',
    },
    askAgent: '营业时间：平日 10AM - 7PM',
    callAgent: '联络我们',
    years: '{count}年份',
    aMonth: '一个月',
  },
  home,
  inquiry,
  rentList,
  roomDetail,
  contact,
  city,
  station,
  formRent,
  formBuy,
  formSell,
  contactPopup,
  tour,
  access,
}
