export default {
  purposes: {
    check_availability: 'quiero saber disponibilidad',
    request_tour: 'Quiero visitar el apartamento.',
    visit_office: 'quiero visitar vuestra oficina',
    check_other_property:
      'También quiero saber otra información de otras propiedades',
    other: 'Otro',
  },
  whenToMoveIn: {
    asap: 'Lo antes posible',
    in_1_or_2_weeks: 'En 1 semana - 2 semanas',
    in_a_month: 'En un mes',
    in_2_months: 'en 2 meses',
    once_find_one: 'Cuando encuentro uno bonito',
    other: 'Otro',
  },
  propertyInquiry: 'Consulta sobre la propiedad',
  purposeTitle: 'Propósito de la consulta',
  languageTitle: 'Idioma preferido',
  labelName: 'Nombre',
  labelEmail: 'Correo electrónico',
  labelPhoneNumber: 'Número de teléfono',
  labelMessage: 'Mensaje',
  placeholderPhoneNumber: 'Número de teléfono',
  placeholderMessage:
    'Cualquier cosa que desee preguntarle al agente, como el presupuesto deseado, la ubicación y la fecha para un recorrido por la habitación.',
  placeholderMessageRenew:
    "Cualquier cosa que desee preguntarle al agente, como el presupuesto deseado, la ubicación y la fecha para un recorrido por la habitación. Si hay una propiedad en particular que le interesa, puede compartir su URL desde sitios web como SUUMO, HOME's y otros.",
  labelPolicy: 'Estoy de acuerdo con la',
  buttonSubmit: 'Entregar',
  thankTitle: '¡Gracias!',
  thankText:
    'Su envío fue recibido exitosamente y nuestro agente se comunicará con usted pronto.',
  thankText2: 'Si tiene un asunto urgente por favor llámenos al 03-4579-2103',
  leaveDialogMessage:
    'Aún no has enviado la solicitud. También puedes comunicarte con nosotros llamando.',
  leaveConfirmButton: 'Salir',
  leaveCancelButton: '03-4579-2103',
  titleDialog: '¡Espera! No  se ha enviado aún.',
  businessHourDialog: 'Horario comercial: 10 a. m. - 7 p. m.',
}
