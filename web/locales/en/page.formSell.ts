import { EnumFormFormSellPropertyType as PropertyType } from '~/types/types'

export default {
  required: {
    input: 'Please fill the required',
    select: 'Please choose one of the option',
    email: 'Please fill the the correct email',
  },
  header: 'for Sell',
  title: 'Quick Form for Sell',
  description:
    'Please fill out this form with your preferred criteria and our agent will respond to you as soon as possible.',
  propertyType: {
    label: 'Property Type',
    options: {
      [PropertyType.MansionApartment]: 'Mansion/Apartment',
      [PropertyType.House]: 'House',
      [PropertyType.ResidentialBuilding]: 'Residential Building',
      [PropertyType.OfficeBuilding]: 'Office Building',
      [PropertyType.Land]: 'Land',
      [PropertyType.Other]: 'Other',
    },
  },
  nearestStation: {
    label: 'Nearest Station',
    placeholder: 'Shibuya Station',
  },
  desiredPrice: {
    label: 'Desired Sales Price',
    placeholder: '100M yen',
  },
  submit: 'Submit',
}
