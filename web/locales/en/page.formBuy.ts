import {
  EnumFormFormBuyPurpose as Purpose,
  EnumFormFormBuyPaymentMethod as PaymentMethod,
  EnumFormFormBuyPropertyType as PropertyType,
} from '~/types/types'

export default {
  required: {
    input: 'Please fill the required',
    select: 'Please choose one of the option',
    email: 'Please fill the the correct email',
  },
  header: 'for Buy',
  title: 'Quick Form for Buy',
  description:
    'Please fill out this form with your preferred criteria and our agent will respond to you as soon as possible.',
  purpose: {
    label: 'Purpose',
    options: {
      [Purpose.OwnUse]: 'Own use',
      [Purpose.Investment]: 'Investment',
      [Purpose.OwnUseInvestment]: 'Own use & Investment',
    },
  },
  propertyType: {
    label: 'Property Type',
    options: {
      [PropertyType.MansionApartment]: 'Mansion/Apartment',
      [PropertyType.House]: 'House',
      [PropertyType.ResidentialBuilding]: 'Residential Building',
      [PropertyType.OfficeBuilding]: 'Office Building',
      [PropertyType.Land]: 'Land',
      [PropertyType.Other]: 'Other',
    },
  },
  desiredAreas: {
    label: 'Desired Areas',
    placeholder: 'Tokyo',
  },
  budget: {
    label: 'Budget',
    options: {
      a50m: '50M JPY',
      a100m: '100M JPY',
      a300m: '300M JPY',
      a500m: '500M JPY',
      over_1b: '1B JPY or more',
    },
  },
  paymentMethod: {
    label: 'Payment Method',
    options: {
      [PaymentMethod.Cash]: 'Cash',
      [PaymentMethod.Loan]: 'Loan',
      [PaymentMethod.Other]: 'Other',
    },
  },
  timeHorizon: {
    label: 'Time Horizon for Purchase',
    placeholder: 'In a few month',
  },
  note: {
    label: 'Note',
    placeholder: 'Please share as much information as possible.',
  },
  submit: 'Submit',
}
