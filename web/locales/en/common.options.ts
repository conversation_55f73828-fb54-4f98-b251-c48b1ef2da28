import {
  EnumPropertyBuildingType as BuildingType,
  EnumPropertyRoomsAvailabilityStatus as AvailabilityStatus,
  EnumPropertyRoomsContractType as ContractType,
  EnumPropertyRoomsFeesMoveInDepositFeeType as MoveInFeeType,
  EnumPropertyRoomsFeesOthersChargeTiming as OthersChargeTiming,
  EnumPropertyRoomsFeesOthersFeeType as OthersFeeType,
  EnumPropertyRoomsMainWindow as MainWindow,
  EnumPropertyRoomsTransactionType as TransactionType,
  EnumPropertyRoomsVisibility as Visibility,
  EnumPropertyStructure as Structure,
} from '~/types/types'

export default {
  property: {
    buildingType: {
      [BuildingType.Apartment]: 'Apartment',
      [BuildingType.House]: 'House',
      [BuildingType.ShareHouse]: 'Share House',
      [BuildingType.MonthlyApartment]: 'Monthly Apartment',
    },
    structure: {
      [Structure.Wooden]: 'Wooden',
      [Structure.LightSteel]: 'Light Steel',
      [Structure.HeavySteel]: 'Heavy Steel',
      [Structure.Steel]: 'Steel',
      [Structure.ReinforceConcrete]: 'Reinforce Concrete',
      [Structure.SteelFramedReinforcedConcreteBlock]:
        'Steel Framed Reinforced Concrete Block',
      [Structure.Other]: 'Other',
    },
  },
  room: {
    visibility: {
      [Visibility.Public]: 'Public',
      [Visibility.Private]: 'Private',
    },
    mainWindow: {
      [MainWindow.Unknown]: 'Unknown',
      [MainWindow.East]: 'East',
      [MainWindow.Southeast]: 'Southeast',
      [MainWindow.South]: 'South',
      [MainWindow.Southwest]: 'Southwest',
      [MainWindow.West]: 'West',
      [MainWindow.Northwest]: 'Northwest',
      [MainWindow.North]: 'North',
      [MainWindow.Northeast]: 'Northeast',
    },
    availabilityStatus: {
      [AvailabilityStatus.Available]: 'Available',
      [AvailabilityStatus.AvailableSoon]: 'Available Soon',
      [AvailabilityStatus.Renovation]: 'Renovation',
      [AvailabilityStatus.UnderConstruction]: 'Under Construction',
    },
    transactionType: {
      [TransactionType.Agency]: 'Agency',
      [TransactionType.Owner]: 'Owner',
      [TransactionType.Agent]: 'Agent',
    },
    contractType: {
      [ContractType.Standard]: 'Ordinary Lease',
      [ContractType.FixedTerm]: 'Fixed Term Lease',
    },
    feeType: {
      [MoveInFeeType.MonthlyMultiplier]: 'Monthly Multiplier',
      [MoveInFeeType.FixedPrice]: 'Fixed Price',
    },
    otherFeeType: {
      [OthersFeeType.KeyReplacement]: 'Key Replacement',
      [OthersFeeType.Support_24Hours]: 'Support 24 Hours',
      [OthersFeeType.Cleaning]: 'Cleaning',
      [OthersFeeType.Administrative]: 'Administrative',
      [OthersFeeType.Disinfection]: 'Deodorant',
      [OthersFeeType.FireInsurance]: 'Fire Insurance',
      [OthersFeeType.Community]: 'Community Fee',
      [OthersFeeType.Other]: 'Other',
      [OthersFeeType.Na]: '[missing_type]',
    },
    otherFeeChargeTiming: {
      [OthersChargeTiming.MoveIn]: 'Move In',
      [OthersChargeTiming.MoveOut]: 'Move Out',
      [OthersChargeTiming.Monthly]: 'Monthly',
      [OthersChargeTiming.Yearly]: 'Yearly',
    },
  },
}
