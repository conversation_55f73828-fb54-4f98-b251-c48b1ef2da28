/* eslint-disable prettier/prettier */
export default {
  foreignersWelcome: 'FOREIGNERS WELCOME',
  maintenanceFee: 'Maintenance Fee',
  propertyDetails: 'Property Details',
  layout: 'Layout',
  size: 'Size',
  yearBuilt: 'Year Built',
  floor: 'Floor',
  windowDirection: 'Window Direction',
  availableFrom: 'Available from',
  type: 'Type',
  structure: 'Structure',
  features: 'Features',
  contractType: 'Contract Type',
  transactionType: 'Transaction Type',
  updatedDate: 'Updated Date',
  nextUpdateDate: 'Next Update Date',
  nextUpdateContent: 'Within 15 days after the updated date',
  moreProperties: 'More Properties',
  otherAvailableRooms: 'Other Available Rooms',
  feeNotice: 'Total Monthly Cost is calculated based on moving on the 1st of the month. Please note that all listed fees are estimated amounts and may differ depending on your move in date. Please inquire for exact costs.',
  leopalaceNotice: 'The guarantor fee may vary from 100% to 120% of monthly cost including the community fee. You also need to join the fire insurance. ',
  monthlyCost: 'MONTHLY COST',
  estimatedFee: 'ESTIMATED MOVE IN FEES',
  button: {
    askAvailability: 'ASK ROOM AVAILABILITY (FREE)',
    requestTour: 'REQUEST A TOUR',
  },
  availability: {
    now: 'Now',
    ask: 'Please ask the agent',
  },
  listedBy: 'Listed by',
  language: 'Language',
  businessHours: 'Business Hours',
  rentFee: 'Rent',
  totalMonthlyCost: 'Total Monthly Cost',
  deposit: 'Deposit',
  keyMoney: 'Key Money',
  agencyFee: 'Agency Fee',
  guarantorFee: 'Guarantor Fee',
  totalMoveInFees: 'Total Move-in Fees',
  pvPopupLabel: 'HOT 🔥',
  pvPopupContent: 'This property was checked by <strong>{count} people</strong> within <strong>24 hours</strong>',
  meta: {
    title: '{propertyName}, {prefectureName} - Living Japan',
    description: '{propertyName} for rent in {cityName}, {prefectureName}. Check monthly price, photos, layout, room size, access, features and more. ',
  },
  agentInfo: "Agent Information",
  company: 'Company',
  address: 'Address',
  shortAddressValue: '{chome}-{streetAddress}, {prefecture}',
  addressValue: '{buildingName}, {chome}-{streetAddress}, {prefecture}',
  serviceTitle: 'FREE UTILITIES SET UP SERVICE',
  serviceElectricity: 'Electricity',
  serviceGas: 'Gas',
  serviceWater: 'Water',
  serviceWifi: 'Internet / Wifi',
}
