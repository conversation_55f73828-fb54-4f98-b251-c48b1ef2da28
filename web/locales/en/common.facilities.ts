export default {
  basic: {
    name: 'Basic',
    items: {
      free_wifi: 'Free Internet',
      air_conditioner: 'Air Conditioner',
      floor_heating_system: 'Floor Heating System',
      bath_toilet_separated: 'Bath / Toilet Separated',
      wash_basin: 'Wash Basin',
      bath_reheating_system: 'Bath Reheating System',
      bath_dehumidifier: 'Bath Dehumidifier',
      jacuzzi: 'Jacuzzi',
      mist_sauna: 'Mist Sauna',
      bidet: 'Washlet Toilet',
      washing_machine: 'Washing Machine',
      washing_machine_space: 'Washing Machine Inside',
      electric_stove: 'Induction Stove',
      gas_stove: 'Gas Stove',
      builtin_kitchen: 'Built-in Kitchen',
      counter_kitchen: 'Counter Kitchen',
      electric_water_heater: 'Electric Water Heater',
      gas_water_heater: 'Gas Water Heater',
      loft: 'Loft',
      walkin_closet: 'Walk-in Closet',
      auto_lock: 'Self Locking Entrance',
    },
    tv_intercom: 'Monitor Intercom',
    ceiling_light_installed: 'Ceiling Light',
    furnished: 'Furnished',
  },
  building: {
    name: 'Building',
    items: {
      parking: 'Parking lot',
      motorbike_parking: 'Motorbike Parking ',
      biycle_parking: 'Bicycle Parking ',
      delivery_box: 'Delivery Box',
      elevator: 'Elevator',
      concierge: 'Concierge',
      balcony: 'Balcony',
      communal_garden: 'Communal Garden',
      private_garden: 'Private Garden',
    },
  },
  other: {
    name: 'other',
    items: {
      allow_pet: 'Pet Allowed',
      allow_musical_instrument: 'Musical Instruments Allowed',
      allow_room_sharing: 'Room Sharing Allowed',
      corner_room: 'Corner Room',
      barrier_free: 'Barrior Free',
    },
  },
}
