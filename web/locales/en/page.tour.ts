import {
  EnumFormFormTourPurpose as Purpose,
  EnumFormFormTourPropertyType as PropertyType,
} from '~/types/types'

export default {
  menuItem: {
    home: 'Home',
    rent: 'Rent',
    buy: 'Buy',
    vipTour: 'VIP Tour',
    inquiryNow: 'Inquire Now',
    forRent: 'For Rent',
    property: 'Property',
  },
  tourInformation: {
    title: 'TOUR INFORMATION',
    subTitle: 'Our professional agent takes you to the exceptional. ',
    description:
      'Living Japan is here to introduce carefully selected real estate properties with high asset values. Taking advantage of the wide networks and expertise, we partner with major real estate firms in Japan to provide you with exclusive information and services. ',
    button: 'MAKE AN INQUIRY →',
  },
  allInclusive: {
    title: 'ALL INCLUSIVE TOUR',
    description:
      'Our inclusive real estate tour covers below. You can also add anything what you need upon request.',
    flightTicket: 'Flight Ticket',
    hotel: 'Hotel',
    limoTaxi: 'Limo Taxi',
    pcrTest: 'PCR Test\n(Negative Certificate)',
    multilingual: 'Multilingual Support',
    dinnerInvitation: 'Dinner Invitation',
  },
  tourFlow: {
    title: 'TOUR FLOW',
    stepOne: 'STEP 1',
    titleOne: 'Make an inquiry',
    DescriptionOne: 'Please apply a private real estate tour here. ',
    stepTwo: 'STEP 2',
    titleTwo: 'Free consultation',
    DescriptionTwo:
      'Schedule a free online consultation and discuss what to buy in Japan.',
    stepThree: 'STEP 3',
    titleThree: 'Make you tour plan',
    DescriptionThree: 'We’ll make a plan of real estate tour just for you.',
    stepFour: 'STEP 4',
    titleFour: 'Real estate tour in Japan',
    DescriptionFour: 'Visit Japan and see in person what you’re intesrsted in.',
    stepFive: 'STEP 5',
    titleFive: 'Buy property in Japan',
    DescriptionFive:
      'Sign a contract and complete the purchase if you want to buy it. ',
  },
  tourSchedule: {
    title: 'TOUR SCHEDULE',
    dayOne: 'DAY 1',
    dayTimeOne_1: 'Until 5PM',
    dayDescriptionOne_1: 'Aiport pick-up and take to your hotel',
    dayTimeOne_2: '6PM - 8PM',
    dayDescriptionOne_2: 'Special dinner with us',
    dayTwo: 'DAY 2',
    dayTimeTwo_1: '10AM - 5PM',
    dayDescriptionTwo_1: 'Real estate tour in Tokyo',
    dayTimeTwo_2: '6PM - 8PM',
    dayDescriptionTwo_2: 'Consultation and dinner',
    dayThree: 'DAY 3',
    dayTimeThree_1: '10AM - 3PM',
    dayDescriptionThree_1: 'Contract procedures if you want to buy',
    description:
      '* This is just an example of schedule. We can create our own schedule for you.',
  },
  inquiry: {
    title: 'Real Estate Tour Inquiry',
    description:
      'Please fill out this form and we will respond to you and schedule an online meeting as soon as possible.',
    buttonSubmit: 'SUBMIT REQUEST',
    purpose: {
      label: 'Purpose',
      options: {
        [Purpose.OwnUse]: 'Own use',
        [Purpose.Investment]: 'Investment',
        [Purpose.OwnUseInvestment]: 'Own use & Investment',
      },
    },
    propertyType: {
      label: 'Property Type',
      options: {
        [PropertyType.MansionApartment]: 'Mansion/Apartment',
        [PropertyType.House]: 'House',
        [PropertyType.ResidentialBuilding]: 'Residential Building',
        [PropertyType.OfficeBuilding]: 'Office Building',
        [PropertyType.Land]: 'Land',
        [PropertyType.Other]: 'Other',
      },
    },
    budget: {
      label: 'Budget',
      options: {
        a50m: '50M JPY',
        a100m: '100M JPY',
        a300m: '300M JPY',
        a500m: '500M JPY',
        over_1b: '1B JPY or more',
      },
    },
    note: {
      label: 'Note',
      placeholder: 'Please share as much information as possible.',
    },
    commonPlaceholders: {
      select: 'Please select',
    },
    required: {
      input: 'Please fill the required',
      select: 'Please choose one of the option',
      email: 'Please fill the the correct email',
    },
  },
  thanks: {
    title: 'THANK YOU!',
    description: 'Your request has been sent.',
    button: ' Go Home ',
  },
  breadCrumbs: {
    home: 'Home',
    vipTour: 'Vip Tour',
    tourInquiry: 'Real Estate Tour Inquiry',
    thanks: 'Thanks',
  },
  callAgent: 'Call Agent',
}
