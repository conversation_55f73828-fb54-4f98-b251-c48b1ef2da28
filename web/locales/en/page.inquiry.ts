/* eslint-disable prettier/prettier */
import {
  EnumInquiryPurpose,
  EnumFormFormRentWhenToMoveIn as WhenToMoveIn,
} from '~/types/types'

export default {
  purposes: {
    [EnumInquiryPurpose.CheckAvailability]: 'I want to know availability',
    [EnumInquiryPurpose.RequestTour]: 'I want to take a room tour',
    [EnumInquiryPurpose.VisitOffice]: 'I want to visit your store',
    [EnumInquiryPurpose.CheckOtherProperty]:
      'I also want to know other property information',
    [EnumInquiryPurpose.Other]: 'Other',
  },
  whenToMoveIn: {
    [WhenToMoveIn.Asap]: 'As soon as possible',
    [WhenToMoveIn.In_1Or_2Weeks]: 'In 1 week - 2 weeks',
    [WhenToMoveIn.InAMonth]: 'In a month',
    [WhenToMoveIn.In_2Months]: 'In 2 months',
    [WhenToMoveIn.OnceFindOne]: 'When I find a nice one',
    [WhenToMoveIn.Other]: 'Other',
  },
  propertyInquiry: 'Property Inquiry',
  purposeTitle: 'Purpose of inquiry',
  languageTitle: 'Preferred Language',
  labelName: 'Name',
  labelEmail: 'Email',
  labelPhoneNumber: 'Phone Number',
  labelMessage: 'Message',
  placeholderPhoneNumber: 'Phone Number',
  placeholderMessage:
    'Anything you want to ask the agent such as preferred budget, location, and date for a room tour.',
  placeholderMessageRenew:
    "Anything you want to ask the agent such as preferred budget, location, and date for a room tour. If there's a particular property you're interested in, you can share its URL from websites such as SUUMO, HOME's, and others.",
  labelPolicy: 'I agree to the',
  buttonSubmit: 'Submit',
  thankTitle: 'Thank you!',
  thankText:
    'Your submission was successfully received and our agent will contact you soon.',
  thankText2: 'If you have an urgent matter please call us at 03-4579-2103',
  leaveDialogMessage:
    "You haven't submitted the request yet. You can also reach us by calling.",
  leaveConfirmButton: 'Leave',
  leaveCancelButton: '03-4579-2103',
  titleDialog: 'Wait! Not submitted yet.',
  businessHourDialog: 'Business Hours: 10AM - 7PM',
}
