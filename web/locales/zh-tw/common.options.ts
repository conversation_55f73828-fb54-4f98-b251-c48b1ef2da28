import {
  EnumPropertyBuildingType as BuildingType,
  EnumPropertyRoomsAvailabilityStatus as AvailabilityStatus,
  EnumPropertyRoomsContractType as ContractType,
  EnumPropertyRoomsFeesMoveInDepositFeeType as MoveInFeeType,
  EnumPropertyRoomsFeesOthersChargeTiming as OthersChargeTiming,
  EnumPropertyRoomsFeesOthersFeeType as OthersFeeType,
  EnumPropertyRoomsMainWindow as MainWindow,
  EnumPropertyRoomsTransactionType as TransactionType,
  EnumPropertyRoomsVisibility as Visibility,
  EnumPropertyStructure as Structure,
} from '~/types/types'

export default {
  property: {
    buildingType: {
      [BuildingType.Apartment]: '公寓',
      [BuildingType.House]: '獨棟公寓',
      [BuildingType.ShareHouse]: '合租房',
      [BuildingType.MonthlyApartment]: '月租型公寓',
    },
    structure: {
      [Structure.Wooden]: '木造',
      [Structure.LightSteel]: '輕鋼構屋',
      [Structure.HeavySteel]: '重型鋼構',
      [Structure.Steel]: '鋼架',
      [Structure.ReinforceConcrete]: '鋼筋混擬土',
      [Structure.SteelFramedReinforcedConcreteBlock]: '鋼骨鋼筋混凝土構造',
      [Structure.Other]: '其他',
    },
  },
  room: {
    visibility: {
      [Visibility.Public]: '公開',
      [Visibility.Private]: '非公開',
    },
    mainWindow: {
      [MainWindow.Unknown]: '不明',
      [MainWindow.East]: '東方',
      [MainWindow.Southeast]: '東南方',
      [MainWindow.South]: '南方',
      [MainWindow.Southwest]: '南西方',
      [MainWindow.West]: '西方',
      [MainWindow.Northwest]: '西北方',
      [MainWindow.North]: '北方',
      [MainWindow.Northeast]: '東北方',
    },
    availabilityStatus: {
      [AvailabilityStatus.Available]: '可出租',
      [AvailabilityStatus.AvailableSoon]: '近期可出租',
      [AvailabilityStatus.Renovation]: '裝修',
      [AvailabilityStatus.UnderConstruction]: '改建中',
    },
    transactionType: {
      [TransactionType.Agency]: '房屋仲介',
      [TransactionType.Owner]: '屋主自租',
      [TransactionType.Agent]: '房地產經理人',
    },
    contractType: {
      [ContractType.Standard]: '普通租賃合約',
      [ContractType.FixedTerm]: '定期租賃合約',
    },
    feeType: {
      [MoveInFeeType.MonthlyMultiplier]: 'Monthly Multiplier',
      [MoveInFeeType.FixedPrice]: 'Fixed Price',
    },
    otherFeeType: {
      [OthersFeeType.KeyReplacement]: '交換鑰匙',
      [OthersFeeType.Support_24Hours]: '24小時提供客戶服務',
      [OthersFeeType.Cleaning]: '清潔服務',
      [OthersFeeType.Administrative]: '服務費',
      [OthersFeeType.Disinfection]: '除臭劑',
      [OthersFeeType.FireInsurance]: '火災保險',
      [OthersFeeType.Community]: '環境保持費',
      [OthersFeeType.Other]: '其他',
      [OthersFeeType.Na]: '[missing_type]',
    },
    otherFeeChargeTiming: {
      [OthersChargeTiming.MoveIn]: '遷出',
      [OthersChargeTiming.MoveOut]: '遷入',
      [OthersChargeTiming.Monthly]: '每月',
      [OthersChargeTiming.Yearly]: '每年',
    },
  },
}
