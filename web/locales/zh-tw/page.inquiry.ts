/* eslint-disable prettier/prettier */
import { EnumInquiryPurpose } from '~/types/types'

export default {
  purposes: {
    [EnumInquiryPurpose.CheckAvailability]: '我想知道是否還有空房',
    [EnumInquiryPurpose.RequestTour]: '我想要參觀房間',
    [EnumInquiryPurpose.VisitOffice]: '我想訪問您的店面',
    [EnumInquiryPurpose.CheckOtherProperty]: '我還想知道其他物件的資訊',
    [EnumInquiryPurpose.Other]: '其他',
  },
  propertyInquiry: '詢問的物件',
  purposeTitle: '詢問的目的',
  languageTitle: '選擇偏好語言',
  labelName: '姓名',
  labelEmail: '電子信箱',
  labelPhoneNumber: '電話號碼',
  labelMessage: '訊息',
  placeholderPhoneNumber: '電話號碼（非必須）',
  placeholderMessage: '任何您所想要詢問或想確認的事情，例如預算、位置以及預約看房的時間。',
  placeholderMessageRenew: '如果您有任何問題，例如物件預算、所在地點以及參觀房源的日期，您都可以諮詢我們專業的房屋管家。如果您對特定的物件感興趣，可以直接分享來自SUUMO、HOME\'s等網站的連結給我們，我們同樣能夠為您提供幫助。',
  labelPolicy: '我已閱讀並同意',
  buttonSubmit: '提交',
  thankTitle: '誠摯感謝您',
  thankText: '我們已經收到您的需求，專員會盡快與您聯繫。',
  thankText2: '',
  leaveDialogMessage: '您確定要離開本頁嗎？您尚未提交您的需求！',
  leaveConfirmButton: '確定離開',
  leaveCancelButton: '繼續填寫',
}
