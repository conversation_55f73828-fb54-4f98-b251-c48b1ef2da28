import {
  EnumFormFormBuyPurpose as Purpose,
  EnumFormFormBuyPaymentMethod as PaymentMethod,
  EnumFormFormBuyPropertyType as PropertyType,
} from '~/types/types'

export default {
  header: '買屋委託申請',
  title: '買屋委託申請',
  description:
    '請根據您的要求填寫以下表格，我們的房屋經紀人在收到後會立即與您取得聯繫。',
  purpose: {
    label: '購買目的',
    options: {
      [Purpose.OwnUse]: '自用',
      [Purpose.Investment]: '投資',
      [Purpose.OwnUseInvestment]: '自用住宅兼投資',
    },
  },
  propertyType: {
    label: '房屋型態',
    options: {
      [PropertyType.MansionApartment]: '公寓',
      [PropertyType.House]: '獨棟住宅',
      [PropertyType.ResidentialBuilding]: '整棟住宅樓',
      [PropertyType.OfficeBuilding]: '辦公樓',
      [PropertyType.Land]: '土地',
      [PropertyType.Other]: '其他',
    },
  },
  desiredAreas: {
    label: '購房區域',
    placeholder: '東京',
  },
  budget: {
    label: '預算',
    options: {
      a50m: '五千萬日圓',
      a100m: '一億日圓',
      a300m: '三億日圓',
      a500m: '五億日圓',
      over_1b: '十億日圓及以上',
    },
  },
  paymentMethod: {
    label: '付款方式',
    options: {
      [PaymentMethod.Cash]: '現金',
      [PaymentMethod.Loan]: '貸款',
      [PaymentMethod.Other]: '其他',
    },
  },
  timeHorizon: {
    label: '購房時間',
    placeholder: '幾個月內',
  },
  note: {
    label: '備註信息',
    placeholder: '關於您的要求請儘可能詳細描述更多',
  },
  submit: '提交',
}
