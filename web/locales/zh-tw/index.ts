// Common
import tags from './common.tags'
import filter from './common.filter'
import options from './common.options'
import dateTime from './common.dateTime'
import { nav, menu, headerMenu, headerBanner } from './common.menu'
import agency from './common.agency'
import language from './common.language'
import facilities from './common.facilities'
import areas from './common.areas'

// Page
import home from './page.home'
import inquiry from './page.inquiry'
import rentList from './page.rentList'
import roomDetail from './page.roomDetail'
import contact from './page.contact'
import city from './page.city'
import station from './page.station'
import formRent from './page.formRent'
import formBuy from './page.formBuy'
import formSell from './page.formSell'

import contactPopup from './popup.contact'
import tour from './page.tour'
import access from './page.access'

export default {
  common: {
    headerBanner,
    nav,
    tags,
    menu,
    headerMenu,
    filter,
    agency,
    options,
    dateTime,
    language,
    facilities,
    areas,
    fullBadge: '已簽約',
    property: '物件',
    properties: '多項物件',
    propertyCount: '{count}物件總數',
    privacyPolicy: '隱私權政策及個人信息處理',
    topPage: '主頁',
    top: '主頁',
    stationName: '{stationName}車站 ',
    pageNotFound: '用戶欲訪問的網頁內容已被刪除、移動或從未存在',
    accessByWalk: '{line}{station}車站 徒歩{minute}分鐘',
    accessByBus: '{line}{station}車站 坐巴士{minute}分鐘',
    minWalkFrom: '{station}車站 徒歩{time}分鐘（{line}）',
    maintenanceFee: '管理費',
    noMaintenanceFee: '免費管理費',
    addressDetail: '{prefecture}{city}{chome}',
    rent: '租',
    prefecture: {
      tokyo: '東京都',
    },
    askAgent: '營業時間：平日 10AM - 7PM',
    callAgent: '聯絡我們',
    years: '{count}年份',
    aMonth: '一個月',
  },
  home,
  inquiry,
  rentList,
  roomDetail,
  contact,
  city,
  station,
  formRent,
  formBuy,
  formSell,
  contactPopup,
  tour,
  access,
}
