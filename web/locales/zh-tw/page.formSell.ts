import { EnumFormFormSellPropertyType as PropertyType } from '~/types/types'

export default {
  header: '賣屋委託申請',
  title: '賣屋委託申請',
  description:
    '請根據您的要求填寫以下表格，我們的房屋經紀人在收到後會立即與您取得聯繫。',
  propertyType: {
    label: '房屋型態',
    options: {
      [PropertyType.MansionApartment]: '公寓',
      [PropertyType.House]: '獨棟住宅',
      [PropertyType.ResidentialBuilding]: '整棟住宅樓',
      [PropertyType.OfficeBuilding]: '辦公樓',
      [PropertyType.Land]: '土地',
      [PropertyType.Other]: '其他',
    },
  },
  nearestStation: {
    label: '最近的車站',
    placeholder: '澀谷站',
  },
  desiredPrice: {
    label: '預期售價',
    placeholder: '一億日圓',
  },
  submit: '提交',
}
