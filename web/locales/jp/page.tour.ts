import {
  EnumFormFormTourPurpose as Purpose,
  EnumFormFormTourPropertyType as PropertyType,
} from '~/types/types'

export default {
  menuItem: {
    home: 'ホーム',
    rent: '賃貸',
    buy: '購入',
    vipTour: '現地視察ツアー',
    inquiryNow: 'お問い合わせ',
    forRent: '賃貸',
    property: '不動産を探す',
  },
  tourInformation: {
    title: '現地視察ツアー',
    subTitle: '日本の不動産に関しては、Living Japanにお任せください。',
    description:
      '日本の不動産マーケットに精通するプロフェッショナルなエージェントがお客様のご要望にあわせてご提案させていただきます。大手不動産企業とのパートナーシップやネットワークを活用し、最新の動向や不動産をご案内いたします。',
    button: 'お問い合わせする →',
  },
  allInclusive: {
    title: '視察ツアーに含まれるサービス',
    description:
      '航空券・宿泊先の手配をはじめとし、視察ツアーに必要な様々なーサービスをご用意しております。その他必要なサービスがございましたらお問い合わせください。',
    flightTicket: '往復航空券',
    hotel: 'ホテル',
    limoTaxi: 'リムジンタクシー',
    pcrTest: 'PCR検査\n（陰性証明書付き）',
    multilingual: '多言語翻訳サポート',
    dinnerInvitation: '特別ディナー招待',
  },
  tourFlow: {
    title: '現地視察ツアーの流れ',
    stepOne: 'STEP 1',
    titleOne: 'お問い合わせをする',
    DescriptionOne:
      '日本での不動産購入をご検討のお客様は、お気軽にお問い合わせください。',
    stepTwo: 'STEP 2',
    titleTwo: '不動産購入に関するコンサルテーション',
    DescriptionTwo:
      'オンラインでのヒアリングを通じて、お客様にあった不動産をご提案させていただきます。',
    stepThree: 'STEP 3',
    titleThree: '不動産ツアーの旅程提案',
    DescriptionThree:
      'お客様のご予定にあわせて不動産ツアーのプランをご提案させていただきます。',
    stepFour: 'STEP 4',
    titleFour: '不動産ツアーの実施',
    DescriptionFour:
      '日本に渡航いただきき、候補となる不動産の視察を実施します。',
    stepFive: 'STEP 5',
    titleFive: '不動産購入のご検討',
    DescriptionFive:
      'ご契約書にご署名いただき、不動産の購入手続きが完了となります。',
  },
  tourSchedule: {
    title: '現地視察ツアーの日程',
    dayOne: '1日目',
    dayTimeOne_1: '5PMまで',
    dayDescriptionOne_1:
      '空港までリムジンタクシーにてお迎えにあがり、滞在先のホテルまで送迎いたします。',
    dayTimeOne_2: '6PM - 8PM',
    dayDescriptionOne_2: '弊社エージェントとのディナーをお楽しみください。',
    dayTwo: '2日目',
    dayTimeTwo_1: '10AM - 5PM',
    dayDescriptionTwo_1:
      'リムジンタクシーにて、現地視察ツアーを実施いたします。',
    dayTimeTwo_2: '6PM - 8PM',
    dayDescriptionTwo_2:
      '視察を行った不動産に関するコンサルテーションとディナーへご招待いたします。',
    dayThree: '3日目',
    dayTimeThree_1: '10AM - 3PM',
    dayDescriptionThree_1: 'ご購入される場合はご契約手続きを行います。',
    description:
      '※こちらの日程はサンプルとなります。お客様のご都合等にあわせて最適なプランをご提案させていただきます。',
  },
  inquiry: {
    title: '現地視察ツアーのお問い合わせ',
    description: '以下のフォームに必要事項をご記入の上、お問い合わせください。',
    buttonSubmit: '送信する',
    purpose: {
      label: '不動産購入の目的',
      options: {
        [Purpose.OwnUse]: '居住用',
        [Purpose.Investment]: '投資用',
        [Purpose.OwnUseInvestment]: '居住用 & 投資用',
      },
    },
    propertyType: {
      label: '不動産の種類',
      options: {
        [PropertyType.MansionApartment]: 'マンション・アパート部屋',
        [PropertyType.House]: '一軒家',
        [PropertyType.ResidentialBuilding]: '居住用1棟ビル',
        [PropertyType.OfficeBuilding]: 'オフィス用ビル',
        [PropertyType.Land]: '土地',
        [PropertyType.Other]: 'その他',
      },
    },
    budget: {
      label: '購入予算',
      options: {
        a50m: '5,000万円',
        a100m: '1億円',
        a300m: '3億円',
        a500m: '5億円',
        over_1b: '10億円以上',
      },
    },
    note: {
      label: 'お問い合わせ',
      placeholder:
        '不動産購入に関する条件や時期等がございましたら、詳細ご記入ください。',
    },
    commonPlaceholders: {
      select: '選択してください',
    },
    required: {
      input: 'ご記入ください。',
      select: '選択してください。',
      email: '正しいメールアドレスをご記入ください。',
    },
  },
  thanks: {
    title: 'お問い合わせが送信されました。',
    description: '弊社からご連絡いたしますので、今しばらくお待ちください。',
    button: 'ホームへ戻る',
  },
  breadCrumbs: {
    home: 'ホーム',
    vipTour: '現地視察ツアー',
    tourInquiry: '現地視察ツアーのお問い合わせ',
    thanks: '送信完了',
  },
  callAgent: '電話',
}
