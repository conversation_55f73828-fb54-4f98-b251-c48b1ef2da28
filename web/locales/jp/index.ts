// Common
import tags from './common.tags'
import filter from './common.filter'
import options from './common.options'
import dateTime from './common.dateTime'
import { nav, menu, subHeaderMenu } from './common.menu'
import agency from './common.agency'
import language from './common.language'
import facilities from './common.facilities'
import areas from './common.areas'

// Page
import home from './page.home'
import inquiry from './page.inquiry'
import rentList from './page.rentList'
import roomDetail from './page.roomDetail'
import contact from './page.contact'
import city from './page.city'
import station from './page.station'
import tour from './page.tour'
import formRent from './page.formRent'
import access from './page.access'

export default {
  common: {
    nav,
    tags,
    menu,
    subHeaderMenu,
    filter,
    agency,
    options,
    dateTime,
    language,
    facilities,
    areas,
    fullBadge: '成約済み',
    property: '物件',
    properties: '物件',
    propertyCount: '{count}物件総数',
    privacyPolicy: 'プライバシーポリシー',
    topPage: 'トップページ',
    top: 'トップ',
    stationName: '{stationName}駅 ',
    pageNotFound: 'ページが見つかりません',
    accessByWalk: '{line}{station}駅 徒歩{minute}分',
    accessByBus: '{line}{station}駅 バス{minute}分',
    minWalkFrom: '{station}駅 徒歩{time}分（{line}）',
    maintenanceFee: '管理費',
    noMaintenanceFee: '管理費ゼロ',
    addressDetail: '{prefecture}{city}{chome}',
    rent: '借りる',
    prefecture: {
      tokyo: '東京都',
    },
    years: '{count}年',
    aMonth: '1 month',
  },
  home,
  inquiry,
  rentList,
  roomDetail,
  contact,
  city,
  station,
  tour,
  formRent,
  access,
}
