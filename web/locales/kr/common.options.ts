import {
  EnumPropertyBuildingType as BuildingType,
  EnumPropertyRoomsAvailabilityStatus as AvailabilityStatus,
  EnumPropertyRoomsContractType as ContractType,
  EnumPropertyRoomsFeesMoveInDepositFeeType as MoveInFeeType,
  EnumPropertyRoomsFeesOthersChargeTiming as OthersChargeTiming,
  EnumPropertyRoomsFeesOthersFeeType as OthersFeeType,
  EnumPropertyRoomsMainWindow as MainWindow,
  EnumPropertyRoomsTransactionType as TransactionType,
  EnumPropertyRoomsVisibility as Visibility,
  EnumPropertyStructure as Structure,
} from '~/types/types'

export default {
  property: {
    buildingType: {
      [BuildingType.Apartment]: '아파트/맨션',
      [BuildingType.House]: '단독 주택',
      [BuildingType.ShareHouse]: '셰어하우스',
      [BuildingType.MonthlyApartment]: '월간 맨션',
    },
    structure: {
      [Structure.Wooden]: '목조',
      [Structure.LightSteel]: '경량철골',
      [Structure.HeavySteel]: '철근 콘크리트',
      [Structure.Steel]: '철골조',
      [Structure.ReinforceConcrete]: '철골조',
      [Structure.SteelFramedReinforcedConcreteBlock]: '철골 철근 콘크리트',
      [Structure.Other]: '기타',
    },
  },
  room: {
    visibility: {
      [Visibility.Public]: '공개',
      [Visibility.Private]: '비공개',
    },
    mainWindow: {
      [MainWindow.Unknown]: '불명',
      [MainWindow.East]: '동',
      [MainWindow.Southeast]: '남동',
      [MainWindow.South]: '남',
      [MainWindow.Southwest]: '남서',
      [MainWindow.West]: '서',
      [MainWindow.Northwest]: '북서',
      [MainWindow.North]: '북',
      [MainWindow.Northeast]: '북동',
    },
    availabilityStatus: {
      [AvailabilityStatus.Available]: '공실',
      [AvailabilityStatus.AvailableSoon]: '퇴거 예정',
      [AvailabilityStatus.Renovation]: '수리중',
      [AvailabilityStatus.UnderConstruction]: '건축중',
    },
    transactionType: {
      [TransactionType.Agency]: '중개',
      [TransactionType.Owner]: '임대인',
      [TransactionType.Agent]: '대리인',
    },
    contractType: {
      [ContractType.Standard]: '보통임대차계약',
      [ContractType.FixedTerm]: '정기임대계약',
    },
    feeType: {
      [MoveInFeeType.MonthlyMultiplier]: '월간 지불',
      [MoveInFeeType.FixedPrice]: '고정 지불',
    },
    otherFeeType: {
      [OthersFeeType.KeyReplacement]: '열쇠 교환 비용',
      [OthersFeeType.Support_24Hours]: '24시간 서포트',
      [OthersFeeType.Cleaning]: '청소 비용',
      [OthersFeeType.Administrative]: '사무 수수료',
      [OthersFeeType.Disinfection]: '살균 탈취 비용',
      [OthersFeeType.FireInsurance]: '화재 보험',
      [OthersFeeType.Community]: '환경유지비',
      [OthersFeeType.Other]: '기타',
      [OthersFeeType.Na]: '[missing_type]',
    },
    otherFeeChargeTiming: {
      [OthersChargeTiming.MoveIn]: '입주시',
      [OthersChargeTiming.MoveOut]: '퇴거시',
      [OthersChargeTiming.Monthly]: '매월',
      [OthersChargeTiming.Yearly]: '매년',
    },
  },
}
