/* eslint-disable prettier/prettier */
import { EnumInquiryPurpose } from '~/types/types'

export default {
  purposes: {
    [EnumInquiryPurpose.CheckAvailability]: '최신 공실 현황을 확인하고싶다.',
    [EnumInquiryPurpose.RequestTour]: '실제로 방을 견학하고 싶다.',
    [EnumInquiryPurpose.VisitOffice]: '점포에 직접 방문하고 싶다.',
    [EnumInquiryPurpose.CheckOtherProperty]: '다른 물건에도 흥미가 있다.',
    [EnumInquiryPurpose.Other]: '기타 문의',
  },
  propertyInquiry: '물건 문의',
  purposeTitle: '문의 내용',
  labelName: '이름',
  labelEmail: '메일 주소',
  labelPhoneNumber: '전화 번호',
  labelMessage: '메시지',
  placeholderPhoneNumber: '(예시)090-1234-5678',
  placeholderMessage: '물건의 상세 정보 혹은 견학 예약 등 중개인에게 확인하고 싶은 것이 있으시면 부담없이 문의해 주세요. ',
  placeholderMessageRenew: '예산, 위치, 방 구경 날짜 등 선호사항이 있으면 중개인에게 문의하세요. 특정 부동산에 관심이 있다면 SUUMO, HOME\'s 등 웹사이트에서 해당 부동산의 URL을 공유할 수 있습니다.',
  labelPolicy: '을 확인한 후에 동의합니다',
  buttonSubmit: '동의 및 송신',
  thankTitle: '문의해주셔서 감사합니다.',
  thankText: '문의해주셔서 감사합니다. 중개인으로부터 신속하게 연락을 드릴테니 잠시만 기다려 주시기 바랍니다.',
  thankText2: '',
  leaveDialogMessage: '이 페이지를 벗어나도 괜찮습니까? 입력하신 내용은 저장되지 않습니다.',
  leaveConfirmButton: '이동하기',
  leaveCancelButton: '머무르기',
}
