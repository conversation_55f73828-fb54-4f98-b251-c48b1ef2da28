// Common
import tags from './common.tags'
import filter from './common.filter'
import options from './common.options'
import dateTime from './common.dateTime'
import { nav, menu, subHeaderMenu, headerBanner } from './common.menu'
import agency from './common.agency'
import language from './common.language'
import facilities from './common.facilities'
import areas from './common.areas'

// Page
import home from './page.home'
import inquiry from './page.inquiry'
import rentList from './page.rentList'
import roomDetail from './page.roomDetail'
import contact from './page.contact'
import city from './page.city'
import station from './page.station'
import tour from './page.tour'
import formRent from './page.formRent'
import access from './page.access'

export default {
  common: {
    headerBanner,
    nav,
    tags,
    menu,
    subHeaderMenu,
    filter,
    agency,
    options,
    dateTime,
    language,
    facilities,
    areas,
    fullBadge: '성약 완료',
    property: '물건',
    properties: '물건',
    propertyCount: '{count}총 물건수',
    privacyPolicy: '개인정보 보호지침',
    topPage: '상위 페이지',
    top: '맨 위로',
    stationName: '{stationName}역 ',
    pageNotFound: '페이지를 찾을 수 없습니다.',
    accessByWalk: '{line}{station}역 도보 {minute}분',
    accessByBus: '{line}{station}역 버스 {minute}분',
    minWalkFrom: '{station}역 도보 {time}분（{line}）',
    maintenanceFee: '관리비',
    noMaintenanceFee: '관리비 없음',
    addressDetail: '{prefecture}{city}{chome}',
    rent: '렌트',
    prefecture: {
      tokyo: '도쿄도',
    },
    askAgent: '상담 전화: 오전 10시 - 오후 7시 (평일)',
    callAgent: '문의하기',
    years: '{count}년',
    aMonth: '1 month',
  },
  home,
  inquiry,
  rentList,
  roomDetail,
  contact,
  city,
  station,
  tour,
  formRent,
  access,
}
