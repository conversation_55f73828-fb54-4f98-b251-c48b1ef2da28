export default {
  propertiesIn: '{prefecture} 물건',
  propertiesOf: '{agency} 취급 물건 ',
  allProperties: '모든 물건',
  location: '소재지',
  access: '주변 역',
  yearBuilt: '건축 연수',
  story: '층',
  storyValue: '{builtDate}년/{floor}층',
  roomsAvailable: '{room}공실',
  floorPlan: '방 배치도',
  floor: '층수',
  rent: '월세',
  maintenanceFee: '관리비',
  size: '전유 면적',
  layout: '방 배치',
  details: '자세히 보기',
  contact: '문의하기',
  noResult: '현재 조건에 맞는 물건을 찾을 수 없습니다',
  monthlyRentFee: '월세',
  distanceFromStation: '역부터 도보 거리',
  paginationInfo: '{num}건 총 {total}건 중',
  features: '특징',
  buildingType: '건물 구조',
  distancePlaceholder: '선택',
  minWalk: '도보 {distance}분',
  sort: {
    new: '신규 물건순',
    lowestPrice: '낮은 가격순',
    highestPrice: '높은 가격순 ',
  },
  hits: '{count}건',
  controlSPButton: {
    filters: '조건 검색',
    sort: '분류',
  },
  filterTitle: '검색 조건',
  sortTitle: '정렬 기준',
  area: '지역',
}
