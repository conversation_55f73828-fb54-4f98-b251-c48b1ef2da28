import {
  EnumFormFormRentOccupation as Occupation,
  EnumFormFormRentVisaStatus as VisaStatus,
  EnumFormFormRentWhenToMoveIn as WhenToMoveIn,
  EnumFormFormRentOtherPreferences as OtherPreferences,
} from '~/types/types'

export default {
  commonPlaceholders: {
    select: 'Please select',
  },
  required: {
    input: 'Please fill the required',
    select: 'Please choose one of the option',
    email: 'Please fill the the correct email',
  },
  header: 'for Rent',
  title: 'Quick Form for Rent',
  description:
    'Please fill out this form with your preferred criteria and our agent will respond to you as soon as possible.',
  name: {
    label: '성명',
    placeholder0: '성',
    placeholder1: '이름',
  },
  email: {
    label: '이메일 주소',
    placeholder: '<EMAIL>',
  },
  phone: {
    label: '전화번호',
    placeholder: 'Please enter your phone number in Japan',
  },
  preferredLanguage: {
    label: '선호하는 언어',
    options: {
      en: 'English',
      jp: '日本語',
      zh: '中文',
      kr: '한국',
    },
  },
  occupation: {
    label: 'Occupation',
    options: {
      [Occupation.CompanyEmployee]: 'Company Employee',
      [Occupation.PartTimer]: 'Part-Timer',
      [Occupation.Student]: 'Student',
      [Occupation.Unemployed]: 'Unemployed',
      [Occupation.Other]: 'Other',
    },
  },
  visaStatus: {
    label: 'Visa Status',
    options: {
      [VisaStatus.WorkVisa]: 'Work Visa',
      [VisaStatus.Student]: 'Student',
      [VisaStatus.LongTermResident]: 'Long Term Resident',
      [VisaStatus.PermanentResident]: 'Permanent Resident',
      [VisaStatus.JapaneseNationality]: 'Japanese Nationality',
      [VisaStatus.Other]: 'Other',
    },
  },
  rentBudget: {
    label: 'Rent Budget',
    options: {
      num_in_k: '¥{num},000',
      over_num_in_k: '¥{num},000 or more',
    },
  },
  floorPlan: {
    label: 'Floor Plan',
    options: {
      or_more: '{plan} or More',
    },
  },
  size: {
    label: 'Min Size',
    options: {
      num: '{num}㎡',
      or_more: '{num}㎡ or more',
    },
  },
  desiredLocation: {
    label: 'Desired Location',
    placeholder: 'Please type station name or area',
  },
  stationDistance: {
    label: 'Distance to the nearest station',
    options: {
      minute_walk: '{num} min walk',
      dont_care: "I don't care much",
    },
  },
  whenToMoveIn: {
    label: 'When to move in',
    options: {
      [WhenToMoveIn.Asap]: 'As soon as possible',
      [WhenToMoveIn.In_1Or_2Weeks]: 'In 1 week - 2 weeks',
      [WhenToMoveIn.InAMonth]: 'In a month',
      [WhenToMoveIn.In_2Months]: 'In 2 months',
      [WhenToMoveIn.OnceFindOne]: 'When I find a nice one',
      [WhenToMoveIn.Other]: 'Other',
    },
  },
  otherPreferences: {
    label: 'Other Preferences',
    options: {
      [OtherPreferences.Not_1stFloor]: 'Not the first floor',
      [OtherPreferences.SeparateBathToilet]: 'Bath and Toilet separated',
      [OtherPreferences.AutoLockEntrance]: 'Auto locking entrance',
      [OtherPreferences.WashStand]: 'Wash stand',
      [OtherPreferences.BicyclePark]: 'Bicycle parking space',
      [OtherPreferences.PetFriendly]: 'Pet friendly',
      [OtherPreferences.PackageDeliveryBox]: 'Package Delivery Box',
      [OtherPreferences.Elevator]: 'Elevator',
      [OtherPreferences.Balcony]: 'Balcony',
      [OtherPreferences.NoKeyMoney]: 'No key money',
      [OtherPreferences.NoDeposit]: 'No deposit',
      [OtherPreferences.Other]: 'Other',
    },
  },
  note: {
    label: 'Note',
    placeholder: 'Please share as much information as possible.',
    placeholderRenew: '가능한 한 많은 정보를 공유해주세요. 특정 부동산에 관심이 있다면 SUUMO, HOME\'s 등 웹사이트에서 해당 부동산의 URL을 공유할 수 있습니다.',
  },
  agreement: {
    labelPolicy: '을 확인한 후에 동의합니다',
    privacyPolicy: '개인정보 보호지침',
  },
  submit: 'Submit',
}
