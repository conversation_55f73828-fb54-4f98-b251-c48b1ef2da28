#import "../fragments/baseAccess.fragment.gql"
#import "../fragments/baseAddressDetail.fragment.gql"

query getProperties(
  $perPage: Int
  $page: Int
  $filter: FilterFindManyPropertyInput
  $sort: SortFindManyPropertyInput
) {
  properties(perPage: $perPage, page: $page, filter: $filter, sort: $sort) {
    count
    pageInfo {
      currentPage
      perPage
      itemCount
      hasNextPage
    }
    items {
      _id
      buildingName
      buildingType
      builtDate
      maxFloor
      structure
      languageComment
      images {
        collections
      }
      prefecturePath
      addressDetail {
        ...BaseAddressDetail
      }
      access {
        ...BaseAccess
      }
      rooms {
        ...SimplifiedRoom
      }
    }
  }
}

fragment SimplifiedRoom on PropertyRooms {
  code
  roomName
  roomLayout
  numberOfRoom
  layout
  tags
  size
  floor
  images {
    featured
    floorPlan
    collections
  }
  visibility
  fees {
    monthly {
      rentFee
      maintenanceFee
    }
  }
}
