query getHomeProperties(
  $sort: SortFindManyPropertyInput!
  $filter: FilterFindManyPropertyInput
) {
  properties(perPage: 8, page: 1, sort: $sort, filter: $filter) {
    items {
      _id
      prefecturePath
      addressDetail {
        city
        prefecture
      }
      access {
        stations {
          stationId
          stationName
          lineId
          lineName
          distance {
            walk
          }
        }
      }
      rooms {
        code
        roomName
        size
        floor
        numberOfRoom
        visibility
        layout
        tags
        images {
          featured
        }
        fees {
          monthly {
            rentFee
            maintenanceFee
          }
        }
      }
    }
  }
}
