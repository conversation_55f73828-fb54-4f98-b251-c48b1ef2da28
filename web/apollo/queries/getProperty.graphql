#import "../fragments/baseRoom.fragment.gql"
#import "../fragments/baseAgency.fragment.gql"
#import "../fragments/baseAccess.fragment.gql"
#import "../fragments/baseAddressDetail.fragment.gql"

query getPropertyByRoomCode($roomCode: String!) {
  property(filter: { rooms: [{ code: $roomCode }] }) {
    _id
    updatedAt
    buildingName
    buildingType
    builtDate
    maxFloor
    structure
    languageComment
    images {
      collections
    }
    prefecturePath
    addressDetail {
      line
      coord
      ...BaseAddressDetail
    }
    access {
      ...BaseAccess
    }
    rooms {
      rejMetadata {
        propertyId
      }
      ...BaseRoom
    }
    agency {
      ...BaseAgency
    }
  }
}
