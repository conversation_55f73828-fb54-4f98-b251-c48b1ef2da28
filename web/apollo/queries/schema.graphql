# This file was generated based on ".graphqlconfig". Do not edit manually.

schema {
  query: Query
  mutation: Mutation
}

interface ErrorInterface {
  "Generic error message"
  message: String
}

type AccessToken {
  accessToken: String!
}

"An entity which own and create the properties listing"
type Agency {
  _id: MongoID!
  basic: AgencyBasic
  createdAt: Date
  "How many inquiries made to the agency"
  inquiryCount: Int
  public: AgencyPublic
  "How many published room under the agency"
  publishedRoomCount: Int
  serviceFee: AgencyServiceFee
  updatedAt: Date
}

type AgencyBasic {
  companyName: String!
  license: AgencyBasicLicense
}

type AgencyBasicLicense {
  expiryDate: AgencyBasicLicenseExpiryDate
  issueNumber: String
  licenseType: EnumAgencyBasicLicenseLicenseType
  number: String
}

type AgencyBasicLicenseExpiryDate {
  from: String
  until: String
}

"List of items with pagination."
type AgencyPagination {
  "Total object count."
  count: Int
  "Array of objects."
  items: [Agency!]
  "Information to aid in pagination."
  pageInfo: PaginationInfo!
}

type AgencyPublic {
  address: AgencyPublicAddress
  availableLanguages: [String]
  businessHour: AgencyPublicBusinessHour
  closeDays: [String]
  email: String!
  imageUrl: String
  name: String
  phone: String
  website: String
}

type AgencyPublicAddress {
  buildingName: String
  city: String
  postalCode: String
  prefecture: String
  streetAddress: String
}

type AgencyPublicBusinessHour {
  end: String
  start: String
}

type AgencyServiceFee {
  feeType: EnumAgencyServiceFeeFeeType
  value: Float
}

"Structured location information for a property"
type Area {
  _id: String!
  createdAt: Date
  en: String
  items: [Area]
  name: String!
  postalCode: String
  "How many properties on the area"
  propertyCounts: Int
  updatedAt: Date
}

type CreateOneAgencyPayload {
  "Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response."
  error: ErrorInterface
  "Created document"
  record: Agency
  "Document ID"
  recordId: MongoID
}

type CreateOneInquiryPayload {
  "Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response."
  error: ErrorInterface
  "Created document"
  record: Inquiry
  "Document ID"
  recordId: MongoID
}

type CreateOnePropertyPayload {
  "Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response."
  error: ErrorInterface
  "Created document"
  record: Property
  "Document ID"
  recordId: MongoID
}

"The entity which can be rented"
type Inquiry {
  _id: MongoID!
  agencyId: String!
  createdAt: Date
  email: String!
  memo: String
  monthlyFee: Float!
  name: String!
  phone: String
  property: Property
  propertyId: String!
  purpose: [EnumInquiryPurpose]!
  whenToMoveIn: String!
  roomCode: String!
  serviceFee: Float!
  unread: Boolean
  updatedAt: Date
}

"List of items with pagination."
type InquiryPagination {
  "Total object count."
  count: Int
  "Array of objects."
  items: [Inquiry!]
  "Information to aid in pagination."
  pageInfo: PaginationInfo!
}

type MongoError implements ErrorInterface {
  "MongoDB error code"
  code: Int
  "MongoDB error message"
  message: String
}

type Mutation {
  changePassword(currentPassword: String!, newPassword: String!): Succeed!
  cloneRoom(code: String!, propertyId: MongoID!): PropertyRooms
  "Create one document with mongoose defaults, setters, hooks and validation"
  createAgency(record: CreateOneAgencyInput!): CreateOneAgencyPayload
  "Create one document with mongoose defaults, setters, hooks and validation"
  createInquiry(record: CreateOneInquiryInput!): CreateOneInquiryPayload
  createOrUpdateFeatured(featured: [String]): [String]
  "Create one document with mongoose defaults, setters, hooks and validation"
  createProperty(record: CreateOnePropertyInput!): CreateOnePropertyPayload
  "Remove one document: 1) Retrieve one document and remove with hooks via findByIdAndRemove. 2) Return removed document."
  deleteAgency(_id: MongoID!): RemoveByIdAgencyPayload
  "Remove one document: 1) Retrieve one document and remove with hooks via findByIdAndRemove. 2) Return removed document."
  deleteProperty(_id: MongoID!): RemoveByIdPropertyPayload
  deleteRoom(code: String!, propertyId: MongoID!): PropertyRooms
  incrementPageView(propertyId: MongoID!, roomCode: String!): Boolean
  logout: Succeed!
  newPassword(newPassword: String!, token: String!): AccessToken!
  resetPassword(email: String!): Succeed!
  sendToSupport(input: SendToSupportInput!): Boolean
  setInquiryRead(inquiryId: MongoID!, status: Boolean!): Boolean
  signIn(email: String!, password: String!): AccessToken!
  signUp(agencyId: String, email: String!, password: String!): AccessToken!
  switchLocale(locale: Locale!): User!
  "Update one document: 1) Retrieve one document by findById. 2) Apply updates to mongoose document. 3) Mongoose applies defaults, setters, hooks and validation. 4) And save it."
  updateAgency(_id: MongoID!, record: UpdateByIdAgencyInput!): UpdateByIdAgencyPayload
  "Update one document: 1) Retrieve one document by findById. 2) Apply updates to mongoose document. 3) Mongoose applies defaults, setters, hooks and validation. 4) And save it."
  updateProperty(_id: MongoID!, record: UpdateByIdPropertyInput!): UpdateByIdPropertyPayload
  updateRoomVisibility(code: String!, propertyId: MongoID!, value: EnumPropertyRoomsVisibility!): Boolean
  updateUser(email: String!, firstName: String!, lastName: String!): User!
  verify(token: String!): AccessToken!
  verifyRequest: Succeed!
}

type PaginationInfo {
  currentPage: Int!
  hasNextPage: Boolean
  hasPreviousPage: Boolean
  itemCount: Int
  pageCount: Int
  perPage: Int!
}

"The entity which can be rented"
type Property {
  _id: MongoID!
  access: PropertyAccess
  agency: Agency
  agencyId: MongoID!
  buildingName: String
  buildingType: EnumPropertyBuildingType!
  builtDate: Date!
  createdAt: Date
  highestMonthlyPrice: Float
  images: PropertyImages!
  location: PropertyLocation!
  "The detailed location information"
  locationDetail: PropertyLocation
  lowestMonthlyPrice: Float
  maxFloor: Float!
  nearestStationDistance: Float
  pageViews: JSON
  rooms: [PropertyRooms]!
  structure: EnumPropertyStructure!
  updatedAt: Date
  languageComment: JSON
}

type PropertyAccess {
  stations: [PropertyAccessStations]
}

type PropertyAccessStations {
  distance: PropertyAccessStationsDistance
  lineDetail: ServiceLine
  lineId: String
  stationDetail: Station
  stationId: String
}

type PropertyAccessStationsDistance {
  bus: Float
  walk: Float
}

type PropertyImages {
  collections: [String]!
}

type PropertyLocation {
  addressLine: String
  area: String!
  chome: String!
  city: String!
  cityArea: String!
  position: PropertyLocationPosition
  postalCode: String
  prefecture: String!
  region: String!
}

type PropertyLocationPosition {
  lat: Float
  long: Float
}

"List of items with pagination."
type PropertyPagination {
  "Total object count."
  count: Int
  "Array of objects."
  items: [Property!]
  "Information to aid in pagination."
  pageInfo: PaginationInfo!
}

type PropertyRooms {
  availability: PropertyRoomsAvailability!
  code: String
  contractLength: Float!
  contractType: EnumPropertyRoomsContractType!
  createdAt: Date
  facilities: PropertyRoomsFacilities
  fees: PropertyRoomsFees!
  floor: String!
  images: PropertyRoomsImages!
  "How many inquiries made to the room"
  inquiryCount: Int
  layout: String
  mainWindow: EnumPropertyRoomsMainWindow!
  monthlyFee: Float
  numberOfRoom: Float!
  pageViews: JSON
  roomLayout: String
  roomName: String
  size: Float!
  tags: [String]
  transactionType: EnumPropertyRoomsTransactionType
  updatedAt: Date
  visibility: EnumPropertyRoomsVisibility!
}

type PropertyRoomsAvailability {
  enquire: Boolean
  now: Boolean
  status: EnumPropertyRoomsAvailabilityStatus!
}

type PropertyRoomsFacilities {
  basic: [String]
  building: [String]
  other: [String]
}

type PropertyRoomsFees {
  monthly: PropertyRoomsFeesMonthly!
  moveIn: PropertyRoomsFeesMoveIn!
  others: [PropertyRoomsFeesOthers]
}

type PropertyRoomsFeesMonthly {
  maintenanceFee: Float
  rentFee: Float
}

type PropertyRoomsFeesMoveIn {
  agencyFee: PropertyRoomsFeesMoveInDeposit!
  contractRenewalFee: PropertyRoomsFeesMoveInDeposit!
  deposit: PropertyRoomsFeesMoveInDeposit!
  guarantorFee: PropertyRoomsFeesMoveInDeposit!
  keyMoney: PropertyRoomsFeesMoveInDeposit!
}

type PropertyRoomsFeesMoveInDeposit {
  feeType: EnumPropertyRoomsFeesMoveInDepositFeeType!
  value: Float!
}

type PropertyRoomsFeesOthers {
  chargeTiming: EnumPropertyRoomsFeesOthersChargeTiming
  feeType: EnumPropertyRoomsFeesOthersFeeType
  value: Float
}

type PropertyRoomsImages {
  collections: [String]!
  featured: String!
  floorPlan: String!
}

type Query {
  agencies(
    "Filter by fields"
    filter: FilterFindManyAgencyInput,
    "Page number for displaying"
    page: Int,
    perPage: Int = 2,
    sort: SortFindManyAgencyInput
  ): AgencyPagination
  agencyById(_id: MongoID!): Agency
  availableLocations: [Area]
  featuredProperties: [Property]
  inquiries(
    "Filter by fields"
    filter: FilterFindManyInquiryInput,
    "Page number for displaying"
    page: Int,
    perPage: Int = 20,
    sort: SortFindManyInquiryInput
  ): InquiryPagination
  inquiryById(_id: MongoID!): Inquiry
  inquiryCount(
    "Filter by fields"
    filter: FilterCountInquiryInput
  ): Int
  properties(
    "Filter by fields"
    filter: FilterFindManyPropertyInput,
    "Page number for displaying"
    page: Int,
    perPage: Int = 20,
    sort: SortFindManyPropertyInput
  ): PropertyPagination
  property(
    "Filter by fields"
    filter: FilterFindOnePropertyInput,
    skip: Int,
    sort: SortFindOnePropertyInput
  ): Property
  propertyById(_id: MongoID!): Property
  s3UploadSignedUrl(path: String, type: String!): S3UploadSignedUrl
  serviceLines(
    "Filter by fields"
    filter: FilterFindManyServiceLineInput,
    limit: Int = 100,
    skip: Int,
    sort: SortFindManyServiceLineInput
  ): [ServiceLine!]!
  setting(
    "Filter by fields"
    filter: FilterFindOneSettingInput,
    skip: Int,
    sort: SortFindOneSettingInput
  ): Setting
  stationById(_id: String!): Station
  stations(
    "Filter by fields"
    filter: FilterFindManyStationInput,
    limit: Int = 100,
    skip: Int,
    sort: SortFindManyStationInput
  ): [Station!]!
  user: User!
}

type RemoveByIdAgencyPayload {
  "Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response."
  error: ErrorInterface
  "Removed document"
  record: Agency
  "Document ID"
  recordId: MongoID
}

type RemoveByIdPropertyPayload {
  "Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response."
  error: ErrorInterface
  "Removed document"
  record: Property
  "Document ID"
  recordId: MongoID
}

type RuntimeError implements ErrorInterface {
  "Runtime error message"
  message: String
}

type S3UploadSignedUrl {
  url: String
}

"The train service line"
type ServiceLine {
  _id: String!
  en: String
  name: String!
  sort: Float
  stationIds: [String]
  stations: [Station]!
}

"Set the featured properties"
type Setting {
  _id: MongoID!
  featured: [String]
}

"The station is a place where the train transit"
type Station {
  _id: String!
  en: String
  lineIds: [String]
  location: StationLocation
  name: String!
  serviceLines: [ServiceLine]!
}

type StationLocation {
  lat: Float
  long: Float
}

type Succeed {
  succeed: Boolean!
}

type UpdateByIdAgencyPayload {
  "Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response."
  error: ErrorInterface
  "Updated document"
  record: Agency
  "Document ID"
  recordId: MongoID
}

type UpdateByIdPropertyPayload {
  "Error that may occur during operation. If you request this field in GraphQL query, you will receive typed error in payload; otherwise error will be provided in root `errors` field of GraphQL response."
  error: ErrorInterface
  "Updated document"
  record: Property
  "Document ID"
  recordId: MongoID
}

type User {
  _id: MongoID!
  account: UserAccount
  agencyId: String
  createdAt: Date
  email: String
  firstName: String
  lastName: String
  locale: String
  roles: [String]
  updatedAt: Date
}

type UserAccount {
  verification: UserAccountVerification
}

type UserAccountVerification {
  verified: Boolean
}

type ValidationError implements ErrorInterface {
  "List of validator errors"
  errors: [ValidatorError!]
  "Combined error message from all validators"
  message: String
}

type ValidatorError {
  "Input record idx in array which occurs the validation error. This `idx` is useful for createMany operation. For singular operations it always be 0. For *Many operations `idx` represents record index in array received from user."
  idx: Int!
  "Validation error message"
  message: String
  "Source of the validation error from the model path"
  path: String
  "Field value which occurs the validation error"
  value: JSON
}

enum EnumAgencyBasicLicenseLicenseType {
  governor_tokyo
  minister_litt
}

enum EnumAgencyServiceFeeFeeType {
  fixed
  percentage
}

enum EnumInquiryPurpose {
  check_availability
  check_other_property
  other
  request_tour
  visit_office
}

enum EnumInquiryWhenToMoveIn {
  asap
  in_1_or_2_weeks
  in_a_month
  in_2_months
  once_find_one
  other
}

enum EnumPropertyBuildingType {
  apartment
  house
  monthly_apartment
  share_house
}

enum EnumPropertyRoomsAvailabilityStatus {
  available
  available_soon
  renovation
  under_construction
}

enum EnumPropertyRoomsContractType {
  fixed_term
  standard
}

enum EnumPropertyRoomsFeesMoveInDepositFeeType {
  fixed_price
  monthly_multiplier
}

enum EnumPropertyRoomsFeesOthersChargeTiming {
  monthly
  move_in
  move_out
  na
  yearly
}

enum EnumPropertyRoomsFeesOthersFeeType {
  administrative
  cleaning
  disinfection
  fire_insurance
  key_replacement
  na
  other
  support_24_hours
}

enum EnumPropertyRoomsMainWindow {
  east
  north
  northeast
  northwest
  south
  southeast
  southwest
  west
}

enum EnumPropertyRoomsTransactionType {
  agency
  owner
}

enum EnumPropertyRoomsVisibility {
  private
  public
}

enum EnumPropertyStructure {
  heavy_steel
  light_steel
  other
  reinforce_concrete
  steel_framed_reinforced_concrete_block
  wooden
}

enum Locale {
  en
  ge
}

enum SortFindManyAgencyInput {
  PUBLIC__EMAIL_ASC
  PUBLIC__EMAIL_DESC
  _ID_ASC
  _ID_DESC
}

enum SortFindManyInquiryInput {
  AGENCYID_ASC
  AGENCYID_DESC
  CREATEDAT_ASC
  CREATEDAT_DESC
  MONTHLYFEE_ASC
  MONTHLYFEE_DESC
  ROOMCODE_ASC
  ROOMCODE_DESC
  SERVICEFEE_ASC
  SERVICEFEE_DESC
  UNREAD_ASC
  UNREAD_DESC
  UPDATEDAT_ASC
  UPDATEDAT_DESC
  _ID_ASC
  _ID_DESC
}

enum SortFindManyPropertyInput {
  AGENCYID_ASC
  AGENCYID_DESC
  CREATEDAT_ASC
  CREATEDAT_DESC
  HIGHESTMONTHLYPRICE_ASC
  HIGHESTMONTHLYPRICE_DESC
  HIGHEST_VIEW_WEEKLY
  LOWESTMONTHLYPRICE_ASC
  LOWESTMONTHLYPRICE_DESC
  NEARESTSTATIONDISTANCE_ASC
  NEARESTSTATIONDISTANCE_DESC
  PAGEVIEWS_ASC
  PAGEVIEWS_DESC
  UPDATEDAT_ASC
  UPDATEDAT_DESC
  _ID_ASC
  _ID_DESC
}

enum SortFindManyServiceLineInput {
  SORT_ASC
  SORT_DESC
  _ID_ASC
  _ID_DESC
}

enum SortFindManyStationInput {
  _ID_ASC
  _ID_DESC
}

enum SortFindOnePropertyInput {
  AGENCYID_ASC
  AGENCYID_DESC
  CREATEDAT_ASC
  CREATEDAT_DESC
  HIGHESTMONTHLYPRICE_ASC
  HIGHESTMONTHLYPRICE_DESC
  LOWESTMONTHLYPRICE_ASC
  LOWESTMONTHLYPRICE_DESC
  NEARESTSTATIONDISTANCE_ASC
  NEARESTSTATIONDISTANCE_DESC
  PAGEVIEWS_ASC
  PAGEVIEWS_DESC
  UPDATEDAT_ASC
  UPDATEDAT_DESC
  _ID_ASC
  _ID_DESC
}

enum SortFindOneSettingInput {
  _ID_ASC
  _ID_DESC
}

input AgencyBasicInput {
  companyName: String!
  license: AgencyBasicLicenseInput
}

input AgencyBasicLicenseExpiryDateInput {
  from: String
  until: String
}

input AgencyBasicLicenseInput {
  expiryDate: AgencyBasicLicenseExpiryDateInput
  issueNumber: String
  licenseType: EnumAgencyBasicLicenseLicenseType
  number: String
}

input AgencyPublicAddressInput {
  buildingName: String
  city: String
  postalCode: String
  prefecture: String
  streetAddress: String
}

input AgencyPublicBusinessHourInput {
  end: String
  start: String
}

input AgencyPublicInput {
  address: AgencyPublicAddressInput
  availableLanguages: [String]
  businessHour: AgencyPublicBusinessHourInput
  closeDays: [String]
  email: String!
  imageUrl: String
  name: String
  phone: String
  website: String
}

input AgencyServiceFeeInput {
  feeType: EnumAgencyServiceFeeFeeType
  value: Float
}

input CreateOneAgencyInput {
  basic: AgencyBasicInput
  public: AgencyPublicInput
  serviceFee: AgencyServiceFeeInput
}

input CreateOneInquiryInput {
  email: String!
  memo: String
  name: String!
  phone: String
  propertyId: String!
  purpose: [EnumInquiryPurpose]!
  whenToMoveIn: String!
  roomCode: String!
  unread: Boolean
}

input CreateOnePropertyInput {
  access: PropertyAccessInput
  agencyId: MongoID!
  buildingName: String
  buildingType: EnumPropertyBuildingType!
  builtDate: Date!
  highestMonthlyPrice: Float
  images: PropertyImagesInput!
  location: PropertyLocationInput!
  lowestMonthlyPrice: Float
  maxFloor: Float!
  nearestStationDistance: Float
  pageViews: JSON
  rooms: [RoomInput]!
  structure: EnumPropertyStructure!
  languageComment: JSON
}

input FilterCountInquiryAgencyIdOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterCountInquiryCreatedAtOperatorsInput {
  exists: Boolean
  gt: Date
  gte: Date
  in: [Date]
  lt: Date
  lte: Date
  ne: Date
  nin: [Date]
}

input FilterCountInquiryInput {
  AND: [FilterCountInquiryInput!]
  OR: [FilterCountInquiryInput!]
  _id: MongoID
  "List of *indexed* fields that can be filtered via operators."
  _operators: FilterCountInquiryOperatorsInput
  agencyId: String
  createdAt: Date
  email: String
  memo: String
  monthlyFee: Float
  name: String
  phone: String
  propertyId: String
  purpose: [EnumInquiryPurpose]
  whenToMoveIn: String
  roomCode: String
  serviceFee: Float
  unread: Boolean
  updatedAt: Date
}

input FilterCountInquiryMonthlyFeeOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

"For performance reason this type contains only *indexed* fields."
input FilterCountInquiryOperatorsInput {
  _id: FilterCountInquiry_idOperatorsInput
  agencyId: FilterCountInquiryAgencyIdOperatorsInput
  createdAt: FilterCountInquiryCreatedAtOperatorsInput
  monthlyFee: FilterCountInquiryMonthlyFeeOperatorsInput
  roomCode: FilterCountInquiryRoomCodeOperatorsInput
  serviceFee: FilterCountInquiryServiceFeeOperatorsInput
  unread: FilterCountInquiryUnreadOperatorsInput
  updatedAt: FilterCountInquiryUpdatedAtOperatorsInput
}

input FilterCountInquiryRoomCodeOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterCountInquiryServiceFeeOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterCountInquiryUnreadOperatorsInput {
  exists: Boolean
  gt: Boolean
  gte: Boolean
  in: [Boolean]
  lt: Boolean
  lte: Boolean
  ne: Boolean
  nin: [Boolean]
}

input FilterCountInquiryUpdatedAtOperatorsInput {
  exists: Boolean
  gt: Date
  gte: Date
  in: [Date]
  lt: Date
  lte: Date
  ne: Date
  nin: [Date]
}

input FilterCountInquiry_idOperatorsInput {
  exists: Boolean
  gt: MongoID
  gte: MongoID
  in: [MongoID]
  lt: MongoID
  lte: MongoID
  ne: MongoID
  nin: [MongoID]
}

input FilterFindManyAgencyBasicInput {
  companyName: String
  license: FilterFindManyAgencyBasicLicenseInput
}

input FilterFindManyAgencyBasicLicenseExpiryDateInput {
  from: String
  until: String
}

input FilterFindManyAgencyBasicLicenseInput {
  expiryDate: FilterFindManyAgencyBasicLicenseExpiryDateInput
  issueNumber: String
  licenseType: EnumAgencyBasicLicenseLicenseType
  number: String
}

input FilterFindManyAgencyInput {
  AND: [FilterFindManyAgencyInput!]
  OR: [FilterFindManyAgencyInput!]
  "List of *indexed* fields that can be filtered via operators."
  _operators: FilterFindManyAgencyOperatorsInput
  basic: FilterFindManyAgencyBasicInput
  public: FilterFindManyAgencyPublicInput
  serviceFee: FilterFindManyAgencyServiceFeeInput
}

"For performance reason this type contains only *indexed* fields."
input FilterFindManyAgencyOperatorsInput {
  public: FilterFindManyAgencyPublicOperatorsInput
}

input FilterFindManyAgencyPublicAddressInput {
  buildingName: String
  city: String
  postalCode: String
  prefecture: String
  streetAddress: String
}

input FilterFindManyAgencyPublicBusinessHourInput {
  end: String
  start: String
}

input FilterFindManyAgencyPublicEmailOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyAgencyPublicInput {
  address: FilterFindManyAgencyPublicAddressInput
  availableLanguages: [String]
  businessHour: FilterFindManyAgencyPublicBusinessHourInput
  closeDays: [String]
  email: String
  imageUrl: String
  name: String
  phone: String
  website: String
}

input FilterFindManyAgencyPublicOperatorsInput {
  email: FilterFindManyAgencyPublicEmailOperatorsInput
}

input FilterFindManyAgencyServiceFeeInput {
  feeType: EnumAgencyServiceFeeFeeType
  value: Float
}

input FilterFindManyInquiryAgencyIdOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyInquiryCreatedAtOperatorsInput {
  exists: Boolean
  gt: Date
  gte: Date
  in: [Date]
  lt: Date
  lte: Date
  ne: Date
  nin: [Date]
}

input FilterFindManyInquiryEmailOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyInquiryInput {
  AND: [FilterFindManyInquiryInput!]
  OR: [FilterFindManyInquiryInput!]
  _id: MongoID
  "List of *indexed* fields that can be filtered via operators."
  _operators: FilterFindManyInquiryOperatorsInput
  agencyId: String
  createdAt: Date
  email: String
  memo: String
  monthlyFee: Float
  name: String
  phone: String
  propertyId: String
  purpose: [EnumInquiryPurpose]
  whenToMoveIn: String
  roomCode: String
  serviceFee: Float
  unread: Boolean
  updatedAt: Date
}

input FilterFindManyInquiryMemoOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyInquiryMonthlyFeeOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyInquiryNameOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

"For performance reason this type contains only *indexed* fields."
input FilterFindManyInquiryOperatorsInput {
  _id: FilterFindManyInquiry_idOperatorsInput
  agencyId: FilterFindManyInquiryAgencyIdOperatorsInput
  createdAt: FilterFindManyInquiryCreatedAtOperatorsInput
  email: FilterFindManyInquiryEmailOperatorsInput
  memo: FilterFindManyInquiryMemoOperatorsInput
  monthlyFee: FilterFindManyInquiryMonthlyFeeOperatorsInput
  name: FilterFindManyInquiryNameOperatorsInput
  phone: FilterFindManyInquiryPhoneOperatorsInput
  propertyId: FilterFindManyInquiryPropertyIdOperatorsInput
  purpose: FilterFindManyInquiryPurposeOperatorsInput
  whenToMoveIn: FilterFindManyInquirywhenToMoveInOperatorsInput
  roomCode: FilterFindManyInquiryRoomCodeOperatorsInput
  serviceFee: FilterFindManyInquiryServiceFeeOperatorsInput
  unread: FilterFindManyInquiryUnreadOperatorsInput
  updatedAt: FilterFindManyInquiryUpdatedAtOperatorsInput
}

input FilterFindManyInquiryPhoneOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyInquiryPropertyIdOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyInquiryPurposeOperatorsInput {
  exists: Boolean
  gt: EnumInquiryPurpose
  gte: EnumInquiryPurpose
  in: [EnumInquiryPurpose]
  lt: EnumInquiryPurpose
  lte: EnumInquiryPurpose
  ne: EnumInquiryPurpose
  nin: [EnumInquiryPurpose]
}

input FilterFindManyInquirywhenToMoveInOperatorsInput {
  exists: Boolean
  gt: EnumInquiryWhenToMoveIn
  gte: EnumInquiryWhenToMoveIn
  in: [EnumInquiryWhenToMoveIn]
  lt: EnumInquiryWhenToMoveIn
  lte: EnumInquiryWhenToMoveIn
  ne: EnumInquiryWhenToMoveIn
  nin: [EnumInquiryWhenToMoveIn]
};

input FilterFindManyInquiryRoomCodeOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyInquiryServiceFeeOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyInquiryUnreadOperatorsInput {
  exists: Boolean
  gt: Boolean
  gte: Boolean
  in: [Boolean]
  lt: Boolean
  lte: Boolean
  ne: Boolean
  nin: [Boolean]
}

input FilterFindManyInquiryUpdatedAtOperatorsInput {
  exists: Boolean
  gt: Date
  gte: Date
  in: [Date]
  lt: Date
  lte: Date
  ne: Date
  nin: [Date]
}

input FilterFindManyInquiry_idOperatorsInput {
  exists: Boolean
  gt: MongoID
  gte: MongoID
  in: [MongoID]
  lt: MongoID
  lte: MongoID
  ne: MongoID
  nin: [MongoID]
}

input FilterFindManyPropertyAccessInput {
  stations: [FilterFindManyPropertyAccessStationsInput]
}

input FilterFindManyPropertyAccessOperatorsInput {
  stations: FilterFindManyPropertyAccessStationsOperatorsInput
}

input FilterFindManyPropertyAccessStationsDistanceBusOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyAccessStationsDistanceInput {
  bus: Float
  walk: Float
}

input FilterFindManyPropertyAccessStationsDistanceOperatorsInput {
  bus: FilterFindManyPropertyAccessStationsDistanceBusOperatorsInput
  walk: FilterFindManyPropertyAccessStationsDistanceWalkOperatorsInput
}

input FilterFindManyPropertyAccessStationsDistanceWalkOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyAccessStationsInput {
  distance: FilterFindManyPropertyAccessStationsDistanceInput
  lineId: String
  stationId: String
}

input FilterFindManyPropertyAccessStationsLineIdOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyAccessStationsOperatorsInput {
  distance: FilterFindManyPropertyAccessStationsDistanceOperatorsInput
  lineId: FilterFindManyPropertyAccessStationsLineIdOperatorsInput
  stationId: FilterFindManyPropertyAccessStationsStationIdOperatorsInput
}

input FilterFindManyPropertyAccessStationsStationIdOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyAgencyIdOperatorsInput {
  exists: Boolean
  gt: MongoID
  gte: MongoID
  in: [MongoID]
  lt: MongoID
  lte: MongoID
  ne: MongoID
  nin: [MongoID]
}

input FilterFindManyPropertyBuildingNameOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyBuildingTypeOperatorsInput {
  exists: Boolean
  gt: EnumPropertyBuildingType
  gte: EnumPropertyBuildingType
  in: [EnumPropertyBuildingType]
  lt: EnumPropertyBuildingType
  lte: EnumPropertyBuildingType
  ne: EnumPropertyBuildingType
  nin: [EnumPropertyBuildingType]
}

input FilterFindManyPropertyBuiltDateOperatorsInput {
  exists: Boolean
  gt: Date
  gte: Date
  in: [Date]
  lt: Date
  lte: Date
  ne: Date
  nin: [Date]
}

input FilterFindManyPropertyHighestMonthlyPriceOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyImagesCollectionsOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyImagesInput {
  collections: [String]
}

input FilterFindManyPropertyImagesOperatorsInput {
  collections: FilterFindManyPropertyImagesCollectionsOperatorsInput
}

input FilterFindManyPropertyInput {
  AND: [FilterFindManyPropertyInput!]
  OR: [FilterFindManyPropertyInput!]
  "List of *indexed* fields that can be filtered via operators."
  _operators: FilterFindManyPropertyOperatorsInput
  access: FilterFindManyPropertyAccessInput
  agencyId: MongoID
  buildingName: String
  buildingType: EnumPropertyBuildingType
  builtDate: Date
  highestMonthlyPrice: Float
  images: FilterFindManyPropertyImagesInput
  location: FilterFindManyPropertyLocationInput
  lowestMonthlyPrice: Float
  maxFloor: Float
  nearestStationDistance: Float
  pageViews: JSON
  rooms: [FilterFindManyPropertyRoomsInput]
  structure: EnumPropertyStructure
  languageComment: JSON
}

input FilterFindManyPropertyLocationAddressLineOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyLocationAreaOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyLocationChomeOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyLocationCityAreaOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyLocationCityOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyLocationInput {
  addressLine: String
  area: String
  chome: String
  city: String
  cityArea: String
  position: FilterFindManyPropertyLocationPositionInput
  postalCode: String
  prefecture: String
  region: String
}

input FilterFindManyPropertyLocationOperatorsInput {
  addressLine: FilterFindManyPropertyLocationAddressLineOperatorsInput
  area: FilterFindManyPropertyLocationAreaOperatorsInput
  chome: FilterFindManyPropertyLocationChomeOperatorsInput
  city: FilterFindManyPropertyLocationCityOperatorsInput
  cityArea: FilterFindManyPropertyLocationCityAreaOperatorsInput
  position: FilterFindManyPropertyLocationPositionOperatorsInput
  postalCode: FilterFindManyPropertyLocationPostalCodeOperatorsInput
  prefecture: FilterFindManyPropertyLocationPrefectureOperatorsInput
  region: FilterFindManyPropertyLocationRegionOperatorsInput
}

input FilterFindManyPropertyLocationPositionInput {
  lat: Float
  long: Float
}

input FilterFindManyPropertyLocationPositionLatOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyLocationPositionLongOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyLocationPositionOperatorsInput {
  lat: FilterFindManyPropertyLocationPositionLatOperatorsInput
  long: FilterFindManyPropertyLocationPositionLongOperatorsInput
}

input FilterFindManyPropertyLocationPostalCodeOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyLocationPrefectureOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyLocationRegionOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyLowestMonthlyPriceOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyMaxFloorOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyNearestStationDistanceOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

"For performance reason this type contains only *indexed* fields."
input FilterFindManyPropertyOperatorsInput {
  access: FilterFindManyPropertyAccessOperatorsInput
  agencyId: FilterFindManyPropertyAgencyIdOperatorsInput
  buildingName: FilterFindManyPropertyBuildingNameOperatorsInput
  buildingType: FilterFindManyPropertyBuildingTypeOperatorsInput
  builtDate: FilterFindManyPropertyBuiltDateOperatorsInput
  highestMonthlyPrice: FilterFindManyPropertyHighestMonthlyPriceOperatorsInput
  images: FilterFindManyPropertyImagesOperatorsInput
  location: FilterFindManyPropertyLocationOperatorsInput
  lowestMonthlyPrice: FilterFindManyPropertyLowestMonthlyPriceOperatorsInput
  maxFloor: FilterFindManyPropertyMaxFloorOperatorsInput
  nearestStationDistance: FilterFindManyPropertyNearestStationDistanceOperatorsInput
  pageViews: FilterFindManyPropertyPageViewsOperatorsInput
  rooms: FilterFindManyPropertyRoomsOperatorsInput
  structure: FilterFindManyPropertyStructureOperatorsInput
  languageComment: FilterFindManyPropertyLanguageCommentOperatorsInput
}

input FilterFindManyPropertyLanguageCommentOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
}

input FilterFindManyPropertyPageViewsOperatorsInput {
  exists: Boolean
  gt: JSON
  gte: JSON
  in: [JSON]
  lt: JSON
  lte: JSON
  ne: JSON
  nin: [JSON]
}

input FilterFindManyPropertyRoomsAvailabilityEnquireOperatorsInput {
  exists: Boolean
  gt: Boolean
  gte: Boolean
  in: [Boolean]
  lt: Boolean
  lte: Boolean
  ne: Boolean
  nin: [Boolean]
}

input FilterFindManyPropertyRoomsAvailabilityInput {
  enquire: Boolean
  now: Boolean
  status: EnumPropertyRoomsAvailabilityStatus
}

input FilterFindManyPropertyRoomsAvailabilityNowOperatorsInput {
  exists: Boolean
  gt: Boolean
  gte: Boolean
  in: [Boolean]
  lt: Boolean
  lte: Boolean
  ne: Boolean
  nin: [Boolean]
}

input FilterFindManyPropertyRoomsAvailabilityOperatorsInput {
  enquire: FilterFindManyPropertyRoomsAvailabilityEnquireOperatorsInput
  now: FilterFindManyPropertyRoomsAvailabilityNowOperatorsInput
  status: FilterFindManyPropertyRoomsAvailabilityStatusOperatorsInput
}

input FilterFindManyPropertyRoomsAvailabilityStatusOperatorsInput {
  exists: Boolean
  gt: EnumPropertyRoomsAvailabilityStatus
  gte: EnumPropertyRoomsAvailabilityStatus
  in: [EnumPropertyRoomsAvailabilityStatus]
  lt: EnumPropertyRoomsAvailabilityStatus
  lte: EnumPropertyRoomsAvailabilityStatus
  ne: EnumPropertyRoomsAvailabilityStatus
  nin: [EnumPropertyRoomsAvailabilityStatus]
}

input FilterFindManyPropertyRoomsCodeOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyRoomsContractLengthOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyRoomsContractTypeOperatorsInput {
  exists: Boolean
  gt: EnumPropertyRoomsContractType
  gte: EnumPropertyRoomsContractType
  in: [EnumPropertyRoomsContractType]
  lt: EnumPropertyRoomsContractType
  lte: EnumPropertyRoomsContractType
  ne: EnumPropertyRoomsContractType
  nin: [EnumPropertyRoomsContractType]
}

input FilterFindManyPropertyRoomsCreatedAtOperatorsInput {
  exists: Boolean
  gt: Date
  gte: Date
  in: [Date]
  lt: Date
  lte: Date
  ne: Date
  nin: [Date]
}

input FilterFindManyPropertyRoomsFacilitiesBasicOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyRoomsFacilitiesBuildingOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyRoomsFacilitiesInput {
  basic: [String]
  building: [String]
  other: [String]
}

input FilterFindManyPropertyRoomsFacilitiesOperatorsInput {
  basic: FilterFindManyPropertyRoomsFacilitiesBasicOperatorsInput
  building: FilterFindManyPropertyRoomsFacilitiesBuildingOperatorsInput
  other: FilterFindManyPropertyRoomsFacilitiesOtherOperatorsInput
}

input FilterFindManyPropertyRoomsFacilitiesOtherOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyRoomsFeesInput {
  monthly: FilterFindManyPropertyRoomsFeesMonthlyInput
  moveIn: FilterFindManyPropertyRoomsFeesMoveInInput
  others: [FilterFindManyPropertyRoomsFeesOthersInput]
}

input FilterFindManyPropertyRoomsFeesMonthlyInput {
  maintenanceFee: Float
  rentFee: Float
}

input FilterFindManyPropertyRoomsFeesMonthlyMaintenanceFeeOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyRoomsFeesMonthlyOperatorsInput {
  maintenanceFee: FilterFindManyPropertyRoomsFeesMonthlyMaintenanceFeeOperatorsInput
  rentFee: FilterFindManyPropertyRoomsFeesMonthlyRentFeeOperatorsInput
}

input FilterFindManyPropertyRoomsFeesMonthlyRentFeeOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyRoomsFeesMoveInAgencyFeeFeeTypeOperatorsInput {
  exists: Boolean
  gt: EnumPropertyRoomsFeesMoveInDepositFeeType
  gte: EnumPropertyRoomsFeesMoveInDepositFeeType
  in: [EnumPropertyRoomsFeesMoveInDepositFeeType]
  lt: EnumPropertyRoomsFeesMoveInDepositFeeType
  lte: EnumPropertyRoomsFeesMoveInDepositFeeType
  ne: EnumPropertyRoomsFeesMoveInDepositFeeType
  nin: [EnumPropertyRoomsFeesMoveInDepositFeeType]
}

input FilterFindManyPropertyRoomsFeesMoveInAgencyFeeOperatorsInput {
  feeType: FilterFindManyPropertyRoomsFeesMoveInAgencyFeeFeeTypeOperatorsInput
  value: FilterFindManyPropertyRoomsFeesMoveInAgencyFeeValueOperatorsInput
}

input FilterFindManyPropertyRoomsFeesMoveInAgencyFeeValueOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyRoomsFeesMoveInContractRenewalFeeFeeTypeOperatorsInput {
  exists: Boolean
  gt: EnumPropertyRoomsFeesMoveInDepositFeeType
  gte: EnumPropertyRoomsFeesMoveInDepositFeeType
  in: [EnumPropertyRoomsFeesMoveInDepositFeeType]
  lt: EnumPropertyRoomsFeesMoveInDepositFeeType
  lte: EnumPropertyRoomsFeesMoveInDepositFeeType
  ne: EnumPropertyRoomsFeesMoveInDepositFeeType
  nin: [EnumPropertyRoomsFeesMoveInDepositFeeType]
}

input FilterFindManyPropertyRoomsFeesMoveInContractRenewalFeeOperatorsInput {
  feeType: FilterFindManyPropertyRoomsFeesMoveInContractRenewalFeeFeeTypeOperatorsInput
  value: FilterFindManyPropertyRoomsFeesMoveInContractRenewalFeeValueOperatorsInput
}

input FilterFindManyPropertyRoomsFeesMoveInContractRenewalFeeValueOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyRoomsFeesMoveInDepositFeeTypeOperatorsInput {
  exists: Boolean
  gt: EnumPropertyRoomsFeesMoveInDepositFeeType
  gte: EnumPropertyRoomsFeesMoveInDepositFeeType
  in: [EnumPropertyRoomsFeesMoveInDepositFeeType]
  lt: EnumPropertyRoomsFeesMoveInDepositFeeType
  lte: EnumPropertyRoomsFeesMoveInDepositFeeType
  ne: EnumPropertyRoomsFeesMoveInDepositFeeType
  nin: [EnumPropertyRoomsFeesMoveInDepositFeeType]
}

input FilterFindManyPropertyRoomsFeesMoveInDepositInput {
  feeType: EnumPropertyRoomsFeesMoveInDepositFeeType!
  value: Float!
}

input FilterFindManyPropertyRoomsFeesMoveInDepositOperatorsInput {
  feeType: FilterFindManyPropertyRoomsFeesMoveInDepositFeeTypeOperatorsInput
  value: FilterFindManyPropertyRoomsFeesMoveInDepositValueOperatorsInput
}

input FilterFindManyPropertyRoomsFeesMoveInDepositValueOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyRoomsFeesMoveInGuarantorFeeFeeTypeOperatorsInput {
  exists: Boolean
  gt: EnumPropertyRoomsFeesMoveInDepositFeeType
  gte: EnumPropertyRoomsFeesMoveInDepositFeeType
  in: [EnumPropertyRoomsFeesMoveInDepositFeeType]
  lt: EnumPropertyRoomsFeesMoveInDepositFeeType
  lte: EnumPropertyRoomsFeesMoveInDepositFeeType
  ne: EnumPropertyRoomsFeesMoveInDepositFeeType
  nin: [EnumPropertyRoomsFeesMoveInDepositFeeType]
}

input FilterFindManyPropertyRoomsFeesMoveInGuarantorFeeOperatorsInput {
  feeType: FilterFindManyPropertyRoomsFeesMoveInGuarantorFeeFeeTypeOperatorsInput
  value: FilterFindManyPropertyRoomsFeesMoveInGuarantorFeeValueOperatorsInput
}

input FilterFindManyPropertyRoomsFeesMoveInGuarantorFeeValueOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyRoomsFeesMoveInInput {
  agencyFee: FilterFindManyPropertyRoomsFeesMoveInDepositInput
  contractRenewalFee: FilterFindManyPropertyRoomsFeesMoveInDepositInput
  deposit: FilterFindManyPropertyRoomsFeesMoveInDepositInput
  guarantorFee: FilterFindManyPropertyRoomsFeesMoveInDepositInput
  keyMoney: FilterFindManyPropertyRoomsFeesMoveInDepositInput
}

input FilterFindManyPropertyRoomsFeesMoveInKeyMoneyFeeTypeOperatorsInput {
  exists: Boolean
  gt: EnumPropertyRoomsFeesMoveInDepositFeeType
  gte: EnumPropertyRoomsFeesMoveInDepositFeeType
  in: [EnumPropertyRoomsFeesMoveInDepositFeeType]
  lt: EnumPropertyRoomsFeesMoveInDepositFeeType
  lte: EnumPropertyRoomsFeesMoveInDepositFeeType
  ne: EnumPropertyRoomsFeesMoveInDepositFeeType
  nin: [EnumPropertyRoomsFeesMoveInDepositFeeType]
}

input FilterFindManyPropertyRoomsFeesMoveInKeyMoneyOperatorsInput {
  feeType: FilterFindManyPropertyRoomsFeesMoveInKeyMoneyFeeTypeOperatorsInput
  value: FilterFindManyPropertyRoomsFeesMoveInKeyMoneyValueOperatorsInput
}

input FilterFindManyPropertyRoomsFeesMoveInKeyMoneyValueOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyRoomsFeesMoveInOperatorsInput {
  agencyFee: FilterFindManyPropertyRoomsFeesMoveInAgencyFeeOperatorsInput
  contractRenewalFee: FilterFindManyPropertyRoomsFeesMoveInContractRenewalFeeOperatorsInput
  deposit: FilterFindManyPropertyRoomsFeesMoveInDepositOperatorsInput
  guarantorFee: FilterFindManyPropertyRoomsFeesMoveInGuarantorFeeOperatorsInput
  keyMoney: FilterFindManyPropertyRoomsFeesMoveInKeyMoneyOperatorsInput
}

input FilterFindManyPropertyRoomsFeesOperatorsInput {
  monthly: FilterFindManyPropertyRoomsFeesMonthlyOperatorsInput
  moveIn: FilterFindManyPropertyRoomsFeesMoveInOperatorsInput
  others: FilterFindManyPropertyRoomsFeesOthersOperatorsInput
}

input FilterFindManyPropertyRoomsFeesOthersChargeTimingOperatorsInput {
  exists: Boolean
  gt: EnumPropertyRoomsFeesOthersChargeTiming
  gte: EnumPropertyRoomsFeesOthersChargeTiming
  in: [EnumPropertyRoomsFeesOthersChargeTiming]
  lt: EnumPropertyRoomsFeesOthersChargeTiming
  lte: EnumPropertyRoomsFeesOthersChargeTiming
  ne: EnumPropertyRoomsFeesOthersChargeTiming
  nin: [EnumPropertyRoomsFeesOthersChargeTiming]
}

input FilterFindManyPropertyRoomsFeesOthersFeeTypeOperatorsInput {
  exists: Boolean
  gt: EnumPropertyRoomsFeesOthersFeeType
  gte: EnumPropertyRoomsFeesOthersFeeType
  in: [EnumPropertyRoomsFeesOthersFeeType]
  lt: EnumPropertyRoomsFeesOthersFeeType
  lte: EnumPropertyRoomsFeesOthersFeeType
  ne: EnumPropertyRoomsFeesOthersFeeType
  nin: [EnumPropertyRoomsFeesOthersFeeType]
}

input FilterFindManyPropertyRoomsFeesOthersInput {
  chargeTiming: EnumPropertyRoomsFeesOthersChargeTiming
  feeType: EnumPropertyRoomsFeesOthersFeeType
  value: Float
}

input FilterFindManyPropertyRoomsFeesOthersOperatorsInput {
  chargeTiming: FilterFindManyPropertyRoomsFeesOthersChargeTimingOperatorsInput
  feeType: FilterFindManyPropertyRoomsFeesOthersFeeTypeOperatorsInput
  value: FilterFindManyPropertyRoomsFeesOthersValueOperatorsInput
}

input FilterFindManyPropertyRoomsFeesOthersValueOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyRoomsFloorOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyRoomsImagesCollectionsOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyRoomsImagesFeaturedOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyRoomsImagesFloorPlanOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyRoomsImagesInput {
  collections: [String]
  featured: String
  floorPlan: String
}

input FilterFindManyPropertyRoomsImagesOperatorsInput {
  collections: FilterFindManyPropertyRoomsImagesCollectionsOperatorsInput
  featured: FilterFindManyPropertyRoomsImagesFeaturedOperatorsInput
  floorPlan: FilterFindManyPropertyRoomsImagesFloorPlanOperatorsInput
}

input FilterFindManyPropertyRoomsInput {
  availability: FilterFindManyPropertyRoomsAvailabilityInput
  code: String
  contractLength: Float
  contractType: EnumPropertyRoomsContractType
  createdAt: Date
  facilities: FilterFindManyPropertyRoomsFacilitiesInput
  fees: FilterFindManyPropertyRoomsFeesInput
  floor: String
  images: FilterFindManyPropertyRoomsImagesInput
  layout: String
  mainWindow: EnumPropertyRoomsMainWindow
  monthlyFee: Float
  numberOfRoom: Float
  pageViews: JSON
  roomLayout: String
  roomName: String
  size: Float
  tags: [String]
  transactionType: EnumPropertyRoomsTransactionType
  updatedAt: Date
  visibility: EnumPropertyRoomsVisibility
}

input FilterFindManyPropertyRoomsLayoutOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyRoomsMainWindowOperatorsInput {
  exists: Boolean
  gt: EnumPropertyRoomsMainWindow
  gte: EnumPropertyRoomsMainWindow
  in: [EnumPropertyRoomsMainWindow]
  lt: EnumPropertyRoomsMainWindow
  lte: EnumPropertyRoomsMainWindow
  ne: EnumPropertyRoomsMainWindow
  nin: [EnumPropertyRoomsMainWindow]
}

input FilterFindManyPropertyRoomsMonthlyFeeOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyRoomsNumberOfRoomOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyRoomsOperatorsInput {
  availability: FilterFindManyPropertyRoomsAvailabilityOperatorsInput
  code: FilterFindManyPropertyRoomsCodeOperatorsInput
  contractLength: FilterFindManyPropertyRoomsContractLengthOperatorsInput
  contractType: FilterFindManyPropertyRoomsContractTypeOperatorsInput
  createdAt: FilterFindManyPropertyRoomsCreatedAtOperatorsInput
  facilities: FilterFindManyPropertyRoomsFacilitiesOperatorsInput
  fees: FilterFindManyPropertyRoomsFeesOperatorsInput
  floor: FilterFindManyPropertyRoomsFloorOperatorsInput
  images: FilterFindManyPropertyRoomsImagesOperatorsInput
  layout: FilterFindManyPropertyRoomsLayoutOperatorsInput
  mainWindow: FilterFindManyPropertyRoomsMainWindowOperatorsInput
  monthlyFee: FilterFindManyPropertyRoomsMonthlyFeeOperatorsInput
  numberOfRoom: FilterFindManyPropertyRoomsNumberOfRoomOperatorsInput
  pageViews: FilterFindManyPropertyRoomsPageViewsOperatorsInput
  roomLayout: FilterFindManyPropertyRoomsRoomLayoutOperatorsInput
  roomName: FilterFindManyPropertyRoomsRoomNameOperatorsInput
  size: FilterFindManyPropertyRoomsSizeOperatorsInput
  tags: FilterFindManyPropertyRoomsTagsOperatorsInput
  transactionType: FilterFindManyPropertyRoomsTransactionTypeOperatorsInput
  updatedAt: FilterFindManyPropertyRoomsUpdatedAtOperatorsInput
  visibility: FilterFindManyPropertyRoomsVisibilityOperatorsInput
}

input FilterFindManyPropertyRoomsPageViewsOperatorsInput {
  exists: Boolean
  gt: JSON
  gte: JSON
  in: [JSON]
  lt: JSON
  lte: JSON
  ne: JSON
  nin: [JSON]
}

input FilterFindManyPropertyRoomsRoomLayoutOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyRoomsRoomNameOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyRoomsSizeOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyPropertyRoomsTagsOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyPropertyRoomsTransactionTypeOperatorsInput {
  exists: Boolean
  gt: EnumPropertyRoomsTransactionType
  gte: EnumPropertyRoomsTransactionType
  in: [EnumPropertyRoomsTransactionType]
  lt: EnumPropertyRoomsTransactionType
  lte: EnumPropertyRoomsTransactionType
  ne: EnumPropertyRoomsTransactionType
  nin: [EnumPropertyRoomsTransactionType]
}

input FilterFindManyPropertyRoomsUpdatedAtOperatorsInput {
  exists: Boolean
  gt: Date
  gte: Date
  in: [Date]
  lt: Date
  lte: Date
  ne: Date
  nin: [Date]
}

input FilterFindManyPropertyRoomsVisibilityOperatorsInput {
  exists: Boolean
  gt: EnumPropertyRoomsVisibility
  gte: EnumPropertyRoomsVisibility
  in: [EnumPropertyRoomsVisibility]
  lt: EnumPropertyRoomsVisibility
  lte: EnumPropertyRoomsVisibility
  ne: EnumPropertyRoomsVisibility
  nin: [EnumPropertyRoomsVisibility]
}

input FilterFindManyPropertyStructureOperatorsInput {
  exists: Boolean
  gt: EnumPropertyStructure
  gte: EnumPropertyStructure
  in: [EnumPropertyStructure]
  lt: EnumPropertyStructure
  lte: EnumPropertyStructure
  ne: EnumPropertyStructure
  nin: [EnumPropertyStructure]
}

input FilterFindManyServiceLineInput {
  AND: [FilterFindManyServiceLineInput!]
  OR: [FilterFindManyServiceLineInput!]
  _id: String
  "List of *indexed* fields that can be filtered via operators."
  _operators: FilterFindManyServiceLineOperatorsInput
  en: String
  name: String
  sort: Float
  stationIds: [String]
}

"For performance reason this type contains only *indexed* fields."
input FilterFindManyServiceLineOperatorsInput {
  _id: FilterFindManyServiceLine_idOperatorsInput
  sort: FilterFindManyServiceLineSortOperatorsInput
}

input FilterFindManyServiceLineSortOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindManyServiceLine_idOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindManyStationInput {
  AND: [FilterFindManyStationInput!]
  OR: [FilterFindManyStationInput!]
  _id: String
  "List of *indexed* fields that can be filtered via operators."
  _operators: FilterFindManyStationOperatorsInput
  en: String
  lineIds: [String]
  location: FilterFindManyStationLocationInput
  name: String
}

input FilterFindManyStationLocationInput {
  lat: Float
  long: Float
}

"For performance reason this type contains only *indexed* fields."
input FilterFindManyStationOperatorsInput {
  _id: FilterFindManyStation_idOperatorsInput
}

input FilterFindManyStation_idOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindOnePropertyAccessInput {
  stations: [FilterFindOnePropertyAccessStationsInput]
}

input FilterFindOnePropertyAccessStationsDistanceInput {
  bus: Float
  walk: Float
}

input FilterFindOnePropertyAccessStationsInput {
  distance: FilterFindOnePropertyAccessStationsDistanceInput
  lineId: String
  stationId: String
}

input FilterFindOnePropertyAgencyIdOperatorsInput {
  exists: Boolean
  gt: MongoID
  gte: MongoID
  in: [MongoID]
  lt: MongoID
  lte: MongoID
  ne: MongoID
  nin: [MongoID]
}

input FilterFindOnePropertyHighestMonthlyPriceOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindOnePropertyImagesInput {
  collections: [String]
}

input FilterFindOnePropertyInput {
  AND: [FilterFindOnePropertyInput!]
  OR: [FilterFindOnePropertyInput!]
  "List of *indexed* fields that can be filtered via operators."
  _operators: FilterFindOnePropertyOperatorsInput
  access: FilterFindOnePropertyAccessInput
  agencyId: MongoID
  buildingName: String
  buildingType: EnumPropertyBuildingType
  builtDate: Date
  highestMonthlyPrice: Float
  images: FilterFindOnePropertyImagesInput
  location: FilterFindOnePropertyLocationInput
  lowestMonthlyPrice: Float
  maxFloor: Float
  nearestStationDistance: Float
  pageViews: JSON
  rooms: [FilterFindOnePropertyRoomsInput]
  structure: EnumPropertyStructure
  languageComment: JSON
}

input FilterFindOnePropertyLocationInput {
  addressLine: String
  area: String
  chome: String
  city: String
  cityArea: String
  position: FilterFindOnePropertyLocationPositionInput
  postalCode: String
  prefecture: String
  region: String
}

input FilterFindOnePropertyLocationPositionInput {
  lat: Float
  long: Float
}

input FilterFindOnePropertyLowestMonthlyPriceOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

input FilterFindOnePropertyNearestStationDistanceOperatorsInput {
  exists: Boolean
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nin: [Float]
}

"For performance reason this type contains only *indexed* fields."
input FilterFindOnePropertyOperatorsInput {
  agencyId: FilterFindOnePropertyAgencyIdOperatorsInput
  highestMonthlyPrice: FilterFindOnePropertyHighestMonthlyPriceOperatorsInput
  lowestMonthlyPrice: FilterFindOnePropertyLowestMonthlyPriceOperatorsInput
  nearestStationDistance: FilterFindOnePropertyNearestStationDistanceOperatorsInput
  pageViews: FilterFindOnePropertyPageViewsOperatorsInput
  rooms: FilterFindOnePropertyRoomsOperatorsInput
}

input FilterFindOnePropertyPageViewsOperatorsInput {
  exists: Boolean
  gt: JSON
  gte: JSON
  in: [JSON]
  lt: JSON
  lte: JSON
  ne: JSON
  nin: [JSON]
}

input FilterFindOnePropertyRoomsAvailabilityInput {
  enquire: Boolean
  now: Boolean
  status: EnumPropertyRoomsAvailabilityStatus
}

input FilterFindOnePropertyRoomsCodeOperatorsInput {
  exists: Boolean
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nin: [String]
  regex: RegExpAsString
}

input FilterFindOnePropertyRoomsFacilitiesInput {
  basic: [String]
  building: [String]
  other: [String]
}

input FilterFindOnePropertyRoomsFeesInput {
  monthly: FilterFindOnePropertyRoomsFeesMonthlyInput
  moveIn: FilterFindOnePropertyRoomsFeesMoveInInput
  others: [FilterFindOnePropertyRoomsFeesOthersInput]
}

input FilterFindOnePropertyRoomsFeesMonthlyInput {
  maintenanceFee: Float
  rentFee: Float
}

input FilterFindOnePropertyRoomsFeesMoveInDepositInput {
  feeType: EnumPropertyRoomsFeesMoveInDepositFeeType!
  value: Float!
}

input FilterFindOnePropertyRoomsFeesMoveInInput {
  agencyFee: FilterFindOnePropertyRoomsFeesMoveInDepositInput
  contractRenewalFee: FilterFindOnePropertyRoomsFeesMoveInDepositInput
  deposit: FilterFindOnePropertyRoomsFeesMoveInDepositInput
  guarantorFee: FilterFindOnePropertyRoomsFeesMoveInDepositInput
  keyMoney: FilterFindOnePropertyRoomsFeesMoveInDepositInput
}

input FilterFindOnePropertyRoomsFeesOthersInput {
  chargeTiming: EnumPropertyRoomsFeesOthersChargeTiming
  feeType: EnumPropertyRoomsFeesOthersFeeType
  value: Float
}

input FilterFindOnePropertyRoomsImagesInput {
  collections: [String]
  featured: String
  floorPlan: String
}

input FilterFindOnePropertyRoomsInput {
  availability: FilterFindOnePropertyRoomsAvailabilityInput
  code: String
  contractLength: Float
  contractType: EnumPropertyRoomsContractType
  createdAt: Date
  facilities: FilterFindOnePropertyRoomsFacilitiesInput
  fees: FilterFindOnePropertyRoomsFeesInput
  floor: String
  images: FilterFindOnePropertyRoomsImagesInput
  layout: String
  mainWindow: EnumPropertyRoomsMainWindow
  monthlyFee: Float
  numberOfRoom: Float
  pageViews: JSON
  roomLayout: String
  roomName: String
  size: Float
  tags: [String]
  transactionType: EnumPropertyRoomsTransactionType
  updatedAt: Date
  visibility: EnumPropertyRoomsVisibility
}

input FilterFindOnePropertyRoomsOperatorsInput {
  code: FilterFindOnePropertyRoomsCodeOperatorsInput
}

input FilterFindOneSettingInput {
  AND: [FilterFindOneSettingInput!]
  OR: [FilterFindOneSettingInput!]
  _id: MongoID
  "List of *indexed* fields that can be filtered via operators."
  _operators: FilterFindOneSettingOperatorsInput
  featured: [String]
}

"For performance reason this type contains only *indexed* fields."
input FilterFindOneSettingOperatorsInput {
  _id: FilterFindOneSetting_idOperatorsInput
}

input FilterFindOneSetting_idOperatorsInput {
  exists: Boolean
  gt: MongoID
  gte: MongoID
  in: [MongoID]
  lt: MongoID
  lte: MongoID
  ne: MongoID
  nin: [MongoID]
}

input PropertyAccessInput {
  stations: [PropertyAccessStationsInput]
}

input PropertyAccessStationsDistanceInput {
  bus: Float
  walk: Float
}

input PropertyAccessStationsInput {
  distance: PropertyAccessStationsDistanceInput
  lineId: String
  stationId: String
}

input PropertyImagesInput {
  collections: [String]!
}

input PropertyLocationInput {
  addressLine: String
  area: String!
  chome: String!
  city: String!
  cityArea: String!
  position: PropertyLocationPositionInput
  postalCode: String
  prefecture: String!
  region: String!
}

input PropertyLocationPositionInput {
  lat: Float
  long: Float
}

input PropertyRoomsAvailabilityInput {
  enquire: Boolean
  now: Boolean
  status: EnumPropertyRoomsAvailabilityStatus!
}

input PropertyRoomsFacilitiesInput {
  basic: [String]
  building: [String]
  other: [String]
}

input PropertyRoomsFeesInput {
  monthly: PropertyRoomsFeesMonthlyInput!
  moveIn: PropertyRoomsFeesMoveInInput!
  others: [PropertyRoomsFeesOthersInput]
}

input PropertyRoomsFeesMonthlyInput {
  maintenanceFee: Float
  rentFee: Float
}

input PropertyRoomsFeesMoveInDepositInput {
  feeType: EnumPropertyRoomsFeesMoveInDepositFeeType!
  value: Float!
}

input PropertyRoomsFeesMoveInInput {
  agencyFee: PropertyRoomsFeesMoveInDepositInput!
  contractRenewalFee: PropertyRoomsFeesMoveInDepositInput!
  deposit: PropertyRoomsFeesMoveInDepositInput!
  guarantorFee: PropertyRoomsFeesMoveInDepositInput!
  keyMoney: PropertyRoomsFeesMoveInDepositInput!
}

input PropertyRoomsFeesOthersInput {
  chargeTiming: EnumPropertyRoomsFeesOthersChargeTiming
  feeType: EnumPropertyRoomsFeesOthersFeeType
  value: Float
}

input PropertyRoomsImagesInput {
  collections: [String]!
  featured: String!
  floorPlan: String!
}

input RoomInput {
  availability: PropertyRoomsAvailabilityInput!
  contractLength: Float!
  contractType: EnumPropertyRoomsContractType!
  facilities: PropertyRoomsFacilitiesInput
  fees: PropertyRoomsFeesInput!
  floor: String!
  images: PropertyRoomsImagesInput!
  layout: String
  mainWindow: EnumPropertyRoomsMainWindow!
  numberOfRoom: Float!
  pageViews: JSON
  roomLayout: String
  roomName: String
  size: Float!
  tags: [String]
  transactionType: EnumPropertyRoomsTransactionType
  visibility: EnumPropertyRoomsVisibility!
}

input SendToSupportInput {
  email: String!
  message: String!
  name: String!
}

input UpdateByIdAgencyBasicInput {
  companyName: String
  license: UpdateByIdAgencyBasicLicenseInput
}

input UpdateByIdAgencyBasicLicenseExpiryDateInput {
  from: String
  until: String
}

input UpdateByIdAgencyBasicLicenseInput {
  expiryDate: UpdateByIdAgencyBasicLicenseExpiryDateInput
  issueNumber: String
  licenseType: EnumAgencyBasicLicenseLicenseType
  number: String
}

input UpdateByIdAgencyInput {
  basic: UpdateByIdAgencyBasicInput
  public: UpdateByIdAgencyPublicInput
  serviceFee: UpdateByIdAgencyServiceFeeInput
}

input UpdateByIdAgencyPublicAddressInput {
  buildingName: String
  city: String
  postalCode: String
  prefecture: String
  streetAddress: String
}

input UpdateByIdAgencyPublicBusinessHourInput {
  end: String
  start: String
}

input UpdateByIdAgencyPublicInput {
  address: UpdateByIdAgencyPublicAddressInput
  availableLanguages: [String]
  businessHour: UpdateByIdAgencyPublicBusinessHourInput
  closeDays: [String]
  email: String
  imageUrl: String
  name: String
  phone: String
  website: String
}

input UpdateByIdAgencyServiceFeeInput {
  feeType: EnumAgencyServiceFeeFeeType
  value: Float
}

input UpdateByIdPropertyAccessInput {
  stations: [UpdateByIdPropertyAccessStationsInput]
}

input UpdateByIdPropertyAccessStationsDistanceInput {
  bus: Float
  walk: Float
}

input UpdateByIdPropertyAccessStationsInput {
  distance: UpdateByIdPropertyAccessStationsDistanceInput
  lineId: String
  stationId: String
}

input UpdateByIdPropertyImagesInput {
  collections: [String]
}

input UpdateByIdPropertyInput {
  access: UpdateByIdPropertyAccessInput
  agencyId: MongoID
  buildingName: String
  buildingType: EnumPropertyBuildingType
  builtDate: Date
  highestMonthlyPrice: Float
  images: UpdateByIdPropertyImagesInput
  location: UpdateByIdPropertyLocationInput
  lowestMonthlyPrice: Float
  maxFloor: Float
  nearestStationDistance: Float
  pageViews: JSON
  rooms: [UpdateByIdPropertyRoomsInput]
  structure: EnumPropertyStructure
  languageComment: JSON
}

input UpdateByIdPropertyLocationInput {
  addressLine: String
  area: String
  chome: String
  city: String
  cityArea: String
  position: UpdateByIdPropertyLocationPositionInput
  postalCode: String
  prefecture: String
  region: String
}

input UpdateByIdPropertyLocationPositionInput {
  lat: Float
  long: Float
}

input UpdateByIdPropertyRoomsAvailabilityInput {
  enquire: Boolean
  now: Boolean
  status: EnumPropertyRoomsAvailabilityStatus
}

input UpdateByIdPropertyRoomsFacilitiesInput {
  basic: [String]
  building: [String]
  other: [String]
}

input UpdateByIdPropertyRoomsFeesInput {
  monthly: UpdateByIdPropertyRoomsFeesMonthlyInput
  moveIn: UpdateByIdPropertyRoomsFeesMoveInInput
  others: [UpdateByIdPropertyRoomsFeesOthersInput]
}

input UpdateByIdPropertyRoomsFeesMonthlyInput {
  maintenanceFee: Float
  rentFee: Float
}

input UpdateByIdPropertyRoomsFeesMoveInDepositInput {
  feeType: EnumPropertyRoomsFeesMoveInDepositFeeType!
  value: Float!
}

input UpdateByIdPropertyRoomsFeesMoveInInput {
  agencyFee: UpdateByIdPropertyRoomsFeesMoveInDepositInput
  contractRenewalFee: UpdateByIdPropertyRoomsFeesMoveInDepositInput
  deposit: UpdateByIdPropertyRoomsFeesMoveInDepositInput
  guarantorFee: UpdateByIdPropertyRoomsFeesMoveInDepositInput
  keyMoney: UpdateByIdPropertyRoomsFeesMoveInDepositInput
}

input UpdateByIdPropertyRoomsFeesOthersInput {
  chargeTiming: EnumPropertyRoomsFeesOthersChargeTiming
  feeType: EnumPropertyRoomsFeesOthersFeeType
  value: Float
}

input UpdateByIdPropertyRoomsImagesInput {
  collections: [String]
  featured: String
  floorPlan: String
}

input UpdateByIdPropertyRoomsInput {
  availability: UpdateByIdPropertyRoomsAvailabilityInput
  code: String
  contractLength: Float
  contractType: EnumPropertyRoomsContractType
  createdAt: Date
  facilities: UpdateByIdPropertyRoomsFacilitiesInput
  fees: UpdateByIdPropertyRoomsFeesInput
  floor: String
  images: UpdateByIdPropertyRoomsImagesInput
  layout: String
  mainWindow: EnumPropertyRoomsMainWindow
  monthlyFee: Float
  numberOfRoom: Float
  pageViews: JSON
  roomLayout: String
  roomName: String
  size: Float
  tags: [String]
  transactionType: EnumPropertyRoomsTransactionType
  updatedAt: Date
  visibility: EnumPropertyRoomsVisibility
}


"The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf)."
scalar JSON

"The `ID` scalar type represents a unique MongoDB identifier in collection. MongoDB by default use 12-byte ObjectId value (https://docs.mongodb.com/manual/reference/bson-types/#objectid). But MongoDB also may accepts string or integer as correct values for _id field."
scalar MongoID

scalar Date

"The string representation of JavaScript regexp. You may provide it with flags \"/^abc.*/i\" or without flags like \"^abc.*\". More info about RegExp characters and flags: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions"
scalar RegExpAsString
