fragment BaseAgency on Agency {
  _id
  updatedAt
  createdAt
  basic {
    companyName
    license {
      haveLicense
      licenseType
      number
      issueNumber
      expiryDate {
        from
        until
      }
    }
  }
  public {
    name
    imageUrl
    phone
    email
    availableLanguages
    businessHour {
      start
      end
    }
    closeDays
    website
  }
  addressDetail {
    streetAddress
    buildingName
    prefecture
    city
    chome
  }
}
