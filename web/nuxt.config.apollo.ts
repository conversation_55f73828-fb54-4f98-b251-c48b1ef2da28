import { Context } from '@nuxt/types'
import { setContext } from 'apollo-link-context'
import { from } from 'apollo-link'

export default (context: Context) => {
  const headersConfig = setContext(() => ({
    headers: {
      'accept-language': context.i18n.localeProperties.iso?.toLowerCase(),
    },
  }))

  return {
    link: from([headersConfig]),
    httpEndpoint: context.$config.gqlSSRUrl || context.$config.gqlUrl,
    browserHttpEndpoint: context.$config.gqlUrl,
  }
}
