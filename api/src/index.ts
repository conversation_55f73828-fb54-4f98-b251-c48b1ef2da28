import express from 'express'
import cors from 'cors'
import 'module-alias/register'
import * as Sen<PERSON> from "@sentry/node";
import { toobusyMiddleware } from '@app/middleware'

console.log("test")

// We don't need dotenv on cloud,
// we grab it from environment variables directly
if (process.env.NODE_ENV === 'local') {
  const dotenv = require('dotenv')
  dotenv.config()
}
import './fetch-polyfill'
import logger from '@app/service/logger'
import '@app/redis'

import { i18next, i18nextMiddleware } from '@app/i18next'
import authentication from '@app/middleware/authentication'
import graphql from '@app/graphql'

const app = express()
if (process.env.NODE_ENV !== "development") {
  Sentry.init({
    dsn: process.env.SENTRY_DSN || "https://<EMAIL>/4509636555046912",
    integrations: [
      // enable HTTP calls tracing
      new Sentry.Integrations.Http({ tracing: true }),
      // enable Express.js middleware tracing
      new Sentry.Integrations.Express({ app }),
    ],
    // Performance Monitoring
    tracesSampleRate: 1.0,
    // Set sampling rate for profiling - this is relative to tracesSampleRate
  });

  // The request handler must be the first middleware on the app
  app.use(Sentry.Handlers.requestHandler());

  // TracingHandler creates a trace for every incoming request
  app.use(Sentry.Handlers.tracingHandler());
}
/*
// Auto redirect any HTTP request to HTTPS
if (process.env.NODE_ENV !== 'local') {
  app.enable('trust proxy')
  app.use((req, res, next) => {
    // Header from AWS ELB as indicator if the request is https or http
    const xfp = req.headers['X-Forwarded-Proto'] || req.headers['x-forwarded-proto']
    const isSecure = xfp != 'http'
    const newUrl = `https://${req.header('host')}${req.url}`
    isSecure ? next() : res.redirect(308, newUrl)
  })
}
*/

/**
 * Setup CORS
 */
const setupCors = () =>
  cors({
    origin: [
      /localhost/,
      /ngrok/,
      ...(process.env.CORS_ORIGIN ? [new RegExp(process.env.CORS_ORIGIN)] : [])
    ],
    optionsSuccessStatus: 200
  })

app.use(toobusyMiddleware)
app.use(
  '/graphql',
  express.json(),
  setupCors(),
  i18nextMiddleware.handle(i18next),
  authentication,
  graphql
)
app.get("/debug-sentry", function mainHandler(req, res) {
  throw new Error("Error from sentry")
});
if (process.env.NODE_ENV !== "development") {
  app.use(Sentry.Handlers.errorHandler());
}
app.use('*', (req, res) => {
  res.status(404).send('404 Not Found')
})

const port = process.env.APP_PORT || 3000;
console.log('APP_PORT:', process.env.APP_PORT)
console.log('CORS_ORIGIN:', process.env.CORS_ORIGIN)
try {
  app.listen(port, () => {
    const message = 'API started on port: ' + port
    logger.info(message)
    // eslint-disable-next-line no-console
    console.log(message)
  })
} catch (err) {
  console.error('Startup error:', err)
  process.exit(1)
}

process.on('unhandledRejection', function (err) {
  // clean up allocated resources
  // log necessary error details to log files
  logger.error(err)
  // process.exit(); // exit the process to avoid unknown state
});

process.on('uncaughtException', function (err: any) {
  console.log('uncaughtException', err)
  // clean up allocated resources
  // log necessary error details to log files
  logger.error(err)
});

process.on('SIGINT', function (err: any) {
  console.log('SIGINT', err)
  // Gracefully shutdown toobusy to allow event loop to exit
  const toobusy = require('toobusy-js')
  toobusy.shutdown()
  process.exit();
});