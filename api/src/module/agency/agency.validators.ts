export const hasAuth = async (resolve, source, args, context, info) => {
  const { user } = context

  const error = new Error('Forbidden. You must be authorized.')
  if (!user) return Promise.reject(error)

  const { agencyId: id, roles } = user
  const { _id: targetId } = args
  const { fieldName: action } = info

  for (const role of roles) {
    if (role === 'admin') {
      return resolve(source, args, context, info)
    }

    if (isAuthorizedAgency({ role, id, targetId, action })) {
      return resolve(source, args, context, info)
    }
  }

  return Promise.reject(error)
}

function isAuthorizedAgency({ role, id, targetId, action }) {
  if (role !== 'agency-owner') return false

  const agencyAllowedActions = ['agencyById', 'updateAgency']
  const isForbiddenAction = !agencyAllowedActions.includes(action)
  if (isForbiddenAction) return false

  const isOtherAgency = id !== targetId
  if (isOtherAgency) return false

  return true
}
