import { composeMongoose } from 'graphql-compose-mongoose'
import { authMiddleware as middleware } from '@app/middleware'
import Property from './property.model'
import { AgencyTC } from '../agency/agency.schema'
import { preparePaginationResolver } from 'graphql-compose-pagination'
import { extendedFindMany } from './properties.resolver'
import get from 'lodash/get'
import { ServiceLineTC, StationTC } from '../access/access.resolvers'
import dayjs from 'dayjs'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import dayOfYear from 'dayjs/plugin/dayOfYear'
import Setting from '../setting/setting.model'
import { schemaComposer } from 'graphql-compose'
import { ServiceLineModel, StationModel } from '../access/access.model'
import { getLocaleField } from '@app/utils'
import PrefectureModel from '../area/prefecture.model'
import ChomeModel from '../area/chome.model'
import CityModel from '../area/city.model'

export const TagCount = schemaComposer.createObjectTC({
  name: 'TagCount',
  fields: {
    id: 'String!',
    count: 'Int!'
  }
})

dayjs.extend(weekOfYear)
dayjs.extend(dayOfYear)

const getPageViewFields = () => {
  const today = dayjs()
  return {
    yearly: `${today.year()}`,
    monthly: `${today.year()}_m${today.month()}`,
    weekly: `${today.year()}_w${today.week()}`,
    daily: `${today.year()}_d${today.dayOfYear()}`
  }
}

export const PropertyTC = composeMongoose(Property, {
  name: 'Property',
  description: 'The entity which can be rented',
  inputType: {
    removeFields: ['_id', 'createdAt', 'updatedAt']
  }
})

PropertyTC.addRelation('agency', {
  resolver: () => AgencyTC.mongooseResolvers.findById(),
  prepareArgs: {
    _id: source => (source as any).agencyId
  },
  projection: { agencyId: 1 }
})

const stationTC = PropertyTC.getFieldOTC('access').getFieldOTC('stations')

// TODO: deprecated, remove this later
stationTC.addRelation('stationDetail', {
  resolver: () => StationTC.mongooseResolvers.findById({ lean: true }),
  prepareArgs: {
    _id: source => (source as any).stationId
  },
  projection: { stationId: 1 }
})

// TODO: deprecated, remove this later
stationTC.addRelation('lineDetail', {
  resolver: () => ServiceLineTC.mongooseResolvers.findById({ lean: true }),
  prepareArgs: {
    _id: source => (source as any).lineId
  },
  projection: { lineId: 1 }
})

stationTC.addFields({
  stationName: {
    type: 'String',
    description: 'Localized name of the train station',
    resolve: async (source: any, __args, context) => {
      const locale = getLocaleField(context)
      const station = await StationModel.findById(source.stationId, { [locale]: 1 }, { lean: true })
      return get(station, locale, '[missing]')
    }
  },
  lineName: {
    type: 'String',
    description: 'Localized name of the train service line',
    resolve: async (source: any, __args, context) => {
      if (!source.lineId) {
        return '[missing]'
      }

      try {
        const locale = getLocaleField(context)
        const lineIds = source.lineId.split(',')
        const filterObject = lineIds.map(lineId => {
          return { _id: lineId }
        })

        const lines = await ServiceLineModel.find(
          {
            $or: filterObject
          },
          { [locale]: 1 },
          { lean: true }
        )

        return lines.map(line => get(line, locale, '[missing]')).join(', ')
      } catch (err) {
        return '[missing]'
      }
    }
  }
})

stationTC.deprecateFields({
  stationDetail: 'Use `stationName` to obtain the name of station',
  lineDetail: 'Use `lineName` to obtain the name of line'
})

PropertyTC.getFieldOTC('rooms').addFields({
  todayPageView: {
    type: 'Int',
    description: 'How many today page view for the room',
    async resolve(source: any) {
      const fieldNames = getPageViewFields()
      const pageViewField = `pageViews.${fieldNames.daily}`
      const query = { 'rooms.code': source.code }
      const projection = {
        ['rooms.code']: 1,
        [`rooms.${pageViewField}`]: 1
      }
      const property = await Property.findOne(query, projection)
      const selectedRoom = (property as any).rooms?.find(item => item.code === source.code)
      if (!selectedRoom) return 0
      return get(selectedRoom, pageViewField, 0)
    }
  }
})

PropertyTC.addFields({
  addressDetail: {
    type: 'PropertyAddress',
    description: 'The Localized of the property address information',
    // eslint-disable-next-line no-unused-vars
    async resolve(source: any, __args, context) {
      const document = await Property.findById(source._id, { address: 1 })
      const { chome, prefecture, city, line, postalCode, coord } = (document as any).address

      const field = getLocaleField(context, 'jp')
      const matchedPrefecture = await PrefectureModel.findById(prefecture, { en: 1, [field]: 1 })
      const matchedCity = await CityModel.findById(city, { en: 1, [field]: 1 })
      const matchedChome = await ChomeModel.findById(chome, { jp: 1, en: 1, [field]: 1 })

      return {
        line,
        postalCode,
        coord,
        prefecture: matchedPrefecture[field] || matchedPrefecture.en,
        city: matchedCity[field] || matchedCity.en,
        chome: matchedChome[field] || matchedChome.en || matchedChome.jp
      }
    }
  },
  prefecturePath: {
    type: 'String',
    description: 'Url friendly version of the prefecture name',
    // eslint-disable-next-line no-unused-vars
    async resolve(source: any) {
      const document = await Property.findById(source._id, { address: 1 })
      const prefecture = await PrefectureModel.findById((document as any).address.prefecture, {
        en: 1
      })

      return prefecture.en.toLowerCase()
    }
  }
})
const { mongooseResolvers: resolver } = PropertyTC
export const propertyById = resolver.findById()
export const property = resolver.findOne({
  lean: true,
  sort: false,
  filter: {
    operators: {
      rooms: { code: true }
    }
  }
})

export const properties = preparePaginationResolver(PropertyTC, {
  findManyResolver: extendedFindMany(Property, PropertyTC, {
    lean: true,
    filter: { operators: true } // FIXME: performance?
  }),
  countResolver: PropertyTC.mongooseResolvers.count(),
  name: 'pagination', // Default
  perPage: 20 // Default
})
  .addSortArg({
    name: 'HIGHEST_VIEW_WEEKLY',
    value: () => {
      const fields = getPageViewFields()
      return {
        [`pageViews.${fields.weekly}`]: -1
      }
    }
  })
  .addSortArg({
    name: 'INQUIRYCOUNT_ASC',
    value: () => ({
      'rooms.inquiryCount': 1
    })
  })
  .addSortArg({
    name: 'INQUIRYCOUNT_DESC',
    value: () => ({
      'rooms.inquiryCount': -1
    })
  })
  .addFilterArg({
    name: '_include_rooms_tags',
    type: '[String]',
    query: (query: any, $all: string[]) => {
      query['rooms.tags'] = { $all }
    }
  })

export const featuredProperties = PropertyTC.addResolver({
  kind: 'query',
  name: 'featuredProperties',
  type: '[Property]',
  resolve: async () => {
    const setting: any = await Setting.findOne()
    if (!setting?.featured) return []
    if (setting?.featured.length < 1) return []
    let result = await Property.aggregate([
      { $match: { 'rooms.code': { $in: setting.featured } } },
      {
        // We filtered out the room which the code didnt match with featured
        $addFields: {
          rooms: {
            $filter: {
              input: '$rooms',
              as: 'item',
              cond: { $in: ['$$item.code', setting.featured] }
            }
          }
        }
      }
    ])
    //Sort result by setting.featured
    result.sort((a, b) => {
      return setting.featured.indexOf(a.rooms[0].code) - setting.featured.indexOf(b.rooms[0].code)
    })

    return result
  }
})

TagCount.addResolver({
  kind: 'query',
  name: 'tags',
  type: '[TagCount]',
  resolve: async () => {
    //Todo: If add new tag to DB, please update this list
    const tagsOrder = [
      'furnished',
      'no_key_money',
      'no_deposit',
      'free_rent',
      'pet_friendly',
      'renovation',
      'penthouse',
      'office_use'
    ]

    const result = await Property.aggregate([
      { $unwind: '$rooms' },
      { $unwind: '$rooms.tags' },
      { $match: { 'rooms.visibility': 'public' } },
      { $group: { _id: { id: '$_id', tag: '$rooms.tags' } } },
      {
        $group: {
          _id: '$_id.tag',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          id: '$_id',
          count: 1
        }
      }
    ])
    result.sort((a, b) => {
      return tagsOrder.indexOf(a.id) - tagsOrder.indexOf(b.id)
    })
    return result
  }
})

export const createProperty = resolver
  .createOne({
    record: {
      isRequired: true
    }
  })
  .withMiddlewares([middleware.isAuth])

export const updateProperty = resolver
  .updateById({
    record: {
      isRequired: true
    }
  })
  .withMiddlewares([middleware.isAuth])

export const deleteProperty = resolver.removeById().withMiddlewares([middleware.isAuth])
export const incrementPageView = {
  type: 'Boolean',
  args: {
    propertyId: 'MongoID!',
    roomCode: 'String!'
  },
  resolve: async (__, args) => {
    const fieldNames = getPageViewFields()
    const values = {
      'pageViews.total': 1,
      [`pageViews.${fieldNames.yearly}`]: 1,
      [`pageViews.${fieldNames.monthly}`]: 1,
      [`pageViews.${fieldNames.weekly}`]: 1,
      [`pageViews.${fieldNames.daily}`]: 1,
      'rooms.$.pageViews.total': 1,
      [`rooms.$.pageViews.${fieldNames.yearly}`]: 1,
      [`rooms.$.pageViews.${fieldNames.monthly}`]: 1,
      [`rooms.$.pageViews.${fieldNames.weekly}`]: 1,
      [`rooms.$.pageViews.${fieldNames.daily}`]: 1
    }

    return !!(await Property.findOneAndUpdate(
      { _id: args.propertyId, 'rooms.code': args.roomCode },
      { $inc: values },
      { timestamps: false }
    ))
  }
}
