import Property from './property.model'
import { generateRoomCode } from './room/room.model'
import get from 'lodash/get'

function getMonthlyCost(room) {
  const { rentFee = 0, maintenanceFee = 0 } = room.fees?.monthly || {
    rentFee: 0,
    maintenanceFee: 0
  }
  return rentFee + maintenanceFee
}

export enum CustomTags {
  NoDeposit = 'no_deposit',
  NoKeyMoney = 'no_key_money',
  PetFriendly = 'pet_friendly'
}

export default function addPropertyHooks(schema: any) {
  // eslint-disable-next-line no-unused-vars
  schema.pre('save', async function (this: any) {
    this.highestMonthlyPrice = this.rooms.reduce((currentHighest: number, room) => {
      if (room.visibility === 'private') return currentHighest
      const monthlyCost = getMonthlyCost(room)
      return monthlyCost > currentHighest ? monthlyCost : currentHighest
    }, 0)

    this.lowestMonthlyPrice = this.rooms.reduce((currentLowest: number, room) => {
      if (room.visibility === 'private') return currentLowest
      // For lowest monthly price, we don't include the maintenance fee as a factor
      const monthlyCost = room.fees?.monthly?.rentFee || 0
      return monthlyCost < currentLowest ? monthlyCost : currentLowest
    }, 999999999)

    // Used to check for any duplicate codes later,
    // regenerate & assign a new code if it's duplicated
    const codesIndex = {}

    /**
     * Sync some computed fields that used as an index for filtering
     * Currently the fields are being computed are
     * - monthlyFee
     * - roomLayout
     * - nearestStationDistance
     * - Add custom tags for no deposit, no key money, pet friendly
     */
    this.rooms.forEach(room => {
      const { numberOfRoom, layout } = room
      const { rentFee, maintenanceFee } = room.fees.monthly
      room.monthlyFee = rentFee + maintenanceFee
      const isMore = numberOfRoom > 3
      room.roomLayout = isMore ? 'more' : `${numberOfRoom}${layout}`

      // Below flow is necessary for saving new multiple property rooms
      const isCodeExist = codesIndex[room.code]
      const noCode = !room.code || room.code === ''
      const shouldAssignCode = isCodeExist || noCode
      if (shouldAssignCode) room.code = generateRoomCode()
      codesIndex[room.code] = true

      function addOrRemoveTag(shouldAdd: boolean, tag: CustomTags) {
        const isTagExist = room.tags.includes(tag)
        if (shouldAdd && !isTagExist) {
          room.tags.push(tag)
        } else if (!shouldAdd && isTagExist) {
          room.tags = room.tags.filter(item => item !== tag)
        }
      }

      const hasNoDeposit = get(room, 'fees.moveIn.deposit.value', 0) === 0
      addOrRemoveTag(hasNoDeposit, CustomTags.NoDeposit)

      const hasNoKeyMoney = get(room, 'fees.moveIn.keyMoney.value', 0) === 0
      addOrRemoveTag(hasNoKeyMoney, CustomTags.NoKeyMoney)

      const allowPet = get(room, 'facilities.other', []).includes('allow_pet')
      addOrRemoveTag(allowPet, CustomTags.PetFriendly)
    })

    // Computed for nearestStationDistance
    this.nearestStationDistance = this.access.stations.reduce((currentNearest: number, station) => {
      const lineId = get(station, 'lineId', '').trim()
      const stationId = get(station, 'stationId', '').trim()
      if (lineId === '') return currentNearest
      if (stationId === '') return currentNearest

      const baseDistance = get(station, 'distance.walk', 999999999)
      if (baseDistance < currentNearest) return baseDistance
      return currentNearest
    }, 999999999)

    if (this.isNew) return

    const projection = { 'rooms.code': 1, 'rooms.pageViews': 1 }
    const result = await Property.findById(this._id, projection)
    if (!result) return

    // Reassign the page views value,
    // So we don't lost any page views count during update operation
    const existingData = result as any
    const pageViewsDict = existingData.rooms.reduce(
      (acc: object, old: any) => ({
        ...acc,
        [old.code]: old.pageViews
      }),
      {}
    )

    this.rooms.forEach((room: any, index: number) => {
      this.rooms[index].pageViews = pageViewsDict[room.code]
    })
  })
}
