/**
 * Original file https://raw.githubusercontent.com/graphql-compose/graphql-compose-mongoose/0030927377e7a6782d271a511053de4214fa1216/src/resolvers/findMany.ts
 * Modified to enable the projection of the results
 */
import type { Resolver, ObjectTypeComposer } from 'graphql-compose'
import type { Model, Document } from 'mongoose'
import {
  limitHelper,
  limitHelperArgs,
  skip<PERSON><PERSON>per,
  skipHelperArgs,
  filterHelper,
  filterHelperArgs,
  sortHelper,
  sortHelperArgs,
  projectionHelper,
  prepareNestedAliases,
  prepareAliasesReverse,
  replaceAliases,
  FilterHelperArgsOpts,
  SortHelperArgsOpts,
  LimitHelperArgsOpts
} from 'graphql-compose-mongoose/lib/resolvers/helpers'
import type { ExtendedResolveParams } from 'graphql-compose-mongoose/lib/resolvers/index'
import {
  beforeQueryHelper,
  beforeQueryHelperLean
} from 'graphql-compose-mongoose/lib/resolvers/helpers/beforeQueryHelper'
import pullAt from 'lodash/pullAt'

export interface FindManyResolverOpts {
  /**
   * Enabling the lean option tells Mongoose to skip instantiating
   * a full Mongoose document and just give you the plain JavaScript objects.
   * Documents are much heavier than vanilla JavaScript objects,
   * because they have a lot of internal state for change tracking.
   * The downside of enabling lean is that lean docs don't have:
   *   Default values
   *   Getters and setters
   *   Virtuals
   * Read more about `lean`: https://mongoosejs.com/docs/tutorials/lean.html
   */
  lean?: boolean
  /** If you want to generate different resolvers you may avoid Type name collision by adding a suffix to type names */
  suffix?: string
  /** Customize input-type for `filter` argument. If `false` then arg will be removed. */
  filter?: FilterHelperArgsOpts | false
  sort?: SortHelperArgsOpts | false
  limit?: LimitHelperArgsOpts | false
  skip?: false
}

type TArgs = {
  filter?: any
  limit?: number
  skip?: number
  sort?: string | string[] | Record<string, any>
}

export function extendedFindMany<TSource = any, TContext = any, TDoc extends Document = any>(
  model: Model<TDoc>,
  tc: ObjectTypeComposer<TDoc, TContext>,
  opts?: FindManyResolverOpts
): Resolver<TSource, TContext, TArgs, TDoc> {
  if (!model || !model.modelName || !model.schema) {
    throw new Error('First arg for Resolver findMany() should be instance of Mongoose Model.')
  }

  if (!tc || tc.constructor.name !== 'ObjectTypeComposer') {
    throw new Error('Second arg for Resolver findMany() should be instance of ObjectTypeComposer.')
  }

  const aliases = prepareNestedAliases(model.schema)
  const aliasesReverse = prepareAliasesReverse(model.schema)

  return tc.schemaComposer.createResolver<TSource, TArgs>({
    type: tc.NonNull.List.NonNull,
    name: 'findMany',
    kind: 'query',
    args: {
      ...filterHelperArgs(tc, model, {
        prefix: 'FilterFindMany',
        suffix: `${opts?.suffix || ''}Input`,
        ...opts?.filter
      }),
      ...skipHelperArgs(),
      ...limitHelperArgs({
        ...opts?.limit
      }),
      ...sortHelperArgs(tc, model, {
        sortTypeName: `SortFindMany${tc.getTypeName()}${opts?.suffix || ''}Input`,
        ...opts?.sort
      })
    },
    resolve: (async (resolveParams: ExtendedResolveParams<TDoc>) => {
      resolveParams.query = model.find()
      resolveParams.model = model
      filterHelper(resolveParams, aliases)
      skipHelper(resolveParams)
      limitHelper(resolveParams)
      sortHelper(resolveParams)
      projectionHelper(resolveParams, aliases)

      if (opts?.lean) {
        const result = (await beforeQueryHelperLean(resolveParams)) || []
        return Array.isArray(result) && aliasesReverse
          ? result.map(r => replaceAliases(r, aliasesReverse))
          : stripResults(resolveParams, result)
      } else {
        const result = beforeQueryHelper(resolveParams) || []
        return stripResults(resolveParams, result)
      }
    }) as any
  })
}

/**
 * Used to remove element from the array
 * based on the filter, as it's quite expensive
 * for mongoose to filter the subdocument result
 */
function stripResults(resolveParams, result) {
  const operators = resolveParams.args?.filter?._operators
  if (!operators || !result) return result

  for (const item of result) {
    for (const rootKey of Object.keys(operators)) {
      if (!Array.isArray(item[rootKey])) continue

      const unmetIndexes = item[rootKey].reduce((acc, subItem, index) => {
        if (!isFilterMet(operators[rootKey], subItem)) {
          acc.push(index)
        }
        return acc
      }, [])

      if (unmetIndexes.length > 0) pullAt(item[rootKey], unmetIndexes)
    }
  }

  return result
}

function isFilterMet(filters, value) {
  let notMet = false
  for (const field of Object.keys(filters)) {
    for (const operator of Object.keys(filters[field])) {
      if (operator === 'lte') notMet = value[field] > filters[field][operator]
      if (operator === 'lt') notMet = value[field] >= filters[field][operator]
      if (operator === 'gte') notMet = value[field] < filters[field][operator]
      if (operator === 'gt') notMet = value[field] <= filters[field][operator]
      if (operator === 'in') {
        notMet = Array.isArray(value[field])
          ? !filters[field][operator].some(item => value[field].indexOf(item) > -1)
          : !filters[field][operator].includes(value[field])
      }
      if (operator === 'nin') {
        notMet = Array.isArray(value[field])
          ? filters[field][operator].some(item => value[field].indexOf(item) > -1)
          : filters[field][operator].includes(value[field])
      }
      // TODO: another fields operator
    }
  }
  return !notMet
}
