import {
  PropertyRoomFeeType,
  PropertyRoomOtherFeeChargeTiming,
  PropertyRoomOtherFeeType
} from '../../property/room/room.model'

export const parseRoomFees = row => {
  if (!row.monthly_rent) {
    return {
      errors: [
        {
          colName: 'monthly_rent',
          message: 'Monthly rent fee is missing. Make sure it exists'
        }
      ],
      value: null
    }
  }

  const others = []
  const feeFreeText: string = row.property_other_expenses_en

  // extract any useful information from the free text
  const lockReplacement = feeFreeText.match(/Key exchange fee: ([0-9.]+)/)
  if (lockReplacement) {
    others.push({
      feeType: PropertyRoomOtherFeeType.KeyReplacement,
      chargeTiming: PropertyRoomOtherFeeChargeTiming.MoveIn,
      value: Number.parseInt(lockReplacement[1].replace('.', ''))
    })
  }

  const cleaningFee = feeFreeText.match(/Cleaning fee: ([0-9.]+)/)
  if (cleaningFee) {
    others.push({
      feeType: PropertyRoomOtherFeeType.Cleaning,
      chargeTiming: PropertyRoomOtherFeeChargeTiming.MoveIn,
      value: Number.parseInt(cleaningFee[1].replace('.', ''))
    })
  }

  const communityFee = feeFreeText.match(/Community fee: ([0-9.]+)/)
  if (communityFee) {
    others.push({
      feeType: PropertyRoomOtherFeeType.Community,
      chargeTiming: PropertyRoomOtherFeeChargeTiming.Monthly,
      value: Number.parseInt(communityFee[1].replace('.', ''))
    })
  }

  const emptyFeeCount = 5 - others.length
  for (let i = 0; i < emptyFeeCount; i++) {
    others.push({
      feeType: PropertyRoomOtherFeeType.NA,
      chargeTiming: PropertyRoomOtherFeeChargeTiming.NA,
      value: 0
    })
  }

  return {
    errors: [],
    value: {
      monthly: {
        rentFee: row.monthly_rent,
        maintenanceFee: row.monthly_maintenance
      },
      moveIn: {
        deposit: {
          value: row.months_deposit || row.numeric_deposit || 0,
          feeType: row.numeric_deposit
            ? PropertyRoomFeeType.FixedPrice
            : PropertyRoomFeeType.MonthlyMultiplier
        },
        keyMoney: {
          value: row.months_key || row.numeric_key || 0,
          feeType: row.numeric_key
            ? PropertyRoomFeeType.FixedPrice
            : PropertyRoomFeeType.MonthlyMultiplier
        },
        agencyFee: {
          value: row.months_agency || row.numeric_agency || 0,
          feeType: row.numeric_agency
            ? PropertyRoomFeeType.FixedPrice
            : PropertyRoomFeeType.MonthlyMultiplier
        },
        guarantorFee: {
          value: 1.2,
          feeType: PropertyRoomFeeType.MonthlyMultiplier
        },
        contractRenewalFee: {
          value: row.months_renewal || row.numeric_renewal || 0,
          feeType: row.numeric_renewal
            ? PropertyRoomFeeType.FixedPrice
            : PropertyRoomFeeType.MonthlyMultiplier
        }
      },
      others
    }
  }
}
