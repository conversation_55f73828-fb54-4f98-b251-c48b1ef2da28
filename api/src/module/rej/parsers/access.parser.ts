import range from 'lodash/range'
import { ServiceLineModel, StationModel } from '@app/module/access/access.model'
import { ParsingErrorItem } from '../rej.model'
import { Maybe } from 'graphql/jsutils/Maybe'

interface ParseAccessReturn {
  errors: ParsingErrorItem[]
  value: Maybe<any> // FIXME: use `PropertyAccess` later
}

export const parseAccess = async (row): Promise<ParseAccessReturn> => {
  const result = await Promise.all(range(1, 6).map(getAccess(row)))
  return {
    errors: result.reduce((list, value, index) => {
      if (!value) return list

      const { stationId, lineId } = value
      const listError = list
      if (stationId.includes('__'))
        listError.push({
          colName: `station_${index + 1}`,
          message: `Unidentified keyword of ${stationId.replace('__', '')}`
        })
      if (lineId.includes('__'))
        listError.push({
          colName: `line_${index + 1}`,
          message: `Unidentified keyword of ${lineId.replace('__', '')}`
        })
      return listError
    }, [] as ParsingErrorItem[]),
    value: result.filter(item => item !== null)
  }
}

const lineDictionary = {
  埼玉新都市交通伊奈線: '476',
  東北本線: '134',
  京成成田線: '295',
  京浜急行久里浜線: '326',
  東海道線: '117',
  京浜急行本線: '322',
  相模鉄道いずみの線: '345',
  ['相鉄・ＪＲ直通線']: '344'
}

const getAccess = row => async (index: number) => {
  const stationName = row[`station_name_${index}`]
  const lineName = row[`train_line_name_${index}`]
  if (!stationName || !lineName) return null

  const station = await StationModel.findOne(
    {
      $or: [
        { name: new RegExp(stationName) },
        { name: new RegExp(stationName.replace('ヶ', 'ケ')) }
      ]
    },
    { _id: 1 }
  )

  // Immediately convert it to line code 476 if found
  // ref: https://www.chatwork.com/#!rid198904981-1534748650444443649
  const matchedId = lineDictionary[lineName]
  const line = matchedId
    ? { _id: matchedId }
    : await ServiceLineModel.findOne({ name: new RegExp(lineName) }, { _id: 1 })

  return {
    lineId: line?._id || `__${lineName}`,
    stationId: station?._id || `__${stationName}`,
    distance: {
      bus: Number.parseInt(row[`bus_${index}`] || 0),
      walk: Number.parseInt(row[`walk_${index}`] || 0)
    }
  }
}
