import { model, Model, Schema, Document } from 'mongoose'

export interface ParsingErrorItem {
  colName: string
  message: string
}

export interface ParsingError {
  propertyId: string
  items: ParsingErrorItem[]
}

export interface DatabaseSyncError {
  propertyId: string
  message: string
}

export interface REJImportResultDoc extends Document {
  agencyId: string
  processedRow: number
  importedRow: number
  removedProperty: number
  generalErrors?: string[]
  parsingErrors: ParsingError[]
  databaseErrors: DatabaseSyncError[]
  fileModifiedDate: Date
  fileSize: number
}

const parsingErrorItem = new Schema(
  {
    colName: { type: String, required: true },
    message: { type: String, required: true }
  },
  { _id: false, timestamps: false }
)
const parsingError = new Schema(
  {
    propertyId: { type: String, required: true },
    items: { type: [parsingErrorItem] }
  },
  { _id: false, timestamps: false }
)

const databaseError = new Schema(
  {
    propertyId: { type: String, required: true },
    message: { type: String }
  },
  { _id: false, timestamps: false }
)

const schema = new Schema(
  {
    agencyId: { type: String, required: true },
    processedRow: { type: Number, required: true },
    importedRow: { type: Number, required: true },
    removedProperty: { type: Number, required: true },
    generalErrors: [String],
    parsingErrors: [parsingError],
    databaseErrors: [databaseError],
    fileModifiedDate: { type: Date, required: true },
    fileSize: { type: Number, required: true }
  },
  { timestamps: true }
)

schema.index({ createdAt: 1 })

const REJImportResult = model('REJImportResult', schema)
export default REJImportResult
