import * as fs from 'fs'
import * as path from 'path'
import mongoose from 'mongoose'
import FTPClient from 'ftp'
import winston from 'winston'
import * as csv from 'fast-csv'
import dayjs from 'dayjs'
import get from 'lodash/get'
import set from 'lodash/set'
import unset from 'lodash/unset'
import { authMiddleware as middleware } from '@app/middleware'
import PropertyModel from '../property/property.model'
import { preparePaginationResolver } from 'graphql-compose-pagination'
import { extendedFindMany } from '@app/module/property/properties.resolver'
import {
  EnumPropertyRoomAvailabilityStatus,
  EnumPropertyRoomVisibility
} from '../property/room/room.model'
import { parseAddress } from './parsers/address.parser'
import { parseAccess } from './parsers/access.parser'
import { parseImages } from './parsers/image.parser'
import { parseRoomFees } from './parsers/roomFee.parser'
import {
  parseFacilities,
  parseRoomLayout,
  parseStructure,
  parseWindowDirection,
  parseTags
} from './parsers/property.parser'
import REJImportResult, { ParsingError, DatabaseSyncError } from './rej.model'
import Setting from '../setting/setting.model'
import { composeMongoose } from 'graphql-compose-mongoose'

enum State {
  New = 'new',
  Existing = 'existing',
  Removed = 'removed'
}

const isLocal = process.env.NODE_ENV === 'local'
const csvFilename = isLocal ? 'sample/local.rej.csv' : 'local.rej.csv'

export const importPropertyFromREJ = {
  type: 'JSON',
  args: {
    forceOverwrite: 'Boolean'
  },
  resolve: async (__, args) => {
    const { forceOverwrite = false } = args || { forceOverwrite: false }
    // set leopalace ID for csv import
    const { leopalaceAgencyId } = await Setting.findOne()
    const agencyId = mongoose.Types.ObjectId(leopalaceAgencyId)
    let fileModifiedDate = dayjs('1970').toDate()
    let fileSize = 0

    try {
      if (!isLocal) {
        const ftpResult = await downloadCSVFromFTP()
        const { date, size } = ftpResult
        fileModifiedDate = date
        fileSize = size
        if (!date || !size) return {}
        const shouldContinue = await shouldContinueImport(ftpResult)
        if (!forceOverwrite && !shouldContinue) return {}
      }

      const propertiesMap = await fetchProperties(agencyId)
      const parsingResult = await parseCSV({
        propertiesMap,
        agencyId
      })
      const databaseResult = await syncDatabase(parsingResult.propertiesMap)
      await saveImportResult(agencyId, {
        processedRow: parsingResult.processedRow,
        importedRow: databaseResult.importedRow,
        removedProperty: databaseResult.removedProperty,
        databaseErrors: databaseResult.errors,
        parsingErrors: parsingResult.errors,
        fileModifiedDate,
        fileSize
      })
    } catch (err) {
      await saveImportResult(agencyId, {
        processedRow: 0,
        importedRow: 0,
        removedProperty: 0,
        generalErrors: [err.message],
        databaseErrors: [],
        parsingErrors: [],
        fileModifiedDate,
        fileSize
      })
    }
    return {}
  }
}

interface FTPFileInfo {
  date: Date
  size: number
}

const downloadCSVFromFTP = async (): Promise<FTPFileInfo> => {
  return new Promise<FTPFileInfo>((resolve, reject) => {
    const client = new FTPClient()
    client.on('ready', () => {
      client.list((err, list) => {
        if (err) {
          winston.error(err)
          reject(err)
          return
        }
        const csvFileName = 'rej.csv'
        const fileInfo = list.reduce(
          (prev, item) => {
            return item.name === csvFileName ? item : prev
          },
          { date: dayjs('1970').toDate(), size: 0 }
        )

        client.get(csvFileName, (err, stream) => {
          if (err) {
            winston.error(err)
            reject(err)
            return
          }
          stream.once('close', () => client.end())
          stream.pipe(fs.createWriteStream(csvFilename))
          resolve(fileInfo)
        })
      })
    })

    client.on('error', err => {
      reject(err)
    })

    client.connect({
      host: process.env.REJ_FTP_HOST,
      user: process.env.REJ_FTP_USER,
      password: process.env.REJ_FTP_PASS
    })
  })
}

/**
 * To avoid re-import same stuff
 */
const shouldContinueImport = async (ftpResult: FTPFileInfo) => {
  const existing = await REJImportResult.findOne({
    fileSize: ftpResult.size,
    fileModifiedDate: ftpResult.date
  })
  return !existing
}

const fetchProperties = async (agencyId: any) => {
  const properties = await PropertyModel.find(
    { agencyId },
    { _id: 1, 'rooms.rejMetadata.propertyId': 1 }
  )
  return properties.reduce(
    (acc, property: any) => ({
      ...acc,
      [get(property, 'rooms[0].rejMetadata.propertyId', '__')]: {
        prev: property,
        current: null,
        state: State.Removed
      }
    }),
    {}
  )
}

interface ParseCSVResult {
  processedRow: number
  propertiesMap: { [key: string]: object }
  errors: ParsingError[]
}

const parseCSV = async (args: { agencyId: any; propertiesMap: any }): Promise<ParseCSVResult> => {
  const { agencyId, propertiesMap } = args
  return await new Promise<ParseCSVResult>(resolve => {
    const errors = []
    const hashIds = Object.assign({}, propertiesMap)

    const stream = fs
      .createReadStream(path.join(csvFilename))
      .pipe(csv.parse({ headers: true }))
      .on('error', winston.error)
      .on('data', async row => {
        stream.pause()

        if (!row.featured) {
          row.featured = ""
        }

        // Saitama, Kanagawa, Chiba
        const allowedPrefectures = ['埼玉県', '神奈川県', '千葉県']
        if (!allowedPrefectures.includes(row.prefecture)) {
          stream.resume()
          return
        }

        const rowError: ParsingError = {
          propertyId: row.property_csv_id,
          items: []
        }

        const address = await parseAddress(row)
        if (address.errors.length > 0) rowError.items.push(...address.errors)

        const access = await parseAccess(row)
        if (access.errors.length > 0) rowError.items.push(...access.errors)

        const roomLayout = parseRoomLayout(row.room_type)
        if (roomLayout.error) rowError.items.push(roomLayout.error)

        const roomFees = parseRoomFees(row)
        if (roomFees.errors.length > 0) rowError.items.push(...roomFees.errors)

        const structure = parseStructure(row.structure)
        if (structure.error) rowError.items.push(structure.error)

        const facilities = parseFacilities(row)

        const tags = parseTags(row)

        const { errors: imageErrors, value: images } = parseImages(row)
        if (imageErrors.length > 0) rowError.items.push(...imageErrors)
        if (rowError.items.length > 0) {
          errors.push(rowError)
          stream.resume()
          return
        }

        const availabilityDate = dayjs(row.available_from)
        const isAvailableNow = availabilityDate.isBefore(dayjs())

        const building = {
          agencyId,
          address: address.value,
          access: { stations: access.value },
          buildingName: row.building_name_ja,
          buildingType: 'apartment',
          structure: structure.value,
          maxFloor: Number.parseInt(row.floors),
          builtDate: dayjs(row.year).toDate(),
          images: { collections: images.building },
          rejMetadata: {
            buildingId: row.building_csv_id
          },
          rooms: [
            {
              rejMetadata: {
                propertyId: row.property_csv_id
              },
              roomName: row.unit_no,
              contractType: 'standard',
              contractLength: 12,
              transactionType: 'owner',
              whenAvailable: row.available_from,
              images: {
                floorPlan: images.floorPlan,
                collections: images.room.slice(1),
                featured: images.room[0]
              },
              fees: roomFees.value,
              floor: row.floor_no || '1',
              size: Number.parseFloat(row.size),
              visibility: EnumPropertyRoomVisibility.Public,
              numberOfRoom: roomLayout.value.numberOfRoom,
              layout: roomLayout.value.layout,
              facilities,
              tags,
              availability: {
                now: isAvailableNow,
                enquire: !isAvailableNow,
                status: isAvailableNow
                  ? EnumPropertyRoomAvailabilityStatus.Available
                  : EnumPropertyRoomAvailabilityStatus.AvailableSoon
              },
              mainWindow: parseWindowDirection(row)
            }
          ]
        }

        const isExisting = !!hashIds[row.property_csv_id]
        hashIds[row.property_csv_id] = {
          current: building,
          state: isExisting ? State.Existing : State.New
        }
        stream.resume()
      })
      .on('end', (processedRow: number) => {
        resolve({
          processedRow,
          propertiesMap: hashIds,
          errors
        })
      })
  })
}

const syncDatabase = async (propertyMap: any) => {
  // do bulk here, only insert and update
  const errors = []
  const keys = Object.keys(propertyMap)
  let importedRow = 0
  let removedProperty = 0

  // No bulk, we need to do the operation individually,
  // otherwise the hooks which sync the data is not executed
  for (let i = 0; i < keys.length; i++) {
    const id = keys[i]
    const { prev, current: item, state } = propertyMap[id]

    if (state === State.Removed) {
      const propertyId = get(prev, 'rooms[0].rejMetadata.propertyId', null)
      if (propertyId === null) continue
      const filter = { 'rooms.rejMetadata.propertyId': propertyId }
      // eslint-disable-next-line no-await-in-loop
      await PropertyModel.updateOne(filter, {
        $set: { 'rooms.$.visibility': EnumPropertyRoomVisibility.Private }
      })
      removedProperty++
      continue
    }

    const { propertyId } = item.rooms[0].rejMetadata
    const document = {
      ...Object.keys(item).reduce((acc, key) => {
        if (key === 'rooms') return acc
        return {
          ...acc,
          [key]: item[key]
        }
      }, {}),
      // for rooms
      ...Object.keys(item.rooms[0]).reduce(
        (acc, key) => ({
          ...acc,
          [`rooms.$.${key}`]: item.rooms[0][key]
        }),
        {}
      )
    }

    try {
      if (state === State.Existing) {
        const filter = { 'rooms.rejMetadata.propertyId': propertyId }
        const update = { $set: document }

        // Decide either add or remove `furnished` tag
        const tagKey = 'rooms.$.tags'
        if (document[tagKey].length === 0) {
          set(update, `$pullAll['${tagKey}']`, ['furnished'])
        } else {
          set(update, `$addToSet['${tagKey}']`, 'furnished')
        }

        unset(update, `$set['${tagKey}']`)

        // eslint-disable-next-line no-await-in-loop
        await PropertyModel.updateOne(filter, update)
        importedRow++
      }

      if (state === State.New) {
        // eslint-disable-next-line no-await-in-loop
        await PropertyModel.create(item)
        importedRow++
      }
    } catch (err) {
      winston.error(err)
      errors.push({
        propertyId,
        message: err.message
      })
    }
  }

  return {
    errors,
    removedProperty,
    importedRow
  }
}

interface SaveImportResult {
  generalErrors?: string[]
  databaseErrors: DatabaseSyncError[]
  parsingErrors: ParsingError[]
  processedRow: number
  importedRow: number
  removedProperty: number
  fileModifiedDate: Date
  fileSize: number
}

const saveImportResult = async (agencyId: any, result: SaveImportResult) => {
  await REJImportResult.create({
    agencyId,
    ...result
  })
}

export const REJImportResultTC = composeMongoose(REJImportResult, {
  name: 'REJImportResult',
  description: 'The result log of the rej import'
})

export const rejImportResults = preparePaginationResolver(REJImportResultTC, {
  findManyResolver: extendedFindMany(REJImportResult, REJImportResultTC, {
    lean: true,
    filter: { operators: true } // FIXME: performance?
  }),
  countResolver: REJImportResultTC.mongooseResolvers.count(),
  name: 'pagination', // Default
  perPage: 20 // Default
}).withMiddlewares([middleware.isAuth])

export const rejImportResultById = REJImportResultTC.mongooseResolvers.findById()
