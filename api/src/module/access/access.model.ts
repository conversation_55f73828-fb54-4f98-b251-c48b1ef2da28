import mongoose from '@app/mongoose'
import { Schema, Document } from 'mongoose'

export interface ServiceLine extends Document {
  _id: string
  name: string
  en: string
  zh_cn: string
  zh_tw: string
  ko_kr: string
  stationIds: string[]
  sort?: number
}

const ServiceLineSchema: Schema = new Schema(
  {
    _id: { type: String, unique: true, required: true, index: true },
    name: { type: String, required: true },
    en: { type: String },
    zh_cn: { type: String },
    zh_tw: { type: String },
    ko_kr: { type: String },
    stationIds: { type: [String] },
    sort: { type: Number, index: true }
  },
  { _id: false, timestamps: false, collection: 'serviceLines' }
)

export const ServiceLineModel = mongoose.model<ServiceLine>('ServiceLine', ServiceLineSchema)

export interface Station extends Document {
  _id: string
  name: string
  en: string
  zh_cn: string
  zh_tw: string
  ko_kr: string
  location: {
    lat: number
    long: number
  }
  prefId: string
  lineIds: string[]
}

const StationSchema: Schema = new Schema(
  {
    _id: { type: String, unique: true, required: true, index: true },
    name: { type: String, required: true },
    en: { type: String },
    zh_cn: { type: String },
    zh_tw: { type: String },
    ko_kr: { type: String },
    location: {
      lat: Number,
      long: Number
    },
    prefId: { type: String, required: true, index: true },
    lineIds: { type: [String] }
  },
  { _id: false, timestamps: false }
)

export const StationModel = mongoose.model<Station>('Station', StationSchema)
