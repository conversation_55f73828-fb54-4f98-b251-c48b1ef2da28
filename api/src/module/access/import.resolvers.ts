import { schemaComposer } from 'graphql-compose'
import * as csv from 'fast-csv'
import * as fs from 'fs'
import * as path from 'path'
import omit from 'lodash/omit'
import { ServiceLineModel, StationModel } from './access.model'

export const NewAccess = schemaComposer.createObjectTC({
  name: 'NewAccess'
})

interface CommonItem {
  _id: string
  name: string
  en: string
  zh_cn: string
  zh_tw: string
  ko_kr: string
}

interface StationItem extends CommonItem {
  prefId: string
  lineIds: { [key: string]: number }
}

interface LineItem extends CommonItem {
  sort: number
  stationIds: { [key: string]: number }
}

interface Result {
  stations: { [key: string]: StationItem }
  lines: { [key: string]: LineItem }
}

NewAccess.addResolver({
  kind: 'mutation',
  name: 'syncTrainLineStations',
  type: 'JSON',
  resolve: async ({ args, context }) => {
    const items = await new Promise<Result>(resolve => {
      const stations = {}
      const lines = {}
      fs.createReadStream(path.join('./sample/station.csv'))
        .pipe(csv.parse({ headers: true }))
        .on('error', error => console.error(error))
        .on('data', row => {
          if (!stations[row.station_code]) {
            stations[row.station_code] = {
              _id: row.station_code,
              name: row.station_jp,
              en: row.station_en,
              zh_cn: row.station_zh_cn,
              zh_tw: row.station_zh_tw,
              ko_kr: row.station_ko_kr,
              location: {
                lat: row.station_lat,
                long: row.station_lon
              },
              prefId: row.prefecture_code,
              lineIds: {}
            }
          }
          stations[row.station_code].lineIds[row.line_code] = 1

          if (!lines[row.line_code]) {
            lines[row.line_code] = {
              _id: row.line_code,
              name: row.line_jp,
              en: row.line_en,
              zh_cn: row.line_zh_cn,
              zh_tw: row.line_zh_tw,
              ko_kr: row.line_ko_kr,
              sort: row.sort_order || 999,
              stationIds: {}
            }
          }
          lines[row.line_code].stationIds[row.station_code] = 1
        })
        // eslint-disable-next-line no-unused-vars
        .on('end', (__rowCount: number) => {
          resolve({ lines, stations })
        })
    })

    const stationResult = await StationModel.collection.bulkWrite(
      Object.values(items.stations).map(station => ({
        updateOne: {
          filter: { _id: station._id },
          update: {
            $setOnInsert: omit(station, 'lineIds'),
            $addToSet: {
              lineIds: { $each: Object.keys(station.lineIds) }
            } as any
          },
          upsert: true
        }
      }))
    )

    const lineResult = await ServiceLineModel.collection.bulkWrite(
      Object.values(items.lines).map(line => ({
        updateOne: {
          filter: { _id: line._id },
          update: {
            $setOnInsert: omit(line, 'stationIds'),
            $addToSet: {
              stationIds: { $each: Object.keys(line.stationIds) }
            } as any
          },

          upsert: true
        }
      }))
    )

    return { items, lineResult, stationResult }
  }
})
