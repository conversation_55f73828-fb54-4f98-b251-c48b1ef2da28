import { ServiceLineTC, StationTC } from './access.resolvers'
import { NewAccess } from './import.resolvers'
// import * as <PERSON><PERSON><PERSON>olvers from './job.resolvers'

const isLocal = process.env.NODE_ENV === 'local'

export default {
  queries: {
    serviceLines: ServiceLineTC.mongooseResolvers.findMany({ lean: true }),
    stations: StationTC.mongooseResolvers.findMany({ lean: true }),
    stationById: StationTC.mongooseResolvers.findById({ lean: true })
  },
  mutations: {
    ...(isLocal ? { syncTrainLineStations: NewAccess.getResolver('syncTrainLineStations') } : {}),
    // startPopulateBlankAccess: JobResolvers.startPopulateBlankAccess
  }
}
