import agenda from '@app/agenda'
import jobNames from '@app/jobs/name'
import Property from '@app/module/property/property.model'

export const startPopulateBlankAccess = {
  type: 'JSON',
  args: {},
  resolve: async () => {
    const document: any = await Property.find()
    for (let x = 0; x < document.length; x += 1) {
      agenda.now(jobNames.populateAccess, {
        propertyId: document[x]._id,
        stations: document[x].access.stations
      })
    }
  }
}
