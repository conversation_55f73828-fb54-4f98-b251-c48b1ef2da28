import mongoose from '@app/mongoose'
import { Schema, Document } from 'mongoose'

export interface Area extends Document {
  _id: string
  name: string
  // TODO: remove this field, after checking the admin usage
  en: string
  postalCode: string
  items: Area[]
}

const AreaSchema: Schema = new Schema(
  {
    _id: { type: String, unique: true, required: true, index: true },
    name: { type: String, required: true },
    // TODO: remove this field, after checking the admin usage
    en: { type: String },
    postalCode: { type: String }
  },
  { _id: false, timestamps: true }
)

AreaSchema.add({ items: [AreaSchema] })
const AreaModel = mongoose.model<Area>('Area', AreaSchema)

export default AreaModel
