import mongoose from '@app/mongoose'
import { Schema } from 'mongoose'
import { BaseAddress, addressDef, addressOptions } from './common.model'

export interface Cho<PERSON> extends BaseAddress {
  prefId: String
  cityId: String
  extraName: String
  postalCode: String
  coord: number[]
}

const ChomeSchema: Schema = new Schema(
  {
    ...addressDef,
    prefId: String,
    cityId: String,
    extraName: String,
    postalCode: String,
    coord: [Number]
  },
  addressOptions
)

const ChomeModel = mongoose.model<Chome>('Chome', ChomeSchema)

export default ChomeModel
