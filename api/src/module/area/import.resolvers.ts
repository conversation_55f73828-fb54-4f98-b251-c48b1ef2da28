import { schemaComposer } from 'graphql-compose'
import * as csv from 'fast-csv'
import * as fs from 'fs'
import * as path from 'path'
import startCase from 'lodash/startCase'
import toLower from 'lodash/toLower'
import values from 'lodash/values'
import range from 'lodash/range'
import get from 'lodash/get'
import PrefectureModel from './prefecture.model'
import CityModel from './city.model'
import ChomeModel from './chome.model'
import Property from '../property/property.model'
import AreaModel from './area.model'
import mongoose from 'mongoose'
import { getLocaleField } from '@app/utils'

export const NewAddress = schemaComposer.createObjectTC({
  name: 'NewAddress'
})

NewAddress.addResolver({
  kind: 'mutation',
  name: 'importAddress',
  type: 'JSON',
  resolve: async ({ args, context }) => {
    const notCompatibles = []
    const chomes = {}

    await new Promise(resolve => {
      fs.createReadStream(path.join('./sample/postal.csv'))
        .pipe(csv.parse({ headers: true }))
        .on('error', error => console.error(error))
        .on('data', async row => {
          const prefecture = row['prefecture_en']
          const city = row['city_en']
          const [captured, start_str, end_str] = row['chome_en'].match(/\((\d+)-(\d+)-CHOME\)/) || [
            0,
            0,
            0
          ]
          const start = parseInt(start_str)
          const end = parseInt(end_str)
          if (start > 0 && end > 0) {
            const chome = row['chome_en'].replace(captured, '')
            const keys = range(end - start, end + 1)
            keys.forEach(key => {
              chomes[`${prefecture}___${city}___${chome} ${key}`] = row['postal']
            })
          } else if (row['chome_en'].length > 1) {
            chomes[`${prefecture}___${city}___${row['chome_en']}`] = row['postal']
          } else {
            notCompatibles.push(`${row['prefecture_en']}__${row['city_en']}__${row['chome_en']}`)
          }
        })
        .on('end', (rowCount: number) => {
          resolve('')
        })
    })

    const prefMap = {} as any
    const cityMap = {} as any
    const chomeMap = {}
    let chomeCounter = 1

    const getPostal = (pref, city, chome) => {
      const try1 = chomes[`${pref}___${city}___${chome}`]
      // console.log(`${pref}___${city}___${chome}`)
      if (try1) return try1

      const try2 = chomes[`${pref}___${city}___${chome.replace(/ \d+$/, '')}`]
      if (try2) return try2

      // const [captured, ch1, num1, ch2, num2] = chome.match(/(\w+) (\d+) (\w+) (\d+)/) || [
      //   null,
      //   null,
      //   null,
      //   null,
      //   null
      // ]
      // if ((chome as string).includes('JONISHI 21')) console.log(chome, captured, ch1)
      // if (captured !== null) {
      //   console.log(`${pref}___${city}___${ch1}${num1}-${ch2} ${num2}`)
      //   const try3 = chomes[`${pref}___${city}___${ch1}${num1}-${ch2} ${num2}`]
      //   if (try3) return try3
      //   // const try4 = chomes[`${pref}___${city}___${ch2}${num2}-${ch1} ${num1}`]
      //   // return try4
      // }

      return ''
    }

    await new Promise(resolve => {
      const stream = fs
        .createReadStream(path.join('./sample/data.csv'))
        .pipe(csv.parse({ headers: true }))
        .on('error', error => console.error(error))
        .on('data', row => {
          const prefCode = row['Prefecture Code'].padStart(2, '0')
          const cityCode = row['City Code'].padStart(5, '0')

          if (!prefMap[prefCode]) {
            const prefectureName = toLower(row['Prefecture (EN)'].replace(/ [a-zA-Z]+$/, ''))
            prefMap[prefCode] = {
              _id: prefCode,
              jp: row['Prefecture(Kanji)'],
              kana: row['Prefecture (Kana)'],
              en: prefectureName.charAt(0).toUpperCase().concat(prefectureName.slice(1)),
              zh_cn: row['Prefecture (ZH)'],
              zh_tw: row['Prefecture (ZH-TW)'],
              ko_kr: row['Prefecture (KR)']
            }
          }

          if (!cityMap[cityCode]) {
            const cityName = toLower(row['City (EN)']).replace(
              /(\w+) (shi|ku|gun|cho|mura|machi|son)/g,
              '$1-$2'
            )
            cityMap[cityCode] = {
              _id: cityCode,
              prefId: prefCode,
              jp: row['City(Kanji)'],
              kana: row['City (Kana)'],
              en: cityName.charAt(0).toUpperCase().concat(cityName.slice(1)),
              zh_cn: row['City (ZH)'],
              zh_tw: row['City (ZH-TW)'],
              ko_kr: row['City (KR)']
            }
          }

          const postalCode = getPostal(row['Prefecture (EN)'], row['City (EN)'], row['Chome (EN)'])
          if (!chomeMap[`${prefCode}${cityCode}`]) chomeMap[`${prefCode}${cityCode}`] = []
          if (!row['Chome(Kanji)'] || row['Chome(Kanji)'] === '')
            console.log(`pref: ${prefCode}, city: ${cityCode}, chome: ${row['Chome (EN)']}`)
          chomeMap[`${prefCode}${cityCode}`].push({
            // _id: `${prefCode}${cityCode}${chomeCode}`,
            prefId: prefCode,
            cityId: cityCode,
            jp: row['Chome(Kanji)'],
            kana: row['Chome (Kana)'],
            en: startCase(toLower(row['Chome (EN)'])),
            zh_cn: row['Chome (ZH)'],
            zh_tw: row['Chome (ZH-TW)'],
            ko_kr: row['Chome (KR)'],
            extraName: row['Extra Name'],
            coord: [row['Longitude'], row['Latitude']],
            postalCode
          })
        })
        .on('end', (rowCount: number) => {
          console.log(rowCount)
          resolve('')
        })
    })

    PrefectureModel.insertMany(values(prefMap))
    CityModel.insertMany(values(cityMap))
    const chomeItems = Object.keys(chomeMap).reduce((acc, chomeId) => {
      let chomeCount = 0
      return [
        ...acc,
        ...chomeMap[chomeId].map(item => {
          chomeCount++
          const chomeCode = chomeCount.toString().padStart(4, '0')
          return { _id: `${item.prefId}${item.cityId}${chomeCode}`, ...item }
        })
      ]
    }, [])
    ChomeModel.insertMany(chomeItems)

    return {
      notCompatibles,
      chomes
    }
  }
})

NewAddress.addResolver({
  kind: 'mutation',
  name: 'syncPropertyNewAddress',
  type: 'JSON',
  resolve: async () => {
    // Looks any old properties only
    const properties = await Property.find(
      { location: { $exists: true } },
      { _id: 1, location: 1, buildingName: 1 }
    )

    const address = []
    let notFoundChome = 0
    for (let i = 0; i < properties.length; i++) {
      const {
        chome,
        area,
        cityArea,
        prefecture,
        region,
        city,
        position,
        postalCode,
        addressLine
      } = (properties[i] as any).location

      // eslint-disable-next-line no-await-in-loop
      const [selected] = await AreaModel.aggregate([
        { $match: { _id: region } },
        { $unwind: '$items' },
        { $match: { 'items._id': prefecture } },
        { $unwind: '$items.items' },
        { $match: { 'items.items._id': cityArea } },
        { $unwind: '$items.items.items' },
        { $match: { 'items.items.items._id': city } },
        { $unwind: '$items.items.items.items' },
        { $match: { 'items.items.items.items._id': area } },
        { $unwind: '$items.items.items.items.items' },
        { $match: { 'items.items.items.items.items._id': chome } }
      ])

      const field = 'en'
      const detailed = {
        city: get(selected, `items.items.items.${field}`, '[missing]'),
        area: get(selected, `items.items.items.items.${field}`, '[missing]'),
        areaJp: get(selected, 'items.items.items.items.name', '[missing]'),
        chome: get(selected, `items.items.items.items.items.${field}`, '[missing]'),
        chomeJp: get(selected, 'items.items.items.items.items.name', '[missing]')
      }

      const chomeKeyword =
        detailed.chome === 'None' || detailed.chome === ''
          ? detailed.area
          : `${detailed.area} ${detailed.chome}`

      const chomeInJp = detailed.chomeJp
        .replace('１', '一')
        .replace('２', '二')
        .replace('３', '三')
        .replace('４', '四')
        .replace('５', '五')
        .replace('６', '六')
        .replace('７', '七')
        .replace('８', '八')
        .replace('９', '九')
        .replace('０', '零')
      // .replace('丁目', '')

      const chomeKeywordJp =
        detailed.chomeJp === '未指定' || detailed.chomeJp === ''
          ? detailed.areaJp
          : `${detailed.areaJp}${chomeInJp}`

      const prefectureId = '13' // Tokyo Id?
      // eslint-disable-next-line no-await-in-loop
      const matchedCity = await CityModel.findOne(
        { prefId: prefectureId, en: detailed.city },
        { _id: 1, en: 1 }
      )
      // eslint-disable-next-line no-await-in-loop
      let matchedChome = await ChomeModel.findOne(
        { cityId: matchedCity._id, en: new RegExp(chomeKeyword, 'gi') },
        { _id: 1, en: 1 }
      )

      if (!matchedChome) {
        // eslint-disable-next-line no-await-in-loop
        matchedChome = await ChomeModel.findOne(
          { cityId: matchedCity._id, jp: new RegExp(chomeKeywordJp, 'gi') },
          { _id: 1, en: 1 }
        )
      }

      // eslint-disable-next-line no-await-in-loop
      await Property.updateOne(
        { _id: mongoose.Types.ObjectId(properties[i]._id) },
        {
          $set: {
            address: {
              coord: position?.long && position?.lat ? [position.long, position.lat] : [],
              line: addressLine,
              postalCode,
              prefecture: prefectureId,
              city: matchedCity?._id,
              chome: matchedChome?._id
            }
          }
        }
      )

      if (!matchedChome) {
        notFoundChome++
        address.push({
          index: notFoundChome,
          building_id: properties[i]._id,
          building_name: (properties[i] as any).buildingName,
          city_Id: matchedCity._id,
          city_name: matchedCity.en,
          chome_keyword_jp: chomeKeywordJp,
          chome_keyword_en: chomeKeyword
          // prefectureId,
          // formerChome: detailed.chome,
          // chomeKeyword,
        })
      }
    }

    return address
  }
})
