import { composeMongoose } from 'graphql-compose-mongoose'
import get from 'lodash/get'
import AreaModel, { Area } from './area.model'
import PropertyModel from '@app/module/property/property.model'
import { getLocaleField } from '@app/utils'
import PrefectureModel from './prefecture.model'
import CityModel from './city.model'
import ChomeModel from './chome.model'

export const AreaTC = composeMongoose(AreaModel, {
  name: 'Area',
  description: 'Structured location information for a property'
})

AreaTC.deprecateFields({
  en: 'Will be removed in favor of `name` field and header `accept-language`'
})

AreaTC.addFields({
  propertyCounts: {
    type: 'Int',
    description: 'Determine the total number of property on the area',
    async resolve(source: Area) {
      const areas = source._id.replace(/\d+/g, '').split('_')
      // console.log(areas)
      const areaCode = areas.pop() // last element indicate the closest entity
      const fields = {
        c: 'city',
        r: 'region',
        p: 'prefecture',
        ca: 'cityArea',
        a: 'area',
        d: 'chome'
      }
      const fieldName = `location.${fields[areaCode]}`
      return await PropertyModel.count({ [fieldName]: source._id }).lean()
    }
  }
})

const getAreaTokyo = async () => {
  const kantoId = 'r3'
  const tokyoId = 'r3__p13'
  const areas = await AreaModel.find({ _id: kantoId, ['items._id']: tokyoId })
  return areas[0].items.find(area => area._id === tokyoId)
}

AreaTC.addResolver({
  kind: 'query',
  name: 'availableLocations',
  type: '[Area]',
  // NOTE: if the data is change, the id is likely change too
  // We're currently only serving from tokyo, so keep it in mind
  resolve: async () => {
    const { items } = await getAreaTokyo()
    return items
  }
})

AreaTC.addResolver({
  kind: 'query',
  name: 'searchLocations',
  type: '[Area]',
  // This query designed for web project
  // As it's only need city without chome etc
  resolve: async ({ context }) => {
    const kantoId = 'r3'
    const tokyoId = 'r3__p13'
    const locale = getLocaleField(context) as string
    const [selected] = await AreaModel.aggregate([
      { $unwind: '$items' },
      {
        $match: {
          _id: kantoId,
          'items._id': tokyoId
        }
      },
      { $replaceRoot: { newRoot: { areas: '$items.items' } } },
      { $project: { 'areas.items.items': 0 } },
      {
        $project: {
          'areas._id': 1,
          'areas.items._id': 1,
          [`areas.${locale}`]: 1,
          [`areas.items.${locale}`]: 1
        }
      }
    ])

    // Rename the locale key into name
    // example: { zh_tw: 'sing' } to { name: 'sing' }
    // TODO: find the aggregation pipeline equivalent
    // becase $map operator is not supported on documentdb
    const renameKey = (item: any) => {
      const base = { _id: item._id, name: item[locale] } as any
      if (item.items) {
        base.items = item.items.map(renameKey)
      }
      return base
    }
    return get(selected, 'areas', []).map(renameKey)
  }
})

AreaTC.addResolver({
  kind: 'query',
  name: 'cityNameById',
  type: 'String',
  args: {
    id: 'String!'
  },
  resolve: async ({ args, context }) => {
    const { id } = args
    const ids = id.split('__').reduce((acc, code) => {
      if (acc.length === 0) return [code]
      const last = acc[acc.length - 1]
      return [...acc, `${last}__${code}`]
    }, [])
    const [region, prefecture, cityArea, city] = ids
    const locale = getLocaleField(context)

    const [selected] = await AreaModel.aggregate([
      { $match: { _id: region } },
      { $unwind: '$items' },
      { $match: { 'items._id': prefecture } },
      { $unwind: '$items.items' },
      { $match: { 'items.items._id': cityArea } },
      { $unwind: '$items.items.items' },
      { $match: { 'items.items.items._id': city } },
      { $replaceRoot: { newRoot: '$items.items.items' } },
      { $project: { [locale]: 1 } }
    ])

    return get(selected, locale, '[missing]')
  }
})

const getPropertyCountBy = (fieldName: string) => ({
  type: 'Int',
  description: 'Determine the total number of property on the area',
  async resolve(source: any) {
    return await PropertyModel.count({ [fieldName]: source._id }).lean()
  }
})

export const PrefectureTC = composeMongoose(PrefectureModel, {
  name: 'Prefecture',
  description: 'First level juridiction of Japan'
})
export const prefectures = PrefectureTC.mongooseResolvers.findMany({ lean: true })

PrefectureTC.addFields({
  propertyCounts: getPropertyCountBy('address.prefecture')
})

PrefectureTC.addResolver({
  kind: 'query',
  name: 'prefectureByName',
  type: 'Prefecture',
  args: {
    keyword: 'String!'
  },
  resolve: async ({ args }) => {
    const keyword = args.keyword.toLowerCase()
    return await PrefectureModel.findOne({ en: new RegExp(keyword, 'i') })
  }
})

export const CityTC = composeMongoose(CityModel, {
  name: 'City',
  description: 'Second level juridiction of Japan'
})

CityTC.addFields({
  propertyCounts: getPropertyCountBy('address.city')
})

export const cities = CityTC.mongooseResolvers.findMany({ lean: true })
export const cityById = CityTC.mongooseResolvers.findById()

export const ChomeTC = composeMongoose(ChomeModel, {
  name: 'Chome',
  description: 'Last level juridiction of Japan'
})
export const chomes = ChomeTC.mongooseResolvers.findMany({ lean: true })
