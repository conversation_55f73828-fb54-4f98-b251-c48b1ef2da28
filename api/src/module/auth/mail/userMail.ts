import winston from 'winston'

import { mail } from '@app/service/nodemailer'

export const userMail = {
  verifyRequest: (user, token) => {
    mail
      .send({
        template: 'verify-request',
        message: {
          from: '"Verification request" <<EMAIL>>',
          to: user.email,
          subject: 'Verification request'
        },
        locals: {
          locale: user.locale,
          token
        }
      })
      .catch(error => winston.error(error))
  },

  verify: user => {
    mail
      .send({
        template: 'verify',
        message: {
          from: '"Verification" <<EMAIL>>',
          to: user.email,
          subject: 'Verification'
        },
        locals: { locale: user.locale }
      })
      .catch(error => winston.error(error))
  },

  resetPassword: (user, token) => {
    mail
      .send({
        template: 'reset-password',
        message: {
          from: '"Reset Password" <<EMAIL>>',
          to: user.email,
          subject: 'Reset Password'
        },
        locals: {
          locale: user.locale,
          token
        }
      })
      .catch(error => winston.error(error))
  }
}

export default userMail