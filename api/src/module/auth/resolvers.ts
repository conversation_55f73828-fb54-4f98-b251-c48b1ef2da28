import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import crypto from 'crypto-random-string'
import dayjs from 'dayjs'
import fetch from 'node-fetch'
import { nanoid } from 'nanoid'

import redis from '@app/redis'
import { userMail } from '@app/module/auth/mail'
import { userService } from '@app/module/auth/service'
import UserModel from '@app/module/auth/user'

const user = {
  name: 'user',
  type: 'User!',
  resolve: ({ context: { user } }) => user
}

const signIn = {
  name: 'signIn',
  type: 'AccessToken!',
  args: {
    email: 'String!',
    password: 'String!'
  },
  resolve: async ({ args: { email, password } }) => {
    try {
      const user = await (UserModel as any).emailExist(email)
      if (!user) {
        return Promise.reject(new Error('User not found.'))
      }

      const comparePassword = await user.comparePassword(password)
      if (!comparePassword) {
        return Promise.reject(new Error('Password is incorrect.'))
      }
      const payload = {
        sub: user._id,
        roles: user.roles,
        agencyId: user.agencyId
      }
      const accessToken = jwt.sign(payload, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRATION
      })

      return { accessToken }
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

interface FBResult {
  id: string
  name: string
  email: string
  error?: { message: string }
}

const signInWithFB = {
  name: 'signInWithFB',
  type: 'AccessToken!',
  args: {
    token: 'String!'
  },
  resolve: async ({ args: { token }, context }) => {
    // 1. Call FB API to obtain email and userId
    const baseUrl = 'https://graph.facebook.com/v4.0/me'
    const param = new URLSearchParams({
      fields: 'id,name,email',
      access_token: token
    })
    const response = await fetch(`${baseUrl}?${param.toString()}`)
    const result: FBResult = await response.json()

    if (result.error) {
      throw new Error(result.error.message)
    }

    // 2. If it's existing user, return the jwt token
    const existing: any = await UserModel.findOne({ 'account.facebook.userId': result.id })

    if (existing) {
      const payload = {
        sub: existing._id,
        roles: existing.roles,
        agencyId: existing.agencyId
      }

      const accessToken = jwt.sign(payload, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRATION
      })
      return { accessToken }
    }

    // 3. If not, then automatically create an account and then return jwt token
    const source = {
      args: {
        email: result.email,
        password: nanoid(10)
      },
      context
    }
    const signUpResult = await signUp.resolve(source)
    await UserModel.updateOne(
      { email: result.email },
      {
        $set: {
          firstName: result.name,
          'account.facebook.userId': result.id
        }
      }
    )
    return signUpResult
  }
}

interface SignUpSource {
  args: {
    email: string
    password: string
    agencyId?: string
  }
  context: {
    i18n: any
  }
}

const signUp = {
  name: 'signUp',
  type: 'AccessToken!',
  args: {
    email: 'String!',
    password: 'String!',
    agencyId: 'String'
  },
  resolve: async (source: SignUpSource) => {
    const {
      args: { email, password, agencyId },
      context: { i18n }
    } = source

    try {
      let user = await (UserModel as any).emailExist(email)
      if (user) {
        return Promise.reject(new Error('Email has already been taken.'))
      }

      const hash = bcrypt.hashSync(password, 10)

      user = await new UserModel({
        email,
        roles: agencyId ? ['agency-owner'] : [],
        agencyId: agencyId,
        password: hash,
        locale: i18n.language
      }).save()

      const accessToken = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRATION
      })

      const token = await userService.verifyRequest(user)

      userMail.verifyRequest(user, token)

      return { accessToken }
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

const logout = {
  name: 'logout',
  type: 'Succeed!',
  resolve: async ({ context: { user, accessToken } }) => {
    try {
      await redis.set(`expiredToken:${accessToken}`, user._id, 'EX', process.env.REDIS_TOKEN_EXPIRY)

      return { succeed: true }
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

const verifyRequest = {
  name: 'verifyRequest',
  type: 'Succeed!',
  resolve: async ({ context: { user } }) => {
    try {
      const token = await userService.verifyRequest(user)

      userMail.verifyRequest(user, token)

      return { succeed: true }
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

const verify = {
  name: 'verify',
  type: 'AccessToken!',
  args: { token: 'String!' },
  resolve: async ({ args: { token } }) => {
    try {
      const user = await UserModel.findOne({
        'account.verification.token': token
      })
      if (!user) {
        return Promise.reject(new Error('Access Token is not valid or has expired.'))
      }

      user.set({
        account: {
          verification: {
            verified: true,
            token: null,
            expiresIn: null
          }
        }
      })

      await user.save()

      const accessToken = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRATION
      })

      userMail.verify(user)

      return { accessToken }
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

const resetPassword = {
  name: 'resetPassword',
  type: 'Succeed!',
  args: { email: 'String!' },
  resolve: async ({ args: { email } }) => {
    try {
      const user = await UserModel.findOne({ email })
      if (!user) {
        return Promise.reject(new Error('User not found.'))
      }

      const token = crypto({ length: 48, type: 'url-safe' })
      const expiresIn = dayjs().add(7, 'days')

      user.set({
        account: {
          resetPassword: {
            token,
            expiresIn
          }
        }
      })

      await user.save()

      userMail.resetPassword(user, token)

      return { succeed: true }
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

const newPassword = {
  name: 'newPassword',
  type: 'AccessToken!',
  args: { token: 'String!', newPassword: 'String!' },
  resolve: async ({ args: { token, newPassword } }) => {
    try {
      const user = await UserModel.findOne({
        'account.resetPassword.token': token
      })
      if (!user) {
        return Promise.reject(new Error('Access Token is not valid or has expired.'))
      }

      const hash = bcrypt.hashSync(newPassword, 10)

      user.set({
        password: hash,
        account: {
          resetPassword: {
            token: null,
            expiresIn: null
          }
        }
      })

      await user.save()

      const accessToken = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRATION
      })

      return { accessToken }
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

const changePassword = {
  name: 'changePassword',
  type: 'Succeed!',
  args: { currentPassword: 'String!', newPassword: 'String!' },
  resolve: async ({ args: { currentPassword, newPassword }, context: { user } }) => {
    try {
      const comparePassword = await user.comparePassword(currentPassword)
      if (!comparePassword) {
        return Promise.reject(new Error('Current password is incorrect.'))
      }

      const hash = bcrypt.hashSync(newPassword, 10)

      user.set({ password: hash })

      await user.save()

      return { succeed: true }
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

const updateUser = {
  name: 'updateUser',
  type: 'User!',
  args: { email: 'String!', firstName: 'String!', lastName: 'String!' },
  resolve: async ({ args: { email, firstName, lastName }, context: { user } }) => {
    try {
      let {
          account: {
            verification: { verified }
          }
        } = user,
        verifyRequest = false

      if (user.email !== email) {
        const userExist = await UserModel.findOne({ email })
        if (userExist) {
          return Promise.reject(new Error('Email has already been taken.'))
        }
        verified = false
        verifyRequest = true
      }

      user.set({
        email,
        firstName,
        lastName,
        account: {
          verification: {
            verified
          }
        }
      })

      await user.save()

      if (verifyRequest) {
        const token = await userService.verifyRequest(user)

        userMail.verifyRequest(user, token)
      }

      return user
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

const switchLocale = {
  name: 'switchLocale',
  type: 'User!',
  args: { locale: 'Locale!' },
  resolve: async ({ args: { locale }, context: { user } }) => {
    try {
      user.set({ locale })

      await user.save()

      return user
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

export {
  user,
  signIn,
  signInWithFB,
  signUp,
  logout,
  verifyRequest,
  verify,
  resetPassword,
  newPassword,
  changePassword,
  updateUser,
  switchLocale
}
