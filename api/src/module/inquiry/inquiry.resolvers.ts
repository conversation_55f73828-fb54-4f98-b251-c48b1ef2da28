import { composeMongoose } from 'graphql-compose-mongoose'
import Inquiry from './inquiry.model'
import { PropertyTC } from '../property/property.schema'
import { AffiliateTC } from '../affiliate/affiliate.schema'
import { preparePaginationResolver } from 'graphql-compose-pagination'
import { extendedFindMany } from '@app/module/property/properties.resolver'
import { authMiddleware as middleware } from '@app/middleware'

export const InquiryTC = composeMongoose(Inquiry, {
  name: 'Inquiry',
  description: 'The entity which can be rented'
})

InquiryTC.addRelation('property', {
  resolver: () => PropertyTC.mongooseResolvers.findById(),
  prepareArgs: {
    _id: source => (source as any).propertyId
  },
  projection: { propertyId: 1 }
})

InquiryTC.addRelation('affiliateDetail', {
  resolver: () => AffiliateTC.mongooseResolvers.findOne(),
  prepareArgs: {
    filter: source => ({
      code: (source as any).affiliate.code
    })
  },
  projection: { code: 1, name: 1 }
})

const { mongooseResolvers: resolver } = InquiryTC
export const inquiryById = resolver.findById()

export const inquiries = preparePaginationResolver(InquiryTC, {
  findManyResolver: extendedFindMany(Inquiry, InquiryTC, {
    lean: true,
    filter: { operators: true } // FIXME: performance?
  }),
  countResolver: InquiryTC.mongooseResolvers.count(),
  name: 'pagination', // Default
  perPage: 20 // Default
}).withMiddlewares([middleware.isAuth])

export const inquiryCount = resolver.count().withMiddlewares([middleware.isAuth])
export const createInquiry = resolver.createOne({
  record: {
    removeFields: ['_id', 'createdAt', 'updatedAt', 'monthlyFee', 'serviceFee', 'agencyId'],
    isRequired: true
  }
})

// FIXME: later setup the auth middleware specifically in here as well
// Because it's can be called by everyone
export const setInquiryRead = {
  type: 'Boolean',
  args: {
    inquiryId: 'MongoID!',
    status: 'Boolean!'
  },
  resolve: async (__, args) => {
    return !!(await Inquiry.findByIdAndUpdate(args.inquiryId, { unread: !args.status }))
  }
}
