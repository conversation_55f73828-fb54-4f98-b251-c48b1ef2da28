import mongoose from '@app/mongoose'
import { Document } from 'mongoose'
const { Schema } = mongoose
import addSettingHook from './setting.hooks'

interface SettingDoc extends Document {
  featured: string[]
  inHouseAgencyIds: string[]
  leopalaceAgencyId: string
}

const settingSchema = new Schema({
  featured: [String],
  inHouseAgencyIds: [String],
  leopalaceAgencyId: String
})

addSettingHook(settingSchema)

const Setting = mongoose.model<SettingDoc>('Setting', settingSchema)
export default Setting
