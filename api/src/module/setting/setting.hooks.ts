import PropertyModel from '@app/module/property/property.model'

export default function addSettingHook(schema) {
  // eslint-disable-next-line no-unused-vars
  schema.pre('findOneAndUpdate', async function (this: any) {
    const { featured } = this._update
    if (featured.length < 1) throw new Error('featured: at least one item required')

    const result = await PropertyModel.find(
      {
        'rooms.code': {
          $in: [...featured]
        }
      },
      { 'rooms.code': 1 }
    )
    const hash = result.reduce(
      (acc, item: any) => ({
        ...acc,
        ...item?.rooms?.reduce(
          (acc2, room) => ({
            ...acc2,
            [room.code]: true
          }),
          {}
        )
      }),
      {}
    )

    // If any of the field does not exist, throw an error
    featured.some(code => {
      const notFound = !hash[code]
      if (!notFound) return false
      throw new Error(`featured: property room with code "${code}" does not exist`)
    })
  })
}
