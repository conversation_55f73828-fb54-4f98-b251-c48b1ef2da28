/* eslint-disable no-unused-vars */

import { Schema, Document } from 'mongoose'

export enum SellPropertyType {
  MansionOrApartement = 'mansion_apartment',
  House = 'house',
  ResidentialBuilding = 'residential_building',
  OfficeBuilding = 'office_building',
  Land = 'land',
  Other = 'other'
}

export interface FormSellDoc extends Document {
  propertyType: SellPropertyType
  desiredPrice: string
}

export const FormSellSchema = new Schema(
  {
    nearestStation: {
      type: String,
      required: true
    },
    propertyType: {
      type: String,
      enum: Object.values(SellPropertyType),
      required: true
    },
    desiredPrice: {
      type: String
    }
  },
  {
    timestamps: true,
    _id: false,
    autoIndex: true
  }
)
