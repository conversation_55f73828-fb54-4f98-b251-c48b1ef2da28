/* eslint-disable no-unused-vars */

import range from 'lodash/range'
import { Schema, Document } from 'mongoose'

export enum Occupation {
  CompanyEmployee = 'company_employee',
  PartTimer = 'part_timer',
  Student = 'student',
  Unemployed = 'unemployed',
  Other = 'other'
}

export enum VisaStatus {
  Student = 'student',
  WorkVisa = 'work_visa',
  LongTermResident = 'long_term_resident',
  PermanentResident = 'permanent_resident',
  JapaneseNationality = 'japanese_nationality',
  Other = 'other'
}

export enum WhenToMoveIn {
  ASAP = 'asap',
  In1Or2Weeks = 'in_1_or_2_weeks',
  InAMonth = 'in_a_month',
  In2Month = 'in_2_months',
  OnceFindOne = 'once_find_one',
  Other = 'other'
}

export interface FormRentDoc extends Document {
  occupation: Occupation
  visaStatus: VisaStatus
  rentBudget: string
  floorPlan: string
  size: number
  desiredLocation: string
  stationDistance: string
  whenToMoveIn: WhenToMoveIn
  otherPreference: string[]
}

export const FormRentSchema = new Schema(
  {
    occupation: {
      type: String,
      enum: Object.values(Occupation),
      required: true
    },
    visaStatus: {
      type: String,
      enum: Object.values(VisaStatus)
    },
    rentBudget: {
      type: String
    },
    floorPlan: {
      type: String,
      required: true
    },
    size: {
      type: Number,
      required: true
    },
    desiredLocation: {
      type: String,
      required: true
    },
    stationDistance: {
      type: Number,
      required: true
    },
    whenToMoveIn: {
      type: String,
      enum: Object.values(WhenToMoveIn),
      required: true
    },
    otherPreferences: {
      type: [String],
      enum: [
        'not_1st_floor',
        'separate_bath_toilet',
        'auto_lock_entrance',
        'wash_stand',
        'bicycle_park',
        'pet_friendly',
        'package_delivery_box',
        'elevator',
        'balcony',
        'no_key_money',
        'no_deposit',
        'other'
      ]
    }
  },
  {
    timestamps: true,
    _id: false,
    autoIndex: true
  }
)
