/* eslint-disable no-unused-vars */

import { Schema, Document } from 'mongoose'

export enum BuyPurpose {
  OwnUse = 'own_use',
  Investment = 'investment',
  OwnUseAndInvestment = 'own_use_investment'
}

export enum BuyPropertyType {
  MansionOrApartement = 'mansion_apartment',
  House = 'house',
  ResidentialBuilding = 'residential_building',
  OfficeBuilding = 'office_building',
  Land = 'land',
  Other = 'other'
}

export enum BuyPaymentMethod {
  Cash = 'cash',
  Loan = 'loan',
  Other = 'other'
}

export interface FormBuyDoc extends Document {
  purpose: BuyPurpose
  propertyType: BuyPropertyType
  budget: string
}

export const FormBuySchema = new Schema(
  {
    purpose: {
      type: String,
      enum: Object.values(BuyPurpose),
      required: true
    },
    propertyType: {
      type: String,
      enum: Object.values(BuyPropertyType),
      required: true
    },
    desiredAreas: {
      type: String,
      required: true
    },
    budget: {
      type: String,
      required: true
    },
    paymentMethod: {
      type: String,
      enum: Object.values(BuyPaymentMethod),
      required: true
    },
    timeHorizon: {
      type: String,
      required: true
    }
  },
  {
    timestamps: true,
    _id: false,
    autoIndex: true
  }
)
