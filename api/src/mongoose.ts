import mongoose from 'mongoose'
import { attachDbConnection } from '@app/agenda'
import winston from 'winston'
import fs from 'fs'
import logger from '@app/service/logger'

const isLocal = process.env.NODE_ENV === 'local'

const appendSSLCert = () => ({
  ssl: true,
  sslValidate: false,
  sslCA: [fs.readFileSync('./rds-combined-ca-bundle.pem')]
});

if (!!process.env.MONGOOSE_DEBUG) {
  mongoose.set("debug", (collectionName, method, query, doc) => {
    logger.info(`${collectionName}.${method}`, JSON.stringify(query), doc);
  });
}

mongoose
  //@ts-ignore
  .connect(process.env.DB_CONNECTION_STRING, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    // local 30s, on cloud max 10 minutes
    serverSelectionTimeoutMS: isLocal ? 30000 : 600000,
    //@ts-ignore
    ...(isLocal ? {} : appendSSLCert())
  })
  .catch(winston.error)

mongoose.connection.on('open', () => {
  logger.info('MongoDB connected')
  attachDbConnection(mongoose.connection.db)
})

export default mongoose
