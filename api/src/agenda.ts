import { Agenda } from 'agenda/es'
import dayjs from 'dayjs'
import email from '@app/jobs/email'
import chatwork from '@app/jobs/chatwork'
import rej from '@app/jobs/rej'
import logger from '@app/service/logger'
// import access from '@app/jobs/access'
import jobNames from '@app/jobs/name'

const agenda = new Agenda()

// const getPropertJobName = (job: any) => {
//   if (job.attrs.name === jobNames.populateAccess) {
//     const { propertyId } = job.attrs.data
//     return propertyId
//   } else {
//     return null
//   }
// }

// Register jobs definitions
// email(agenda)
// chatwork(agenda)
// rej(agenda)
// access(agenda)

agenda.on('ready', async () => {
  logger.info('Initializing agenda jobs')
  await agenda.start()
  // remove this once the jobs queue is fixed
  await agenda.cancel({ name: jobNames.populateAccess })
})

agenda.on('start', job => {
  // let propertyIdLog = getPropertJobName(job)
  logger.info(`Job ${job.attrs.name} starting`)
})

agenda.on('complete', job => {
  // let propertyIdLog = getPropertJobName(job)
  logger.info(`Job ${job.attrs.name} finished`)
})

agenda.on('success', job => {
  // let propertyIdLog = getPropertJobName(job)
  logger.info(`Job ${job.attrs.name} success`)
})

agenda.on('fail', (err, job) => {
  // let propertyIdLog = getPropertJobName(job)
  logger.info(`@@@@@@@@@@@@@@@@@@@Job ${job.attrs.name} failed with error: ${err.message}`)
  job.attrs.nextRunAt = dayjs().add(10000, 'milliseconds').toDate() // retry 10 seconds later
  job.save()
})

agenda.on('error', (err, job) => {
  logger.info(`@@@@@@@@@@@@@@@@@@@Job ${job.attrs.name} failed with error: ${err.message}`)
})

const stopAgendaJobs = async () => {
  logger.info('Stopping Agenda Jobs')
  await agenda.stop()
}

process.on('SIGTERM', stopAgendaJobs)
process.on('SIGINT', stopAgendaJobs)

export const attachDbConnection = (mongooseDb: any) => {
  agenda.mongo(mongooseDb)
  agenda.processEvery('30 seconds')
}

export default agenda
