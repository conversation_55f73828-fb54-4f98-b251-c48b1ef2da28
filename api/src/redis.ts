import Redis from 'ioredis'
import winston from 'winston'
import logger from '@app/service/logger'

const client = new Redis({
  host: process.env.REDIS_HOST as string,
  port: (process.env.REDIS_PORT as unknown) as number,
  keyPrefix: process.env.REDIS_PREFIX,
  db: (process.env.REDIS_DBNO as unknown) as number
})

client.on('error', error => {
  logger.error(error)
  client.quit()
})

client.on('connect', () => logger.info('Redis client connected'))

export default client
