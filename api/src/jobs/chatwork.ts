import fetch from 'node-fetch'
import capitalize from 'lodash/capitalize'
import jobNames from './name'

export default agenda => {
  agenda.define(jobNames.notifyInquiryOnChatwork, async job => {
    const { roomId, payload } = job.attrs.data
    const token = process.env.CHATWORK_TOKEN
    if (!token) return

    const url = `https://api.chatwork.com/v2/rooms/${roomId}/messages`

    const body = new URLSearchParams()
    const content = `
[info][title]New inquiry[/title]
Name: ${payload.name}
Email: ${payload.email}
Phone: ${payload.phone}
Message: ${payload.memo}
Requested Property: ${payload.propertyUrl}
[/info]
`
    body.append('body', content)
    // await fetch(url, {
    //   method: 'POST',
    //   headers: {
    //     'X-ChatworkToken': token
    //   },
    //   body
    // })

    const API_SLACK_URL = process.env.SLACK_API_URL
    const SLACK_ROOM_ID = process.env.SLACK_ROOM_ID

    if (!API_SLACK_URL || !SLACK_ROOM_ID) return

    const message =
      '*New inquiry* ``` Name: ' +
      payload.name +
      ' \\n Email: ' +
      payload.email +
      ' \\n Phone: ' +
      payload.phone +
      ' \\n Message: ' +
      payload.memo +
      ' \\n Requested Property: ' +
      payload.propertyUrl +
      '```'
    const request_data = {
      room_id: SLACK_ROOM_ID,
      message: message
    }
    await fetch(API_SLACK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request_data)
    })
  })

  agenda.define(jobNames.notifyFormOnChatwork, async job => {
    const { roomId, payload } = job.attrs.data
    const token = process.env.CHATWORK_TOKEN
    if (!token) return

    const url = `https://api.chatwork.com/v2/rooms/${roomId}/messages`

    const body = new URLSearchParams()
    const content = `
[info][title]Quick Form Response[/title]
Type: ${capitalize(payload.formType)}
Name: ${payload.firstName} ${payload.lastName}
Email: ${payload.email}
Phone: ${payload.phone}
Language: ${payload.preferredLanguage}
Detail: ${process.env.ADMIN_URL}/forms/${payload._id}
[/info]
`
    body.append('body', content)
    // await fetch(url, {
    //   method: 'POST',
    //   headers: {
    //     'X-ChatworkToken': token
    //   },
    //   body
    // })

    const API_SLACK_URL = process.env.SLACK_API_URL
    const SLACK_ROOM_ID = process.env.SLACK_ROOM_ID

    if (!API_SLACK_URL || !SLACK_ROOM_ID) return

    const message =
      '*Quick Form Response* ``` Type: ' +
      capitalize(payload.formType) +
      ' \\n Name: ' +
      payload.firstName +
      ' ' +
      payload.lastName +
      ' \\n Email: ' +
      payload.email +
      ' \\n Phone: ' +
      payload.phone +
      ' \\n Language: ' +
      payload.preferredLanguage +
      ' \\n Detail: ' +
      process.env.ADMIN_URL +
      '/forms/' +
      payload._id +
      '```'
    const request_data = {
      room_id: SLACK_ROOM_ID,
      message: message
    }
    await fetch(API_SLACK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request_data)
    })
  })
}
