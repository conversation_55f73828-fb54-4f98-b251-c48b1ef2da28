import winston, { transports } from 'winston'
import { WinstonTransport as AxiomTransport } from '@axiomhq/winston';


const axiomTransport = new AxiomTransport({
  dataset: 'livingjapan-prod',
  token: 'xaat-0cc73974-8d6d-4736-886c-9abf1e2cfb05',
  orgId: 'ipc-5wji',
});

const {
  format: { combine, timestamp, json, errors }
} = winston


const logger = winston.createLogger({
  exceptionHandlers: [axiomTransport],
  level: 'info',
  format: combine(timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), errors({ stack: true }), json()),
  defaultMeta: { service: 'LivingJapan' },
  transports: [
    new (winston.transports.Console)({ level: 'info' }),
    new winston.transports.File({ filename: process.env.API_LOG_FILENAME || 'api-logs.log' }),
    axiomTransport
  ]
});


winston.add(logger)

export default winston
