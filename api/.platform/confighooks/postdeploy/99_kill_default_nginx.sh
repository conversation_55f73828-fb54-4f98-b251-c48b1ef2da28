#!/bin/bash -xe

# Beanstalk always override our nginx configuration, and i really hate them :-)
# reference:
# - https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/custom-platform-hooks.html
# - https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/platforms-linux-extend.html

echo "Clean up any configuration created by beanstalk post-deployment"
sudo rm -f /etc/nginx/nginx.conf; echo $?
sudo cp /etc/nginx/nginx.pre /etc/nginx/nginx.conf; echo $?

sudo service nginx stop
sudo service nginx start
