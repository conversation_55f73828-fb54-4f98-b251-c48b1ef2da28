{"operationName": "getProperties", "query": "query getProperties($perPage: Int, $page: Int, $filter: FilterFindManyPropertyInput, $sort: SortFindManyPropertyInput) { properties(perPage: $perPage, page: $page, filter: $filter, sort: $sort) { count pageInfo { currentPage perPage itemCount hasNextPage __typename } items { _id buildingName buildingType builtDate maxFloor structure languageComment images { collections __typename } prefecturePath addressDetail { ...BaseAddressDetail __typename } access { ...BaseAccess __typename } rooms { ...SimplifiedRoom __typename } __typename } __typename } } fragment SimplifiedRoom on PropertyRooms { code roomName roomLayout numberOfRoom layout tags size floor images { featured floorPlan collections __typename } visibility fees { monthly { rentFee maintenanceFee __typename } __typename } __typename } fragment BaseAccess on PropertyAccess { stations { lineId stationId lineName stationName distance { bus walk __typename } __typename } __typename } fragment BaseAddressDetail on PropertyAddress { prefecture city chome __typename }", "variables": {"perPage": 10000, "filter": {"_operators": {"rooms": {"visibility": {"in": ["public"]}}}}}}