container_commands:
  10_setup_nginx:
    command: |
      echo "Setup Nginx"
      sudo rm -f /etc/nginx/nginx.conf; echo $?
      sudo cp /etc/nginx/nginx.pre /etc/nginx/nginx.conf; echo $?

      sudo service nginx stop
      sudo service nginx start
  20_install_certbot:
    command: |
      wget -O epel.rpm –nv https://dl.fedoraproject.org/pub/epel/epel-release-latest-7.noarch.rpm
      sudo yum install -y ./epel.rpm
      sudo yum install -y python2-certbot-apache.noarch
  30_create_webroot_path:
    command: |
      sudo rm -rf /var/www/letsencrypt/
      sudo mkdir -p /var/www/letsencrypt/
  40_configure_cert:
    command: |
      certbot_command="certbot certonly --webroot --webroot-path /var/www/letsencrypt --debug --non-interactive --email ${LETSENCRYPT_EMAIL} --agree-tos --expand --keep-until-expiring"
      for domain in $(echo ${LETSENCRYPT_DOMAIN} | sed "s/,/ /g")
      do
        certbot_command="$certbot_command --domains $domain"
      done
      eval $certbot_command
  50_link_cert:
    command: |
      domain="$( cut -d ',' -f 1 <<< "${LETSENCRYPT_DOMAIN}" )";
      if [ -d /etc/letsencrypt/live ]; then
        domain_folder_name="$(ls /etc/letsencrypt/live | sort -n | grep $domain | head -1)";
        if [ -d /etc/letsencrypt/live/${domain_folder_name} ]; then
          ln -sfn /etc/letsencrypt/live/${domain_folder_name} /etc/letsencrypt/live/ebcert
        fi
      fi
  60_enable_https_config:
    command: |
      sudo mv /etc/nginx/conf.custom.d/https_custom.pre /etc/nginx/conf.custom.d/https_custom.conf
      sudo service nginx stop
      sudo service nginx start
