user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /var/run/nginx.pid;

events {
  worker_connections  1024;
}

http {
  port_in_redirect off;
  default_type application/octet-stream;

  log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                  '$status $body_bytes_sent "$http_referer" '
                  '"$http_user_agent" "$http_x_forwarded_for"';

  log_format healthd '$msec"$uri"'
                   '$status"$request_time"$upstream_response_time"'
                   '$http_x_forwarded_for';

  include /etc/nginx/conf.custom.d/*.conf;
  include /etc/nginx/mime.types;

  access_log /var/log/nginx/access.log  main;
  sendfile on;
  keepalive_timeout 65;
}
