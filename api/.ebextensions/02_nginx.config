files:
  "/etc/nginx/nginx.pre":
    mode: "000644"
    owner: root
    group: root
    content: |
      user nginx;
      worker_processes auto;
      error_log /var/log/nginx/error.log;
      pid /var/run/nginx.pid;

      events {
        worker_connections  1024;
      }

      http {
        port_in_redirect off;
        default_type application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        log_format healthd '$msec"$uri"'
                         '$status"$request_time"$upstream_response_time"'
                         '$http_x_forwarded_for';
  
        include /etc/nginx/conf.custom.d/*.conf;
        include /etc/nginx/mime.types;
        
        access_log /var/log/nginx/access.log  main;
        sendfile on;
        keepalive_timeout 65;        
      }

  "/etc/nginx/conf.custom.d/http_custom.conf":
    mode: "000644"
    owner: root
    group: root
    content: |
      server {
        listen 80;

        location ~ /.well-known/  {
          root /var/www/letsencrypt/;
        }

        location / {
          return 301 https://$host$request_uri;
        }
      }

  "/etc/nginx/conf.custom.d/https_custom.pre":
    mode: "000644"
    owner: root
    group: root
    content: |
      server {
        listen 443 ssl default;
        server_name localhost;
        error_page 497 https://$host$request_uri;

        if ($time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})T(\d{2})") {
          set $year $1;
          set $month $2;
          set $day $3;
          set $hour $4;
        }

        access_log /var/log/nginx/healthd/application.log.$year-$month-$day-$hour healthd;
        access_log /var/log/nginx/access.log main;

        ssl_certificate /etc/letsencrypt/live/ebcert/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/ebcert/privkey.pem;

        ssl_session_timeout 5m;
        ssl_protocols TLSv1.1 TLSv1.2;
        ssl_ciphers "EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH";
        ssl_prefer_server_ciphers on;
        
        location / {
            proxy_pass http://localhost:8000;
            proxy_http_version 1.1;

            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

      }
