<%- include('../commons/outlook_start.ejs'); -%>
<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Living Japan Inquiry</title>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    body {
      -webkit-font-smoothing: antialiased;
      font-family: sans-serif;
      font-size: 14px;
      line-height: 100%;
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%;
      padding: 40px;
      background-color: #EDEFF0;
      color: #333;
    }

    .logo {
      margin-bottom: 30px;
    }

    .content {
      background-color: white;
      margin: auto;
      padding: 30px 20px 20px;
      max-width: 600px;
      border-radius: 10px;
    }

    .message {
      line-height: 21px;
      margin-bottom: 30px;
    }

    h3 {
      font-size: 14px;
      margin-bottom: 15px;
      line-height: 18px;
    }

    ul {
      list-style-type: none;
    }

    ul.table-content {
      margin-bottom: 30px;
    }

    li {
      margin-bottom: 10px;
      line-height: 21px;
    }

    .message-content {
      padding-left: 10px;
      border-left: 2px solid #333;
    }

    .bold {
      font-weight: bold;
    }
  </style>
</head>

<body>

  <div class="content">
    <center>
      <img class="logo" src="cid:logo">
    </center>
    <p class="message">
      <%=i18n.getFixedT(locale)('inquiry.confirmMail.hi', { name })%><br /><br />
      <%=i18n.getFixedT(locale)('inquiry.confirmMail.thankYouForRequest')%><br />
      <%=i18n.getFixedT(locale)('inquiry.confirmMail.agentWillGetBack')%>
    </p>
    <div class="message-content">
      <h3>
        <%=i18n.getFixedT(locale)('inquiry.confirmMail.reqInfoLabel')%>
      </h3>
      <ul class="table-content">
        <li><span class="bold">
            <%=i18n.getFixedT(locale)('inquiry.confirmMail.nameLabel')%>
          </span><br />
          <%=name%>
        </li>

        <li>
          <span class="bold">
            <%=i18n.getFixedT(locale)('inquiry.confirmMail.emailLabel')%>
          </span><br />
          <%=email%>
        </li>
        <li>
          <span class="bold">
            <%=i18n.getFixedT(locale)('inquiry.confirmMail.phoneLabel')%>
          </span><br />
          <% if (phone) { %>
          <%=phone%>
          <% } else { %>
          -
          <% } %>
        </li>
        <li>
          <span class="bold">
            <%=i18n.getFixedT(locale)('inquiry.confirmMail.purposeLabel')%>
          </span><br />
          <% for(var i=0; i<purpose.length;i++) { %>
          <%=i18n.getFixedT(locale)(`inquiry.purposes.${purpose[i]}`)%><br />
          <% } %>
        </li>
        <li>
          <span class="bold">
            <%=i18n.getFixedT(locale)('inquiry.confirmMail.languageLabel')%>
          </span><br />
          <%=preferredLanguage%>
        </li>
        <li>
          <span class="bold">
            <%=i18n.getFixedT(locale)('inquiry.confirmMail.whenToMoveIn')%>
          </span><br />
          <%=whenToMoveIn%>
        </li>
        <li>
          <span class="bold">
            <%=i18n.getFixedT(locale)('inquiry.confirmMail.messageLabel')%>
          </span><br />
          <% if (memo) { %>
          <%=memo%>
          <% } else { %>
          -
          <% } %>
        </li>
        <li><span class="bold">
            <%=i18n.getFixedT(locale)('inquiry.confirmMail.reqPropertyLabel')%>
          </span><br />
          <a href="<%=propertyUrl%>">
            <%=propertyUrl%>
          </a>
        </li>
      </ul>
    </div>
    <%- include('../commons/footer.ejs'); -%>
  </div>
</body>

</html>
<%- include('../commons/outlook_end.ejs'); -%>
