import { mail } from '@app/service/nodemailer'
import jobNames from './name'
import { getPropertyNiceName } from '@app/module/property/property.helpers'

export default agenda => {
  agenda.define(jobNames.quickResponse, async job => {
    const { to, userName, roomCode, locale, propertyUrl } = job.attrs.data
    const propertyName = await getPropertyNiceName({ roomCode, locale })
    await mail.send({
      template: `quick-response/${locale}`,
      message: {
        to,
        from: `"Living Japan" <${process.env.SUPPORT_EMAIL}>`,
        cc: process.env.SUPPORT_EMAIL,
        replyTo: process.env.SUPPORT_EMAIL
      },
      locals: {
        locale,
        userName,
        propertyName,
        propertyUrl
      }
    })
  })
}
