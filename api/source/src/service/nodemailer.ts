import path from 'path'
import nodemailer from 'nodemailer'
import Email from 'email-templates'
import i18next from 'i18next'

const transporter = nodemailer.createTransport({
  host: process.env.MAIL_HOST,
  port: process.env.MAIL_PORT,
  auth: {
    user: process.env.MAIL_USER,
    pass: process.env.MAIL_PASSWORD
  }
} as any)

const mail = new Email({
  views: {
    root: path.join('./templates'),
    locals: {
      i18n: i18next,
      clientUrl: process.env.ADMIN_URL
    },
    options: { extension: 'ejs' }
  },
  preview: false,
  send: true,
  transport: transporter
})

export { transporter, mail }
