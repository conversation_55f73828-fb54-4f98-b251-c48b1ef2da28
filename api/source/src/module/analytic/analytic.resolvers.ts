import mongoose from 'mongoose'
import dayjs from 'dayjs'
import set from 'lodash/set'
import setWith from 'lodash/setWith'
import range from 'lodash/range'
import omit from 'lodash/omit'
import { schemaComposer } from 'graphql-compose'
import Inquiry from '../inquiry/inquiry.model'
import Property from '../property/property.model'
import Form from '../form/form.model'
import Setting from '../setting/setting.model'

export const AnalyticForm = schemaComposer.createObjectTC({
  name: 'AnalyticForm',
  fields: {
    unique: 'Int',
    total: 'Int'
  }
})

export const Analytic = schemaComposer.createObjectTC({
  name: 'Analytic',
  fields: {
    uniqueInquiry: 'Int',
    totalInquiry: 'Int',
    totalPageView: 'Int',
    totalServiceFee: 'Int',
    averageInquiredRentFee: 'Int',
    form: AnalyticForm
  }
})

export const FilterAnalyticITC = schemaComposer.createInputTC({
  name: 'FilterAnalyticInput',
  fields: {
    startDate: 'Date!',
    endDate: 'Date!',
    agencyId: 'String',
    agencyIds: '[String]'
  }
})

Analytic.addResolver({
  kind: 'query',
  name: 'analytics',
  type: 'Analytic',
  args: {
    filter: FilterAnalyticITC
  },
  resolve: async ({ args }) => {
    const { filter } = args
    const baseStage = getFirstStage(filter)
    const { inHouseAgencyIds = [] }: any = await Setting.findOne()
    const includeForm = !filter.agencyId || inHouseAgencyIds.includes(filter.agencyId)
    const formStage = omit(baseStage, ['$match.agencyId', '$match.agencyIds'])

    try {
      validateFilter(filter)
      return {
        uniqueInquiry: await getUniqueInquiries(baseStage),
        totalInquiry: await getTotalInquiries(baseStage),
        totalPageView: await getTotalPageView(filter),
        totalServiceFee: await getTotalServiceFee(baseStage),
        averageInquiredRentFee: await getAverageInquiredRentFee(baseStage),
        form: {
          unique: includeForm ? await getUniqueForms(formStage) : 0,
          total: includeForm ? await getTotalForms(formStage) : 0
        }
      }
    } catch (error) {
      return Promise.reject(error)
    }
  }
})

interface AnalyticFilter {
  startDate: string
  endDate: string
  agencyId?: string
  agencyIds?: string[]
}

function validateFilter(filter: AnalyticFilter) {
  const { startDate, endDate, agencyId } = filter

  if (dayjs(endDate).isBefore(startDate)) {
    throw new Error('The endDate filter must be after the startDate')
  }

  if (agencyId && !mongoose.isValidObjectId(agencyId)) {
    throw new Error('The agency id format is invalid')
  }
}

const getFirstStage = (filter: AnalyticFilter) => {
  const baseStage = {
    $match: {
      createdAt: {
        $gte: dayjs(filter.startDate).toDate(),
        $lte: dayjs(filter.endDate).add(1, 'day').toDate()
      }
    }
  }

  if (filter.agencyId) {
    set(baseStage, '$match.agencyId', filter.agencyId)
  }

  if (filter.agencyIds) {
    set(baseStage, '$match.agencyId.$in', filter.agencyIds)
  }
  return baseStage
}

const getUniqueInquiries = async (baseStage: object) => {
  const result = await Inquiry.aggregate([
    baseStage,
    {
      $group: { _id: '$email' }
    },
    { $count: 'count' }
  ])
  return result[0]?.count || 0
}

const getTotalInquiries = async (baseStage: object) => {
  const result = await Inquiry.aggregate([
    baseStage,
    {
      $group: {
        _id: null,
        count: { $sum: 1 }
      }
    }
  ])
  return result[0]?.count || 0
}

const getTotalPageView = async (filter: AnalyticFilter) => {
  const pipelines = [
    {
      $project: {
        totalPageView: {
          $sum: getPageViewFields(filter)
        }
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$totalPageView' }
      }
    }
  ] as any[]

  if (filter.agencyId) {
    pipelines.unshift({
      $match: {
        agencyId: mongoose.Types.ObjectId(filter.agencyId)
      }
    })
  }

  if (filter.agencyIds) {
    const pipeline = setWith(
      {},
      '$match.agencyId.$in',
      filter.agencyIds.map(id => mongoose.Types.ObjectId(id))
    )
    pipelines.unshift(pipeline)
  }

  const result = await Property.aggregate(pipelines)
  return result[0]?.total || 0
}

const getPageViewFields = (filter: AnalyticFilter) => {
  const from = dayjs(filter.startDate)
  const until = dayjs(filter.endDate)
  const fromDay = from.dayOfYear()
  const untilDay = until.dayOfYear()

  const diff = until.year() - from.year() + 1
  const result = [...Array(diff).keys()]
    .map(index => {
      const year = from.year() + index
      const startDay = from.year() === year ? fromDay : 1
      const endDay = until.year() === year ? untilDay : dayjs(`${year}-12-31`).dayOfYear()
      return range(startDay, endDay + 1).map(day => `$pageViews.${year}_d${day}`)
    })
    .flat(1)
  return result
}

const getTotalServiceFee = async (baseStage: object) => {
  const result = await Inquiry.aggregate([
    baseStage,
    {
      $group: {
        _id: null,
        total: { $sum: '$serviceFee' }
      }
    }
  ])
  return Math.floor(result[0]?.total || 0)
}

const getAverageInquiredRentFee = async (baseStage: object) => {
  const result = await Inquiry.aggregate([
    baseStage,
    {
      $group: {
        _id: null,
        avg: { $avg: '$monthlyFee' }
      }
    }
  ])
  return Math.floor(result[0]?.avg || 0)
}
const getUniqueForms = async (baseStage: any) => {
  const result = await Form.aggregate([
    baseStage,
    {
      $group: { _id: '$email' }
    },
    { $count: 'count' }
  ])
  return result[0]?.count || 0
}

const getTotalForms = async (baseStage: any) => {
  const result = await Form.aggregate([
    baseStage,
    {
      $group: {
        _id: null,
        count: { $sum: 1 }
      }
    }
  ])
  return result[0]?.count || 0
}
