import { composeMongoose } from 'graphql-compose-mongoose'
import Affiliate from './affiliate.model'
import { hasAuth } from './affiliate.validators'

export const AffiliateTC = composeMongoose(Affiliate, {
  name: 'Affiliate',
  description: 'An entity reference that helps us promote our website',
  inputType: {
    removeFields: ['_id', 'createdAt', 'updatedAt']
  }
})

const { mongooseResolvers: resolver } = AffiliateTC

export default {
  queries: {
    affiliates: resolver.pagination({ perPage: 2 })
  },
  mutations: {
    createAffiliate: resolver
      .createOne({
        record: {
          isRequired: true,
          requiredFields: ['name', 'code']
        }
      })
      .withMiddlewares([hasAuth])
  }
}
