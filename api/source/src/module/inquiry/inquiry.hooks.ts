import path from 'path'
import dayjs from 'dayjs'
import get from 'lodash/get'
import setWith from 'lodash/setWith'
import winston from '@app/service/logger'
import { mail } from '@app/service/nodemailer'
import jobNames from '@app/jobs/name'
import agenda from '@app/agenda'
import Agency from '../agency/agency.model'
import Property from '../property/property.model'
import Inquiry from './inquiry.model'
import Setting from '@app/module/setting/setting.model'
import { i18next } from '@app/i18next'

export default function addInquiryHooks(schema) {
  /**
   * Validation Hook
   * - Make sure property is valid
   * - Make sure the room code does exist
   * - Save the monthly fee as a record
   * - Applying service fee on top of the monthly rent fee
   */
  schema.pre('validate', async function () {
    const instance = this as any
    const result = await Property.findById(instance.propertyId, { rooms: 1, agencyId: 1 })
    const property = result as any

    if (!property) throw this.invalidate('propertyId', 'Property does not exist')
    instance.agencyId = property.agencyId

    const result2 = await Agency.findById(property.agencyId)
    const agency = result2 as any
    if (!agency) throw this.invalidate('propertyId', 'Agency does not exist, fix the property!')

    const room = property.rooms.find(room => room.code === instance.roomCode)
    if (!room) throw this.invalidate('roomCode', 'Room does not exist')

    // If there is same email who inquiry the same property for the last 30 days
    const { propertyId, roomCode, email } = instance
    const existingInquiry = await Inquiry.findOne(
      {
        propertyId,
        roomCode,
        email,
        createdAt: {
          $gte: dayjs().subtract(30, 'day').toDate()
        }
      },
      { _id: 1 },
      { lean: true }
    )

    const monthlyFee = room.fees?.monthly?.rentFee || 0
    instance.monthlyFee = monthlyFee
    if (existingInquiry) {
      instance.serviceFee = 0
      return
    }

    // Calculate the service fee
    if (agency.serviceFee.feeType === 'percentage') {
      instance.serviceFee = (agency.serviceFee.value / 100) * monthlyFee
    } else {
      instance.serviceFee = agency.serviceFee.value
    }
  })

  /**
   * Created Hook
   * - Send notification email to the agency
   */
  schema.post('save', async function () {
    const instance = this as any
    const result2 = await Agency.findById(instance.agencyId)
    const agency = result2 as any
    if (!agency) throw this.invalidate('propertyId', 'Agency does not exist, fix the property!')

    const { inHouseAgencyIds = [], leopalaceAgencyId } = await Setting.findOne()
    const isLeopalace = instance.agencyId === leopalaceAgencyId
    const locale = this.locale.toLowerCase()
    const webPath = i18next.getFixedT(locale)('inquiry.web.localePath')
    const locals = {
      ...instance,
      locale,
      companyName: agency.basic.companyName,
      name: this.name,
      email: this.email,
      phone: this.phone,
      propertyId: this.propertyId,
      preferredLanguage: this.preferredLanguage
        .map((code: string) => i18next.getFixedT(locale)(`inquiry.availableLanguages.${code}`))
        .join(i18next.getFixedT(locale)('inquiry.comma')),
      // FIXME: Please find a better way later,
      // instead duplicate for the japanese notify inquiry email
      preferredLanguageInJP: this.preferredLanguage
        .map((code: string) => i18next.getFixedT('jp-jp')(`inquiry.availableLanguages.${code}`))
        .join(i18next.getFixedT('jp-jp')('inquiry.comma')),
      purpose: purposeOrders.filter(order => this.purpose.indexOf(order) > -1),
      whenToMoveIn: this.whenToMoveIn,
      memo: this.memo,
      adminUrl: process.env.ADMIN_URL,
      webUrl: process.env.WEB_URL,
      propertyUrl: `${process.env.WEB_URL}/${webPath}rent/tokyo/${this.roomCode}`,
      showFooter: !isLeopalace
    }

    // Increment the property's inquiry count
    try {
      await Property.updateOne(
        { 'rooms.code': this.roomCode },
        { $inc: { 'rooms.$.inquiryCount': 1 } }
      )
    } catch (error) {
      winston.error(error)
    }
    const subject = await getSubjectForAgency({
      isLeopalace,
      locale: instance.locale,
      propertyId: this.propertyId
    })

    mail
      .send({
        template: 'notify-inquiry',
        message: {
          from: '"Living Japan" <<EMAIL>>',
          to: [agency.public.email],
          replyTo: this.email,
          subject,
          attachments: [
            {
              filename: 'logo.png',
              path: path.join('./templates/assets/logo.png'),
              cid: 'logo'
            }
          ]
        },
        locals: {
          ...locals,
          purpose: locals.purpose.map((key: keyof typeof purposeDict) => {
            const selected = purposeDict[key]
            if (!selected) return key
            return `${selected.jp} - ${selected.en}`
          })
        }
      })
      .catch(error => {
        winston.error(error)
      })

    mail
      .send({
        template: 'confirm-inquiry',
        message: {
          from: '"Living Japan" <<EMAIL>>',
          to: [this.email],
          replyTo: agency.public.email,
          attachments: [
            {
              filename: 'logo.png',
              path: path.join('./templates/assets/logo.png'),
              cid: 'logo'
            }
          ]
        },
        locals
      })
      .catch(error => {
        winston.error(error)
      })

    if (!inHouseAgencyIds.includes(this.agencyId)) return

    const baseUrl = process.env.ADMIN_URL
    agenda.now(jobNames.notifyInquiryOnChatwork, {
      roomId: '282253714',
      payload: {
        name: locals.name,
        email: locals.email,
        phone: locals.phone,
        purpose: purposeOrders.filter(order => this.purpose.indexOf(order) > -1),
        whenToMoveIn: locals.whenToMoveIn,
        memo: this.memo,
        propertyUrl: `${baseUrl}/inquiries/${instance._id}`
      }
    })

    const at30DaysAgo = dayjs().subtract(30, 'days').toDate()
    const last30DayJobs = await agenda.jobs({
      name: jobNames.quickResponse,
      'data.to': this.email,
      $or: [
        setWith({}, 'lastFinishedAt.$gte', at30DaysAgo),
        setWith({}, 'nextRunAt.$gte', at30DaysAgo)
      ]
    })
    if (last30DayJobs.length) return

    agenda.schedule('in 12 seconds', jobNames.quickResponse, {
      to: this.email,
      userName: this.name,
      roomCode: this.roomCode,
      locale: locals.locale,
      propertyUrl: locals.propertyUrl
    })
  })
}

const getSubjectForAgency = async (args: {
  isLeopalace: boolean
  locale: string
  propertyId: string
}) => {
  const defaultSubject = '【Living Japan】新着のお問い合わせがありました'
  if (!args.isLeopalace) return defaultSubject
  const result = await Property.findById(args.propertyId, { 'rooms.rejMetadata.propertyId': 1 })
  const propertyId = get(result, 'rooms[0].rejMetadata.propertyId')
  const map = {
    'en-us': `【Living Japan】新着のお問い合わせがありました_英（${propertyId}）`,
    'zh-cn': `【Living Japan】新着のお問い合わせがありました_簡（${propertyId}）`,
    'zh-tw': `【Living Japan】新着のお問い合わせがありました_繁（${propertyId}）`,
    'ko-kr': `【Living Japan】新着のお問い合わせがありました_韓（${propertyId}）`
  }
  return map[args.locale.toLowerCase()] || defaultSubject
}

const purposeOrders = [
  'check_availability',
  'request_tour',
  'visit_office',
  'check_other_property',
  'other'
]

// FIXME: put to i18n later
const purposeDict = {
  check_availability: {
    en: 'I want to know availability',
    jp: '空室状況を確認したい'
  },
  request_tour: {
    en: 'I want to take a room tour',
    jp: '内見をしたい'
  },
  visit_office: {
    en: 'I want to visit your store',
    jp: '店舗に来店して相談したい'
  },
  check_other_property: {
    en: 'I also want to know other property information',
    jp: '他の物件情報も知りたい'
  },
  other: {
    en: 'Other',
    jp: 'その他'
  }
}
