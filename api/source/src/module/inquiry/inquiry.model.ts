import mongoose from '@app/mongoose'
import addInquiryHooks from './inquiry.hooks'
const { Schema } = mongoose

export enum WhenToMoveIn {
  ASAP = 'asap',
  In1Or2Weeks = 'in_1_or_2_weeks',
  InAMonth = 'in_a_month',
  In2Month = 'in_2_months',
  OnceFindOne = 'once_find_one',
  Other = 'other'
}

const inquirySchema = new Schema(
  {
    name: { type: String, required: true },
    phone: { type: String },
    email: {
      type: String,
      required: true,
      validate: (value: string) => {
        return /^([\w-\.]+@([\w-]+\.)+[\w-]{2,4})?$/.test(value)
      }
    },
    locale: {
      type: String,
      required: true,
      // FIXME: should retrieve it from i18n instead of hardcoded
      enum: ['en-US', 'zh-CN', 'zh-TW', 'ko-KR', 'jp-JP', 'es-ES', 'fr-FR']
    },
    memo: String,
    preferredLanguage: {
      type: [String],
      enum: ['en', 'jp', 'zh', 'kr'],
      validate: {
        validator: (values: any) => Array.isArray(values) && values.length > 0,
        message: 'At least one preferred language must present '
      },
      required: true
    },
    purpose: {
      type: [String],
      enum: ['check_availability', 'request_tour', 'visit_office', 'check_other_property', 'other'],
      validate: {
        validator: (values: any) => Array.isArray(values) && values.length > 0,
        message: 'At least one purpose must present '
      },
      required: true
    },
    whenToMoveIn: {
      type: String,
      enum: Object.values(WhenToMoveIn),
      required: true
    },
    unread: { type: Boolean, default: true, index: true },
    agencyId: { type: String, required: true, index: true },
    propertyId: { type: String, required: true },
    roomCode: { type: String, required: true, index: true },
    utm: {
      utm_source: { type: String },
      utm_medium: { type: String },
      utm_campaign: { type: String },
      utm_term: { type: String },
      utm_content: { type: String },
      gclid: { type: String },
      fbclid: { type: String },
    },
    affiliate: {
      code: { type: String },
    },
    monthlyFee: { type: Number, required: true, index: true },
    serviceFee: { type: Number, required: true, index: true }
  },
  { timestamps: true }
)
inquirySchema.index({ createdAt: 1 })
inquirySchema.index({ updatedAt: 1 })

addInquiryHooks(inquirySchema)

const Inquiry = mongoose.model('Inquiry', inquirySchema)
export default Inquiry
