/* eslint-disable no-unused-vars */

import { Schema, Document } from 'mongoose'

export enum TourPurpose {
  OwnUse = 'own_use',
  Investment = 'investment',
  OwnUseAndInvestment = 'own_use_investment'
}

export enum TourPropertyType {
  MansionOrApartement = 'mansion_apartment',
  House = 'house',
  ResidentialBuilding = 'residential_building',
  OfficeBuilding = 'office_building',
  Land = 'land',
  Other = 'other'
}

export interface FormTourDoc extends Document {
  purpose: TourPurpose
  propertyType: TourPropertyType
  budget: string
}

export const FormTourSchema = new Schema(
  {
    purpose: {
      type: String,
      enum: Object.values(TourPurpose),
      required: true
    },
    propertyType: {
      type: String,
      enum: Object.values(TourPropertyType),
      required: true
    },
    budget: {
      type: String,
      required: true
    }
  },
  {
    timestamps: true,
    _id: false,
    autoIndex: true
  }
)
