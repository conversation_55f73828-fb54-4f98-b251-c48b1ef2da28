import { Schema } from 'mongoose'
import path from 'path'
import capitalize from 'lodash/capitalize'
import winston from '@app/service/logger'
import { mail } from '@app/service/nodemailer'
import { i18next } from '@app/i18next'
import { FormDoc } from './form.model'
import jobNames from '@app/jobs/name'
import agenda from '@app/agenda'

export default function addFormHooks(schema: Schema) {
  /**
   * Created Hook
   * - Send notification email to the agency
   */
  schema.post<FormDoc>('save', async function () {
    const locale = this.locale.toLowerCase()
    const locals = {
      locale,
      userName: this.firstName + ' ' + this.lastName,
      firstName: this.firstName,
      lastName: this.lastName,
      email: this.email,
      phone: this.phone,
      formRent: this.formRent,
      formBuy: this.formBuy,
      formSell: this.formSell,
      formTour: this.formTour,
      preferredLanguage: this.preferredLanguage
        .map((code: string) => i18next.getFixedT(locale)(`inquiry.availableLanguages.${code}`))
        .join(i18next.getFixedT(locale)('inquiry.comma')),
      memo: this.memo,
      adminUrl: process.env.ADMIN_URL,
      webUrl: process.env.WEB_URL
    }

    const from = '"Living Japan" <<EMAIL>>'
    const attachments = [
      {
        filename: 'logo.png',
        path: path.join('./templates/assets/logo.png'),
        cid: 'logo'
      }
    ]

    mail
      .send({
        template: `form-${this.formType}`,
        message: {
          from,
          to: [process.env.SUPPORT_EMAIL],
          replyTo: this.email,
          subject: `【Living Japan】${capitalize(this.formType)} Quick Form Response`,
          attachments
        },
        locals: {
          ...locals,
          locale: 'en-US', // Currently only english
          preferredLanguage: this.preferredLanguage
            .map((code: string) => i18next.getFixedT('en-US')(`inquiry.availableLanguages.${code}`))
            .join(i18next.getFixedT('en-US')('inquiry.comma'))
        }
      })
      .catch(error => {
        winston.error(error)
        // eslint-disable-next-line no-console
        console.error(error)
      })

    mail
      .send({
        template: `confirm-form-${this.formType}`,
        message: {
          from,
          to: [this.email],
          replyTo: process.env.SUPPORT_EMAIL,
          attachments
        },
        locals
      })
      .catch(error => {
        winston.error(error)
        // eslint-disable-next-line no-console
        console.error(error)
      })

    if (this.formType != 'tour') {
      mail
        .send({
          template: `form-response-${this.formType}/${locale != 'jp-jp' ? locale : 'en-us'}`,
          message: {
            from,
            to: [this.email],
            replyTo: process.env.SUPPORT_EMAIL,
            attachments
          },
          locals
        })
        .catch(error => {
          winston.error(error)
          // eslint-disable-next-line no-console
          console.error(error)
        })
    }

    agenda.now(jobNames.notifyFormOnChatwork, {
      roomId: '282253714',
      payload: {
        locale: locals.locale,
        _id: this._id,
        firstName: this.firstName,
        lastName: this.lastName,
        email: this.email,
        phone: this.phone,
        formRent: this.formRent,
        formBuy: this.formBuy,
        formSell: this.formSell,
        formTour: this.formTour,
        memo: this.memo,
        preferredLanguage: locals.preferredLanguage,
        formType: this.formType
      }
    })
  })
}
