import addFormHooks from './form.hooks'
import { FormRentSchema, FormRentDoc } from './form.rent.model'
import { FormBuySchema, FormBuyDoc } from './form.buy.model'
import { FormSellSchema, FormSellDoc } from './form.sell.model'
import { FormTourSchema, FormTourDoc } from './form.tour.model'
import { model, Model, Schema, Document } from 'mongoose'

export enum FormType {
  Rent = 'rent',
  Buy = 'buy',
  Sell = 'sell',
  Tour = 'tour'
}

export interface FormDoc extends Document {
  firstName: string
  lastName: string
  email: string
  phone?: string
  locale: string
  preferredLanguage: string[]
  memo: string
  formType: FormType
  formRent: FormRentDoc
  formBuy: FormBuyDoc
  formSell: FormSellDoc
  formTour: FormTourDoc
}

const formSchema = new Schema(
  {
    firstName: {
      type: String,
      required: true
    },
    lastName: {
      type: String
    },
    email: {
      type: String,
      required: true,
      validate: (value: string) => {
        return /^([\w-\.]+@([\w-]+\.)+[\w-]{2,4})?$/.test(value)
      }
    },
    phone: {
      type: String
    },
    locale: {
      type: String,
      required: true,
      // FIXME: should retrieve it from i18n instead of hardcoded
      enum: ['en-US', 'zh-CN', 'zh-TW', 'ko-KR', 'jp-JP']
    },
    preferredLanguage: {
      type: [String],
      enum: ['en', 'jp', 'zh', 'kr'],
      validate: {
        validator: (values: any) => Array.isArray(values) && values.length > 0,
        message: 'At least one preferred language must present '
      },
      required: true
    },
    memo: String,
    formType: {
      type: String,
      enum: Object.values(FormType),
      required: true
    },
    formRent: {
      type: FormRentSchema
    },
    formBuy: {
      type: FormBuySchema
    },
    formSell: {
      type: FormSellSchema
    },
    formTour: {
      type: FormTourSchema
    },
    utm: {
      utm_source: { type: String },
      utm_medium: { type: String },
      utm_campaign: { type: String },
      utm_term: { type: String },
      utm_content: { type: String },
      gclid: { type: String },
      fbclid: { type: String }
    },
    affiliate: {
      code: { type: String }
    }
  },
  { timestamps: true }
)

formSchema.index({ createdAt: 1 })
formSchema.index({ updatedAt: 1 })

addFormHooks(formSchema)

const Form = model('Form', formSchema)
export default Form
