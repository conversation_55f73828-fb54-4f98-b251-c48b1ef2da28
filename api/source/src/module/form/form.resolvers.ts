import { composeMongoose } from 'graphql-compose-mongoose'
import { authMiddleware as middleware } from '@app/middleware'
import { preparePaginationResolver } from 'graphql-compose-pagination'
import { extendedFindMany } from '@app/module/property/properties.resolver'
import Form from './form.model'
import { AffiliateTC } from '../affiliate/affiliate.schema'

export const FormTC = composeMongoose(Form, {
  name: 'Form',
  description: 'The entity which can be rented'
})

FormTC.addRelation('affiliateDetail', {
  resolver: () => AffiliateTC.mongooseResolvers.findOne(),
  prepareArgs: {
    filter: source => ({
      code: (source as any).affiliate.code
    })
  },
  projection: { code: 1, name: 1 }
})

const { mongooseResolvers: resolver } = FormTC
export const formById = resolver.findById()

export const forms = preparePaginationResolver(FormTC, {
  findManyResolver: extendedFindMany(Form, FormTC, {
    lean: true,
    filter: { operators: true } // FIXME: performance?
  }),
  countResolver: FormTC.mongooseResolvers.count(),
  name: 'pagination', // Default
  perPage: 20 // Default
}).withMiddlewares([middleware.isAuth])

export const sendForm = resolver.createOne({
  record: {
    removeFields: ['_id', 'createdAt', 'updatedAt'],
    isRequired: true
  }
})
