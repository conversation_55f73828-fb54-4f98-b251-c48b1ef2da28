import get from 'lodash/get'
import minBy from 'lodash/minBy'
import { getLocaleField } from '@app/utils'

interface GetPropertyNiceNameParams {
  roomCode: string
  locale: string
}

export const getPropertyNiceName = async (params: GetPropertyNiceNameParams) => {
  const { StationModel } = await import('../access/access.model')
  const { default: Property } = await import('./property.model')

  const property: any = await Property.findOne(
    { 'rooms.code': params.roomCode },
    { 'access.stations': 1, 'rooms.roomName': 1, 'rooms.code': 1 },
    { lean: true }
  )
  const validStations = property.access.stations.filter(
    station => station?.stationId && station?.stationId !== ''
  )
  const nearestStation: any = minBy(validStations, 'distance.walk')
  const locale = getLocaleField({ locale: params.locale })
  const station = await StationModel.findById(
    nearestStation.stationId,
    { [locale]: 1 },
    { lean: true }
  )
  const stationName = get(station, locale, '[missing]')
  const roomName = property.rooms.find(room => room.code === params.roomCode)?.roomName
  return `${stationName} Sta. ${roomName}`
}
