import Property from '../property.model'
import { PropertyTC } from '../property.resolvers'
import { generateRoomCode, syncFees } from './room.model'

const RoomTC = PropertyTC.getFieldTC('rooms').getType()
const excludedFields = ['createdAt', 'updatedAt', 'code', 'monthlyFee']
const InputRoomTC = PropertyTC.getFieldOTC('rooms')
  .getITC()
  .removeField(excludedFields)
  .setTypeName('RoomInput')
  .getType()

// TODO: will be used in the future
// eslint-disable-next-line no-unused-vars
const updateRoom = {
  type: RoomTC,
  args: {
    propertyId: 'MongoID!',
    code: 'String!',
    record: InputRoomTC
  },
  resolve: async (__, args) => {
    const filter = {
      _id: args.propertyId,
      'rooms.code': args.code
    }

    const update = {
      $set: Object.keys(args.record).reduce(
        (acc, key) => ({
          ...acc,
          [`rooms.$.${key}`]: args.record[key]
        }),

        // Sync the calculation of monthly fee, for quicker index & search
        args.record.fees ? { ['rooms.$.monthlyFee']: syncFees(args.record.fees) } : {}
      )
    }

    const property = await Property.findOneAndUpdate(filter, update, { new: true })

    if (!property) return null
    return (property as any).rooms.find((room: any) => room.code === args.code)
  }
}

const updateRoomVisibility = {
  type: 'Boolean',
  args: {
    propertyId: 'MongoID!',
    code: 'String!',
    value: 'EnumPropertyRoomsVisibility!'
  },
  resolve: async (__, args) => {
    const filter = {
      _id: args.propertyId,
      'rooms.code': args.code
    }

    const update = {
      $set: {
        'rooms.$.visibility': args.value
      }
    }

    const result = await Property.findOneAndUpdate(filter, update)
    return !!result
  }
}

const deleteRoom = {
  type: RoomTC,
  args: {
    propertyId: 'MongoID!',
    code: 'String!'
  },
  resolve: async (__, args) => {
    const result = await Property.findById(args.propertyId)
    const property = result as any

    if (property.rooms.length === 1) {
      await Property.findByIdAndDelete(args.propertyId)
      return null
    }
    const deleted = property.rooms.find((room: any) => room.code === args.code)
    await Property.findByIdAndUpdate(args.propertyId, {
      $pull: {
        rooms: { code: args.code }
      }
    } as any)

    return deleted
  }
}
const cloneRoom = {
  type: RoomTC,
  args: {
    propertyId: 'MongoID!',
    code: 'String!'
  },
  resolve: async (__, args) => {
    const result = await Property.findById(args.propertyId)
    if (!result) throw new Error('Property does not exist')
    const property = result as any
    const selectedRoom = property.rooms.find((room: any) => room.code === args.code)
    if (!selectedRoom) throw new Error('Room does not exist')

    // We'll generate the code
    selectedRoom.code = generateRoomCode()

    const update = {
      $push: {
        rooms: selectedRoom
      }
    }

    // TODO: return the clone item
    await Property.findByIdAndUpdate(args.propertyId, update)
    return null
  }
}

export default { mutations: { cloneRoom, updateRoomVisibility, deleteRoom }, queries: {} }
