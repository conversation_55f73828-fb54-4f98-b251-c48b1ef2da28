import { customAlphabet } from 'nanoid'
import mongoose from '@app/mongoose'

const { Schema } = mongoose

export const generateRoomCode = customAlphabet('1234567890', 8)

export const RoomImage = new Schema(
  {
    featured: {
      type: String,
      required: true
    },
    floorPlan: {
      type: String,
      required: true
    },
    collections: {
      type: [String],
      required: true,
      validate: {
        validator: function (collections: string[]) {
          return collections && collections.length > 0
        },
        message: 'Images collection must have at least one value'
      }
    }
  },
  { _id: false }
)

export enum PropertyRoomFeeType {
  FixedPrice = 'fixed_price',
  MonthlyMultiplier = 'monthly_multiplier'
}

const RoomFee = new Schema(
  {
    feeType: {
      type: String,
      enum: Object.values(PropertyRoomFeeType),
      required: true
    },
    value: {
      type: Number,
      required: true
    }
  },
  { _id: false }
)

export enum PropertyRoomOtherFeeType {
  KeyReplacement = 'key_replacement',
  Support24Hours = 'support_24_hours',
  Cleaning = 'cleaning',
  Administrative = 'administrative',
  Disinfection = 'disinfection',
  FireInsurance = 'fire_insurance',
  Community = 'community',
  Other = 'other',
  NA = 'na'
}

export enum PropertyRoomOtherFeeChargeTiming {
  MoveOut = 'move_out',
  MoveIn = 'move_in',
  Monthly = 'monthly',
  Yearly = 'yearly',
  NA = 'na'
}

const OtherRoomFee = new Schema(
  {
    feeType: {
      type: String,
      enum: Object.values(PropertyRoomOtherFeeType),
      default: 'na'
    },
    value: {
      type: Number
    },
    chargeTiming: {
      type: String,
      enum: Object.values(PropertyRoomOtherFeeChargeTiming),
      default: 'na'
    }
  },
  { _id: false }
)

const MonthlyFee = new Schema(
  {
    rentFee: Number,
    maintenanceFee: Number
  },
  { _id: false }
)

const MoveInFee = new Schema(
  {
    deposit: { type: RoomFee, required: true },
    keyMoney: { type: RoomFee, required: true },
    agencyFee: { type: RoomFee, required: true },
    guarantorFee: { type: RoomFee, required: true },
    contractRenewalFee: { type: RoomFee, required: true }
  },
  { _id: false }
)

const Fee = new Schema(
  {
    monthly: {
      type: MonthlyFee,
      required: true
    },
    moveIn: {
      type: MoveInFee,
      required: true
    },
    others: [OtherRoomFee]
  },
  { _id: false }
)

export enum EnumPropertyRoomAvailabilityStatus {
  Available = 'available',
  AvailableSoon = 'available_soon',
  Renovation = 'renovation',
  UnderConstruction = 'under_construction'
}

const Availability = new Schema(
  {
    status: {
      type: String,
      enum: Object.values(EnumPropertyRoomAvailabilityStatus),
      required: true
    },
    now: { type: Boolean, default: false },
    enquire: { type: Boolean, default: false }
  },
  { _id: false }
)

export enum EnumPropertyRoomMainWindow {
  East = 'east',
  Southeast = 'southeast',
  South = 'south',
  Southwest = 'southwest',
  West = 'west',
  Northwest = 'northwest',
  Northeast = 'northeast',
  North = 'north',
  Unknown = 'unknown'
}

export enum EnumPropertyRoomVisibility {
  Public = 'public',
  Private = 'private',
  Full = 'full'
}

export enum EnumPropertyRoomTransactionType {
  Agency = 'agency',
  Owner = 'owner',
  Agent = 'agent'
}

export enum EnumPropertyRoomContractType {
  Standard = 'standard',
  FixedTerm = 'fixed-term'
}

export const PropertyRoom = new Schema(
  {
    code: {
      type: String,
      index: true,
      unique: true,
      default: generateRoomCode
    },
    roomName: String,
    numberOfRoom: {
      type: Number,
      min: 1,
      max: 10,
      required: true
    },
    visibility: {
      type: String,
      enum: Object.values(EnumPropertyRoomVisibility),
      required: true
    },
    companyManagement: {
      type: String,
      default: ''
    },
    telephone: {
      type: String,
      default: ''
    },
    images: {
      type: RoomImage,
      required: true
    },
    availability: {
      type: Availability,
      required: true
    },
    layout: {
      type: String,
      validate: {
        validator: (value: string) => /^(S?L?D?K|R)$/.test(value),
        message: 'Valid layout must R, K, DK, SK, LDK, SDK, or SLDK.'
      }
    },
    contractLength: {
      type: Number,
      required: true
    },
    transactionType: {
      type: String,
      enum: Object.values(EnumPropertyRoomTransactionType)
      // FIXME: later confirm if this is really a necessary required
      // If required, we need to fix all the database
      // required: true,
    },
    contractType: {
      type: String,
      enum: Object.values(EnumPropertyRoomContractType),
      required: true
    },
    size: {
      type: Number,
      index: true,
      required: true
    },
    mainWindow: {
      type: String,
      enum: Object.values(EnumPropertyRoomMainWindow),
      required: true
    },
    floor: {
      type: String,
      validate: {
        validator: (value: string) => /^[1-9][0-9]?$|^100$|^B[1-3]$/.test(value),
        message: 'Valid floor must be between 1~100 or B1~B3'
      },
      required: true
    },
    tags: {
      type: [String],
      default: [],
      index: true
    },
    facilities: {
      basic: [String],
      building: [String],
      other: [String]
    },
    // These fields sync automatically so we can search it easily
    monthlyFee: { type: Number, index: true },
    roomLayout: { type: String, index: true },
    fees: {
      type: Fee,
      required: true
    },
    pageViews: {
      type: Object,
      default: {}
    },
    inquiryCount: {
      type: Number,
      default: 0,
      index: true
    },
    rejMetadata: {
      propertyId: {
        type: String,
        index: true
      }
    }
  },
  {
    timestamps: true,
    _id: false,
    autoIndex: true
  }
)

export function syncFees(fees: any) {
  if (!fees?.monthly) return 0
  const feeKeys = Object.keys(fees.monthly)
  return feeKeys.reduce((acc, key) => acc + (fees.monthly[key] || 0), 0)
}
