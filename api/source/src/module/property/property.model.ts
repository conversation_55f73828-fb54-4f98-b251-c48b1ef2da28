import mongoose from '@app/mongoose'
import addPropertyHooks from './property.hooks'
import { PropertyRoom } from './room/room.model'

const { Schema } = mongoose

const StationAccess = new Schema(
  {
    lineId: { type: String },
    stationId: { type: String },
    distance: {
      bus: Number,
      walk: Number
    }
  },
  { _id: false }
)

export const PropertyImage = new Schema(
  {
    collections: {
      type: [String],
      required: true,
      validate: {
        validator: function (collections: string[]) {
          return collections && collections.length > 0
        },
        message: 'Images collection must have at least one value'
      }
    }
  },
  { _id: false }
)

export const PropertyLocation = new Schema(
  {
    position: { lat: Number, long: Number },
    addressLine: { type: String },
    postalCode: { type: String },
    region: { type: String, required: true },
    prefecture: { type: String, required: true },
    cityArea: { type: String, required: true },
    city: { type: String, required: true },
    area: { type: String, required: true },
    chome: { type: String, required: true }
  },
  { _id: false }
)

export enum EnumPropertyStructure {
  Wooden = 'wooden',
  LightSteel = 'light_steel',
  HeavySteel = 'heavy_steel',
  Steel = 'steel',
  ReinforceConcrete = 'reinforce_concrete',
  SteelFramedReinforcedConcreteBlock = 'steel_framed_reinforced_concrete_block',
  Other = 'other'
}

export const PropertyAddress = new Schema(
  {
    coord: [Number],
    line: { type: String },
    postalCode: { type: String },
    prefecture: { type: String, required: true },
    city: { type: String, required: true },
    chome: { type: String, required: true }
  },
  { _id: false }
)

const PropertySchema = new Schema(
  {
    buildingName: String,
    agencyId: {
      type: Schema.Types.ObjectId,
      index: true,
      ref: 'Agency',
      required: true,
      validate: {
        isAsync: true,
        validator: async function (value: String) {
          return await mongoose.model('Agency').findById(value)
        },
        message: 'The specified agency does not exist'
      }
    },
    location: {
      type: PropertyLocation
      // required: true
    },
    address: {
      type: PropertyAddress,
      required: true
    },
    buildingType: {
      type: 'String',
      enum: ['apartment', 'house', 'share_house', 'monthly_apartment'],
      required: true
    },
    builtDate: {
      type: Date,
      required: true
    },
    maxFloor: {
      type: Number,
      required: true
    },
    structure: {
      type: String,
      enum: Object.values(EnumPropertyStructure),
      required: true
    },
    images: {
      type: PropertyImage,
      required: true
    },
    access: {
      stations: [StationAccess]
    },
    rooms: {
      type: [PropertyRoom],
      required: true
    },
    languageComment: {
      type: Object
    },
    // These fields automatically calculated or filled
    highestMonthlyPrice: {
      type: Number,
      index: true
    },
    lowestMonthlyPrice: {
      type: Number,
      index: true
    },
    pageViews: {
      type: Object,
      index: true,
      default: {}
    },
    nearestStationDistance: {
      type: Number,
      index: true,
      default: 99999
    },
    rejMetadata: {
      buildingId: {
        type: String,
        index: true
      }
    }
  },
  { timestamps: true }
)

PropertySchema.index({ createdAt: 1 })
PropertySchema.index({ updatedAt: 1 })

addPropertyHooks(PropertySchema)

const Property = mongoose.model('Property', PropertySchema)

export default Property
