import * as PropertyResolvers from './property.resolvers'
import RoomSchema from './room/room.schema'

export { PropertyTC } from './property.resolvers'

export default {
  queries: {
    propertyById: PropertyResolvers.propertyById,
    properties: PropertyResolvers.properties,
    property: PropertyResolvers.property,
    featuredProperties: PropertyResolvers.PropertyTC.getResolver('featuredProperties'),

    // Subdocument schemas
    ...RoomSchema.queries,
    tags: PropertyResolvers.TagCount.getResolver('tags')
  },
  mutations: {
    createProperty: PropertyResolvers.createProperty,
    updateProperty: PropertyResolvers.updateProperty,
    deleteProperty: PropertyResolvers.deleteProperty,
    incrementPageView: PropertyResolvers.incrementPageView,

    // Subdocument schemas
    ...RoomSchema.mutations
  }
}
