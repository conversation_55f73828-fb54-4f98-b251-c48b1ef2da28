import winston from '@app/service/logger'
import { mail } from '@app/service/nodemailer'
import { EmailOptions } from 'email-templates'
import { schemaComposer } from 'graphql-compose'
import validator from 'validator'
import fetch from 'node-fetch'
import escape from 'lodash/escape'

export const Mail = schemaComposer.createObjectTC({
  name: 'Mail'
})
console.log("test")

schemaComposer.createInputTC({
  name: 'SendToSupportInput',
  fields: {
    name: 'String!',
    email: 'String!',
    message: 'String!',
    token: 'String!'
  }
})

Mail.addResolver({
  kind: 'mutation',
  name: 'sendToSupport',
  type: 'Boolean',
  args: {
    input: 'SendToSupportInput!'
  },
  resolve: async ({ args }) => {
    if (validator.isEmpty(args.input.name)) throw new Error('name is required')
    if (!validator.isEmail(args.input.email)) throw new Error('Email is not valid')
    if (validator.isEmpty(args.input.message)) throw new Error('message is required')

    // Recaptcha token checking
    const SECRET_KEY = process.env.RECAPTCHA_SECRET_KEY
    const baseUrl = 'https://www.google.com/recaptcha/api/siteverify'
    const { token } = args.input
    const response = await fetch(`${baseUrl}?secret=${SECRET_KEY}&response=${token}`)
    const result = await response.json()

    if (!result.success) {
      throw new Error('Invalid Recaptcha Token')
    }

    type Data = {
      name: string
      email: string
      message: string
    }
    const options: EmailOptions<Data> = {
      template: 'mail-to-support',
      message: {
        from: `"${args.input.name}" <<EMAIL>>`,
        to: [process.env.SUPPORT_EMAIL],
        replyTo: args.input.email,
        subject: '【Living Japan】Support Message'
      },
      locals: {
        name: args.input.name,
        email: args.input.email,
        message: escape(args.input.message).replace(/\n/g, '<br />')
      }
    }

    try {
      await mail.send(options)
      return true
    } catch (error) {
      winston.error(error)
      // eslint-disable-next-line no-console
      console.log(error.message)
      return false
    }
  }
})
export default {
  queries: {},
  mutations: {
    sendToSupport: Mail.getResolver('sendToSupport')
  }
}
