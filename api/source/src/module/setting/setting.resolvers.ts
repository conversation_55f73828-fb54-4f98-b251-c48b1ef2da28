import { composeMongoose } from 'graphql-compose-mongoose'
import { GraphQLList, GraphQLString } from 'graphql'
import Setting from '@app/module/setting/setting.model'

export const SettingTC = composeMongoose(Setting, {
  name: 'Setting',
  description: 'Set the featured properties'
})

const { mongooseResolvers: resolver } = SettingTC

// FIXME: set the middleware and only let admin can use it
export const createOrUpdateFeatured = {
  type: new GraphQLList(GraphQLString),
  args: {
    featured: {
      type: new GraphQLList(GraphQLString)
    }
  },
  resolve: async (__, args) => {
    const options = { new: true, upsert: true }
    const result: any = await Setting.findOneAndUpdate({}, args, options)
    return result.featured
  }
}

export const getFeatureProperties = resolver.findOne()

export const getAPIVersion = {
  type: 'String',
  resolve: () => {
    const pkg = require('../../../package.json')
    return pkg.version
  }
}
