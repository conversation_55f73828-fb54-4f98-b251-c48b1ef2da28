import { AreaTC, PrefectureTC, cityById, cities, chomes, prefectures } from './area.resolvers'
import { NewAddress } from './import.resolvers'

const isLocal = process.env.NODE_ENV === 'local'

export default {
  queries: {
    availableLocations: AreaTC.getResolver('availableLocations'),
    searchLocations: AreaTC.getResolver('searchLocations'),
    cityNameById: AreaTC.getResolver('cityNameById'),

    // New address
    prefectureByName: PrefectureTC.getResolver('prefectureByName'),
    prefectures,
    cityById,
    cities,
    chomes
  },
  mutations: {
    ...(isLocal ? { importAddress: NewAddress.getResolver('importAddress') } : {}),

    // FIXME: Remove this as soon the new address stable enough in prod
    syncPropertyNewAddress: NewAddress.getResolver('syncPropertyNewAddress')
  }
}
