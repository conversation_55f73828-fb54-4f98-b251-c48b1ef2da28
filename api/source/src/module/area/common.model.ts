import { Document } from 'mongoose'

export interface BaseAddress extends Document {
  _id: string
  jp: string
  kana: string
  en: string
  zh_cn: string
  zh_tw: string
  ko_kr: string
}

export const addressDef = {
  _id: { type: String, unique: true, required: true, index: true },
  jp: { type: String, required: true },
  kana: { type: String },
  en: { type: String },
  zh_cn: { type: String },
  zh_tw: { type: String },
  ko_kr: { type: String }
}

export const addressOptions = { _id: false, timestamps: false, versionKey: false }
