import { signUp } from '../auth/resolvers'
import User from '../auth/user'
import Agency from './agency.model'
import mongoose from 'mongoose'

export default function addAgencyHooks(schema) {
  /**
   * Hook to check duplicate email when agency is being updated
   * also update the user account email according to the agency email
   */
  schema.pre('save', async function () {
    if (this.isNew) return
    const instance = this as any
    const agencyId = instance._id
    const existingEmail = instance.public.email

    // Check any duplicated email first
    const email = instance.public.email

    const duplicatedOnAgency = await Agency.findOne({
      _id: { $ne: mongoose.Types.ObjectId(agencyId) },
      'public.email': email
    })
    if (duplicatedOnAgency) {
      throw new Error('Email already taken by other agency')
    }

    const duplicatedOnUser = await User.findOne({ agencyId: { $ne: agencyId }, email: email })
    if (duplicatedOnUser) {
      throw new Error('Email already taken as an account login')
    }

    await User.findOneAndUpdate({ agencyId }, { email: existingEmail })
  })

  /**
   * Hook to check the new agency data before being saved
   */
  schema.pre('save', async function () {
    if (!this.isNew) return
    const instance = this as any

    // Check any duplicated email first
    const email = instance.public.email
    const duplicate = await Agency.findOne({ 'public.email': email })
    if (duplicate) {
      throw new Error('Email already taken by other agency')
    }

    await signUp.resolve({
      args: {
        email,
        agencyId: instance._id,
        password: 'default__password__please__change__it__soon'
      },
      context: { i18n: { language: 'jp-JP' } }
    })
  })
}
