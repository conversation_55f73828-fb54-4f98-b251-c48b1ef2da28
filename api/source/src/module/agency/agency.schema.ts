import { getLocale<PERSON>ield } from '@app/utils'
import { composeMongoose } from 'graphql-compose-mongoose'
import get from 'lodash/get'
import Inquiry from '../inquiry/inquiry.model'
import Property from '../property/property.model'
import Agency from './agency.model'
import { hasAuth } from './agency.validators'
import PrefectureModel from '../area/prefecture.model'
import ChomeModel from '../area/chome.model'
import CityModel from '../area/city.model'

export const AgencyTC = composeMongoose(Agency, {
  name: 'Agency',
  description: 'An entity which own and create the properties listing',
  inputType: {
    removeFields: ['_id', 'createdAt', 'updatedAt']
  }
})

const { mongooseResolvers: resolver } = AgencyTC

AgencyTC.addFields({
  inquiryCount: {
    type: 'Int',
    description: 'How many inquiries made to the agency',
    async resolve(source: any) {
      return await Inquiry.countDocuments({ agencyId: source._id })
    }
  },
  publishedRoomCount: {
    type: 'Int',
    description: 'How many published room under the agency',
    async resolve(source: any) {
      const result = await Property.aggregate([
        { $match: { agencyId: source._id } },
        { $unwind: '$rooms' },
        { $match: { 'rooms.visibility': 'public' } },
        { $group: { _id: null, count: { $sum: 1 } } }
      ])
      return get(result, '[0].count', 0)
    }
  },
  totalRoomCount: {
    type: 'Int',
    description: 'How many room under the agency',
    async resolve(source: any) {
      const result = await Property.aggregate([
        { $match: { agencyId: source._id } },
        { $unwind: '$rooms' },
        { $group: { _id: null, count: { $sum: 1 } } }
      ])
      return get(result, '[0].count', 0)
    }
  },
  addressDetail: {
    type: 'AgencyPublicAddress',
    description: 'The Localized of the property address information',
    // eslint-disable-next-line no-unused-vars
    async resolve(source: any, __args, context) {
      const document = await Agency.findById(source._id, { 'public.address': 1 })
      const { chome, prefecture, city, ...remaining } = (document as any).public?.address

      const field = getLocaleField(context, 'jp')
      const matchedPrefecture = await PrefectureModel.findById(prefecture, { en: 1, [field]: 1 })
      const matchedCity = await CityModel.findById(city, { en: 1, [field]: 1 })
      const matchedChome = await ChomeModel.findById(chome, { jp: 1, en: 1, [field]: 1 })

      return {
        ...remaining,
        prefecture: get(matchedPrefecture, field) || matchedPrefecture?.en || '[missing]',
        city: get(matchedCity, field) || matchedCity?.en || '[missing]',
        chome: get(matchedChome, field) || matchedChome?.en || matchedChome?.jp || '[missing]'
      }
    }
  }
})

export default {
  queries: {
    agencyById: resolver.findById(),
    agencies: resolver.pagination({ perPage: 2 })
  },
  mutations: {
    createAgency: resolver
      .createOne({
        record: {
          isRequired: true,
          requiredFields: ['name', 'email']
        }
      })
      .withMiddlewares([hasAuth]),

    updateAgency: resolver
      .updateById({
        record: {
          isRequired: true
        }
      })
      .withMiddlewares([hasAuth]),

    deleteAgency: resolver.removeById().withMiddlewares([hasAuth])
  }
}
