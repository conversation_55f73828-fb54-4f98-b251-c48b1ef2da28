import validator from 'validator'
import mongoose from '@app/mongoose'
import addAgencyHooks from './agency.hooks'
const { Schema } = mongoose

const agencySchema = new Schema(
  {
    basic: {
      companyName: {
        type: String,
        required: true
      },
      license: {
        haveLicense: {
          type: Boolean,
          default: true
        },
        licenseType: {
          type: String,
          enum: ['minister_litt', 'governor_tokyo', 'na']
        },
        number: String,
        issueNumber: String,
        expiryDate: {
          from: String,
          until: String
        }
      }
    },
    public: {
      name: String,
      imageUrl: String,
      address: {
        postalCode: String,
        prefecture: String,
        city: String,
        chome: String,
        streetAddress: String,
        buildingName: String
      },
      phone: String,
      email: {
        type: String,
        required: true,
        index: true,
        unique: true,
        validate: [validator.isEmail, 'invalid email']
      },
      availableLanguages: [String],
      businessHour: {
        start: String,
        end: String
      },
      closeDays: [String],
      website: String
    },
    serviceFee: {
      feeType: {
        type: String,
        enum: ['percentage', 'fixed'],
        default: 'percentage'
      },
      value: {
        type: Number,
        default: 3
      }
    }
  },
  { timestamps: true }
)

addAgencyHooks(agencySchema)

const Agency = mongoose.model('Agency', agencySchema)

export default Agency
