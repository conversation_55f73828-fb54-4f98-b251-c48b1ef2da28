import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3'
import { schemaComposer } from 'graphql-compose'

const s3 = new S3Client({
  region: 'ap-northeast-1', // FIXME: move to env?
  useAccelerateEndpoint: true
})

export const S3UploadSignedUrl = schemaComposer.createObjectTC({
  name: 'S3UploadSignedUrl',
  fields: {
    url: 'String'
  }
})

S3UploadSignedUrl.addResolver({
  kind: 'query',
  name: 's3UploadSignedUrl',
  type: 'S3UploadSignedUrl',
  args: {
    type: 'String!', // ie. 'image/jpeg'
    path: 'String'
  },
  resolve: async ({ args }) => {
    try {
      const params = {
        Bucket: process.env.S3_BUCKET,
        Key: args.path,
        ContentType: args.type
      }
      const command = new PutObjectCommand(params)
      const url = await getSignedUrl(s3, command)
      return { url }
    } catch (error) {
      return Promise.reject(error)
    }
  }
})
