import crypto from 'crypto-random-string'
import dayjs from 'dayjs'

const userService = {
  verifyRequest: async user => {
    const token = crypto({ length: 48, type: 'url-safe' })
    const expiresIn = dayjs().add(7, 'days')

    user.set({
      account: {
        verification: {
          token,
          expiresIn
        }
      }
    })

    await user.save()

    return token
  }
}

export default userService
