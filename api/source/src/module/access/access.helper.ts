export const populateAccessData = async (propertyId: string, stations: any) => {
  const { StationModel } = await import('../access/access.model')
  const { default: Property } = await import('../property/property.model')

  const getLineId = async (sourceStationId: string) => {
    const station = await StationModel.findById(sourceStationId).exec()
    return station.lineIds.join(',')
  }

  const updateAccess = async (propertyId: string, stations: any) => {
    const blank = [null, undefined, '']
    let tempAccess = []
    let isUpdated = false
    for (let x = 0; x < stations.length; x += 1) {
      let updatedLineId
      if (
        !blank.includes(stations[x].stationId) &&
        blank.includes(stations[x].lineId) &&
        (stations[x].distance.walk !== 0 || stations[x].distance.bus !== 0)
      ) {
        // eslint-disable-next-line no-await-in-loop
        updatedLineId = await getLineId(stations[x].stationId)
        tempAccess.push({
          distance: stations[x].distance,
          stationId: stations[x].stationId,
          lineId: updatedLineId || ''
        })
        isUpdated = true
      } else {
        tempAccess.push({
          distance: stations[x].distance,
          stationId: stations[x].stationId,
          lineId: stations[x].lineId
        })
      }
    }
    if (isUpdated) {
      await Property.updateOne(
        { _id: propertyId },
        {
          access: {
            stations: tempAccess
          }
        }
      ).exec()
      isUpdated = false
      tempAccess = []
    }
  }

  updateAccess(propertyId, stations)
}
