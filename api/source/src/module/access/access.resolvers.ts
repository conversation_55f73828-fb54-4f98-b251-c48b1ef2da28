import { getLocale<PERSON>ield } from '@app/utils'
import { composeMongoose } from 'graphql-compose-mongoose'
import { StationModel, ServiceLineModel } from './access.model'

export const ServiceLineTC = composeMongoose(ServiceLineModel, {
  name: 'ServiceLine',
  description: 'The train service line'
})

export const StationTC = composeMongoose(StationModel, {
  name: 'Station',
  description: 'The station is a place where the train transit'
})

const localeName = {
  type: 'String',
  resolve: (source, _args, context) => {
    const locale = getLocaleField(context)
    return source[locale]
  },
  projection: { name: true, en: true, zh_cn: true, zh_tw: true, ko_kr: true }
}

ServiceLineTC.addFields({ localeName })
StationTC.addFields({ localeName })

ServiceLineTC.addRelation('stations', {
  resolver: () => StationTC.mongooseResolvers.dataLoaderMany({ lean: true }),
  prepareArgs: {
    _ids: source => (source as any).stationIds
  },
  projection: { stationIds: 1 }
})

StationTC.addRelation('serviceLines', {
  resolver: () => ServiceLineTC.mongooseResolvers.dataLoaderMany({ lean: true }),
  prepareArgs: {
    _ids: source => (source as any).lineIds
  },
  projection: { lineIds: 1 }
})

StationTC.deprecateFields({
  en: 'Will be removed in favor of `localeName` field and header `accept-language`'
})

ServiceLineTC.deprecateFields({
  en: 'Will be removed in favor of `localeName` field and header `accept-language`'
})
