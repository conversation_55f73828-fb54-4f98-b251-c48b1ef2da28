import agenda from '@app/agenda'
import jobNames from '@app/jobs/name'

export const startSyncREJImport = {
  type: 'JSON',
  args: {},
  resolve: async () => {
    const existingJobs = await agenda.jobs({ name: jobNames.importREJCsv })
    if (existingJobs) {
      await stopSyncREJImport.resolve()
    }

    const job = agenda.create(jobNames.importREJCsv)
    job.repeatEvery('2 hours')
    return await job.save()
  }
}
export const stopSyncREJImport = {
  type: 'JSON',
  args: {},
  resolve: async () => {
    const numEnabled = await agenda.cancel({ name: jobNames.importREJCsv })
    return { numEnabled }
  }
}
