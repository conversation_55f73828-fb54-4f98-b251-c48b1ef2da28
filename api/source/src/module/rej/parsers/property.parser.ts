import { EnumPropertyStructure } from '@app/module/property/property.model'
import { EnumPropertyRoomMainWindow } from '@app/module/property/room/room.model'
import { Maybe } from 'graphql/jsutils/Maybe'
import { ParsingErrorItem } from '../rej.model'

export const parseStructure = (text: string) => {
  const index = {
    Wood: EnumPropertyStructure.Wooden,
    'Steel Frame': EnumPropertyStructure.Steel,
    'Light Gauge Steel': EnumPropertyStructure.LightSteel,
    'Reinforced Concrete': EnumPropertyStructure.ReinforceConcrete
  }

  const value = index[text]
  const error: Maybe<ParsingErrorItem> = value
    ? null
    : { colName: 'structure', message: `Unknown keyword of '${text}'` }

  return { value, error }
}

export const parseRoomLayout = (text: string) => {
  const pattern = /(\d+)(S?L?D?K|R)/
  const [, numberOfRoom, layout] = pattern.exec(text)
  const value = { numberOfRoom, layout }
  const error: Maybe<ParsingErrorItem> =
    numberOfRoom && layout
      ? null
      : { colName: 'room_type', message: `Unknown keyword of '${text}'` }
  return { value, error }
}

export const parseFacilities = row => {
  const basicIndex = {
    internet_wifi: 'free_wifi',
    aircon: 'air_conditioner',
    underfloor_heating: 'floor_heating_system',
    separate_toilet: 'bath_toilet_separated',
    washlet: 'bidet',
    washing_machine: 'washing_machine',
    induction_cooker: 'electric_stove',
    full_kitchen: 'builtin_kitchen',
    counter_kitchen: 'counter_kitchen',
    loft: 'loft',
    autolock: 'auto_lock'
  }

  const buildingIndex = {
    elevator: 'elevator',
    concierge: 'concierge',
    balcony: 'balcony',
    parking: 'parking',
    delivery_box: 'delivery_box'
  }

  return {
    basic: Object.keys(row)
      .map(key => (row[key] === 'Y' ? basicIndex[key] : null))
      .filter(item => !!item),
    building: Object.keys(row)
      .map(key => (row[key] === 'Y' ? buildingIndex[key] : null))
      .filter(item => !!item),
    other: []
  }
}

export const parseWindowDirection = row => {
  return Object.values(EnumPropertyRoomMainWindow).reduce((origin, direction) => {
    if (row[`facing_${direction}`] === 'Y') return direction
    return origin
  }, EnumPropertyRoomMainWindow.Unknown)
}

export const parseTags = row => {
  if (row.furnished === 'Y') return ['furnished']
  return []
}
