import { ParsingErrorItem } from '../rej.model'

export const parseImages = row => {
  const keywords = {
    building: [
      'Exterior',
      'Parking',
      'Equipment',
      'Layout Drawing',
      'Shared Facility',
      'Entrance Hall',
      'View / Scenery',
      'Garden',
      'Lobby'
    ],
    room: [
      'Living Room',
      'Bedroom',
      'Kitchen',
      'Storage',
      'Toilet',
      'Bathroom',
      'Washroom',
      'Security',
      'Entrance',
      'Balcony / Veranda'
    ],
    floorPlan: 'Floorplan'
  }

  const images = {
    building: [],
    room: [],
    floorPlan: null
  }

  const maxImageCount = 16
  let errors = [] as ParsingErrorItem[]
  for (let index = 1; index <= maxImageCount; index++) {
    const colName = `image_category_${index}`
    const category = row[colName]
    const url = row[`image_url_${index}`]

    if (keywords.building.includes(category)) {
      images.building.push(url)
    } else if (keywords.room.includes(category)) {
      images.room.push({ category, url })
    } else if (keywords.floorPlan === category) {
      if (images.floorPlan) {
        errors.push({
          colName,
          message: 'Floor plan image is duplicated. Make sure only have one image exist at a time.'
        })
        continue
      }
      images.floorPlan = url
    } else if (!category || category === '') {
      continue
    } else {
      errors.push({
        colName,
        message: `Unidentified image category keyword of '${category}'`
      })
    }
  }

  // Sort to get better room views
  images.room.sort((item1, item2) => {
    return keywords.room.indexOf(item1.category) - keywords.room.indexOf(item2.category)
  })

  if (!images.floorPlan)
    errors.push({
      colName: 'image_category_',
      message: 'Floor image is required. Make sure one `Floorplan` image category exist at a time'
    })
  if (images.room.length === 0) {
    errors.push({
      colName: 'image_category_',
      message: 'Room image is required. Make sure at least one room image exist at a time'
    })
  }

  return {
    errors,
    value: {
      ...images,
      room: images.room.map(item => item.url)
    }
  }
}
