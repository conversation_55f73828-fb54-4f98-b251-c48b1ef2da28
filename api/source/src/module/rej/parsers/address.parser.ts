import ChomeModel from '@app/module/area/chome.model'
import CityModel from '@app/module/area/city.model'
import PrefectureModel from '@app/module/area/prefecture.model'
import { Maybe } from 'graphql/jsutils/Maybe'
import { ParsingErrorItem } from '../rej.model'

interface ParseAddressReturn {
  errors: ParsingErrorItem[]
  value: Maybe<any> // FIXME: use `PropertyAddress` type later
}

export const parseAddress = async (row): Promise<ParseAddressReturn> => {
  let baseDebuggingInfo = ''
  const { map_lat: lat, map_lng: long, prefecture, city, district } = row
  const matchedPrefecture = await PrefectureModel.findOne(
    { jp: new RegExp(prefecture) },
    { _id: 1, jp: 1 }
  )
  if (!matchedPrefecture) {
    return {
      errors: [
        {
          colName: 'prefecture',
          message: `Unidentified prefecture keyword of ${prefecture}`
        }
      ],
      value: null
    }
  }

  baseDebuggingInfo = `prefId: ${matchedPrefecture._id}. `
  const matchedCity = await CityModel.findOne(
    {
      prefId: matchedPrefecture._id,
      $or: [{ jp: new RegExp(city) }, { jp: new RegExp(city.replace('ケ', 'ヶ')) }]
    },
    { _id: 1, jp: 1 }
  )
  if (!matchedCity) {
    return {
      errors: [
        {
          colName: 'city',
          message: `${baseDebuggingInfo}Unidentified city keyword of ${city}`
        }
      ],
      value: null
    }
  }

  baseDebuggingInfo = `${baseDebuggingInfo}. cityId: ${matchedCity._id}. `

  const matchedChome = await ChomeModel.findOne(
    {
      prefId: matchedPrefecture._id,
      cityId: matchedCity._id,
      $or: [{ jp: new RegExp(district) }, { jp: new RegExp(district.replace('ケ', 'ヶ')) }]
    },
    { _id: 1 }
  )
  if (!matchedChome) {
    return {
      errors: [
        {
          colName: 'district',
          message: `${baseDebuggingInfo}Unidentified district keyword ${district}`
        }
      ],
      value: null
    }
  }

  return {
    errors: [],
    value: {
      coord: [long, lat],
      line: '',
      postalCode: row.postcode,
      prefecture: matchedPrefecture._id,
      city: matchedCity._id,
      chome: matchedChome._id
    }
  }
}
