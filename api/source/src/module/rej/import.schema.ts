import * as ImportResolvers from './import.resolvers'
import * as JobResolvers from './job.resolvers'

export default {
  queries: {
    rejImportResults: ImportResolvers.rejImportResults,
    rejImportResultById: ImportResolvers.rejImportResultById
  },
  mutations: {
    importPropertyFromREJ: ImportResolvers.importPropertyFromREJ,
    startSyncREJImport: JobResolvers.startSyncREJImport,
    stopSyncREJImport: JobResolvers.stopSyncREJImport
  }
}
