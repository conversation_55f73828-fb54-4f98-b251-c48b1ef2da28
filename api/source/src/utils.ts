/**
 * Extract locale from resolver function context
 * @param context resolve context object
 * @returns locale code with friendly format for db field
 */
export const getLocaleField = (context: any, jpField = 'name') => {
  const locale = context?.i18n?.language || 'en'
  // we'll use the original name for jp
  if (locale.includes('jp')) return jpField
  return locale.replace('-', '_').toLowerCase()
}
