import toobusy from 'toobusy-js'
import { Request, Response, NextFunction } from 'express'

/**
 * Middleware that checks node event-loop lag using toobusy-js.
 * When the server is under heavy load, it immediately responds
 * with 503 Service Unavailable instead of processing the request.
 */
export default function toobusyMiddleware(req: Request, res: Response, next: NextFunction): void {
    if (toobusy()) {
        res.status(503).json({ message: 'Server is too busy. Please try again later.' })
    } else {
        next()
    }
}