import i18next from 'i18next'
import i18nextMiddleware from 'i18next-express-middleware'
import localeEN from './locales/en.json'
import localeJP from './locales/jp.json'
import localeZHCN from './locales/zh-cn.json'
import localeZHTW from './locales/zh-tw.json'
import localeKoKr from './locales/ko-kr.json'
import localeEsEs from './locales/es.json'
import localeFrFr from './locales/fr.json'

i18next.use(i18nextMiddleware.LanguageDetector).init({
  detection: {
    order: ['header'],
    lookupHeader: 'accept-language'
  },
  lowerCaseLng: true,
  preload: ['en'],
  supportedLngs: ['en', 'jp-jp', 'zh-cn', 'zh-tw', 'ko-kr', 'es-es', 'fr-fr'],
  fallbackLng: 'en',
  resources: {
    en: { translation: localeEN },
    'ko-kr': { translation: localeKoKr },
    'jp-jp': { translation: localeJP },
    'zh-cn': { translation: localeZHCN },
    'zh-tw': { translation: localeZHTW },
    'es-es': { translation: localeEsEs },
    'fr-fr': { translation: localeFrFr }
  }
})

export { i18next, i18nextMiddleware }
