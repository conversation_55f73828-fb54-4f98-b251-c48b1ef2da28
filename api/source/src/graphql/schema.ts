const { schemaComposer } = require('graphql-compose')

import '@app/graphql/types'

import { authMiddleware as middleware } from '@app/middleware'
import { user<PERSON>alidator as validator } from '@app/validator'
import {
  UserTC,
  AgencyTC,
  AffiliateTC,
  PropertyTC,
  AreaTC,
  InquiryTC,
  UploadSchema,
  AccessSchema,
  MailSchema,
  SettingTC,
  AnalyticTC,
  REJ,
  FormTC
} from '@app/module'

schemaComposer.Query.addFields({
  user: UserTC.getResolver('user', [middleware.isAuth]),
  ...AgencyTC.queries,
  ...PropertyTC.queries,
  ...AreaTC.queries,
  ...UploadSchema.queries,
  ...InquiryTC.queries,
  ...AccessSchema.queries,
  ...SettingTC.queries,
  ...AnalyticTC.queries,
  ...FormTC.queries,
  ...REJ.queries,
  ...AffiliateTC.queries
})

schemaComposer.Mutation.addFields({
  ...AgencyTC.mutations,
  ...PropertyTC.mutations,
  ...InquiryTC.mutations,
  ...AccessSchema.mutations,
  ...MailSchema.mutations,
  ...SettingTC.mutations,
  ...REJ.mutations,
  ...FormTC.mutations,
  ...AreaTC.mutations,
  ...AffiliateTC.mutations,
  signIn: UserTC.getResolver('signIn', [middleware.isGuest, validator.signIn]),
  signInWithFB: UserTC.getResolver('signInWithFB', [middleware.isGuest, validator.signInWithFB]),
  signUp: UserTC.getResolver('signUp', [middleware.isGuest, validator.signUp]),
  logout: UserTC.getResolver('logout', [middleware.isAuth]),
  verifyRequest: UserTC.getResolver('verifyRequest', [middleware.isAuth, middleware.isUnverfied]),
  verify: UserTC.getResolver('verify'),
  resetPassword: UserTC.getResolver('resetPassword', [middleware.isGuest, validator.resetPassword]),
  newPassword: UserTC.getResolver('newPassword', [middleware.isGuest, validator.newPassword]),
  changePassword: UserTC.getResolver('changePassword', [
    middleware.isAuth,
    validator.changePassword
  ]),
  updateUser: UserTC.getResolver('updateUser', [middleware.isAuth, validator.updateUser]),
  switchLocale: UserTC.getResolver('switchLocale', [middleware.isAuth])
})

const schema = schemaComposer.buildSchema()

export default schema
