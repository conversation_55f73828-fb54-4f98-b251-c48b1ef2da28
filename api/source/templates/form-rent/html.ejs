<%- include('../commons/outlook_start.ejs'); -%>
<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Live In Japan Contact Support</title>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    body {
      -webkit-font-smoothing: antialiased;
      font-family: sans-serif;
      font-size: 14px;
      line-height: 100%;
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%;
      padding: 40px;
      background-color: #EDEFF0;
      color: #333;
    }

    .logo {
      margin-bottom: 30px;
    }

    .content {
      background-color: white;
      margin: auto;
      padding: 30px 20px 20px;
      max-width: 600px;
      border-radius: 10px;
    }

    .message {
      line-height: 21px;
      margin-bottom: 30px;
    }

    h3 {
      font-size: 14px;
      margin-bottom: 15px;
      line-height: 18px;
    }

    ul {
      list-style-type: none;
    }

    ul.table-content {
      margin-bottom: 30px;
    }

    li {
      margin-bottom: 10px;
      line-height: 21px;
    }

    .message-content {
      padding-left: 10px;
      border-left: 2px solid #333;
    }

    .bold {
      font-weight: bold;
    }
  </style>
</head>

<body>

  <div class="content">
    <center>
      <img class="logo" src="cid:logo">
    </center>
    <div class="message-content">
      <h3>
        <%=i18n.getFixedT(locale)('inquiry.confirmMail.reqInfoLabel')%>
      </h3>
      <ul class="table-content">
        <li><span class="bold">Name</span><br />
          <%=firstName%>
          <%=lastName%>
        </li>
        <li><span class="bold">Email</span><br />
          <%=email%>
        </li>
        <li><span class="bold">Phone</span><br />
          <%=phone%>
        </li>
        <li><span class="bold">Preferred Language</span><br />
          <%=preferredLanguage%>
        </li>
        <li><span class="bold">Occupation</span><br />
          <%=i18n.getFixedT(locale)(`formRent.occupation.${formRent.occupation}`)%>
        </li>
        <li><span class="bold">Visa Status</span><br />
          <%=i18n.getFixedT(locale)(`formRent.visaStatus.${formRent.visaStatus}`)%>
        </li>
        <li><span class="bold">Rent Budget</span><br />
          <%if(!i18n.getFixedT(locale)(`formRent.rentBudget.${formRent.rentBudget}`).includes('formRent.')) {%>
          <%=i18n.getFixedT(locale)(`formRent.rentBudget.${formRent.rentBudget}`)%>
          <% } else { %>
          ¥<%=formRent.rentBudget.replace('K', ',000' )%>
          <% } %>
        </li>
        <li><span class="bold">Floor Plan</span><br />
          <%if(!i18n.getFixedT(locale)(`formRent.floorPlan.${formRent.floorPlan}`).includes('formRent.')) {%>
          <%=i18n.getFixedT(locale)(`formRent.floorPlan.${formRent.floorPlan}`)%>
          <% } else { %>
          <%=formRent.floorPlan%>
          <% } %>
        </li>
        <li><span class="bold">Min Size</span><br />
          <%if(formRent.size > 90 ) {%>
          <%=formRent.size%>㎡ or more
          <% } else { %>
          <%=formRent.size%>㎡
          <% } %>
        </li>
        <li><span class="bold">Desired Location</span><br />
          <%=formRent.desiredLocation%>
        </li>
        <li><span class="bold">Distance to the nearest station</span><br />
          <%if(formRent.stationDistance> 0) {%>
          <%=formRent.stationDistance%> min walk
          <% } else { %>
          I don't care much
          <% } %>
        </li>
        <li><span class="bold">When to move in</span><br />
          <%=i18n.getFixedT(locale)(`formRent.whenToMoveIn.${formRent.whenToMoveIn}`)%>
        </li>
        <li><span class="bold">Other Preferences</span><br />
          <% for(var i=0; i<formRent.otherPreferences.length;i++) { %>
          <%=i18n.getFixedT(locale)(`formRent.otherPreferences.${formRent.otherPreferences[i]}`)%><br />
          <% } %>
        </li>
        <li><span class="bold">Memo</span><br />
          <% if (memo) { %>
          <%=memo%>
          <% } else { %>
          -
          <% } %>
        </li>
      </ul>
    </div>
    <%- include('../commons/footer.ejs'); -%>
  </div>
</body>

</html>
<%- include('../commons/outlook_end.ejs'); -%>