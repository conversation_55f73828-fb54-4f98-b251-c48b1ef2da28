{"name": "livein-japan-api", "version": "2.10.2", "description": "Living Japan GraphQL API", "scripts": {"build": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": "pm2-runtime --json ./dist", "start:local": "cross-env NODE_ENV=local APP_PATH=./src ts-node ./src/index.ts", "dev": "cross-env NODE_ENV=local APP_PATH=./src nodemon", "docker-local": "npm install && npm run dev", "lint": "eslint --debug ./src/**/*.ts", "lint:fix": "eslint --debug ./src --fix", "prettier": "prettier \"**/*.+(js|jsx|json|css|md)\"", "prettier:fix": "prettier --write \"**/*.+(js|jsx|json|css|md)\"", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org ipc-eng --project living-japan dist && sentry-cli sourcemaps upload --org ipc-eng --project living-japan dist"}, "engines": {"node": "14.16.0"}, "_moduleAliases": {"@app": "dist"}, "dependencies": {"@aws-sdk/client-s3": "^3.21.0", "@aws-sdk/s3-request-presigner": "^3.21.0", "@axiomhq/winston": "1.0.0-rc.1", "@fast-csv/parse": "^4.3.6", "@sentry/node": "^7.76.0", "agenda": "^4.1.3", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto-random-string": "^3.3.0", "dayjs": "^1.10.4", "ejs": "^3.1.5", "email-templates": "^8.0.2", "express": "^5.0.0-alpha.8", "express-graphql": "^0.12.0", "fast-csv": "^4.3.6", "flat": "^5.0.2", "ftp": "^0.3.10", "graphql": "^15.4.0", "graphql-compose": "^7.23.0", "graphql-compose-mongoose": "^9.2.0", "graphql-tag": "^2.11.0", "i18next": "^19.8.4", "i18next-express-middleware": "^2.0.0", "ioredis": "^4.19.2", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "module-alias": "^2.2.2", "mongoose": "^5.11.6", "nanoid": "^3.1.22", "node-fetch": "^2.7.0", "nodemailer": "^6.4.16", "pm2": "5.3.0", "toobusy-js": "^0.5.1", "validator": "^13.5.2", "winston": "^3.3.3", "dotenv": "^8.2.0"}, "devDependencies": {"@types/agenda": "^4.1.0", "@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.10", "@types/email-templates": "^8.0.2", "@types/express": "^4.17.11", "@types/flat": "^5.0.1", "@types/ftp": "^0.3.33", "@types/ioredis": "^4.22.0", "@types/jsonwebtoken": "^8.5.0", "@types/lodash": "^4.14.168", "@types/mongoose": "^5.10.4", "@types/node": "^14.14.34", "@types/node-fetch": "^2.5.10", "@types/nodemailer": "^6.4.1", "@types/validator": "^13.1.3", "@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "cross-env": "^7.0.3", "eslint": "^7.22.0", "eslint-config-prettier": "^6.14.0", "eslint-config-standard": "^16.0.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-standard": "^5.0.0", "prettier": "^2.2.1", "ts-node": "^9.1.1", "ts-node-dev": "^1.1.6", "typescript": "^4.2.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}