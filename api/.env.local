APP_NAME="livein-japan-api"
APP_URL="http://localhost"
APP_PORT=8000
NODE_PATH="./dist"
NODE_ENV=local
WEB_URL="http://localhost:3000"
ADMIN_URL="http://localhost:3001"
CORS_ORIGIN="localhost"

REDIS_HOST="redis"
REDIS_PORT=6379
REDIS_TOKEN_EXPIRY=86400
REDIS_PREFIX="local:"
REDIS_DBNO=8

DB_CONNECTION_STRING="mongodb://mongo:27017/livein-japan-local"

JWT_SECRET="CHANGE_ME_PLEASE"
JWT_EXPIRATION="48h"

MAIL_HOST="smtp.mailtrap.io"
MAIL_PORT=2525
MAIL_USER="CHANGE_ME_PLEASE"
MAIL_PASSWORD="CHANGE_ME_PLEASE"

API_LOG_FILENAME="api-logs.log"
S3_BUCKET="ASK__OTHER__DEVELOPER"
SUPPORT_EMAIL="<EMAIL>"

RECAPTCHA_SECRET_KEY="CHANGE_ME_PLEASE"

SLACK_API_URL="CHANGE_ME_PLEASE"
SLACK_ROOM_ID="CHANGE_ME_PLEASE"