# Living Japan API

Built using GRAPHQL-first approach.

## Migration Tips

We'll mainly using mongoDB Shell. For example, if we wanna move the fields inside the properties:

```javascript
db.properties.find().forEach(function (item) {
    // In case the field already exists, we are gonna skip it
    if (item.rooms[i].images.floorPlan) return
    for (i = 0; i < item.rooms.length; i++) {
        item.rooms[i].images.floorPlan = item.rooms[i].images.featured
        item.rooms[i].images.featured = item.images.featured
    }

    delete item.images.featured
    db.properties.replaceOne({ _id: item._id }, item )
})
```
