# ================================
# 01. Build from Source
# ================================

FROM node:18.20.0-alpine AS build
RUN echo "Building API"
LABEL stage=builder
WORKDIR /usr/src/api
COPY ./package.json ./package.json
COPY ./package-lock.json ./package-lock.json
RUN npm install
COPY . .
RUN npm run build

# ================================
# 02. Install Minimal Dependencies
# ================================

FROM node:18.20.0-alpine AS dependencies
LABEL stage=builder
RUN apk add bash
WORKDIR /usr/src/api
COPY ./package.json ./package.json
COPY ./package-lock.json ./package-lock.json
RUN npm install --only=prod
RUN npm prune --production
RUN npx node-prune

# ================================
# 03. Serving
# ================================

FROM node:18.20.0-alpine
WORKDIR /usr/src/api
COPY --from=dependencies /usr/src/api/node_modules ./node_modules
COPY --from=build /usr/src/api/dist ./dist
COPY --from=build /usr/src/api/templates ./templates
COPY --from=build /usr/src/api/package.json ./
COPY --from=build /usr/src/api/package-lock.json ./
COPY --from=build /usr/src/api/rds-combined-ca-bundle.pem ./
COPY --from=build /usr/src/api/.env ./
RUN npm install pm2 -g
EXPOSE 8000
# Add --max-memory-restart 1G to restart the process if it uses more than 1GB of memory
CMD ["pm2-runtime", "--json", "--max-memory-restart", "1G", "dist"]
