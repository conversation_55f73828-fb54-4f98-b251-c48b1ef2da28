# Live In Japan
Property rental in Japan

## Stack
- Typescript
- NuxtJS (VueJS)
- Express (NodeJS)
- MongoDB
- Apollo GraphQL

## Prequisite
- [Node.js v14.16.0](https://nodejs.org/en/download/)
- [Yarn](https://classic.yarnpkg.com/en/docs/install/) 
- [Docker](https://www.docker.com/products/docker-desktop)

## Development

First of all, copy the dotenv files from `.env.local` into `.env`
```bash
cp ./web/.env.local ./web/.env
cp ./admin/.env.local ./admin/.env
cp ./api/.env.local ./api/.env
```

Thanks to docker-compose, we can run all service easily:
```bash
# For the first time, this is include seeding the sample data
$ docker-compose -f docker-compose.dev.yml up --build 

# And for the next time run, to avoid replacing your existing data
# we can turn off the mongo_seed service
$ docker-compose -f docker-compose.dev.yml up --scale mongo_seed=0

# To restart specific service, ie. web
$ docker-compose -f docker-compose.dev.yml restart web
```

However, if you prefer to develop individually, you can enter the directory and run this command directly. 

```bash
# Example if you want to run the web
$ cd web && yarn && yarn dev # https://localhost:3000

# Example if you want to run the admin
$ cd admin && yarn && yarn dev  # https://localhost:3001
```

## Notes
For web and admin project, everytime there is new `.gql` or `.graphql` file, please run this to sync and generate the typescript definition. 
```bash
$ yarn gql
```

## More
For tools and more information, Please refer to the docs directory