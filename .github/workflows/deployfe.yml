name: deployfe

on:
  push:
    branches:
      - develop

  workflow_dispatch:
    inputs:
      input_text:
        description: Enter your commit want rollback
        type: text

jobs:
  deployment:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        ip: [1,2]
      fail-fast: true
      max-parallel: 1

    permissions:
      id-token: write
      contents: write

    steps:
    - name: BSTEP1
      id: ip
      uses: haythem/public-ip@v1.3

    - name: BSTEP2
      uses: actions/checkout@v4
      with:
          path: workdir

    - name: BSTEP3
      run: echo "${{ vars.BSTEP1 }}" | /bin/bash

    - name: BSTEP3
      uses: aws-actions/configure-aws-credentials@v4
      with:
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}
          role-to-assume: 'arn:aws:iam::${{ secrets.AWS_ID }}:role/${{ secrets.AWS_ROLE_NAME }}'

    - name: BSTEP4
      run: ${{ secrets.BSTEP2 }}

    - name: BSTEP5
      run: ${{ secrets.BRUN3 }} ${{ matrix.ip }} ${{github.actor}} 1

    - name: <PERSON>TEP6
      uses: docker/setup-buildx-action@v3

    - name: <PERSON>TEP7
      uses: docker/login-action@v3
      with:
          registry: ${{ env.ECR_REGISTRY }}

    - name: BSTEP8
      run: ${{ secrets.BRUN3 }} ${{ matrix.ip }} ${{github.actor}} 4 ${{ steps.ip.outputs.ipv4 }}

    - name: BSTEP9
      uses: shimataro/ssh-key-action@v2
      with:
        key: ${{ secrets.SSH_KEY_BASTION }}
        name: id_rsa_bastion
        known_hosts: unnecessary
        if_key_exists: replace
        config: |
          Host bastion
          HostName ${{ env.AWS_EC2_BASTION_IP }}
          Port ${{ env.AWS_EC2_BASTION_PORT }}
          User fwuser
          IdentityFile ~/.ssh/id_rsa_bastion

    - name: BSTEP10
      run: ${{ secrets.BRUN3 }} ${{ matrix.ip }} ${{github.actor}} 2 >> ~/.ssh/known_hosts

    - name: BSTEP11
      run: ${{ secrets.BRUN3 }} ${{ matrix.ip }} ${{github.actor}} 6 ${{ env.AWS_EC2_PORT }} ${{ steps.ip.outputs.ipv4 }} ${{ env.SERVERIP }}

    - name: BSTEP12
      uses: shimataro/ssh-key-action@v2
      with:
        key: ${{ secrets.SSH_KEY }}
        name: id_rsa_ec2
        known_hosts: unnecessary
        if_key_exists: replace
        config: |
          Host target_server
          HostName ${{ env.AWS_EC2_BASTION_IP }}
          Port ${{ env.AWS_EC2_PORT }}
          User ipc-deploy
          IdentityFile ~/.ssh/id_rsa_ec2

    - name: BSTEP13
      run: ${{ secrets.BRUN3 }} ${{ matrix.ip }} ${{github.actor}} 9 ${{ env.AWS_EC2_PORT }} >> ~/.ssh/known_hosts

    - name: BSTEP14
      continue-on-error: true
      run: ${{ secrets.BRUN3 }} ${{ matrix.ip }} ${{github.actor}} 13 ${{ env.AWS_EC2_PORT }} ${{ steps.ip.outputs.ipv4 }} "${{ env.SERVERIP }}" ${{ github.event.inputs.input_text }}

    - name: BSTEP15
      uses: shimataro/ssh-key-action@v2
      with:
        key: ${{ secrets.SSH_KEY_BASTION }}
        name: id_rsa_bastion
        known_hosts: unnecessary
        if_key_exists: replace
        config: |
          Host bastion
          HostName ${{ env.AWS_EC2_BASTION_IP }}
          Port ${{ env.AWS_EC2_BASTION_PORT }}
          User fwuser
          IdentityFile ~/.ssh/id_rsa_bastion

    - name: BSTEP16
      run: ${{ secrets.BRUN3 }} ${{ matrix.ip }} ${{github.actor}} 15 >> ~/.ssh/known_hosts
      if: always()

    - name: BSTEP17
      run: ${{ secrets.BRUN3 }} ${{ matrix.ip }} ${{github.actor}} 16 ${{ env.AWS_EC2_PORT }} ${{ env.SERVERIP }} ${{ steps.ip.outputs.ipv4 }}
      if: always()