#!/bin/sh

source cf-ssl-venv/bin/activate

export AWS_ACCESS_KEY_ID="<FILL_HERE>"
export AWS_SECRET_ACCESS_KEY="<FILL_HERE>"

certbot --agree-tos -a certbot-s3front:auth \
--certbot-s3front:auth-s3-bucket <YOUR_S3_BUCKET> \
--certbot-s3front:auth-s3-region <YOUR_S3_REGION> \
-i certbot-s3front:installer \
--certbot-s3front:installer-cf-distribution-id <YOUR_CF_DISTRIBUTION_ID> \
-d <YOUR_DOMAIN> --work-dir work --logs-dir log --config-dir conf