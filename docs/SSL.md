# SSL
Setup the SSL is obviously the most painful beside debugging a blackbox environment

So here is the steps to setup the SSL for the **first time only** using letsencrypt on our admin or cdn static hosting website.

## Step 1
First create a new [IAM policy](https://console.aws.amazon.com/iam/home?region=ap-northeast-1) with this `json` content.

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "VisualEditor0",
            "Effect": "Allow",
            "Action": [
                "iam:UpdateServerCertificate",
                "iam:ListServerCertificates",
                "iam:DeleteServerCertificate",
                "cloudfront:UpdateDistribution",
                "cloudfront:GetDistributionConfig",
                "iam:UploadServerCertificate"
            ],
            "Resource": "*"
        },
        {
            "Sid": "VisualEditor1",
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:PutObjectAcl"
            ],
            "Resource": [
                "arn:aws:s3:::admin.dev.livingjapan.com/*",
                "arn:aws:s3:::admin.livingjapan.com/*",
                "arn:aws:s3:::cdn.livingjapan.com/*"
            ]
        },
        {
            "Sid": "VisualEditor2",
            "Effect": "Allow",
            "Action": "s3:DeleteObject",
            "Resource": [
                "arn:aws:s3:::admin.dev.livingjapan.com/.well-known/*",
                "arn:aws:s3:::admin.livingjapan.com/.well-known/*",
                "arn:aws:s3:::cdn.livingjapan.com/.well-known/*"
            ]
        }
    ]
}
```

Create the user and attach the policy you just created before, the don't forget grab the `Access Key ID` and `Secret Access`, or it will lost forever. 

## Step 2
Next, within the server environment which will be responsible for the ongoing renewal of the SSL certificate, we need to have Python3 and Python3 Virtual Environments available. On my Ubuntu box, I needed to install the latter using, sudo apt-get install python3-venv.

* Within the current User's home directory, create a new directory to hold the SSL certificate renewal script and change directory into it
* Create a new Virtual Environment entitled, "cf-ssl-venv"
* Source the new environment's /bin/activate directory to enter the Python Virtual Environment
* Use pip to install Let's Encrypt's official "certbot" and the S3 + CloudFront plugin "certbot-s3front"
* Then exit the Python Virtual Environment

```bash
mkdir ~/cloudfront-ssl && cd ~/cloudfront-ssl
python3 -m venv cf-ssl-venv
source cf-ssl-venv/bin/activate
pip install certbot certbot-s3front
deactivate
```

## Step 3
Grab the script inside `scripts/renew-cert.sh` modify it, and give it the `+X` permission
```bash
chmod +X renew-cert.sh
bash renew-cert.sh
```

## Enjoy

Full reference

https://trycatchfinally.dev/posts/how-to-use-letsencrypt-ssl-cert-to-secure-custom-domain-with-aws-cloudfront/