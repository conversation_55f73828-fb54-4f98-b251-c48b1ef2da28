# Tools

Bunch of command that will help administrate the livein-japan system

## MongoDB

First we are gonna need the cli tool for the mongodb

```shell
mkdir mongodb-tools-install && cd mongodb-tools-install
wget https://fastdl.mongodb.org/tools/db/mongodb-database-tools-ubuntu2004-x86_64-100.3.1.deb
sudo apt install ./mongodb-database-tools-ubuntu2004-x86_64-100.3.1.deb
```

To backup from the existing database, simply use this command
```shell
mongodump --uri mongodb+srv://<USERNAME>:<PASSWORD>@cluster0.xr7fu.mongodb.net/livein-japan-dev
```

To restore into the database, we can do
```shell
mongorestore --uri mongodb+srv://<USERNAME>:<PASSWORD>@production.2sji1.mongodb.net 
```